interface ErrtionOptions {
    /** The message to use for the error */
    message: string;
    /** The code to categorise the error */
    code: string | number;
    /** The severity level of the error */
    level?: string | number;
    /** The parent of the error */
    parent?: Errtion | Error;
}
export interface Errtion extends Error, ErrtionOptions {
}
/**
 * Allow code and level inputs on Errlop.
 * We do this instead of a class extension, as class extensions do not interop well on node 0.8, which is our target.
 */
export declare function errtion(this: void, opts: ErrtionOptions, parent?: Errtion | Error): Errtion;
export {};
//# sourceMappingURL=util.d.ts.map