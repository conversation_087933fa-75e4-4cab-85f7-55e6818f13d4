// Model Discovery Service
// Dynamically fetches available models from various AI providers

import axios from 'axios';
import { ProviderRegistryService } from './ProviderRegistryService';

export interface Model {
    id: string;
    name: string;
    description: string;
    capabilities: string[];
    contextLength?: number;
    pricing?: {
        input: number;  // per 1K tokens
        output: number; // per 1K tokens
    };
    provider: string;
}

export interface ProviderModels {
    [provider: string]: Model[];
}

export class ModelDiscoveryService {
    private _modelCache: Map<string, { models: Model[]; timestamp: number }> = new Map();
    private _discoveryInProgress = new Map<string, Promise<Model[]>>();
    private readonly _cacheTimeout = 30 * 60 * 1000; // 30 minutes
    private readonly _discoveryTimeout = 15000; // 15 seconds
    private readonly _maxRetries = 3;

    /**
     * Get models for all providers
     */
    public async getAllModels(apiKeys: Record<string, string>): Promise<ProviderModels> {
        const providerRegistry = ProviderRegistryService.getInstance();
        const providerIds = providerRegistry.getProviderIds();
        const results: ProviderModels = {};

        await Promise.allSettled(
            providerIds.map(async (providerId) => {
                try {
                    const models = await this.getModelsForProvider(providerId, apiKeys[providerId]);
                    results[providerId] = models;
                } catch (error) {
                    console.warn(`Failed to fetch models for ${providerId}:`, error);
                    results[providerId] = this._getFallbackModels(providerId);
                }
            })
        );

        return results;
    }

    /**
     * Get models for a specific provider with enhanced caching and error handling
     */
    public async getModelsForProvider(provider: string, apiKey?: string): Promise<Model[]> {
        // Check cache first
        const cached = this._modelCache.get(provider);
        if (cached && Date.now() - cached.timestamp < this._cacheTimeout) {
            return cached.models;
        }

        // Check if discovery is already in progress for this provider
        const cacheKey = `${provider}_${apiKey ? 'auth' : 'noauth'}`;
        if (this._discoveryInProgress.has(cacheKey)) {
            return this._discoveryInProgress.get(cacheKey)!;
        }

        // Start discovery with timeout and retry logic
        const discoveryPromise = this._discoverModelsWithRetry(provider, apiKey);
        this._discoveryInProgress.set(cacheKey, discoveryPromise);

        try {
            const models = await discoveryPromise;

            // Cache the results
            this._modelCache.set(provider, {
                models,
                timestamp: Date.now()
            });

            return models;
        } catch (error) {
            console.error(`Error fetching models for ${provider}:`, error);
            return this._getFallbackModels(provider);
        } finally {
            // Clean up discovery tracking
            this._discoveryInProgress.delete(cacheKey);
        }
    }

    /**
     * Discover models with retry logic and timeout
     */
    private async _discoverModelsWithRetry(provider: string, apiKey?: string, retryCount = 0): Promise<Model[]> {
        try {
            return await Promise.race([
                this._fetchModelsForProvider(provider, apiKey),
                new Promise<Model[]>((_, reject) =>
                    setTimeout(() => reject(new Error('Discovery timeout')), this._discoveryTimeout)
                )
            ]);
        } catch (error) {
            if (retryCount < this._maxRetries) {
                console.warn(`Model discovery failed for ${provider}, retrying... (${retryCount + 1}/${this._maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
                return this._discoverModelsWithRetry(provider, apiKey, retryCount + 1);
            }
            throw error;
        }
    }

    /**
     * Fetch models for a specific provider
     */
    private async _fetchModelsForProvider(provider: string, apiKey?: string): Promise<Model[]> {
        switch (provider) {
            case 'deepseek':
                return this._fetchDeepSeekModels(apiKey);
            case 'groq':
                return this._fetchGroqModels(apiKey);
            case 'openrouter':
                return this._fetchOpenRouterModels(apiKey);
            case 'local':
                return this._fetchLocalModels();
            default:
                return this._getFallbackModels(provider);
        }
    }

    /**
     * Fetch OpenRouter models dynamically
     */
    private async _fetchOpenRouterModels(apiKey?: string): Promise<Model[]> {
        try {
            const response = await axios.get('https://openrouter.ai/api/v1/models', {
                headers: apiKey ? {
                    'Authorization': `Bearer ${apiKey}`,
                    'HTTP-Referer': 'https://github.com/v1b3-sama/extension',
                    'X-Title': 'V1b3-Sama VS Code Extension'
                } : {
                    'HTTP-Referer': 'https://github.com/v1b3-sama/extension',
                    'X-Title': 'V1b3-Sama VS Code Extension'
                },
                timeout: 10000
            });

            return response.data.data.map((model: any) => ({
                id: model.id,
                name: model.name || model.id,
                description: model.description || `${model.id} via OpenRouter`,
                capabilities: this._inferCapabilities(model),
                contextLength: model.context_length,
                pricing: model.pricing ? {
                    input: parseFloat(model.pricing.prompt) * 1000000, // Convert to per 1K tokens
                    output: parseFloat(model.pricing.completion) * 1000000
                } : undefined,
                provider: 'openrouter'
            }));
        } catch (error) {
            console.error('Failed to fetch OpenRouter models:', error);
            throw error;
        }
    }

    /**
     * Fetch Groq models with real API call
     */
    private async _fetchGroqModels(apiKey?: string): Promise<Model[]> {
        if (!apiKey) {
            return this._getFallbackModels('groq');
        }

        try {
            const response = await axios.get('https://api.groq.com/openai/v1/models', {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.data && response.data.data) {
                return response.data.data.map((model: any) => ({
                    id: model.id,
                    name: this._formatGroqModelName(model.id),
                    description: this._getGroqModelDescription(model.id),
                    capabilities: this._inferCapabilities(model),
                    contextLength: this._getGroqContextLength(model.id),
                    pricing: this._getGroqPricing(model.id),
                    provider: 'groq'
                }));
            }

            return this._getFallbackModels('groq');
        } catch (error) {
            console.error('Failed to fetch Groq models:', error);
            return this._getFallbackModels('groq');
        }
    }

    /**
     * Format Groq model name for display
     */
    private _formatGroqModelName(modelId: string): string {
        const nameMap: Record<string, string> = {
            'llama-3.3-70b-versatile': 'Llama 3.3 70B Versatile',
            'llama-3.1-8b-instant': 'Llama 3.1 8B Instant',
            'llama3-70b-8192': 'Llama 3 70B',
            'llama3-8b-8192': 'Llama 3 8B',
            'mixtral-8x7b-32768': 'Mixtral 8x7B',
            'gemma2-9b-it': 'Gemma 2 9B IT',
            'qwen3-32b': 'Qwen3 32B',
            'qwen-qwq-32b': 'Qwen QwQ 32B (Preview)',
            'deepseek-r1-distill-llama-70b': 'DeepSeek R1 Distill Llama 70B',
            'llama-guard-3-8b': 'Llama Guard 3 8B',
            'whisper-large-v3': 'Whisper Large v3',
            'whisper-large-v3-turbo': 'Whisper Large v3 Turbo'
        };
        return nameMap[modelId] || modelId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Get Groq model description
     */
    private _getGroqModelDescription(modelId: string): string {
        const descriptions: Record<string, string> = {
            'llama-3.3-70b-versatile': 'Latest Llama 3.3 70B model with 128K context - balanced performance and speed',
            'llama-3.1-8b-instant': 'Fast and efficient Llama 3.1 8B model for quick responses',
            'llama3-70b-8192': 'Llama 3 70B model with 8K context - high performance',
            'llama3-8b-8192': 'Llama 3 8B model with 8K context - fast and efficient',
            'mixtral-8x7b-32768': 'High-performance mixture of experts model with 32K context',
            'gemma2-9b-it': 'Google Gemma 2 instruction-tuned model',
            'qwen3-32b': 'Qwen3 32B model with 131K context - advanced reasoning',
            'qwen-qwq-32b': 'Qwen QwQ 32B preview model with enhanced reasoning capabilities',
            'deepseek-r1-distill-llama-70b': 'DeepSeek R1 distilled into Llama 70B architecture',
            'llama-guard-3-8b': 'Llama Guard 3 8B safety model for content moderation',
            'whisper-large-v3': 'OpenAI Whisper Large v3 for speech recognition',
            'whisper-large-v3-turbo': 'OpenAI Whisper Large v3 Turbo - faster speech recognition'
        };
        return descriptions[modelId] || `Groq ${modelId} model`;
    }

    /**
     * Get Groq context length
     */
    private _getGroqContextLength(modelId: string): number {
        const contextLengths: Record<string, number> = {
            'llama-3.3-70b-versatile': 131072, // 128K context
            'llama-3.1-8b-instant': 131072, // 128K context
            'llama3-70b-8192': 8192,
            'llama3-8b-8192': 8192,
            'mixtral-8x7b-32768': 32768,
            'gemma2-9b-it': 8192,
            'qwen3-32b': 131072, // 131K context
            'qwen-qwq-32b': 131072, // 128K context
            'deepseek-r1-distill-llama-70b': 131072,
            'llama-guard-3-8b': 8192,
            'whisper-large-v3': 448, // Audio model
            'whisper-large-v3-turbo': 448 // Audio model
        };
        return contextLengths[modelId] || 8192;
    }

    /**
     * Get Groq pricing
     */
    private _getGroqPricing(modelId: string): { input: number; output: number } | undefined {
        const pricing: Record<string, { input: number; output: number }> = {
            'llama-3.3-70b-versatile': { input: 0.59, output: 0.79 },
            'llama-3.1-8b-instant': { input: 0.05, output: 0.08 },
            'llama3-70b-8192': { input: 0.59, output: 0.79 },
            'llama3-8b-8192': { input: 0.05, output: 0.08 },
            'mixtral-8x7b-32768': { input: 0.24, output: 0.24 },
            'gemma2-9b-it': { input: 0.20, output: 0.20 },
            'qwen3-32b': { input: 0.29, output: 0.59 },
            'qwen-qwq-32b': { input: 0.29, output: 0.39 },
            'deepseek-r1-distill-llama-70b': { input: 0.75, output: 0.99 },
            'llama-guard-3-8b': { input: 0.20, output: 0.20 },
            'whisper-large-v3': { input: 0.111, output: 0.111 }, // Per hour transcribed
            'whisper-large-v3-turbo': { input: 0.04, output: 0.04 } // Per hour transcribed
        };
        return pricing[modelId];
    }

    /**
     * Fetch Local models (Ollama/LM Studio) with real API call
     */
    private async _fetchLocalModels(): Promise<Model[]> {
        try {
            // Try Ollama API first
            const response = await axios.get('http://localhost:11434/api/tags', {
                timeout: 5000
            });

            if (response.data && response.data.models) {
                return response.data.models.map((model: any) => ({
                    id: model.name,
                    name: this._formatLocalModelName(model.name),
                    description: `${model.name} running locally via Ollama`,
                    capabilities: this._inferLocalModelCapabilities(model.name),
                    contextLength: this._getLocalModelContextLength(model.name),
                    provider: 'local',
                    size: model.size,
                    modified: model.modified_at
                }));
            }

            return this._getFallbackModels('local');
        } catch (error) {
            // If Ollama is not available, try LM Studio
            try {
                const response = await axios.get('http://localhost:1234/v1/models', {
                    timeout: 5000
                });

                if (response.data && response.data.data) {
                    return response.data.data.map((model: any) => ({
                        id: model.id,
                        name: this._formatLocalModelName(model.id),
                        description: `${model.id} running locally via LM Studio`,
                        capabilities: this._inferLocalModelCapabilities(model.id),
                        contextLength: this._getLocalModelContextLength(model.id),
                        provider: 'local'
                    }));
                }
            } catch (lmStudioError) {
                console.log('Neither Ollama nor LM Studio detected, using fallback models');
            }

            return this._getFallbackModels('local');
        }
    }

    /**
     * Format local model name for display
     */
    private _formatLocalModelName(modelName: string): string {
        // Remove version tags and format nicely
        const cleanName = modelName.replace(/:\w+$/, '').replace(/-/g, ' ');
        return cleanName.replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Infer capabilities for local models
     */
    private _inferLocalModelCapabilities(modelName: string): string[] {
        const capabilities = ['text'];

        if (modelName.toLowerCase().includes('code') ||
            modelName.toLowerCase().includes('coder') ||
            modelName.toLowerCase().includes('llama')) {
            capabilities.push('code');
        }

        return capabilities;
    }

    /**
     * Get context length for local models
     */
    private _getLocalModelContextLength(modelName: string): number {
        // Extract context length from model name if present
        const contextMatch = modelName.match(/(\d+)k/i);
        if (contextMatch) {
            return parseInt(contextMatch[1]) * 1024;
        }

        // Default context lengths based on model type
        if (modelName.includes('llama3.2')) return 128000;
        if (modelName.includes('llama3.1')) return 128000;
        if (modelName.includes('codellama')) return 16384;
        if (modelName.includes('mistral')) return 32768;
        if (modelName.includes('qwen')) return 32768;

        return 8192; // Default
    }

    /**
     * Fetch DeepSeek models with real API call
     */
    private async _fetchDeepSeekModels(apiKey?: string): Promise<Model[]> {
        if (!apiKey) {
            return this._getFallbackModels('deepseek');
        }

        try {
            const response = await axios.get('https://api.deepseek.com/v1/models', {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.data && response.data.data) {
                return response.data.data.map((model: any) => ({
                    id: model.id,
                    name: model.id.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
                    description: this._getDeepSeekModelDescription(model.id),
                    capabilities: this._inferCapabilities(model),
                    contextLength: this._getDeepSeekContextLength(model.id),
                    pricing: this._getDeepSeekPricing(model.id),
                    provider: 'deepseek'
                }));
            }

            return this._getFallbackModels('deepseek');
        } catch (error) {
            console.error('Failed to fetch DeepSeek models:', error);
            return this._getFallbackModels('deepseek');
        }
    }

    /**
     * Get DeepSeek model description
     */
    private _getDeepSeekModelDescription(modelId: string): string {
        const descriptions: Record<string, string> = {
            'deepseek-coder': 'DeepSeek Coder - Specialized for code generation and analysis (legacy, merged into deepseek-chat)',
            'deepseek-chat': 'DeepSeek-V3-0324 - Latest general-purpose conversational model with 64K context',
            'deepseek-reasoner': 'DeepSeek-R1-0528 - Advanced reasoning model with chain-of-thought capabilities'
        };
        return descriptions[modelId] || `DeepSeek ${modelId} model`;
    }

    /**
     * Get DeepSeek context length
     */
    private _getDeepSeekContextLength(modelId: string): number {
        const contextLengths: Record<string, number> = {
            'deepseek-coder': 64000, // Updated to match current API
            'deepseek-chat': 64000,
            'deepseek-reasoner': 64000
        };
        return contextLengths[modelId] || 64000;
    }

    /**
     * Get DeepSeek pricing
     */
    private _getDeepSeekPricing(modelId: string): { input: number; output: number } | undefined {
        const pricing: Record<string, { input: number; output: number }> = {
            'deepseek-coder': { input: 0.27, output: 1.10 }, // Standard pricing (cache miss)
            'deepseek-chat': { input: 0.27, output: 1.10 }, // Standard pricing (cache miss)
            'deepseek-reasoner': { input: 0.55, output: 2.19 } // Standard pricing (cache miss)
        };
        return pricing[modelId];
    }

    /**
     * Get fallback models when API calls fail
     */
    private _getFallbackModels(provider: string): Model[] {
        const fallbackModels: Record<string, Model[]> = {
            groq: [
                {
                    id: 'llama-3.3-70b-versatile',
                    name: 'Llama 3.3 70B Versatile',
                    description: 'Latest Llama 3.3 70B model with 128K context - balanced performance and speed',
                    capabilities: ['text', 'code'],
                    contextLength: 131072,
                    pricing: { input: 0.59, output: 0.79 },
                    provider: 'groq'
                },
                {
                    id: 'llama-3.1-8b-instant',
                    name: 'Llama 3.1 8B Instant',
                    description: 'Fast and efficient Llama 3.1 8B model for quick responses',
                    capabilities: ['text', 'code'],
                    contextLength: 131072,
                    pricing: { input: 0.05, output: 0.08 },
                    provider: 'groq'
                },
                {
                    id: 'qwen3-32b',
                    name: 'Qwen3 32B',
                    description: 'Qwen3 32B model with 131K context - advanced reasoning',
                    capabilities: ['text', 'code', 'reasoning'],
                    contextLength: 131072,
                    pricing: { input: 0.29, output: 0.59 },
                    provider: 'groq'
                }
            ],
            openrouter: [
                {
                    id: 'openai/gpt-4o',
                    name: 'GPT-4o (via OpenRouter)',
                    description: 'OpenAI GPT-4o via OpenRouter',
                    capabilities: ['text', 'code', 'vision'],
                    provider: 'openrouter'
                },
                {
                    id: 'anthropic/claude-3.5-sonnet',
                    name: 'Claude 3.5 Sonnet (via OpenRouter)',
                    description: 'Anthropic Claude 3.5 Sonnet via OpenRouter',
                    capabilities: ['text', 'code', 'vision'],
                    provider: 'openrouter'
                },
                {
                    id: 'meta-llama/llama-3.1-405b-instruct',
                    name: 'Llama 3.1 405B (via OpenRouter)',
                    description: 'Meta Llama 3.1 405B via OpenRouter',
                    capabilities: ['text', 'code', 'reasoning'],
                    provider: 'openrouter'
                }
            ],
            local: [
                {
                    id: 'llama3.2',
                    name: 'Llama 3.2',
                    description: 'Meta Llama 3.2 running locally',
                    capabilities: ['text', 'code'],
                    provider: 'local'
                },
                {
                    id: 'mistral',
                    name: 'Mistral',
                    description: 'Mistral AI model running locally',
                    capabilities: ['text', 'code'],
                    provider: 'local'
                }
            ],
            deepseek: [
                {
                    id: 'deepseek-chat',
                    name: 'DeepSeek Chat (V3-0324)',
                    description: 'DeepSeek-V3-0324 - Latest general-purpose conversational model with 64K context',
                    capabilities: ['text', 'code'],
                    contextLength: 64000,
                    pricing: { input: 0.27, output: 1.10 },
                    provider: 'deepseek'
                },
                {
                    id: 'deepseek-reasoner',
                    name: 'DeepSeek Reasoner (R1-0528)',
                    description: 'DeepSeek-R1-0528 - Advanced reasoning model with chain-of-thought capabilities',
                    capabilities: ['text', 'code', 'reasoning'],
                    contextLength: 64000,
                    pricing: { input: 0.55, output: 2.19 },
                    provider: 'deepseek'
                },
                {
                    id: 'deepseek-coder',
                    name: 'DeepSeek Coder (Legacy)',
                    description: 'DeepSeek Coder - Specialized for code generation (legacy, merged into deepseek-chat)',
                    capabilities: ['text', 'code'],
                    contextLength: 64000,
                    pricing: { input: 0.27, output: 1.10 },
                    provider: 'deepseek'
                }
            ]
        };

        return fallbackModels[provider] || [];
    }

    /**
     * Infer model capabilities from model data
     */
    private _inferCapabilities(model: any): string[] {
        const capabilities = ['text'];

        // Add code capability for coding-focused models
        if (model.id?.includes('gpt-4') ||
            model.id?.includes('claude') ||
            model.id?.includes('gemini') ||
            model.id?.includes('deepseek') ||
            model.id?.includes('llama') ||
            model.id?.includes('mixtral') ||
            model.id?.includes('gemma') ||
            model.id?.includes('qwen') ||
            model.id?.includes('codellama') ||
            model.id?.includes('coder')) {
            capabilities.push('code');
        }

        // Add vision capability for multimodal models
        if (model.id?.includes('vision') ||
            model.id?.includes('claude-3') ||
            model.id?.includes('gemini') ||
            model.id?.includes('gpt-4o')) {
            capabilities.push('vision');
        }

        // Add reasoning capability for specialized reasoning models
        if (model.id?.includes('deepseek-reasoner') ||
            model.id?.includes('reasoning') ||
            model.id?.includes('405b')) {
            capabilities.push('reasoning');
        }

        return capabilities;
    }

    /**
     * Format model name for display
     */
    private _formatModelName(modelId: string): string {
        return modelId
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Clear model cache
     */
    public clearCache(): void {
        this._modelCache.clear();
    }

    /**
     * Get cache status
     */
    public getCacheStatus(): Record<string, { modelCount: number; age: number }> {
        const status: Record<string, { modelCount: number; age: number }> = {};

        for (const [provider, cache] of this._modelCache.entries()) {
            status[provider] = {
                modelCount: cache.models.length,
                age: Date.now() - cache.timestamp
            };
        }

        return status;
    }

    /**
     * Force refresh models for a provider (bypass cache)
     */
    public async forceRefreshModels(provider: string, apiKey?: string): Promise<Model[]> {
        // Clear cache for this provider
        this._modelCache.delete(provider);

        // Clear any ongoing discovery
        const cacheKey = `${provider}_${apiKey ? 'auth' : 'noauth'}`;
        this._discoveryInProgress.delete(cacheKey);

        // Fetch fresh models
        return this.getModelsForProvider(provider, apiKey);
    }

    /**
     * Get models by capability
     */
    public async getModelsByCapability(capability: string, apiKeys: Record<string, string>): Promise<Model[]> {
        const allModels = await this.getAllModels(apiKeys);
        const modelsWithCapability: Model[] = [];

        for (const [provider, models] of Object.entries(allModels)) {
            const filteredModels = models.filter(model =>
                model.capabilities && model.capabilities.includes(capability)
            );
            modelsWithCapability.push(...filteredModels);
        }

        return modelsWithCapability;
    }

    /**
     * Get fastest models (lowest latency providers)
     */
    public async getFastestModels(apiKeys: Record<string, string>): Promise<Model[]> {
        const allModels = await this.getAllModels(apiKeys);
        const fastProviders = ['groq', 'local']; // Known fast providers
        const fastModels: Model[] = [];

        for (const provider of fastProviders) {
            if (allModels[provider]) {
                fastModels.push(...allModels[provider]);
            }
        }

        return fastModels;
    }

    /**
     * Get most cost-effective models
     */
    public async getCostEffectiveModels(apiKeys: Record<string, string>): Promise<Model[]> {
        const allModels = await this.getAllModels(apiKeys);
        const costEffectiveModels: Model[] = [];

        for (const [provider, models] of Object.entries(allModels)) {
            const cheapModels = models.filter(model => {
                if (!model.pricing) return false;
                const avgCost = (model.pricing.input + model.pricing.output) / 2;
                return avgCost < 0.5; // Less than $0.50 per 1M tokens average
            });
            costEffectiveModels.push(...cheapModels);
        }

        // Sort by cost (ascending)
        return costEffectiveModels.sort((a, b) => {
            if (!a.pricing || !b.pricing) return 0;
            const avgCostA = (a.pricing.input + a.pricing.output) / 2;
            const avgCostB = (b.pricing.input + b.pricing.output) / 2;
            return avgCostA - avgCostB;
        });
    }

    /**
     * Search models by name or description
     */
    public async searchModels(query: string, apiKeys: Record<string, string>): Promise<Model[]> {
        const allModels = await this.getAllModels(apiKeys);
        const searchResults: Model[] = [];
        const lowerQuery = query.toLowerCase();

        for (const [provider, models] of Object.entries(allModels)) {
            const matchingModels = models.filter(model =>
                model.name.toLowerCase().includes(lowerQuery) ||
                model.description?.toLowerCase().includes(lowerQuery) ||
                model.id.toLowerCase().includes(lowerQuery)
            );
            searchResults.push(...matchingModels);
        }

        return searchResults;
    }

    /**
     * Get discovery statistics
     */
    public getDiscoveryStats(): {
        totalProviders: number;
        cachedProviders: number;
        activeDiscoveries: number;
        cacheHitRate: number;
    } {
        const totalRequests = this._modelCache.size + this._discoveryInProgress.size;
        const cacheHits = this._modelCache.size;

        return {
            totalProviders: this._modelCache.size,
            cachedProviders: this._modelCache.size,
            activeDiscoveries: this._discoveryInProgress.size,
            cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0
        };
    }
}
