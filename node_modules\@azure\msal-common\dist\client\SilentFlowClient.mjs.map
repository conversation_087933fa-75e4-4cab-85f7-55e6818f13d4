{"version": 3, "file": "SilentFlowClient.mjs", "sources": ["../../src/client/SilentFlowClient.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.tokenRefreshRequired", "ClientAuthErrorCodes.noAccountInSilentRequest", "TimeUtils.wasClockTurnedBack", "TimeUtils.isTokenExpired", "ClientAuthErrorCodes.authTimeNotFound"], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGG;AAsBH;AACM,MAAO,gBAAiB,SAAQ,UAAU,CAAA;IAC5C,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;AAEtC,QAAA,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;KAC3C;AAED;;;AAGG;IACH,MAAM,kBAAkB,CACpB,OAAgC,EAAA;AAEhC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,kCAAkC,EACpD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,IAAI,gBAAgB,GAAiB,YAAY,CAAC,cAAc,CAAC;QAEjE,IACI,OAAO,CAAC,YAAY;AACpB,aAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,yBAAyB;gBAChD,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAC9C;;YAEE,IAAI,CAAC,eAAe,CAChB,YAAY,CAAC,uBAAuB,EACpC,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvBA,oBAAyC,CAC5C,CAAC;AACL,SAAA;;AAGD,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,YAAA,MAAM,qBAAqB,CACvBC,wBAA6C,CAChD,CAAC;AACL,SAAA;AAED,QAAA,MAAM,eAAe,GACjB,OAAO,CAAC,OAAO,CAAC,QAAQ;AACxB,YAAA,4BAA4B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CACtD,OAAO,CAAC,OAAO,EACf,OAAO,EACP,SAAS,EACT,eAAe,EACf,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE;;YAEpB,IAAI,CAAC,eAAe,CAChB,YAAY,CAAC,sBAAsB,EACnC,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvBD,oBAAyC,CAC5C,CAAC;AACL,SAAA;AAAM,aAAA,IACHE,kBAA4B,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AACxD,YAAAC,cAAwB,CACpB,iBAAiB,CAAC,SAAS,EAC3B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CACtD,EACH;;YAEE,IAAI,CAAC,eAAe,CAChB,YAAY,CAAC,2BAA2B,EACxC,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvBH,oBAAyC,CAC5C,CAAC;AACL,SAAA;aAAM,IACH,iBAAiB,CAAC,SAAS;YAC3BG,cAAwB,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,EAC1D;;AAEE,YAAA,gBAAgB,GAAG,YAAY,CAAC,qBAAqB,CAAC;;AAGzD,SAAA;AAED,QAAA,MAAM,WAAW,GACb,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;AAC5D,QAAA,MAAM,WAAW,GAAgB;YAC7B,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC;AAChE,YAAA,WAAW,EAAE,iBAAiB;YAC9B,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,CACjC,OAAO,CAAC,OAAO,EACf,SAAS,EACT,eAAe,EACf,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB;AACD,YAAA,YAAY,EAAE,IAAI;YAClB,WAAW,EACP,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,WAAW,CAAC;SAC9D,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAE9D,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;AAC3D,SAAA;QAED,OAAO;AACH,YAAA,MAAM,WAAW,CACb,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC7C,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,WAAW,EAAE,OAAO,CAAC;YACvB,gBAAgB;SACnB,CAAC;KACL;IAEO,eAAe,CACnB,YAA0B,EAC1B,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,sBAAsB,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;AACI,YAAA,YAAY,EAAE,YAAY;SAC7B,EACD,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,YAAY,KAAK,YAAY,CAAC,cAAc,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,CAAmD,gDAAA,EAAA,YAAY,CAAE,CAAA,CACpE,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACK,IAAA,MAAM,6BAA6B,CACvC,WAAwB,EACxB,OAAgC,EAAA;AAEhC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,6CAA6C,EAC/D,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,IAAI,aAAsC,CAAC;QAC3C,IAAI,WAAW,CAAC,OAAO,EAAE;AACrB,YAAA,aAAa,GAAG,kBAAkB,CAC9B,WAAW,CAAC,OAAO,CAAC,MAAM,EAC1B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAC3C,CAAC;AACL,SAAA;;QAGD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACxC,YAAA,MAAM,QAAQ,GAAG,aAAa,EAAE,SAAS,CAAC;YAC1C,IAAI,CAAC,QAAQ,EAAE;AACX,gBAAA,MAAM,qBAAqB,CACvBC,gBAAqC,CACxC,CAAC;AACL,aAAA;AAED,YAAA,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,SAAA;QAED,OAAO,eAAe,CAAC,4BAA4B,CAC/C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,WAAW,EACX,IAAI,EACJ,OAAO,EACP,aAAa,CAChB,CAAC;KACL;AACJ;;;;"}