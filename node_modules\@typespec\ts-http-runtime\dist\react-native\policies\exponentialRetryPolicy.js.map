{"version": 3, "file": "exponentialRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/exponentialRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,gDAAgD,CAAC;AAC1F,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAyBnE;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CACpC,UAAyC,EAAE;;IAE3C,OAAO,WAAW,CAChB;QACE,wBAAwB,iCACnB,OAAO,KACV,kBAAkB,EAAE,IAAI,IACxB;KACH,EACD;QACE,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;KAC7D,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * The programmatic identifier of the exponentialRetryPolicy.\n */\nexport const exponentialRetryPolicyName = \"exponentialRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ExponentialRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A policy that attempts to retry requests while introducing an exponentially increasing delay.\n * @param options - Options that configure retry logic.\n */\nexport function exponentialRetryPolicy(\n  options: ExponentialRetryPolicyOptions = {},\n): PipelinePolicy {\n  return retryPolicy(\n    [\n      exponentialRetryStrategy({\n        ...options,\n        ignoreSystemErrors: true,\n      }),\n    ],\n    {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    },\n  );\n}\n"]}