{"version": 3, "file": "NodeType.d.ts", "sourceRoot": "", "sources": ["../../src/NodeType.ts"], "names": [], "mappings": "AAGA;;;;GAIG;AACH,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAEnD;;;GAGG;AACH,MAAM,MAAM,WAAW,GAAG,MAAM,OAAO,YAAY,CAAC;AAEpD;;GAEG;AACH,MAAM,MAAM,UAAU,GAAG,OAAO,GAAG,WAAW,GAAG,aAAa,CAAC;AAE/D;;;;;GAKG;AACH,MAAM,MAAM,eAAe,GAAG;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAC1B,KAAK,EAAE,eAAe,CAAC;IACvB,GAAG,EAAE,eAAe,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAE3E;;;GAGG;AACH,MAAM,WAAW,OAAO;IACpB,IAAI,EAAE,WAAW,CAAC;IAClB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,YAAY,CAAC;IACpB,GAAG,EAAE,eAAe,CAAC;IAErB,MAAM,CAAC,EAAE,aAAa,CAAC;CAC1B;AAED;;;;GAIG;AACH,MAAM,WAAW,WAAY,SAAQ,OAAO;IACxC,KAAK,EAAE,MAAM,CAAC;CACjB;AAED;;;GAGG;AACH,MAAM,WAAW,aAAc,SAAQ,OAAO;IAC1C,QAAQ,EAAE,OAAO,EAAE,CAAC;CACvB;AAKD,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;AAC3D,MAAM,MAAM,aAAa,GAAG,UAAU,GAAG,WAAW,GAAG,MAAM,CAAC;AAE9D,MAAM,MAAM,OAAO,GAAG,eAAe,GAAG,WAAW,GAAG,YAAY,GAAG,UAAU,GAAG,eAAe,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,YAAY,CAAC;AAC3C;;;GAGG;AACH,MAAM,MAAM,YAAY,GAClB,gBAAgB,GAChB,aAAa,GACb,qBAAqB,GACrB,iBAAiB,GACjB,WAAW,GACX,YAAY,GACZ,WAAW,GACX,gBAAgB,CAAC;AACvB;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG,eAAe,CAAC;AAC1C;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC;AAC3C;;GAEG;AACH,MAAM,MAAM,UAAU,GAAG,gBAAgB,CAAC;AAC1C;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,WAAW,GAAG,qBAAqB,CAAC;AAClE;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAC3B,UAAU,GACV,eAAe,GACf,aAAa,GACb,aAAa,GACb,WAAW,GACX,WAAW,GACX,YAAY,GACZ,YAAY,GACZ,cAAc,CAAC;AAErB,MAAM,WAAW,eAAgB,SAAQ,aAAa;IAClD,IAAI,EAAE,UAAU,CAAC;CACpB;AAED,MAAM,WAAW,gBAAiB,SAAQ,aAAa;IACnD,IAAI,EAAE,WAAW,CAAC;IAClB,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa;IAChD,IAAI,EAAE,QAAQ,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7B,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,qBAAsB,SAAQ,OAAO;IAClD,IAAI,EAAE,gBAAgB,CAAC;CAC1B;AAED,MAAM,WAAW,iBAAkB,SAAQ,aAAa;IACpD,IAAI,EAAE,YAAY,CAAC;IACnB,QAAQ,EAAE,YAAY,EAAE,CAAC;CAC5B;AAED,MAAM,WAAW,WAAY,SAAQ,aAAa;IAC9C,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,QAAQ,EAAE,WAAW,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,eAAgB,SAAQ,aAAa;IAClD,IAAI,EAAE,UAAU,CAAC;IACjB,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,QAAQ,EAAE,YAAY,EAAE,CAAC;CAC5B;AAED,MAAM,WAAW,YAAa,SAAQ,aAAa;IAC/C,IAAI,EAAE,OAAO,CAAC;IACd,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC,QAAQ,EAAE,YAAY,EAAE,CAAC;CAC5B;AAED,MAAM,WAAW,eAAgB,SAAQ,aAAa;IAClD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,UAAU,EAAE,CAAC;CAC1B;AAED,MAAM,WAAW,gBAAiB,SAAQ,aAAa;IACnD,IAAI,EAAE,WAAW,CAAC;IAClB,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW;IAC5C,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,cAAe,SAAQ,WAAW;IAC/C,IAAI,EAAE,SAAS,CAAC;CACnB;AAED,MAAM,WAAW,gBAAiB,SAAQ,WAAW;IACjD,IAAI,EAAE,WAAW,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACpC;AAED,MAAM,WAAW,UAAW,SAAQ,WAAW;IAC3C,IAAI,EAAE,KAAK,CAAC;CACf;AAED,MAAM,WAAW,eAAgB,SAAQ,aAAa;IAClD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa;IAChD,IAAI,EAAE,QAAQ,CAAC;IACf,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa;IAChD,IAAI,EAAE,QAAQ,CAAC;IACf,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC/B;AAGD,MAAM,WAAW,WAAY,SAAQ,WAAW;IAC5C,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,YAAa,SAAQ,OAAO;IACzC,IAAI,EAAE,OAAO,CAAC;CACjB;AAED,MAAM,WAAW,WAAY,SAAQ,aAAa,EAAE,WAAW;IAC3D,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,qBAAqB,EAAE,CAAC;CACrC;AAED,MAAM,WAAW,oBAAqB,SAAQ,aAAa,EAAE,YAAY;IACrE,IAAI,EAAE,eAAe,CAAC;IACtB,QAAQ,EAAE,qBAAqB,EAAE,CAAC;IAClC,aAAa,EAAE,aAAa,CAAC;CAChC;AAED,MAAM,WAAW,YAAa,SAAQ,OAAO,EAAE,WAAW,EAAE,cAAc;IACtE,IAAI,EAAE,OAAO,CAAC;CACjB;AAED,MAAM,WAAW,qBAAsB,SAAQ,OAAO,EAAE,cAAc,EAAE,YAAY;IAChF,IAAI,EAAE,gBAAgB,CAAC;IACvB,aAAa,EAAE,aAAa,CAAC;CAChC;AAED,MAAM,WAAW,iBAAkB,SAAQ,OAAO,EAAE,WAAW,EAAE,YAAY;IACzE,IAAI,EAAE,YAAY,CAAC;CACtB;AAGD,MAAM,WAAW,WAAW;IACxB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACrC;AAED,MAAM,WAAW,cAAc;IAC3B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACnC;AAED,MAAM,WAAW,YAAY;IACzB,UAAU,EAAE,MAAM,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;CACjB"}