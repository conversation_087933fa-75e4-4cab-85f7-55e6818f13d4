import type { OperationOptions, RequestParameters } from "./common.js";
/**
 * Helper function to convert OperationOptions to RequestParameters
 * @param options - the options that are used by Modular layer to send the request
 * @returns the result of the conversion in RequestParameters of RLC layer
 */
export declare function operationOptionsToRequestParameters(options: OperationOptions): RequestParameters;
//# sourceMappingURL=operationOptionHelpers.d.ts.map