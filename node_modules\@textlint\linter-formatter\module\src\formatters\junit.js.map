{"version": 3, "file": "junit.js", "sourceRoot": "", "sources": ["../../../src/formatters/junit.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,YAAY,CAAC;AAGb,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,cAAc,CAAC,OAAY;IAChC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;QAC1C,OAAO,OAAO,CAAC;IACnB,CAAC;SAAM,CAAC;QACJ,OAAO,SAAS,CAAC;IACrB,CAAC;AACL,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,OAAyB;IACxC,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,MAAM,IAAI,0CAA0C,CAAC;IACrD,MAAM,IAAI,gBAAgB,CAAC;IAE3B,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;QAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEjC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM;gBACF,kDAAkD;oBAClD,QAAQ,CAAC,MAAM;oBACf,YAAY;oBACZ,QAAQ,CAAC,MAAM;oBACf,UAAU;oBACV,MAAM,CAAC,QAAQ;oBACf,MAAM,CAAC;QACf,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO;YAC9B,MAAM,IAAI,GAAI,OAAe,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;YAC1D,MAAM,IAAI,sCAAsC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC;YACxF,MAAM,IAAI,GAAG,GAAG,IAAI,GAAG,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;YAClF,MAAM,IAAI,WAAW,CAAC;YACtB,MAAM,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;YACnD,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;YACjE,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC;YAChB,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;YAC5B,MAAM,IAAI,eAAe,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,gBAAgB,CAAC;QAC/B,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,IAAI,iBAAiB,CAAC;IAE5B,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,eAAe,SAAS,CAAC"}