// AI Tools Service Interface
// Provides tools that AI can call automatically for file operations and system tasks

export interface AIToolCall {
    name: string;
    parameters: Record<string, any>;
    id?: string;
}

export interface AIToolResult {
    success: boolean;
    result?: any;
    error?: string;
    metadata?: {
        filesCreated?: string[];
        filesModified?: string[];
        filesDeleted?: string[];
        commandsExecuted?: string[];
        exitCode?: number;
        duration?: number;
        searchQuery?: string;
        errorMessage?: string;
        resultsCount?: number;
        technology?: string;
        searchTime?: number;
        searchType?: string;
    };
}

export interface AITool {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, {
            type: string;
            description: string;
            required?: boolean;
            enum?: string[];
        }>;
        required: string[];
    };
    handler: (parameters: Record<string, any>) => Promise<AIToolResult>;
}

export interface IAIToolsService {
    /**
     * Get all available AI tools
     */
    getAvailableTools(): AITool[];

    /**
     * Execute a tool call from the AI
     */
    executeTool(toolCall: AIToolCall): Promise<AIToolResult>;

    /**
     * Register a new tool
     */
    registerTool(tool: AITool): void;

    /**
     * Unregister a tool
     */
    unregisterTool(toolName: string): void;

    /**
     * Check if a tool is available
     */
    hasToolAvailable(toolName: string): boolean;
}
