{"version": 3, "file": "tokenCredentialOptions.js", "sourceRoot": "", "sources": ["../../src/tokenCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CommonClientOptions } from \"@azure/core-client\";\nimport type { LogPolicyOptions } from \"@azure/core-rest-pipeline\";\n\n/**\n * Provides options to configure how the Identity library makes authentication\n * requests to Microsoft Entra ID.\n */\nexport interface TokenCredentialOptions extends CommonClientOptions {\n  /**\n   * The authority host to use for authentication requests.\n   * Possible values are available through {@link AzureAuthorityHosts}.\n   * The default is \"https://login.microsoftonline.com\".\n   */\n  authorityHost?: string;\n  /**\n   * Allows users to configure settings for logging policy options, allow logging account information and personally identifiable information for customer support.\n   */\n  loggingOptions?: LogPolicyOptions & {\n    /**\n     * Allows logging account information once the authentication flow succeeds.\n     */\n    allowLoggingAccountIdentifiers?: boolean;\n    /**\n     * Allows logging personally identifiable information for customer support.\n     */\n    enableUnsafeSupportLogging?: boolean;\n  };\n}\n"]}