// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
// @ts-expect-error The recommended approach to sharing module state between ESM and CJS.
// See https://github.com/isaacs/tshy/blob/main/README.md#module-local-state for additional information.
import { state as cjsState } from "../commonjs/state.js";
/**
 * Defines the shared state between CJS and ESM by re-exporting the CJS state.
 */
export const state = cjsState;
//# sourceMappingURL=state.js.map