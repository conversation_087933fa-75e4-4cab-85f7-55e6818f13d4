/**
 * Calculates the delay interval for retry attempts using exponential delay with jitter.
 * @param retryAttempt - The current retry attempt number.
 * @param config - The exponential retry configuration.
 * @returns An object containing the calculated retry delay.
 */
export declare function calculateRetryDelay(retryAttempt: number, config: {
    retryDelayInMs: number;
    maxRetryDelayInMs: number;
}): {
    retryAfterInMs: number;
};
//# sourceMappingURL=delay.d.ts.map