{"name": "@types/diff", "version": "7.0.2", "description": "TypeScript definitions for diff", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/diff", "license": "MIT", "contributors": [{"name": "vvakame", "githubUsername": "vvakame", "url": "https://github.com/vvakame"}, {"name": "szdc", "githubUsername": "szdc", "url": "https://github.com/szdc"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/diff"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "c151b380a54d8e1b09aaa9bedba60d128bf66af2f0ffacc0c6ff05744add1cca", "typeScriptVersion": "5.0"}