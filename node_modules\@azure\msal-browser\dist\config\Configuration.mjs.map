{"version": 3, "file": "Configuration.mjs", "sources": ["../../src/config/Configuration.ts"], "sourcesContent": [null], "names": ["BrowserUtils.getCurrentUri"], "mappings": ";;;;;;;;AAAA;;;AAGG;AA+BH;AACO,MAAM,wBAAwB,GAAG,MAAM;AACvC,MAAM,yBAAyB,GAAG,MAAM;AACxC,MAAM,2BAA2B,GAAG,MAAM;AAC1C,MAAM,0CAA0C,GAAG,KAAK;AA+N/D;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAC9B,EACI,IAAI,EAAE,aAAa,EACnB,KAAK,EAAE,cAAc,EACrB,MAAM,EAAE,eAAe,EACvB,SAAS,EAAE,kBAAkB,GACjB,EAChB,oBAA6B,EAAA;;AAG7B,IAAA,MAAM,oBAAoB,GAAwB;QAC9C,QAAQ,EAAE,SAAS,CAAC,YAAY;AAChC,QAAA,SAAS,EAAE,CAAA,EAAG,SAAS,CAAC,iBAAiB,CAAE,CAAA;AAC3C,QAAA,gBAAgB,EAAE,EAAE;QACpB,sBAAsB,EAAE,SAAS,CAAC,YAAY;QAC9C,iBAAiB,EAAE,SAAS,CAAC,YAAY;AACzC,QAAA,WAAW,EACP,OAAO,MAAM,KAAK,WAAW,GAAGA,aAA0B,EAAE,GAAG,EAAE;QACrE,qBAAqB,EAAE,SAAS,CAAC,YAAY;AAC7C,QAAA,yBAAyB,EAAE,IAAI;AAC/B,QAAA,kBAAkB,EAAE,EAAE;QACtB,YAAY,EAAE,YAAY,CAAC,GAAG;AAC9B,QAAA,WAAW,EAAE;YACT,kBAAkB,EAAE,kBAAkB,CAAC,QAAQ;AAC/C,YAAA,aAAa,EAAE;AACX,gBAAA,SAAS,CAAC,YAAY;AACtB,gBAAA,SAAS,CAAC,aAAa;AACvB,gBAAA,SAAS,CAAC,oBAAoB;AACjC,aAAA;AACJ,SAAA;AACD,QAAA,iBAAiB,EAAE;YACf,kBAAkB,EAAE,kBAAkB,CAAC,IAAI;YAC3C,MAAM,EAAE,SAAS,CAAC,YAAY;AACjC,SAAA;AACD,QAAA,0BAA0B,EAAE,KAAK;AACjC,QAAA,qBAAqB,EAAE,KAAK;AAC5B,QAAA,aAAa,EAAE,KAAK;AACpB,QAAA,sBAAsB,EAAE,KAAK;KAChC,CAAC;;AAGF,IAAA,MAAM,qBAAqB,GAA2B;QAClD,aAAa,EAAE,oBAAoB,CAAC,cAAc;QAClD,sBAAsB,EAAE,oBAAoB,CAAC,cAAc;AAC3D,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,aAAa,EAAE,KAAK;;AAEpB,QAAA,qBAAqB,EACjB,cAAc;AACd,YAAA,cAAc,CAAC,aAAa,KAAK,oBAAoB,CAAC,YAAY;AAC9D,cAAE,IAAI;AACN,cAAE,KAAK;AACf,QAAA,yBAAyB,EAAE,KAAK;KACnC,CAAC;;AAGF,IAAA,MAAM,sBAAsB,GAAkB;;QAE1C,cAAc,EAAE,MAAW;;SAE1B;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACvB,QAAA,iBAAiB,EAAE,KAAK;KAC3B,CAAC;;AAGF,IAAA,MAAM,8BAA8B,GAAmC;AACnE,QAAA,GAAG,sBAAsB;AACzB,QAAA,aAAa,EAAE,sBAAsB;AACrC,QAAA,aAAa,EAAE,oBAAoB;cAC7B,IAAI,WAAW,EAAE;AACnB,cAAE,oBAAoB;QAC1B,gBAAgB,EAAE,IAAI,gBAAgB,EAAE;AACxC,QAAA,gBAAgB,EAAE,CAAC;;AAEnB,QAAA,iBAAiB,EACb,eAAe,EAAE,gBAAgB,IAAI,wBAAwB;AACjE,QAAA,iBAAiB,EACb,eAAe,EAAE,gBAAgB,IAAI,yBAAyB;AAClE,QAAA,iBAAiB,EAAE,CAAC;AACpB,QAAA,yBAAyB,EAAE,2BAA2B;AACtD,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,qBAAqB,EAAE,KAAK;AAC5B,QAAA,mBAAmB,EAAE,KAAK;QAC1B,4BAA4B,EACxB,eAAe,EAAE,4BAA4B;YAC7C,0CAA0C;QAC9C,wBAAwB,EAAE,gBAAgB,CAAC,wBAAwB;KACtE,CAAC;AAEF,IAAA,MAAM,qBAAqB,GAAmC;AAC1D,QAAA,GAAG,8BAA8B;AACjC,QAAA,GAAG,eAAe;AAClB,QAAA,aAAa,EAAE,eAAe,EAAE,aAAa,IAAI,sBAAsB;KAC1E,CAAC;AAEF,IAAA,MAAM,yBAAyB,GAAsC;AACjE,QAAA,WAAW,EAAE;YACT,OAAO,EAAE,SAAS,CAAC,YAAY;YAC/B,UAAU,EAAE,SAAS,CAAC,YAAY;AACrC,SAAA;QACD,MAAM,EAAE,IAAI,qBAAqB,EAAE;KACtC,CAAC;;AAGF,IAAA,IACI,aAAa,EAAE,YAAY,KAAK,YAAY,CAAC,IAAI;QACjD,aAAa,EAAE,WAAW,EAC5B;QACE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAC/D,QAAA,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,SAAS,CACV,8BAA8B,CAC1B,6BAA6B,CAAC,oBAAoB,CACrD,CACJ,CACJ,CAAC;AACL,KAAA;;IAGD,IACI,aAAa,EAAE,YAAY;AAC3B,QAAA,aAAa,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI;QAChD,qBAAqB,EAAE,mBAAmB,EAC5C;AACE,QAAA,MAAM,8BAA8B,CAChC,6BAA6B,CAAC,yBAAyB,CAC1D,CAAC;AACL,KAAA;AAED,IAAA,MAAM,eAAe,GAAyB;AAC1C,QAAA,IAAI,EAAE;AACF,YAAA,GAAG,oBAAoB;AACvB,YAAA,GAAG,aAAa;AAChB,YAAA,WAAW,EAAE;gBACT,GAAG,oBAAoB,CAAC,WAAW;gBACnC,GAAG,aAAa,EAAE,WAAW;AAChC,aAAA;AACJ,SAAA;AACD,QAAA,KAAK,EAAE,EAAE,GAAG,qBAAqB,EAAE,GAAG,cAAc,EAAE;AACtD,QAAA,MAAM,EAAE,qBAAqB;AAC7B,QAAA,SAAS,EAAE,EAAE,GAAG,yBAAyB,EAAE,GAAG,kBAAkB,EAAE;KACrE,CAAC;AAEF,IAAA,OAAO,eAAe,CAAC;AAC3B;;;;"}