import * as vscode from 'vscode';
import * as path from 'path';
import { FileOperationsService } from './FileOperationsService';
import { LivePreviewService } from './LivePreviewService';
import { DiffPreviewService } from './DiffPreviewService';

export interface FileGenerationResult {
    success: boolean;
    filePath?: string;
    uri?: vscode.Uri;
    error?: string;
}

export interface DualViewResult {
    success: boolean;
    filePath?: string;
    uri?: vscode.Uri;
    error?: string;
    diffResult?: any;
    diffHtml?: string;
    editorResult?: {
        opened: boolean;
        uri?: vscode.Uri;
        isNewFile: boolean;
    };
    fileExists?: boolean;
    language?: string;
}

export interface GeneratedFile {
    fileName: string;
    content: string;
    language: string;
    shouldPreview?: boolean;
}

export class DirectFileGenerationService {
    private _fileOperationsService: FileOperationsService;
    private _livePreviewService: LivePreviewService;
    private _diffPreviewService: DiffPreviewService;
    private _outputChannel: vscode.OutputChannel;

    constructor(
        fileOperationsService: FileOperationsService,
        livePreviewService: LivePreviewService,
        diffPreviewService: DiffPreviewService
    ) {
        this._fileOperationsService = fileOperationsService;
        this._livePreviewService = livePreviewService;
        this._diffPreviewService = diffPreviewService;
        this._outputChannel = vscode.window.createOutputChannel('V1b3-Sama File Generation');
    }

    /**
     * Generate and open a file directly in VS Code editor
     */
    public async generateAndOpenFile(fileName: string, fileContent: string): Promise<FileGenerationResult> {
        try {
            // 1. Get the workspace root
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                throw new Error('No project folder is open.');
            }

            // 2. Enhance HTML content with proper structure
            const enhancedContent = this._enhanceHtmlContent(fileName, fileContent);

            // 3. Create the file using FileOperationsService
            const result = await this._fileOperationsService.createFile(fileName, enhancedContent);
            
            if (!result.success) {
                throw new Error(result.error || 'Failed to create file');
            }

            // 4. Open the newly created file in a new editor tab
            if (result.uri) {
                const document = await vscode.workspace.openTextDocument(result.uri);
                await vscode.window.showTextDocument(document, vscode.ViewColumn.One);
                
                this._outputChannel.appendLine(`✅ Successfully created and opened ${fileName}`);
                vscode.window.showInformationMessage(`Successfully created and opened ${fileName}`);

                return {
                    success: true,
                    filePath: fileName,
                    uri: result.uri
                };
            } else {
                throw new Error('File created but URI not available');
            }

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to create file: ${errorMsg}`);
            vscode.window.showErrorMessage(`Failed to create file: ${errorMsg}`);
            
            return {
                success: false,
                error: errorMsg
            };
        }
    }

    /**
     * Generate file and show live preview (for HTML/web files)
     */
    public async generateFileWithLivePreview(fileName: string, fileContent: string): Promise<FileGenerationResult> {
        try {
            // 1. First create and open the file
            const fileResult = await this.generateAndOpenFile(fileName, fileContent);
            
            if (!fileResult.success) {
                return fileResult;
            }

            // 2. If it's an HTML file, show live preview
            if (this._isWebFile(fileName)) {
                await this._livePreviewService.showLivePreview(fileName);
                this._outputChannel.appendLine(`🌐 Live preview started for ${fileName}`);
            }

            return fileResult;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: errorMsg
            };
        }
    }

    /**
     * Generate multiple files at once (e.g., HTML + CSS + JS)
     */
    public async generateMultipleFiles(files: GeneratedFile[]): Promise<FileGenerationResult[]> {
        const results: FileGenerationResult[] = [];
        let mainHtmlFile: string | null = null;

        try {
            // Create all files first
            for (const file of files) {
                const result = await this.generateAndOpenFile(file.fileName, file.content);
                results.push(result);

                // Track the main HTML file for preview
                if (file.language.toLowerCase() === 'html' && file.shouldPreview !== false) {
                    mainHtmlFile = file.fileName;
                }
            }

            // Show live preview for the main HTML file
            if (mainHtmlFile) {
                await this._livePreviewService.showLivePreview(mainHtmlFile);
                this._outputChannel.appendLine(`🌐 Live preview started for ${mainHtmlFile}`);
            }

            return results;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to generate multiple files: ${errorMsg}`);
            
            // Add error result if we haven't processed all files
            if (results.length < files.length) {
                results.push({
                    success: false,
                    error: errorMsg
                });
            }

            return results;
        }
    }

    /**
     * Enhanced file generation with automatic preview detection
     */
    public async generateWithAutoPreview(fileName: string, fileContent: string): Promise<FileGenerationResult> {
        // Determine if this should have live preview
        const shouldPreview = this._isWebFile(fileName) || this._containsWebContent(fileContent);

        if (shouldPreview) {
            return this.generateFileWithLivePreview(fileName, fileContent);
        } else {
            return this.generateAndOpenFile(fileName, fileContent);
        }
    }

    /**
     * Generate file with diff preview for modifications
     */
    public async generateFileWithDiffPreview(fileName: string, fileContent: string, language?: string): Promise<FileGenerationResult & { diffResult?: any }> {
        try {
            // 1. Read current file content if it exists
            const originalContent = await this._diffPreviewService.readCurrentFileContent(fileName);

            // 2. Generate diff if file exists
            let diffResult = null;
            if (originalContent) {
                diffResult = this._diffPreviewService.generateDiff(originalContent, fileContent, fileName);
                this._outputChannel.appendLine(`📊 Generated diff for ${fileName}: +${diffResult.additions} -${diffResult.deletions}`);
            }

            // 3. Create/modify the file
            const result = await this.generateAndOpenFile(fileName, fileContent);

            if (!result.success) {
                return result;
            }

            // 4. Return result with diff data
            return {
                ...result,
                diffResult
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to generate file with diff preview: ${errorMsg}`);
            return {
                success: false,
                error: errorMsg
            };
        }
    }

    /**
     * TASK 3: Unified dual-view workflow - Generate file with simultaneous diff preview and editor opening
     * This is the core method for the new end-to-end workflow
     */
    public async generateWithDualView(fileName: string, fileContent: string, language?: string): Promise<DualViewResult> {
        try {
            // 1. Read current file content if it exists
            const originalContent = await this._diffPreviewService.readCurrentFileContent(fileName);
            const fileExists = originalContent !== '';

            // 2. Generate diff for chat preview (always, even for new files)
            let diffResult = null;
            if (fileExists) {
                // For existing files, show actual diff
                diffResult = this._diffPreviewService.generateDiff(originalContent, fileContent, fileName);
                this._outputChannel.appendLine(`📊 Generated diff for ${fileName}: +${diffResult.additions} -${diffResult.deletions}`);
            } else {
                // For new files, show "creation" diff
                diffResult = this._diffPreviewService.generateDiff('', fileContent, fileName);
                this._outputChannel.appendLine(`📊 Generated creation preview for ${fileName}: +${diffResult.additions} lines`);
            }

            // 3. Write file to disk AND open in editor (dual-view core)
            const fileResult = await this.generateAndOpenFile(fileName, fileContent);

            if (!fileResult.success) {
                return {
                    success: false,
                    error: fileResult.error,
                    diffResult: null,
                    editorResult: undefined
                };
            }

            // 4. Generate rich diff HTML for chat webview
            const diffHtml = this._diffPreviewService.generateDiffPreviewHtml(diffResult);

            // 5. Return comprehensive dual-view result
            return {
                success: true,
                filePath: fileName,
                uri: fileResult.uri,
                diffResult,
                diffHtml,
                editorResult: {
                    opened: true,
                    uri: fileResult.uri,
                    isNewFile: !fileExists
                },
                fileExists,
                language: language || this._inferLanguageFromPath(fileName)
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to generate dual-view for ${fileName}: ${errorMsg}`);
            return {
                success: false,
                error: errorMsg,
                diffResult: null,
                editorResult: undefined
            };
        }
    }

    /**
     * Check if file is a web file that should have live preview
     */
    private _isWebFile(fileName: string): boolean {
        const webExtensions = ['.html', '.htm', '.xhtml'];
        const ext = path.extname(fileName).toLowerCase();
        return webExtensions.includes(ext);
    }

    /**
     * Enhance HTML content with proper structure and CSS links
     */
    private _enhanceHtmlContent(fileName: string, content: string): string {
        // If it's not an HTML file, return as-is
        if (!this._isWebFile(fileName)) {
            return content;
        }

        // Check if content already has proper HTML structure
        if (content.includes('<!DOCTYPE html>') && content.includes('<html>')) {
            return content;
        }

        // If it's just HTML fragments, wrap in proper structure
        const cssFileName = fileName.replace(/\.html?$/i, '.css');

        // Create the enhanced HTML content
        const enhancedHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${path.basename(fileName, path.extname(fileName))}</title>
    <link rel="stylesheet" href="${cssFileName}">
    <style>
        /* Basic styling for better presentation */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            margin-top: 0;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .gallery img {
            border-radius: 4px;
            transition: transform 0.2s;
        }
        .gallery img:hover {
            transform: scale(1.05);
        }
        form {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0 15px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007acc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        ${content}
    </div>
</body>
</html>`;

        // Auto-generate CSS file if it doesn't exist
        this._autoGenerateCssFile(cssFileName);

        return enhancedHtml;
    }

    /**
     * Auto-generate a CSS file for HTML files
     */
    private async _autoGenerateCssFile(cssFileName: string): Promise<void> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                return;
            }

            const cssPath = path.join(workspaceFolders[0].uri.fsPath, cssFileName);

            // Only create if CSS file doesn't exist
            if (!require('fs').existsSync(cssPath)) {
                const cssContent = `/* Generated CSS for ${cssFileName} */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container and layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-top: 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 600;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    color: #555;
}

/* Images and media */
img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Gallery styles */
.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.gallery img {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Forms */
form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    margin: 30px 0;
    border: 1px solid #e9ecef;
}

input, textarea, select {
    width: 100%;
    padding: 12px;
    margin: 8px 0 20px 0;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #007acc;
    box-shadow: 0 0 0 3px rgba(0,122,204,0.1);
}

/* Buttons */
button {
    background: linear-gradient(135deg, #007acc, #005a9e);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,122,204,0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,122,204,0.4);
}

button:active {
    transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
        border-radius: 8px;
    }

    .gallery {
        grid-template-columns: 1fr;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; }
}
`;

                await this._fileOperationsService.createFile(cssFileName, cssContent);
                this._outputChannel.appendLine(`📄 Auto-generated CSS file: ${cssFileName}`);
            }
        } catch (error) {
            this._outputChannel.appendLine(`⚠️ Could not auto-generate CSS file: ${error}`);
        }
    }

    /**
     * Infer programming language from file path (helper for dual-view)
     */
    private _inferLanguageFromPath(filePath: string): string {
        const extension = path.extname(filePath).toLowerCase().slice(1);

        const languageMap: { [key: string]: string } = {
            'js': 'javascript',
            'jsx': 'jsx',
            'ts': 'typescript',
            'tsx': 'tsx',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'h': 'c',
            'cs': 'csharp',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go',
            'rs': 'rust',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'sass': 'sass',
            'json': 'json',
            'yaml': 'yaml',
            'yml': 'yaml',
            'xml': 'xml',
            'md': 'markdown',
            'sh': 'bash',
            'bat': 'batch',
            'ps1': 'powershell'
        };

        return languageMap[extension] || 'text';
    }

    /**
     * Check if content contains web-related code
     */
    private _containsWebContent(content: string): boolean {
        const webIndicators = [
            '<!DOCTYPE html',
            '<html',
            '<head>',
            '<body>',
            '<div',
            '<script',
            '<style'
        ];
        
        const lowerContent = content.toLowerCase();
        return webIndicators.some(indicator => lowerContent.includes(indicator));
    }

    /**
     * Get file language from extension
     */
    private _getLanguageFromFileName(fileName: string): string {
        const ext = path.extname(fileName).toLowerCase();
        const languageMap: { [key: string]: string } = {
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascriptreact',
            '.tsx': 'typescriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'shellscript',
            '.ps1': 'powershell',
            '.sql': 'sql',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.txt': 'plaintext'
        };
        
        return languageMap[ext] || 'plaintext';
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._outputChannel.dispose();
        this._diffPreviewService.dispose();
    }
}
