{"name": "@vscode/vsce-sign", "description": "Visual Studio Code extension package signing and verification module", "version": "2.0.6", "author": {"name": "Microsoft"}, "license": "SEE LICENSE IN LICENSE.txt", "devDependencies": {"mocha": "^10.0.0"}, "optionalDependencies": {"@vscode/vsce-sign-darwin-arm64": "2.0.5", "@vscode/vsce-sign-darwin-x64": "2.0.5", "@vscode/vsce-sign-win32-arm64": "2.0.5", "@vscode/vsce-sign-win32-x64": "2.0.5", "@vscode/vsce-sign-linux-arm64": "2.0.5", "@vscode/vsce-sign-linux-arm": "2.0.5", "@vscode/vsce-sign-linux-x64": "2.0.5", "@vscode/vsce-sign-alpine-arm64": "2.0.5", "@vscode/vsce-sign-alpine-x64": "2.0.5"}, "homepage": "https://github.com/Microsoft/node-vsce-sign", "main": "src/main.js", "scripts": {"postinstall": "node ./src/postinstall.js", "package": "node ./build/package.js", "download": "node ./build/download.js", "test": "mocha ./src/test/*.test.js --timeout 5s --fail-zero --exit"}}