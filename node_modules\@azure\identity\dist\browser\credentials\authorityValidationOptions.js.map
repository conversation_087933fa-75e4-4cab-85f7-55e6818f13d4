{"version": 3, "file": "authorityValidationOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/authorityValidationOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Provides options to configure how the Identity library\n * does authority validation during authentication requests\n * to Microsoft Entra ID.\n */\nexport interface AuthorityValidationOptions {\n  /**\n   * The field determines whether instance discovery is performed when attempting to authenticate.\n   * Setting this to `true` will completely disable both instance discovery and authority validation.\n   * As a result, it's crucial to ensure that the configured authority host is valid and trustworthy.\n   * This functionality is intended for use in scenarios where the metadata endpoint cannot be reached, such as in private clouds or Azure Stack.\n   * The process of instance discovery entails retrieving authority metadata from https://login.microsoft.com/ to validate the authority.\n   */\n  disableInstanceDiscovery?: boolean;\n}\n"]}