// Test script to verify auto-execution functionality
// Direct test of the regex pattern used in LLMResponseParser

// Test LLM response with file operations
const testResponse = `I'll create a simple test file for you.

<file_operation type="create" path="test-output.txt">
This is a test file created by the auto-execution system.
Current time: ${new Date().toISOString()}
</file_operation>

The file has been created successfully!`;

console.log('Testing LLM Response Parser Regex...');

// This is the exact regex from LLMResponseParser
const FILE_OPERATION_REGEX = /<file_operation\s+type="([^"]+)"\s+path="([^"]+)"(?:\s+language="([^"]+)")?>([\s\S]*?)<\/file_operation>/g;

console.log('Test Response:');
console.log(testResponse);
console.log('\n' + '='.repeat(50));

const fileOperations = [];
let textContent = testResponse;

// Extract file operations
let match;
while ((match = FILE_OPERATION_REGEX.exec(testResponse)) !== null) {
    const [fullMatch, type, path, language, operationContent] = match;

    console.log('\nFound file operation:');
    console.log('- Full Match:', fullMatch);
    console.log('- Type:', type);
    console.log('- Path:', path);
    console.log('- Language:', language);
    console.log('- Content:', operationContent.trim());

    fileOperations.push({
        type: type,
        path: path.trim(),
        content: operationContent.trim(),
        language: language
    });

    // Remove the file operation from text content
    textContent = textContent.replace(fullMatch, '');
}

console.log('\n' + '='.repeat(50));
console.log('Results:');
console.log('- File Operations Found:', fileOperations.length);
console.log('- Remaining Text Content:', textContent.trim());

if (fileOperations.length > 0) {
    console.log('- First Operation Details:', JSON.stringify(fileOperations[0], null, 2));
} else {
    console.log('❌ No file operations found! This indicates a parsing issue.');
}

console.log('\nTest completed.');
