{"version": 3, "file": "visualStudioCodeCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEnE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,6DAA6D,CAC9D,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,IAAI,sBAAsB,CAAC,OAAc;QACvC,MAAM,IAAI,KAAK,CACb,qJAAqJ,CACtJ,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,OAAO,0BAA0B;IACrC;;OAEG;IACH;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\n\nconst BrowserNotSupportedError = new Error(\n  \"VisualStudioCodeCredential is not supported in the browser.\",\n);\nconst logger = credentialLogger(\"VisualStudioCodeCredential\");\n\nexport const vsCodeCredentialControl = {\n  set vsCodeCredentialFinder(_finder: never) {\n    throw new Error(\n      \"Attempted to register a VisualStudioCodeCredential provider plugin in the browser. This environment is not supported by VisualStudioCodeCredential.\",\n    );\n  },\n};\n\n/**\n * Connects to Azure using the credential provided by the VSCode extension 'Azure Account'.\n * \n * @deprecated This credential is deprecated because the VS Code Azure Account extension on which this credential\n * relies has been deprecated. Users should use other dev-time credentials, such as {@link AzureCliCredential},\n * {@link AzureDeveloperCliCredential}, or {@link AzurePowerShellCredential} for their\n * local development needs. See Azure account extension deprecation notice [here](https://github.com/microsoft/vscode-azure-account/issues/964).\n */\nexport class VisualStudioCodeCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}