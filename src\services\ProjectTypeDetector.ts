/**
 * Project Type Detector - Intelligent project type detection from requirements
 */

import * as vscode from 'vscode';
import * as path from 'path';
import {
    IProjectTypeDetector,
    TechnologyStack
} from '../interfaces/IProjectScaffold';

interface DetectionPattern {
    keywords: string[];
    stack: TechnologyStack;
    weight: number;
    category: string;
}

interface DetectionResult {
    stack: TechnologyStack;
    confidence: number;
    reasons: string[];
}

export class ProjectTypeDetector implements IProjectTypeDetector {
    private detectionPatterns: DetectionPattern[] = [];

    constructor() {
        this.initializeDetectionPatterns();
    }

    public async detectFromRequirements(requirements: string): Promise<TechnologyStack> {
        const normalizedRequirements = requirements.toLowerCase();
        const results: DetectionResult[] = [];

        // Score each technology stack based on keyword matches
        for (const pattern of this.detectionPatterns) {
            const score = this.calculatePatternScore(normalizedRequirements, pattern);
            if (score > 0) {
                results.push({
                    stack: pattern.stack,
                    confidence: score,
                    reasons: this.getMatchingKeywords(normalizedRequirements, pattern.keywords)
                });
            }
        }

        // Sort by confidence and return the best match
        results.sort((a, b) => b.confidence - a.confidence);
        
        if (results.length > 0) {
            return results[0].stack;
        }

        // Default fallback based on common patterns
        return this.getDefaultStack(normalizedRequirements);
    }

    public async detectFromFiles(filePaths: string[]): Promise<TechnologyStack | null> {
        const fileExtensions = filePaths.map(filePath => path.extname(filePath).toLowerCase());
        const fileNames = filePaths.map(filePath => path.basename(filePath).toLowerCase());

        // Check for specific configuration files
        if (fileNames.includes('package.json')) {
            return await this.detectFromPackageJson(filePaths);
        }

        if (fileNames.includes('requirements.txt') || fileNames.includes('pyproject.toml')) {
            return this.detectPythonStack(fileNames, fileExtensions);
        }

        if (fileNames.includes('cargo.toml')) {
            return TechnologyStack.RUST_CLI;
        }

        if (fileNames.includes('go.mod')) {
            return TechnologyStack.GO_CLI;
        }

        // Detect by file extensions
        return this.detectByExtensions(fileExtensions, fileNames);
    }

    public getDetectionConfidence(requirements: string, detectedType: TechnologyStack): number {
        const normalizedRequirements = requirements.toLowerCase();
        const pattern = this.detectionPatterns.find(p => p.stack === detectedType);
        
        if (!pattern) {
            return 0;
        }

        return this.calculatePatternScore(normalizedRequirements, pattern);
    }

    private initializeDetectionPatterns(): void {
        this.detectionPatterns = [
            // Web Frontend
            {
                keywords: ['react', 'jsx', 'tsx', 'component', 'hook', 'state management'],
                stack: TechnologyStack.REACT_VITE_TS,
                weight: 10,
                category: 'frontend'
            },
            {
                keywords: ['vue', 'vuejs', 'composition api', 'vue 3', 'pinia'],
                stack: TechnologyStack.VUE_VITE_TS,
                weight: 10,
                category: 'frontend'
            },
            {
                keywords: ['svelte', 'sveltekit', 'svelte component'],
                stack: TechnologyStack.SVELTE_VITE_TS,
                weight: 10,
                category: 'frontend'
            },
            {
                keywords: ['angular', 'angular component', 'typescript', 'rxjs'],
                stack: TechnologyStack.ANGULAR_TS,
                weight: 10,
                category: 'frontend'
            },
            {
                keywords: ['vanilla', 'html', 'css', 'javascript', 'dom', 'web page', 'website'],
                stack: TechnologyStack.VANILLA_WEB,
                weight: 5,
                category: 'frontend'
            },

            // Backend
            {
                keywords: ['express', 'node', 'nodejs', 'server', 'api', 'rest', 'middleware'],
                stack: TechnologyStack.NODE_EXPRESS_TS,
                weight: 10,
                category: 'backend'
            },
            {
                keywords: ['fastify', 'node', 'nodejs', 'fast server', 'high performance'],
                stack: TechnologyStack.NODE_FASTIFY_TS,
                weight: 10,
                category: 'backend'
            },
            {
                keywords: ['fastapi', 'python', 'async', 'pydantic', 'uvicorn'],
                stack: TechnologyStack.PYTHON_FASTAPI,
                weight: 10,
                category: 'backend'
            },
            {
                keywords: ['flask', 'python', 'web framework', 'jinja', 'werkzeug'],
                stack: TechnologyStack.PYTHON_FLASK,
                weight: 10,
                category: 'backend'
            },
            {
                keywords: ['django', 'python', 'orm', 'admin', 'mvc'],
                stack: TechnologyStack.PYTHON_DJANGO,
                weight: 10,
                category: 'backend'
            },

            // Mobile
            {
                keywords: ['react native', 'mobile app', 'ios', 'android', 'expo', 'native'],
                stack: TechnologyStack.REACT_NATIVE_EXPO,
                weight: 10,
                category: 'mobile'
            },
            {
                keywords: ['flutter', 'dart', 'mobile', 'cross platform'],
                stack: TechnologyStack.FLUTTER,
                weight: 10,
                category: 'mobile'
            },

            // Desktop
            {
                keywords: ['electron', 'desktop app', 'cross platform desktop', 'chromium'],
                stack: TechnologyStack.ELECTRON_TS,
                weight: 10,
                category: 'desktop'
            },
            {
                keywords: ['tauri', 'rust', 'desktop', 'native desktop'],
                stack: TechnologyStack.TAURI_RUST,
                weight: 10,
                category: 'desktop'
            },

            // Data Science
            {
                keywords: ['jupyter', 'notebook', 'data analysis', 'pandas', 'numpy', 'matplotlib'],
                stack: TechnologyStack.PYTHON_JUPYTER,
                weight: 10,
                category: 'data-science'
            },
            {
                keywords: ['machine learning', 'ml', 'ai', 'tensorflow', 'pytorch', 'scikit'],
                stack: TechnologyStack.PYTHON_DATA_SCIENCE,
                weight: 10,
                category: 'data-science'
            },

            // Game Development
            {
                keywords: ['phaser', 'game', 'web game', 'browser game', 'canvas'],
                stack: TechnologyStack.PHASER_TS,
                weight: 10,
                category: 'game'
            },
            {
                keywords: ['unity', 'c#', 'csharp', '3d game', 'game engine'],
                stack: TechnologyStack.UNITY_CSHARP,
                weight: 10,
                category: 'game'
            },
            {
                keywords: ['godot', 'gdscript', 'indie game', '2d game'],
                stack: TechnologyStack.GODOT_GDSCRIPT,
                weight: 10,
                category: 'game'
            },

            // CLI Tools
            {
                keywords: ['cli', 'command line', 'tool', 'rust', 'cargo'],
                stack: TechnologyStack.RUST_CLI,
                weight: 8,
                category: 'cli'
            },
            {
                keywords: ['cli', 'command line', 'tool', 'go', 'golang'],
                stack: TechnologyStack.GO_CLI,
                weight: 8,
                category: 'cli'
            },
            {
                keywords: ['cli', 'command line', 'tool', 'python', 'script'],
                stack: TechnologyStack.PYTHON_CLI,
                weight: 8,
                category: 'cli'
            }
        ];
    }

    private calculatePatternScore(requirements: string, pattern: DetectionPattern): number {
        let score = 0;
        let matchCount = 0;

        for (const keyword of pattern.keywords) {
            if (requirements.includes(keyword)) {
                score += pattern.weight;
                matchCount++;
            }
        }

        // Bonus for multiple keyword matches
        if (matchCount > 1) {
            score *= (1 + (matchCount - 1) * 0.2);
        }

        // Normalize score to 0-100 range
        return Math.min(100, score);
    }

    private getMatchingKeywords(requirements: string, keywords: string[]): string[] {
        return keywords.filter(keyword => requirements.includes(keyword));
    }

    private getDefaultStack(requirements: string): TechnologyStack {
        // Analyze for common patterns
        if (requirements.includes('web') || requirements.includes('website') || requirements.includes('html')) {
            return TechnologyStack.VANILLA_WEB;
        }

        if (requirements.includes('app') && (requirements.includes('mobile') || requirements.includes('phone'))) {
            return TechnologyStack.REACT_NATIVE_EXPO;
        }

        if (requirements.includes('server') || requirements.includes('api') || requirements.includes('backend')) {
            return TechnologyStack.NODE_EXPRESS_TS;
        }

        if (requirements.includes('data') || requirements.includes('analysis') || requirements.includes('python')) {
            return TechnologyStack.PYTHON_DATA_SCIENCE;
        }

        if (requirements.includes('game')) {
            return TechnologyStack.PHASER_TS;
        }

        // Default to vanilla web for general requests
        return TechnologyStack.VANILLA_WEB;
    }

    private async detectFromPackageJson(filePaths: string[]): Promise<TechnologyStack> {
        const packageJsonPath = filePaths.find(p => path.basename(p).toLowerCase() === 'package.json');
        
        if (!packageJsonPath) {
            return TechnologyStack.NODE_EXPRESS_TS;
        }

        try {
            const content = await vscode.workspace.fs.readFile(vscode.Uri.file(packageJsonPath));
            const packageJson = JSON.parse(content.toString());
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

            // Check for React
            if (dependencies.react || dependencies['@types/react']) {
                if (dependencies['react-native'] || dependencies.expo) {
                    return TechnologyStack.REACT_NATIVE_EXPO;
                }
                return TechnologyStack.REACT_VITE_TS;
            }

            // Check for Vue
            if (dependencies.vue || dependencies['@vue/cli']) {
                return TechnologyStack.VUE_VITE_TS;
            }

            // Check for Svelte
            if (dependencies.svelte || dependencies['@sveltejs/kit']) {
                return TechnologyStack.SVELTE_VITE_TS;
            }

            // Check for Angular
            if (dependencies['@angular/core']) {
                return TechnologyStack.ANGULAR_TS;
            }

            // Check for Electron
            if (dependencies.electron) {
                return TechnologyStack.ELECTRON_TS;
            }

            // Check for Express
            if (dependencies.express) {
                return TechnologyStack.NODE_EXPRESS_TS;
            }

            // Check for Fastify
            if (dependencies.fastify) {
                return TechnologyStack.NODE_FASTIFY_TS;
            }

            // Check for Phaser
            if (dependencies.phaser) {
                return TechnologyStack.PHASER_TS;
            }

            // Default to Node.js
            return TechnologyStack.NODE_EXPRESS_TS;

        } catch (error) {
            console.error('Error reading package.json:', error);
            return TechnologyStack.NODE_EXPRESS_TS;
        }
    }

    private detectPythonStack(fileNames: string[], fileExtensions: string[]): TechnologyStack {
        // Check for Jupyter notebooks
        if (fileExtensions.includes('.ipynb')) {
            return TechnologyStack.PYTHON_JUPYTER;
        }

        // Check for data science indicators
        if (fileNames.some(name => 
            name.includes('analysis') || 
            name.includes('data') || 
            name.includes('model') ||
            name.includes('notebook')
        )) {
            return TechnologyStack.PYTHON_DATA_SCIENCE;
        }

        // Check for web framework indicators
        if (fileNames.some(name => 
            name.includes('app') || 
            name.includes('server') || 
            name.includes('api')
        )) {
            return TechnologyStack.PYTHON_FASTAPI;
        }

        // Default to data science for Python projects
        return TechnologyStack.PYTHON_DATA_SCIENCE;
    }

    private detectByExtensions(fileExtensions: string[], fileNames: string[]): TechnologyStack | null {
        const extensionCounts = fileExtensions.reduce((acc, ext) => {
            acc[ext] = (acc[ext] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        // TypeScript/JavaScript detection
        if (extensionCounts['.tsx'] || extensionCounts['.jsx']) {
            return TechnologyStack.REACT_VITE_TS;
        }

        if (extensionCounts['.vue']) {
            return TechnologyStack.VUE_VITE_TS;
        }

        if (extensionCounts['.svelte']) {
            return TechnologyStack.SVELTE_VITE_TS;
        }

        if (extensionCounts['.ts'] > extensionCounts['.js']) {
            // More TypeScript than JavaScript
            if (fileNames.some(name => name.includes('server') || name.includes('app'))) {
                return TechnologyStack.NODE_EXPRESS_TS;
            }
            return TechnologyStack.VANILLA_WEB;
        }

        if (extensionCounts['.js']) {
            return TechnologyStack.VANILLA_WEB;
        }

        // Python detection
        if (extensionCounts['.py']) {
            return this.detectPythonStack(fileNames, fileExtensions);
        }

        // Rust detection
        if (extensionCounts['.rs']) {
            return TechnologyStack.RUST_CLI;
        }

        // Go detection
        if (extensionCounts['.go']) {
            return TechnologyStack.GO_CLI;
        }

        // C# detection
        if (extensionCounts['.cs']) {
            return TechnologyStack.UNITY_CSHARP;
        }

        // HTML/CSS detection
        if (extensionCounts['.html'] || extensionCounts['.css']) {
            return TechnologyStack.VANILLA_WEB;
        }

        return null;
    }
}
