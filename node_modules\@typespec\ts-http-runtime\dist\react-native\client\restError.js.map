{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../../src/client/restError.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAWtD,MAAM,UAAU,eAAe,CAC7B,iBAAiD,EACjD,QAAgC;;IAEhC,MAAM,IAAI,GAAG,OAAO,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC;IACnF,MAAM,aAAa,GAAG,MAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,KAAK,mCAAI,IAAI,CAAC,IAAI,CAAC;IACpD,MAAM,OAAO,GACX,OAAO,iBAAiB,KAAK,QAAQ;QACnC,CAAC,CAAC,iBAAiB;QACnB,CAAC,CAAC,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,mCAAI,2BAA2B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3E,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE;QAC5B,UAAU,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,EAAE,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI;QACzB,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAC;KACnC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,QAA+B;;IACzD,OAAO;QACL,OAAO,EAAE,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC5C,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,MAAM,EAAE,MAAA,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,mCAAI,CAAC,CAAC;KAClD,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAkB;IAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAE3C,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;AACnD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport { RestError } from \"../restError.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport type { PathUncheckedResponse } from \"./common.js\";\n\n/**\n * Creates a rest error from a PathUnchecked response\n */\nexport function createRestError(response: PathUncheckedResponse): RestError;\n/**\n * Creates a rest error from an error message and a PathUnchecked response\n */\nexport function createRestError(message: string, response: PathUncheckedResponse): RestError;\nexport function createRestError(\n  messageOrResponse: string | PathUncheckedResponse,\n  response?: PathUncheckedResponse,\n): RestError {\n  const resp = typeof messageOrResponse === \"string\" ? response! : messageOrResponse;\n  const internalError = resp.body?.error ?? resp.body;\n  const message =\n    typeof messageOrResponse === \"string\"\n      ? messageOrResponse\n      : (internalError?.message ?? `Unexpected status code: ${resp.status}`);\n  return new RestError(message, {\n    statusCode: statusCodeToNumber(resp.status),\n    code: internalError?.code,\n    request: resp.request,\n    response: toPipelineResponse(resp),\n  });\n}\n\nfunction toPipelineResponse(response: PathUncheckedResponse): PipelineResponse {\n  return {\n    headers: createHttpHeaders(response.headers),\n    request: response.request,\n    status: statusCodeToNumber(response.status) ?? -1,\n  };\n}\n\nfunction statusCodeToNumber(statusCode: string): number | undefined {\n  const status = Number.parseInt(statusCode);\n\n  return Number.isNaN(status) ? undefined : status;\n}\n"]}