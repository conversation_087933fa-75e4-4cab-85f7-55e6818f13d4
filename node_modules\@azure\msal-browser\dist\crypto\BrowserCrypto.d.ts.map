{"version": 3, "file": "BrowserCrypto.d.ts", "sourceRoot": "", "sources": ["../../src/crypto/BrowserCrypto.ts"], "names": [], "mappings": "AASA,OAAO,EACH,kBAAkB,EAErB,MAAM,4BAA4B,CAAC;AA6CpC;;GAEG;AACH,wBAAgB,uBAAuB,CACnC,wBAAwB,EAAE,OAAO,GAClC,IAAI,CAeN;AAED;;;;;GAKG;AACH,wBAAsB,YAAY,CAC9B,UAAU,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,OAAO,CAAC,WAAW,CAAC,CAWtB;AAED;;;GAGG;AACH,wBAAgB,eAAe,CAAC,UAAU,EAAE,UAAU,GAAG,UAAU,CAElE;AAWD;;;;GAIG;AACH,wBAAgB,aAAa,IAAI,MAAM,CAuCtC;AAED;;;;GAIG;AACH,wBAAsB,eAAe,CACjC,WAAW,EAAE,OAAO,EACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,GACxB,OAAO,CAAC,aAAa,CAAC,CAMxB;AAED;;;GAGG;AACH,wBAAsB,SAAS,CAAC,GAAG,EAAE,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAKnE;AAED;;;;;GAKG;AACH,wBAAsB,SAAS,CAC3B,GAAG,EAAE,UAAU,EACf,WAAW,EAAE,OAAO,EACpB,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,GACxB,OAAO,CAAC,SAAS,CAAC,CAQpB;AAED;;;;GAIG;AACH,wBAAsB,IAAI,CACtB,GAAG,EAAE,SAAS,EACd,IAAI,EAAE,WAAW,GAClB,OAAO,CAAC,WAAW,CAAC,CAMtB;AAED;;GAEG;AACH,wBAAsB,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,CAWtD;AAED;;;;GAIG;AACH,wBAAsB,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CASrE;AAED;;;;;GAKG;AACH,wBAAsB,kBAAkB,CACpC,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,GACf,OAAO,CAAC,MAAM,CAAC,CA8CjB;AAED;;GAEG;AACH,wBAAsB,eAAe,IAAI,OAAO,CAAC,WAAW,CAAC,CAU5D;AAED;;;;GAIG;AACH,wBAAsB,YAAY,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAI3E;AA4BD;;;;GAIG;AACH,wBAAsB,OAAO,CACzB,OAAO,EAAE,SAAS,EAClB,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,GAChB,OAAO,CAAC;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC,CAkB1C;AAED;;;;;;GAMG;AACH,wBAAsB,OAAO,CACzB,OAAO,EAAE,SAAS,EAClB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,MAAM,CAAC,CAajB;AAED;;;GAGG;AACH,wBAAsB,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAInE"}