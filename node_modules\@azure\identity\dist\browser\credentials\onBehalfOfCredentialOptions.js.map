{"version": 3, "file": "onBehalfOfCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/onBehalfOfCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Defines the parameters to authenticate the {@link OnBehalfOfCredential} with a secret.\n */\nexport interface OnBehalfOfCredentialSecretOptions {\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId: string;\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   */\n  clientId: string;\n  /**\n   * A client secret that was generated for the App Registration.\n   */\n  clientSecret: string;\n  /**\n   * The user assertion for the On-Behalf-Of flow.\n   */\n  userAssertionToken: string;\n}\n\n/**\n * Defines the parameters to authenticate the {@link OnBehalfOfCredential} with a certificate.\n */\nexport interface OnBehalfOfCredentialCertificateOptions {\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId: string;\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   */\n  clientId: string;\n  /**\n   * The path to a PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificatePath: string;\n  /**\n   * The user assertion for the On-Behalf-Of flow.\n   */\n  userAssertionToken: string;\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n}\n\n/**\n * Defines the parameters to authenticate the {@link OnBehalfOfCredential} with an assertion.\n */\nexport interface OnBehalfOfCredentialAssertionOptions {\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId: string;\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   */\n  clientId: string;\n  /**\n   * A function that retrieves the client assertion for the credential to use\n   */\n  getAssertion: () => Promise<string>;\n  /**\n   * The user assertion for the On-Behalf-Of flow.\n   */\n  userAssertionToken: string;\n}\n/**\n * Optional parameters for the {@link OnBehalfOfCredential} class.\n */\nexport type OnBehalfOfCredentialOptions = (\n  | OnBehalfOfCredentialSecretOptions\n  | OnBehalfOfCredentialCertificateOptions\n  | OnBehalfOfCredentialAssertionOptions\n) &\n  MultiTenantTokenCredentialOptions &\n  CredentialPersistenceOptions &\n  AuthorityValidationOptions;\n"]}