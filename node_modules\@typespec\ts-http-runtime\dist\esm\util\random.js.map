{"version": 3, "file": "random.js", "sourceRoot": "", "sources": ["../../../src/util/random.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;GAOG;AACH,MAAM,UAAU,yBAAyB,CAAC,GAAW,EAAE,GAAW;IAChE,iCAAiC;IACjC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtB,2DAA2D;IAC3D,+EAA+E;IAC/E,yEAAyE;IACzE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAM,GAAG,GAAG,CAAC;AACtB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Returns a random integer value between a lower and upper bound,\n * inclusive of both bounds.\n * Note that this uses Math.random and isn't secure. If you need to use\n * this for any kind of security purpose, find a better source of random.\n * @param min - The smallest integer value allowed.\n * @param max - The largest integer value allowed.\n */\nexport function getRandomIntegerInclusive(min: number, max: number): number {\n  // Make sure inputs are integers.\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  // Pick a random offset from zero to the size of the range.\n  // Since Math.random() can never return 1, we have to make the range one larger\n  // in order to be inclusive of the maximum value after we take the floor.\n  const offset = Math.floor(Math.random() * (max - min + 1));\n  return offset + min;\n}\n"]}