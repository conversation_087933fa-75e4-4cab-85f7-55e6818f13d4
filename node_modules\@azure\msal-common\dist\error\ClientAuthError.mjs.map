{"version": 3, "file": "ClientAuthError.mjs", "sources": ["../../src/error/ClientAuthError.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.clientInfoDecodingError", "ClientAuthErrorCodes.clientInfoEmptyError", "ClientAuthErrorCodes.tokenParsingError", "ClientAuthErrorCodes.nullOrEmptyToken", "ClientAuthErrorCodes.endpointResolutionError", "ClientAuthErrorCodes.networkError", "ClientAuthErrorCodes.openIdConfigError", "ClientAuthErrorCodes.hashNotDeserialized", "ClientAuthErrorCodes.invalidState", "ClientAuthErrorCodes.stateMismatch", "ClientAuthErrorCodes.stateNotFound", "ClientAuthErrorCodes.nonceMismatch", "ClientAuthErrorCodes.authTimeNotFound", "ClientAuthErrorCodes.maxAgeTranspired", "ClientAuthErrorCodes.multipleMatchingTokens", "ClientAuthErrorCodes.multipleMatchingAccounts", "ClientAuthErrorCodes.multipleMatchingAppMetadata", "ClientAuthErrorCodes.requestCannotBeMade", "ClientAuthErrorCodes.cannotRemoveEmptyScope", "ClientAuthErrorCodes.cannotAppendScopeSet", "ClientAuthErrorCodes.emptyInputScopeSet", "ClientAuthErrorCodes.deviceCodePollingCancelled", "ClientAuthErrorCodes.deviceCodeExpired", "ClientAuthErrorCodes.deviceCodeUnknownError", "ClientAuthErrorCodes.noAccountInSilentRequest", "ClientAuthErrorCodes.invalidCacheRecord", "ClientAuthErrorCodes.invalidCacheEnvironment", "ClientAuthErrorCodes.noAccountFound", "ClientAuthErrorCodes.noCryptoObject", "ClientAuthErrorCodes.unexpectedCredentialType", "ClientAuthErrorCodes.invalidAssertion", "ClientAuthErrorCodes.invalidClientCredential", "ClientAuthErrorCodes.tokenRefreshRequired", "ClientAuthErrorCodes.userTimeoutReached", "ClientAuthErrorCodes.tokenClaimsCnfRequiredForSignedJwt", "ClientAuthErrorCodes.authorizationCodeMissingFromServerResponse", "ClientAuthErrorCodes.bindingKeyNotRemoved", "ClientAuthErrorCodes.endSessionEndpointNotSupported", "ClientAuthErrorCodes.keyIdMissing", "ClientAuthErrorCodes.noNetworkConnectivity", "ClientAuthErrorCodes.userCanceled", "ClientAuthErrorCodes.missingTenantIdError", "ClientAuthErrorCodes.methodNotImplemented", "ClientAuthErrorCodes.nestedAppAuthBridgeDisabled"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMH;;AAEG;AAEU,MAAA,uBAAuB,GAAG;AACnC,IAAA,CAACA,uBAA4C,GACzC,uDAAuD;AAC3D,IAAA,CAACC,oBAAyC,GAAG,2BAA2B;AACxE,IAAA,CAACC,iBAAsC,GAAG,wBAAwB;AAClE,IAAA,CAACC,gBAAqC,GAAG,4BAA4B;AACrE,IAAA,CAACC,uBAA4C,GACzC,8BAA8B;AAClC,IAAA,CAACC,YAAiC,GAAG,wBAAwB;AAC7D,IAAA,CAACC,iBAAsC,GACnC,6IAA6I;AACjJ,IAAA,CAACC,mBAAwC,GACrC,+CAA+C;AACnD,IAAA,CAACC,YAAiC,GAAG,mCAAmC;AACxE,IAAA,CAACC,aAAkC,GAAG,sBAAsB;AAC5D,IAAA,CAACC,aAAkC,GAAG,iBAAiB;AACvD,IAAA,CAACC,aAAkC,GAAG,sBAAsB;AAC5D,IAAA,CAACC,gBAAqC,GAClC,2EAA2E;QAC3E,qFAAqF;QACrF,kEAAkE;AACtE,IAAA,CAACC,gBAAqC,GAClC,2FAA2F;AAC/F,IAAA,CAACC,sBAA2C,GACxC,kEAAkE;QAClE,mFAAmF;AACvF,IAAA,CAACC,wBAA6C,GAC1C,2HAA2H;AAC/H,IAAA,CAACC,2BAAgD,GAC7C,kIAAkI;AACtI,IAAA,CAACC,mBAAwC,GACrC,2EAA2E;AAC/E,IAAA,CAACC,sBAA2C,GACxC,iDAAiD;AACrD,IAAA,CAACC,oBAAyC,GAAG,wBAAwB;AACrE,IAAA,CAACC,kBAAuC,GACpC,0CAA0C;AAC9C,IAAA,CAACC,0BAA+C,GAC5C,iHAAiH;AACrH,IAAA,CAACC,iBAAsC,GAAG,yBAAyB;AACnE,IAAA,CAACC,sBAA2C,GACxC,kDAAkD;AACtD,IAAA,CAACC,wBAA6C,GAC1C,yFAAyF;AAC7F,IAAA,CAACC,kBAAuC,GACpC,4CAA4C;AAChD,IAAA,CAACC,uBAA4C,GACzC,2DAA2D;AAC/D,IAAA,CAACC,cAAmC,GAChC,0CAA0C;AAC9C,IAAA,CAACC,cAAmC,GAAG,4BAA4B;AACnE,IAAA,CAACC,wBAA6C,GAC1C,6BAA6B;AACjC,IAAA,CAACC,gBAAqC,GAClC,0FAA0F;AAC9F,IAAA,CAACC,uBAA4C,GACzC,gKAAgK;AACpK,IAAA,CAACC,oBAAyC,GACtC,oOAAoO;AACxO,IAAA,CAACC,kBAAuC,GACpC,sDAAsD;AAC1D,IAAA,CAACC,kCAAuD,GACpD,iEAAiE;AACrE,IAAA,CAACC,0CAA+D,GAC5D,mEAAmE;AACvE,IAAA,CAACC,oBAAyC,GACtC,6DAA6D;AACjE,IAAA,CAACC,8BAAmD,GAChD,gDAAgD;AACpD,IAAA,CAACC,YAAiC,GAC9B,uIAAuI;AAC3I,IAAA,CAACC,qBAA0C,GACvC,0DAA0D;AAC9D,IAAA,CAACC,YAAiC,GAAG,0BAA0B;AAC/D,IAAA,CAACC,oBAAyC,GACtC,mHAAmH;AACvH,IAAA,CAACC,oBAAyC,GACtC,sCAAsC;AAC1C,IAAA,CAACC,2BAAgD,GAC7C,wCAAwC;EAC9C;AAEF;;;AAGG;AACU,MAAA,sBAAsB,GAAG;AAClC,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAE3C,uBAA4C;AAClD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,uBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,oBAAyC;AAC/C,QAAA,IAAI,EAAE,uBAAuB,CACzBA,oBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,iBAAsC;AAC5C,QAAA,IAAI,EAAE,uBAAuB,CAACA,iBAAsC,CAAC;AACxE,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAqC;AAC3C,QAAA,IAAI,EAAE,uBAAuB,CAACA,gBAAqC,CAAC;AACvE,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,uBAA4C;AAClD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,uBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,YAAY,EAAE;QACV,IAAI,EAAEC,YAAiC;AACvC,QAAA,IAAI,EAAE,uBAAuB,CAACA,YAAiC,CAAC;AACnE,KAAA;AACD,IAAA,4BAA4B,EAAE;QAC1B,IAAI,EAAEC,iBAAsC;AAC5C,QAAA,IAAI,EAAE,uBAAuB,CAACA,iBAAsC,CAAC;AACxE,KAAA;AACD,IAAA,mBAAmB,EAAE;QACjB,IAAI,EAAEC,mBAAwC;AAC9C,QAAA,IAAI,EAAE,uBAAuB,CAACA,mBAAwC,CAAC;AAC1E,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,YAAiC;AACvC,QAAA,IAAI,EAAE,uBAAuB,CAACA,YAAiC,CAAC;AACnE,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,aAAkC;AACxC,QAAA,IAAI,EAAE,uBAAuB,CAACA,aAAkC,CAAC;AACpE,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,aAAkC;AACxC,QAAA,IAAI,EAAE,uBAAuB,CAACA,aAAkC,CAAC;AACpE,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,aAAkC;AACxC,QAAA,IAAI,EAAE,uBAAuB,CAACA,aAAkC,CAAC;AACpE,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,gBAAqC;AAC3C,QAAA,IAAI,EAAE,uBAAuB,CAACA,gBAAqC,CAAC;AACvE,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAqC;AAC3C,QAAA,IAAI,EAAE,uBAAuB,CAACA,gBAAqC,CAAC;AACvE,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,sBAA2C;AACjD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,sBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAA6C;AACnD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,wBAA6C,CAChD;AACJ,KAAA;AACD,IAAA,2BAA2B,EAAE;QACzB,IAAI,EAAEC,2BAAgD;AACtD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,2BAAgD,CACnD;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,mBAAwC;AAC9C,QAAA,IAAI,EAAE,uBAAuB,CAACA,mBAAwC,CAAC;AAC1E,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,sBAA2C;AACjD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,sBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,mBAAmB,EAAE;QACjB,IAAI,EAAEC,oBAAyC;AAC/C,QAAA,IAAI,EAAE,uBAAuB,CACzBA,oBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,kBAAuC;AAC7C,QAAA,IAAI,EAAE,uBAAuB,CAACA,kBAAuC,CAAC;AACzE,KAAA;AACD,IAAA,0BAA0B,EAAE;QACxB,IAAI,EAAEC,0BAA+C;AACrD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,0BAA+C,CAClD;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,iBAAsC;AAC5C,QAAA,IAAI,EAAE,uBAAuB,CAACA,iBAAsC,CAAC;AACxE,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,sBAA2C;AACjD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,sBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAA6C;AACnD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,wBAA6C,CAChD;AACJ,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,kBAAuC;AAC7C,QAAA,IAAI,EAAE,uBAAuB,CAACA,kBAAuC,CAAC;AACzE,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,uBAA4C;AAClD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,uBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,cAAc,EAAE;QACZ,IAAI,EAAEC,cAAmC;AACzC,QAAA,IAAI,EAAE,uBAAuB,CAACA,cAAmC,CAAC;AACrE,KAAA;AACD,IAAA,WAAW,EAAE;QACT,IAAI,EAAEC,cAAmC;AACzC,QAAA,IAAI,EAAE,uBAAuB,CAACA,cAAmC,CAAC;AACrE,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAA6C;AACnD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,wBAA6C,CAChD;AACJ,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAqC;AAC3C,QAAA,IAAI,EAAE,uBAAuB,CAACA,gBAAqC,CAAC;AACvE,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,uBAA4C;AAClD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,uBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,oBAAyC;AAC/C,QAAA,IAAI,EAAE,uBAAuB,CACzBA,oBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,kBAAuC;AAC7C,QAAA,IAAI,EAAE,uBAAuB,CAACA,kBAAuC,CAAC;AACzE,KAAA;AACD,IAAA,mBAAmB,EAAE;QACjB,IAAI,EAAEC,kCAAuD;AAC7D,QAAA,IAAI,EAAE,uBAAuB,CACzBA,kCAAuD,CAC1D;AACJ,KAAA;AACD,IAAA,6BAA6B,EAAE;QAC3B,IAAI,EAAEC,0CAA+D;AACrE,QAAA,IAAI,EAAE,uBAAuB,CACzBA,0CAA+D,CAClE;AACJ,KAAA;AACD,IAAA,yBAAyB,EAAE;QACvB,IAAI,EAAEC,oBAAyC;AAC/C,QAAA,IAAI,EAAE,uBAAuB,CACzBA,oBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,8BAAmD;AACzD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,8BAAmD,CACtD;AACJ,KAAA;AACD,IAAA,YAAY,EAAE;QACV,IAAI,EAAEC,YAAiC;AACvC,QAAA,IAAI,EAAE,uBAAuB,CAACA,YAAiC,CAAC;AACnE,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,qBAA0C;AAChD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,qBAA0C,CAC7C;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,YAAiC;AACvC,QAAA,IAAI,EAAE,uBAAuB,CAACA,YAAiC,CAAC;AACnE,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,oBAAyC;AAC/C,QAAA,IAAI,EAAE,uBAAuB,CACzBA,oBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,2BAA2B,EAAE;QACzB,IAAI,EAAEE,2BAAgD;AACtD,QAAA,IAAI,EAAE,uBAAuB,CACzBA,2BAAgD,CACnD;AACJ,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,eAAgB,SAAQ,SAAS,CAAA;IAC1C,WAAY,CAAA,SAAiB,EAAE,iBAA0B,EAAA;QACrD,KAAK,CACD,SAAS,EACT,iBAAiB;cACX,GAAG,uBAAuB,CAAC,SAAS,CAAC,CAAA,EAAA,EAAK,iBAAiB,CAAE,CAAA;AAC/D,cAAE,uBAAuB,CAAC,SAAS,CAAC,CAC3C,CAAC;AACF,QAAA,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAE9B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;KAC1D;AACJ,CAAA;AAEe,SAAA,qBAAqB,CACjC,SAAiB,EACjB,iBAA0B,EAAA;AAE1B,IAAA,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;AAC7D;;;;"}