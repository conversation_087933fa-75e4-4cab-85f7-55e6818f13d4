{"version": 3, "file": "ManagedIdentityId.mjs", "sources": ["../../src/config/ManagedIdentityId.ts"], "sourcesContent": [null], "names": ["ManagedIdentityErrorCodes.invalidManagedIdentityIdType"], "mappings": ";;;;;;AAAA;;;AAGG;MAYU,iBAAiB,CAAA;AAE1B,IAAA,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;KACnB;IACD,IAAY,EAAE,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;KACpB;AAGD,IAAA,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAY,MAAM,CAAC,KAA4B,EAAA;AAC3C,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;AAED,IAAA,WAAA,CAAY,uBAAiD,EAAA;AACzD,QAAA,MAAM,oBAAoB,GACtB,uBAAuB,EAAE,oBAAoB,CAAC;AAClD,QAAA,MAAM,sBAAsB,GACxB,uBAAuB,EAAE,sBAAsB,CAAC;AACpD,QAAA,MAAM,oBAAoB,GACtB,uBAAuB,EAAE,oBAAoB,CAAC;AAElD,QAAA,IAAI,oBAAoB,EAAE;YACtB,IAAI,sBAAsB,IAAI,oBAAoB,EAAE;AAChD,gBAAA,MAAM,0BAA0B,CAC5BA,4BAAsD,CACzD,CAAC;AACL,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,GAAG,oBAAoB,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;AAC/D,SAAA;AAAM,aAAA,IAAI,sBAAsB,EAAE;YAC/B,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;AAC9C,gBAAA,MAAM,0BAA0B,CAC5BA,4BAAsD,CACzD,CAAC;AACL,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,GAAG,sBAAsB,CAAC;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,yBAAyB,CAAC;AACjE,SAAA;AAAM,aAAA,IAAI,oBAAoB,EAAE;YAC7B,IAAI,oBAAoB,IAAI,sBAAsB,EAAE;AAChD,gBAAA,MAAM,0BAA0B,CAC5BA,4BAAsD,CACzD,CAAC;AACL,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,GAAG,oBAAoB,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;AAC/D,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,EAAE,GAAG,2BAA2B,CAAC;AACtC,YAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,eAAe,CAAC;AACvD,SAAA;KACJ;AACJ;;;;"}