// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { exponentialRetryPolicyName as tspExponentialRetryPolicyName, exponentialRetryPolicy as tspExponentialRetryPolicy, } from "@typespec/ts-http-runtime/internal/policies";
/**
 * The programmatic identifier of the exponentialRetryPolicy.
 */
export const exponentialRetryPolicyName = tspExponentialRetryPolicyName;
/**
 * A policy that attempts to retry requests while introducing an exponentially increasing delay.
 * @param options - Options that configure retry logic.
 */
export function exponentialRetryPolicy(options = {}) {
    return tspExponentialRetryPolicy(options);
}
//# sourceMappingURL=exponentialRetryPolicy.js.map