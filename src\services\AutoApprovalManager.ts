import * as vscode from 'vscode';
import * as path from 'path';
import { FileOperation } from './LLMResponseParser';

// Simple glob pattern matching function (fallback if minimatch is not available)
function simpleMatch(pattern: string, str: string): boolean {
    // Convert glob pattern to regex
    const regexPattern = pattern
        .replace(/\*\*/g, '.*')
        .replace(/\*/g, '[^/]*')
        .replace(/\?/g, '.')
        .replace(/\{([^}]+)\}/g, '($1)')
        .replace(/,/g, '|');

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(str);
}

export interface AutoApprovalRule {
    id: string;
    name: string;
    pattern: string;           // Glob pattern for file paths
    operationType: 'create' | 'modify' | 'delete' | 'all';
    action: 'auto_approve' | 'require_approval' | 'deny';
    conditions?: {
        maxFileSize?: number;   // Max file size in bytes
        allowedExtensions?: string[];
        blockedPaths?: string[];
        requiresBackup?: boolean;
    };
    enabled: boolean;
    priority: number;          // Higher priority rules override lower ones
}

export interface AutoApprovalConfig {
    globalEnabled: boolean;
    defaultAction: 'auto_approve' | 'require_approval';
    rules: AutoApprovalRule[];
    safetyLimits: {
        maxFilesPerOperation: number;
        maxTotalSizePerOperation: number;
        requireConfirmationForDeletion: boolean;
    };
    executeTerminalCommands: boolean;
    useMcpServers: boolean;
    createBackups: boolean;
    maxFilesPerOperation: number;
}

export interface ApprovalResult {
    action: 'auto_approve' | 'require_approval' | 'deny';
    rule?: AutoApprovalRule;
    reason?: string;
}

/**
 * Manages automatic approval of file operations based on configurable rules
 * Similar to Refact's confirmation system but with enhanced safety features
 */
export class AutoApprovalManager {
    private config: AutoApprovalConfig;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.config = this.getDefaultConfig();
        this.loadConfig();
    }

    /**
     * Evaluate a file operation against auto-approval rules
     * HARDCODED: Always auto-approve file operations (like Cursor/Cline)
     */
    public async evaluateFileOperation(operation: FileOperation): Promise<ApprovalResult> {
        // Basic validation for truly dangerous operations
        const validation = this.validateOperation(operation);
        if (!validation.valid) {
            return {
                action: 'deny',
                reason: validation.reason
            };
        }

        // HARDCODED: Always auto-approve file operations
        // This makes V1b3-Sama behave like Cursor/Cline with automatic file operations
        return {
            action: 'auto_approve',
            reason: 'Automatic approval enabled for all file operations'
        };
    }

    /**
     * Evaluate multiple operations for batch processing
     */
    public async evaluateOperations(operations: FileOperation[]): Promise<{
        autoApproved: FileOperation[];
        requireApproval: FileOperation[];
        denied: FileOperation[];
        results: Map<FileOperation, ApprovalResult>;
    }> {
        const autoApproved: FileOperation[] = [];
        const requireApproval: FileOperation[] = [];
        const denied: FileOperation[] = [];
        const results = new Map<FileOperation, ApprovalResult>();

        // Check safety limits first
        if (operations.length > this.config.safetyLimits.maxFilesPerOperation) {
            const result: ApprovalResult = {
                action: 'require_approval',
                reason: `Too many operations (${operations.length} > ${this.config.safetyLimits.maxFilesPerOperation})`
            };
            
            for (const op of operations) {
                requireApproval.push(op);
                results.set(op, result);
            }
            
            return { autoApproved, requireApproval, denied, results };
        }

        // Calculate total size
        const totalSize = operations.reduce((sum, op) => sum + (op.content?.length || 0), 0);
        if (totalSize > this.config.safetyLimits.maxTotalSizePerOperation) {
            const result: ApprovalResult = {
                action: 'require_approval',
                reason: `Total content size too large (${totalSize} > ${this.config.safetyLimits.maxTotalSizePerOperation})`
            };
            
            for (const op of operations) {
                requireApproval.push(op);
                results.set(op, result);
            }
            
            return { autoApproved, requireApproval, denied, results };
        }

        // Evaluate each operation
        for (const operation of operations) {
            const result = await this.evaluateFileOperation(operation);
            results.set(operation, result);

            switch (result.action) {
                case 'auto_approve':
                    autoApproved.push(operation);
                    break;
                case 'require_approval':
                    requireApproval.push(operation);
                    break;
                case 'deny':
                    denied.push(operation);
                    break;
            }
        }

        return { autoApproved, requireApproval, denied, results };
    }

    /**
     * Update the auto-approval configuration
     */
    public async updateConfig(config: Partial<AutoApprovalConfig>): Promise<void> {
        this.config = { ...this.config, ...config };
        await this.saveConfig();
    }

    /**
     * Add a new auto-approval rule
     */
    public async addRule(rule: AutoApprovalRule): Promise<void> {
        // Ensure unique ID
        if (this.config.rules.some(r => r.id === rule.id)) {
            throw new Error(`Rule with ID '${rule.id}' already exists`);
        }

        this.config.rules.push(rule);
        await this.saveConfig();
    }

    /**
     * Remove an auto-approval rule
     */
    public async removeRule(ruleId: string): Promise<void> {
        const index = this.config.rules.findIndex(r => r.id === ruleId);
        if (index === -1) {
            throw new Error(`Rule with ID '${ruleId}' not found`);
        }

        this.config.rules.splice(index, 1);
        await this.saveConfig();
    }

    /**
     * Get the current configuration
     */
    public async getConfig(): Promise<AutoApprovalConfig> {
        return { ...this.config };
    }

    /**
     * Reset to default configuration
     */
    public async resetToDefaults(): Promise<void> {
        this.config = this.getDefaultConfig();
        await this.saveConfig();
    }

    /**
     * Check if a rule matches an operation
     */
    private ruleMatches(rule: AutoApprovalRule, operation: FileOperation): boolean {
        // Check operation type
        if (rule.operationType !== 'all' && rule.operationType !== operation.type) {
            return false;
        }

        // Check path pattern
        return simpleMatch(rule.pattern, operation.path);
    }

    /**
     * Check rule conditions
     */
    private async checkRuleConditions(rule: AutoApprovalRule, operation: FileOperation): Promise<{
        passed: boolean;
        reason?: string;
    }> {
        if (!rule.conditions) {
            return { passed: true };
        }

        const conditions = rule.conditions;

        // Check file size
        if (conditions.maxFileSize && operation.content) {
            const size = Buffer.byteLength(operation.content, 'utf8');
            if (size > conditions.maxFileSize) {
                return {
                    passed: false,
                    reason: `File size (${size} bytes) exceeds limit (${conditions.maxFileSize} bytes)`
                };
            }
        }

        // Check file extension
        if (conditions.allowedExtensions) {
            const ext = path.extname(operation.path).toLowerCase();
            if (!conditions.allowedExtensions.includes(ext)) {
                return {
                    passed: false,
                    reason: `File extension '${ext}' not in allowed list`
                };
            }
        }

        // Check blocked paths
        if (conditions.blockedPaths) {
            for (const blockedPath of conditions.blockedPaths) {
                if (simpleMatch(blockedPath, operation.path)) {
                    return {
                        passed: false,
                        reason: `Path matches blocked pattern: ${blockedPath}`
                    };
                }
            }
        }

        return { passed: true };
    }

    /**
     * Validate operation for basic safety
     */
    private validateOperation(operation: FileOperation): { valid: boolean; reason?: string } {
        // Check for dangerous paths
        if (operation.path.includes('..') || operation.path.startsWith('/')) {
            return {
                valid: false,
                reason: 'Invalid path: cannot use absolute paths or parent directory references'
            };
        }

        // Check for empty path
        if (!operation.path.trim()) {
            return {
                valid: false,
                reason: 'File path cannot be empty'
            };
        }

        // Check content for non-directory operations
        if (operation.type !== 'create_directory' && !operation.content) {
            return {
                valid: false,
                reason: 'File content cannot be empty for create/modify operations'
            };
        }

        return { valid: true };
    }

    /**
     * Get hardcoded configuration (no user configuration needed)
     */
    private getDefaultConfig(): AutoApprovalConfig {
        return {
            globalEnabled: true, // HARDCODED: Always enabled
            defaultAction: 'auto_approve', // HARDCODED: Always auto-approve
            rules: this.getDefaultRules(),
            safetyLimits: {
                maxFilesPerOperation: 50, // Increased limit
                maxTotalSizePerOperation: 10000000, // 10MB limit
                requireConfirmationForDeletion: false // No confirmation needed
            },
            executeTerminalCommands: false, // Keep disabled for safety
            useMcpServers: false, // Keep disabled for safety
            createBackups: true, // Always create backups
            maxFilesPerOperation: 50 // Increased limit
        };
    }

    /**
     * Get default auto-approval rules
     */
    private getDefaultRules(): AutoApprovalRule[] {
        return [
            {
                id: 'safe-create-code',
                name: 'Auto-approve creating code files',
                pattern: '**/*.{js,ts,jsx,tsx,py,java,cpp,c,h,cs,php,rb,go,rs,vue,svelte}',
                operationType: 'create',
                action: 'auto_approve',
                conditions: {
                    maxFileSize: 100000, // 100KB
                    blockedPaths: ['node_modules/**', '.git/**', 'dist/**', 'build/**', 'out/**']
                },
                enabled: true, // Enabled for automatic workspace operations
                priority: 100
            },
            {
                id: 'safe-create-config',
                name: 'Auto-approve creating config files',
                pattern: '**/*.{json,yaml,yml,toml,ini,env,md,txt}',
                operationType: 'create',
                action: 'auto_approve',
                conditions: {
                    maxFileSize: 50000, // 50KB
                    blockedPaths: ['node_modules/**', '.git/**']
                },
                enabled: true, // Enabled for automatic workspace operations
                priority: 90
            },
            {
                id: 'safe-create-web',
                name: 'Auto-approve creating web files',
                pattern: '**/*.{html,css,scss,sass,less}',
                operationType: 'create',
                action: 'auto_approve',
                conditions: {
                    maxFileSize: 100000, // 100KB
                    blockedPaths: ['node_modules/**', '.git/**']
                },
                enabled: true, // Enabled for automatic workspace operations
                priority: 85
            },
            {
                id: 'safe-modify-code',
                name: 'Auto-approve modifying code files',
                pattern: '**/*.{js,ts,jsx,tsx,py,java,cpp,c,h,cs,php,rb,go,rs,vue,svelte,html,css,scss,sass,less,json,yaml,yml,md,txt}',
                operationType: 'modify',
                action: 'auto_approve',
                conditions: {
                    maxFileSize: 200000, // 200KB for modifications
                    blockedPaths: ['node_modules/**', '.git/**', 'dist/**', 'build/**', 'out/**']
                },
                enabled: true,
                priority: 80
            },
            {
                id: 'safe-create-directories',
                name: 'Auto-approve creating directories',
                pattern: '**/',
                operationType: 'create',
                action: 'auto_approve',
                conditions: {
                    blockedPaths: ['node_modules/**', '.git/**', 'dist/**', 'build/**', 'out/**']
                },
                enabled: true,
                priority: 95
            },
            {
                id: 'deny-system-files',
                name: 'Deny operations on system files',
                pattern: '{package-lock.json,yarn.lock,Cargo.lock,.gitignore,.git/**}',
                operationType: 'all',
                action: 'deny',
                enabled: true,
                priority: 1000
            },
            {
                id: 'deny-sensitive-config',
                name: 'Deny operations on sensitive config files',
                pattern: '{.env,.env.*,*.key,*.pem,*.p12,config/secrets/**}',
                operationType: 'all',
                action: 'deny',
                enabled: true,
                priority: 999
            }
        ];
    }

    /**
     * Load hardcoded configuration (no user configuration)
     */
    private loadConfig(): void {
        // HARDCODED: Always use default configuration
        // No user configuration loading needed
        this.config = this.getDefaultConfig();
    }

    /**
     * Save configuration (no-op since configuration is hardcoded)
     */
    private async saveConfig(): Promise<void> {
        // NO-OP: Configuration is hardcoded, no saving needed
    }

    /**
     * Legacy method for backward compatibility
     * HARDCODED: Always return true for file operations
     */
    public checkApproval(actionType: string): boolean {
        // HARDCODED: Always approve file operations (like Cursor/Cline)
        switch (actionType) {
            case 'readProjectFiles':
            case 'readAllFiles':
            case 'editProjectFiles':
            case 'editAllFiles':
            case 'useBrowser':
                return true; // Always allow file operations
            case 'executeSafeCommands':
            case 'executeAllCommands':
                return false; // Keep terminal commands disabled for safety
            case 'useMcpServers':
                return false; // Keep MCP servers disabled for safety
            default:
                return true; // Default to allowing operations
        }
    }

    /**
     * Get status summary for UI display
     */
    public getStatusSummary(): string {
        // HARDCODED: Always enabled
        return '✓ Always Enabled';
    }

    /**
     * Show configuration panel (disabled - auto-approval is hardcoded)
     */
    public showConfigurationPanel(): void {
        // NO-OP: Auto-approval is hardcoded, no configuration panel needed
        vscode.window.showInformationMessage('Auto-approval is always enabled. No configuration needed.');
    }

    /**
     * Save settings from webview to VS Code configuration
     */
    private async _saveSettingsFromWebview(settings: any): Promise<void> {
        // Update VS Code configuration
        const config = vscode.workspace.getConfiguration('v1b3-sama.autoApproval');

        await config.update('enabled', settings.globalEnabled, vscode.ConfigurationTarget.Global);
        await config.update('executeTerminalCommands', settings.executeTerminalCommands, vscode.ConfigurationTarget.Global);
        await config.update('useMcpServers', settings.useMcpServers, vscode.ConfigurationTarget.Global);
        await config.update('createBackups', settings.createBackups, vscode.ConfigurationTarget.Global);
        await config.update('maxFilesPerOperation', settings.maxFilesPerOperation, vscode.ConfigurationTarget.Global);

        // Update internal configuration
        await this.updateConfig({
            globalEnabled: settings.globalEnabled,
            executeTerminalCommands: settings.executeTerminalCommands,
            useMcpServers: settings.useMcpServers,
            createBackups: settings.createBackups,
            maxFilesPerOperation: settings.maxFilesPerOperation
        });

        // Update rules
        const currentConfig = await this.getConfig();
        const updatedRules = currentConfig.rules.map(rule => {
            switch (rule.id) {
                case 'safe-create-code':
                    return { ...rule, enabled: settings.autoCreateFiles };
                case 'safe-modify-code':
                    return { ...rule, enabled: settings.autoModifyFiles };
                case 'safe-create-directories':
                    return { ...rule, enabled: settings.autoCreateDirectories };
                default:
                    return rule;
            }
        });

        await this.updateConfig({ rules: updatedRules });
    }

    /**
     * Generate HTML for configuration webview
     */
    private _getConfigurationHtml(): string {
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Auto-Approval Configuration</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 20px;
                        margin: 0;
                    }
                    .container {
                        max-width: 600px;
                        margin: 0 auto;
                    }
                    h1 {
                        color: var(--vscode-foreground);
                        border-bottom: 1px solid var(--vscode-panel-border);
                        padding-bottom: 10px;
                        margin-bottom: 20px;
                    }
                    .setting-group {
                        margin-bottom: 20px;
                        padding: 15px;
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 6px;
                        background-color: var(--vscode-editor-background);
                    }
                    .setting-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;
                    }
                    .setting-item:last-child {
                        margin-bottom: 0;
                    }
                    .setting-item input[type="checkbox"] {
                        margin-right: 10px;
                    }
                    .setting-item input[type="number"] {
                        margin-left: 10px;
                        padding: 4px 8px;
                        border: 1px solid var(--vscode-input-border);
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border-radius: 3px;
                        width: 100px;
                    }
                    .setting-label {
                        flex: 1;
                        font-weight: 500;
                    }
                    .setting-description {
                        font-size: 0.9em;
                        color: var(--vscode-descriptionForeground);
                        margin-top: 5px;
                    }
                    .button {
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-right: 10px;
                    }
                    .button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    .button.secondary {
                        background-color: var(--vscode-button-secondaryBackground);
                        color: var(--vscode-button-secondaryForeground);
                    }
                    .actions {
                        margin-top: 30px;
                        text-align: center;
                    }
                    .warning {
                        background-color: var(--vscode-inputValidation-warningBackground);
                        border: 1px solid var(--vscode-inputValidation-warningBorder);
                        color: var(--vscode-inputValidation-warningForeground);
                        padding: 10px;
                        border-radius: 4px;
                        margin-bottom: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>⚙️ Auto-Approval Configuration</h1>

                    <div class="warning">
                        <strong>⚠️ Security Notice:</strong> Auto-approval allows V1b3-Sama to automatically perform file operations without user confirmation. Only enable features you trust.
                    </div>

                    <div class="setting-group">
                        <h3>Global Settings</h3>
                        <div class="setting-item">
                            <input type="checkbox" id="globalEnabled">
                            <label for="globalEnabled" class="setting-label">Enable Auto-Approval</label>
                        </div>
                        <div class="setting-description">Master switch for all auto-approval features</div>
                    </div>

                    <div class="setting-group">
                        <h3>File Operations</h3>
                        <div class="setting-item">
                            <input type="checkbox" id="autoCreateFiles">
                            <label for="autoCreateFiles" class="setting-label">Auto-Create Files</label>
                        </div>
                        <div class="setting-description">Automatically create new code files</div>

                        <div class="setting-item">
                            <input type="checkbox" id="autoModifyFiles">
                            <label for="autoModifyFiles" class="setting-label">Auto-Modify Files</label>
                        </div>
                        <div class="setting-description">Automatically modify existing files</div>

                        <div class="setting-item">
                            <input type="checkbox" id="autoCreateDirectories">
                            <label for="autoCreateDirectories" class="setting-label">Auto-Create Directories</label>
                        </div>
                        <div class="setting-description">Automatically create directory structures</div>

                        <div class="setting-item">
                            <input type="checkbox" id="createBackups">
                            <label for="createBackups" class="setting-label">Create Backups</label>
                        </div>
                        <div class="setting-description">Create backups before modifying existing files</div>
                    </div>

                    <div class="setting-group">
                        <h3>Advanced Operations</h3>
                        <div class="setting-item">
                            <input type="checkbox" id="executeTerminalCommands">
                            <label for="executeTerminalCommands" class="setting-label">Execute Terminal Commands</label>
                        </div>
                        <div class="setting-description">Allow automatic execution of safe terminal commands</div>

                        <div class="setting-item">
                            <input type="checkbox" id="useMcpServers">
                            <label for="useMcpServers" class="setting-label">Use MCP Servers</label>
                        </div>
                        <div class="setting-description">Enable automatic use of MCP server tools and resources</div>
                    </div>

                    <div class="setting-group">
                        <h3>Safety Limits</h3>
                        <div class="setting-item">
                            <label for="maxFilesPerOperation" class="setting-label">Max Files Per Operation:</label>
                            <input type="number" id="maxFilesPerOperation" min="1" max="100" value="10">
                        </div>
                        <div class="setting-description">Maximum number of files that can be created/modified in a single operation</div>
                    </div>

                    <div class="actions">
                        <button class="button" onclick="saveSettings()">Save Settings</button>
                        <button class="button secondary" onclick="openVSCodeSettings()">Open VS Code Settings</button>
                    </div>
                </div>

                <script>
                    const vscode = acquireVsCodeApi();

                    // Load settings on page load
                    window.addEventListener('load', () => {
                        vscode.postMessage({ command: 'loadSettings' });
                    });

                    // Handle messages from extension
                    window.addEventListener('message', event => {
                        const message = event.data;
                        switch (message.command) {
                            case 'settingsLoaded':
                                loadSettingsIntoUI(message.settings);
                                break;
                            case 'settingsSaved':
                                // Settings saved successfully
                                break;
                        }
                    });

                    function loadSettingsIntoUI(settings) {
                        document.getElementById('globalEnabled').checked = settings.globalEnabled;
                        document.getElementById('autoCreateFiles').checked = settings.autoCreateFiles;
                        document.getElementById('autoModifyFiles').checked = settings.autoModifyFiles;
                        document.getElementById('autoCreateDirectories').checked = settings.autoCreateDirectories;
                        document.getElementById('executeTerminalCommands').checked = settings.executeTerminalCommands;
                        document.getElementById('useMcpServers').checked = settings.useMcpServers;
                        document.getElementById('createBackups').checked = settings.createBackups;
                        document.getElementById('maxFilesPerOperation').value = settings.maxFilesPerOperation;
                    }

                    function saveSettings() {
                        const settings = {
                            globalEnabled: document.getElementById('globalEnabled').checked,
                            autoCreateFiles: document.getElementById('autoCreateFiles').checked,
                            autoModifyFiles: document.getElementById('autoModifyFiles').checked,
                            autoCreateDirectories: document.getElementById('autoCreateDirectories').checked,
                            executeTerminalCommands: document.getElementById('executeTerminalCommands').checked,
                            useMcpServers: document.getElementById('useMcpServers').checked,
                            createBackups: document.getElementById('createBackups').checked,
                            maxFilesPerOperation: parseInt(document.getElementById('maxFilesPerOperation').value)
                        };

                        vscode.postMessage({
                            command: 'saveSettings',
                            settings: settings
                        });
                    }

                    function openVSCodeSettings() {
                        vscode.postMessage({ command: 'openVSCodeSettings' });
                    }
                </script>
            </body>
            </html>`;
    }
}
