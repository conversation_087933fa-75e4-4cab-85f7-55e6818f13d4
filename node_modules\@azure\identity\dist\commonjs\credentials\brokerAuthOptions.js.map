{"version": 3, "file": "brokerAuthOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/brokerAuthOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport type { BrokerOptions } from \"../msal/nodeFlows/brokerOptions.js\";\n\n/**\n * Configuration options for InteractiveBrowserCredential\n * to support WAM Broker Authentication.\n */\n\nexport interface BrokerAuthOptions {\n  /**\n   * Options to allow broker authentication when using InteractiveBrowserCredential\n   *\n   */\n  brokerOptions?: BrokerOptions;\n}\n"]}