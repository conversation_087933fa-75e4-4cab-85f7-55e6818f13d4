import type { PipelinePolicy } from "../pipeline.js";
import type { TlsSettings } from "../interfaces.js";
/**
 * Name of the TLS Policy
 */
export declare const tlsPolicyName = "tlsPolicy";
/**
 * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.
 */
export declare function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy;
//# sourceMappingURL=tlsPolicy.d.ts.map