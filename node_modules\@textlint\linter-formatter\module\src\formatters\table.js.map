{"version": 3, "file": "table.js", "sourceRoot": "", "sources": ["../../../src/formatters/table.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,YAAY,CAAC;AAIb,gFAAgF;AAChF,eAAe;AACf,gFAAgF;AAEhF,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC9B,4BAA4B;AAC5B,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,SAAS,MAAM,YAAY,CAAC;AACnC,gFAAgF;AAChF,UAAU;AACV,gFAAgF;AAEhF;;;;GAIG;AACH,SAAS,SAAS,CAAC,QAA2B;IAC1C,IAAI,IAAI,GAAQ,EAAE,CAAC;IAEnB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAI,CAAC,IAAI,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;QACrB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;KACxB,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAwB;QAC/C,IAAI,WAAW,CAAC;QAEhB,IAAK,OAAe,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACnD,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACJ,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE;QACvB,OAAO,EAAE;YACL,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACjB;SACJ;QACD,kBAAkB,EAAE,UAAU,KAAa;YACvC,OAAO,KAAK,KAAK,CAAC,CAAC;QACvB,CAAC;KACJ,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CAAC,OAAY;IAC5B,IAAI,KAAK,CAAC;IAEV,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,MAAW;QACrC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,OAAe;QAC1C,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,MAAW,EAAE,OAAyB;IACrD,gBAAgB;IAChB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,MAAM,CAAC,OAAO,CAAC,UAAU,UAAe;QACpC,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC;QACpC,YAAY,IAAI,UAAU,CAAC,YAAY,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAI,UAAU,IAAI,YAAY,EAAE,CAAC;QAC7B,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,MAAM;QACF,IAAI;YACJ,KAAK,CACD;gBACI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;gBACjD,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;aAC3D,EACD;gBACI,OAAO,EAAE;oBACL,CAAC,EAAE;wBACC,KAAK,EAAE,GAAG;wBACV,QAAQ,EAAE,IAAI;qBACjB;iBACJ;gBACD,kBAAkB,EAAE;oBAChB,OAAO,IAAI,CAAC;gBAChB,CAAC;aACJ,CACJ,CAAC;IAEN,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,eAAe,SAAS,CAAC"}