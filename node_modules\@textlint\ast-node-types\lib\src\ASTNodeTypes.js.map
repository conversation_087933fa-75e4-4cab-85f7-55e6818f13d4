{"version": 3, "file": "ASTNodeTypes.js", "sourceRoot": "", "sources": ["../../src/ASTNodeTypes.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,uCAAuC;AACvC,uBAAuB;AACvB,oEAAoE;;;AAEpE;;GAEG;AACH,IAAY,YAgEX;AAhED,WAAY,YAAY;IACpB,qCAAqB,CAAA;IACrB,8CAA8B,CAAA;IAC9B,uCAAuB,CAAA;IACvB,gDAAgC,CAAA;IAChC,yCAAyB,CAAA;IACzB,kDAAkC,CAAA;IAClC,qCAAqB,CAAA;IACrB,8CAA8B,CAAA;IAC9B,6BAAa,CAAA;IACb,sCAAsB,CAAA;IACtB,iCAAiB,CAAA;IACjB,0CAA0B,CAAA;IAC1B,uCAAuB,CAAA;IACvB,gDAAgC,CAAA;IAChC;;OAEG;IACH,uCAAuB,CAAA;IACvB,gDAAgC,CAAA;IAChC,iDAAiC,CAAA;IACjC,0DAA0C,CAAA;IAC1C,mCAAmB,CAAA;IACnB,4CAA4B,CAAA;IAC5B;;OAEG;IACH,6CAA6B,CAAA;IAC7B;;OAEG;IACH,sDAAsC,CAAA;IACtC,SAAS;IACT,2BAAW,CAAA;IACX,oCAAoB,CAAA;IACpB,+BAAe,CAAA;IACf,wCAAwB,CAAA;IACxB,qCAAqB,CAAA;IACrB,8CAA8B,CAAA;IAC9B,iCAAiB,CAAA;IACjB,0CAA0B,CAAA;IAC1B,6BAAa,CAAA;IACb,sCAAsB,CAAA;IACtB,6BAAa,CAAA;IACb,sCAAsB,CAAA;IACtB,+CAA+B,CAAA;IAC/B,wDAAwC,CAAA;IACxC,+BAAe,CAAA;IACf,wCAAwB,CAAA;IACxB,iDAAiC,CAAA;IACjC,0DAA0C,CAAA;IAC1C,yCAAyB,CAAA;IACzB,kDAAkC,CAAA;IAClC,6BAAa,CAAA;IACb,sCAAsB,CAAA;IACtB,iCAAiB,CAAA;IACjB,0CAA0B,CAAA;IAC1B,sCAAsC;IACtC,+BAAe,CAAA;IACf,wCAAwB,CAAA;IACxB,qCAAqB,CAAA;IACrB,8CAA8B,CAAA;IAC9B,uCAAuB,CAAA;IACvB,gDAAgC,CAAA;AACpC,CAAC,EAhEW,YAAY,4BAAZ,YAAY,QAgEvB"}