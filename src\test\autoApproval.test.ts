import * as assert from 'assert';
import * as vscode from 'vscode';
import { AutoApprovalManager } from '../services/AutoApprovalManager';
import { FileOperation } from '../services/LLMResponseParser';

suite('AutoApprovalManager Tests', () => {
    let autoApprovalManager: AutoApprovalManager;
    let mockContext: vscode.ExtensionContext;

    setup(() => {
        // Create a mock extension context
        mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            },
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            }
        } as any;

        autoApprovalManager = new AutoApprovalManager(mockContext);
    });

    test('Should deny operations when auto-approval is disabled', async () => {
        const operation: FileOperation = {
            type: 'create',
            path: 'test.js',
            content: 'console.log("Hello World");'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'require_approval');
        assert.strictEqual(result.reason, 'Auto-approval is globally disabled');
    });

    test('Should auto-approve safe code file creation', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        const operation: FileOperation = {
            type: 'create',
            path: 'src/utils/helper.ts',
            content: 'export function helper() { return "test"; }'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'auto_approve');
        assert.ok(result.rule);
        assert.strictEqual(result.rule.id, 'safe-create-code');
    });

    test('Should require approval for modifying existing files', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        const operation: FileOperation = {
            type: 'modify',
            path: 'src/existing.ts',
            content: 'export function modified() { return "changed"; }'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'require_approval');
        assert.ok(result.rule);
        assert.strictEqual(result.rule.id, 'require-approval-modify');
    });

    test('Should deny operations on system files', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        const operation: FileOperation = {
            type: 'create',
            path: 'package-lock.json',
            content: '{ "lockfileVersion": 1 }'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'deny');
        assert.ok(result.rule);
        assert.strictEqual(result.rule.id, 'deny-system-files');
    });

    test('Should respect file size limits', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        // Create a large file content (over 100KB)
        const largeContent = 'x'.repeat(200000);
        const operation: FileOperation = {
            type: 'create',
            path: 'large.js',
            content: largeContent
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'require_approval');
        assert.ok(result.reason?.includes('File size'));
    });

    test('Should handle batch operations with safety limits', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        // Create more operations than the limit
        const operations: FileOperation[] = [];
        for (let i = 0; i < 15; i++) {
            operations.push({
                type: 'create',
                path: `file${i}.js`,
                content: `console.log(${i});`
            });
        }

        const result = await autoApprovalManager.evaluateOperations(operations);
        
        // Should require approval due to too many operations
        assert.strictEqual(result.autoApproved.length, 0);
        assert.strictEqual(result.requireApproval.length, 15);
        assert.ok(result.results.get(operations[0])?.reason?.includes('Too many operations'));
    });

    test('Should auto-approve config files', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        const operation: FileOperation = {
            type: 'create',
            path: 'config/settings.json',
            content: '{ "setting": "value" }'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'auto_approve');
        assert.ok(result.rule);
        assert.strictEqual(result.rule.id, 'safe-create-config');
    });

    test('Should handle custom rules', async () => {
        // Enable auto-approval
        await autoApprovalManager.updateConfig({ globalEnabled: true });

        // Add a custom rule
        await autoApprovalManager.addRule({
            id: 'custom-test-rule',
            name: 'Custom test rule',
            pattern: '**/*.test.js',
            operationType: 'create',
            action: 'deny',
            enabled: true,
            priority: 500
        });

        const operation: FileOperation = {
            type: 'create',
            path: 'src/test.test.js',
            content: 'test("should work", () => {});'
        };

        const result = await autoApprovalManager.evaluateFileOperation(operation);
        
        assert.strictEqual(result.action, 'deny');
        assert.ok(result.rule);
        assert.strictEqual(result.rule.id, 'custom-test-rule');
    });
});
