{"version": 3, "file": "RunningEvents.js", "sourceRoot": "", "sources": ["../src/RunningEvents.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AA4B1D,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAAkB,EAAE;IACnD,MAAM,aAAa,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAChD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;IACtC,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;IACzC,MAAM,KAAK,GAAqB,EAAE,CAAC;IACnC,OAAO;QACH,sBAAsB;YAClB,MAAM,eAAe,GAAmB,EAAE,CAAC;YAC3C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YACH,OAAO,eAAe,CAAC;QAC3B,CAAC;QACD;;;WAGG;QACH,OAAO,CAAC,OAA6C;YACjD,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;QACD,YAAY,CAAC,EACT,cAAc,EACd,OAAO,GAIV;YACG,IAAI,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE;gBACpC,oCAAoC;gBACpC,MAAM,IAAI,KAAK,CAAC,WAAW,cAAc,CAAC,EAAE;;;CAG3D,CAAC,CAAC;aACU;YACD,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,IAAI,GAAG,IAAI,cAAc,CAAC;gBAC5B,cAAc,EAAE,cAAc;gBAC9B,OAAO;aACV,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjB,aAAa,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,UAAU,EAAwC,EAAE,EAAE;gBACzF,yBAAyB;gBACzB,gBAAgB;gBAChB,SAAS;gBACT,kBAAkB,CAAC,IAAI,CAAC;oBACpB,IAAI,EAAE,2BAA2B;oBACjC,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBAClC,CAAC,CAAC;gBACH,wCAAwC;gBACxC,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;oBACpC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC/B;gBACD,kBAAkB,CAAC,IAAI,CAAC;oBACpB,IAAI,EAAE,yBAAyB;oBAC/B,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBAClC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC;QACD,kBAAkB,CAAC,EACf,oBAAoB,EACpB,OAAO,GAIV;YACG,iCAAiC;YACjC,MAAM,wBAAwB,GAAG,oBAAoB,CAAC,OAAO,IAAI,EAAE,CAAC;YACpE,sDAAsD;YACtD,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC;QACxE,CAAC;QACD,YAAY,CAAC,MAAc;YACvB,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;KACJ,CAAC;AACN,CAAC,CAAC"}