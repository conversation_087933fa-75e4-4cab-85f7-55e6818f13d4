{"version": 3, "file": "RefreshTokenClient.mjs", "sources": ["../../src/client/RefreshTokenClient.ts"], "sourcesContent": [null], "names": ["TimeUtils.nowSeconds", "ClientConfigurationErrorCodes.tokenRequestEmpty", "ClientAuthErrorCodes.noAccountInSilentRequest", "InteractionRequiredAuthErrorCodes.noTokensFound", "TimeUtils.isTokenExpired", "InteractionRequiredAuthErrorCodes.refreshTokenExpired", "InteractionRequiredAuthErrorCodes.badToken", "RequestParameterBuilder.addClientId", "AADServerParamKeys.CLIENT_ID", "RequestParameterBuilder.addRedirectUri", "RequestParameterBuilder.addScopes", "RequestParameterBuilder.addGrantType", "RequestParameterBuilder.addClientInfo", "RequestParameterBuilder.addLibraryInfo", "RequestParameterBuilder.addApplicationTelemetry", "RequestParameterBuilder.addThrottling", "RequestParameterBuilder.addServerTelemetry", "RequestParameterBuilder.addRefreshToken", "RequestParameterBuilder.addClientSecret", "RequestParameterBuilder.addClientAssertion", "RequestParameterBuilder.addClientAssertionType", "RequestParameterBuilder.addPopToken", "RequestParameterBuilder.addSshJwk", "ClientConfigurationErrorCodes.missingSshJwk", "RequestParameterBuilder.addClaims", "RequestParameterBuilder.addCcsOid", "RequestParameterBuilder.addCcsUpn", "RequestParameterBuilder.addBrokerParameters", "RequestParameterBuilder.addExtraQueryParameters", "RequestParameterBuilder.instrumentBrokerParams", "UrlUtils.mapToQueryString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAmDH,MAAM,+CAA+C,GAAG,GAAG,CAAC;AAE5D;;;AAGG;AACG,MAAO,kBAAmB,SAAQ,UAAU,CAAA;IAC9C,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;AAEtC,QAAA,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;KAC3C;IACM,MAAM,YAAY,CACrB,OAAkC,EAAA;AAElC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,8BAA8B,EAChD,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAGA,UAAoB,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,qCAAqC,EACvD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;QAG3B,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;AAClE,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AACF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAErD,OAAO,WAAW,CACd,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/D,iBAAiB,CAAC,yBAAyB,EAC3C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,SAAS,EACT,IAAI,EACJ,OAAO,CAAC,UAAU,EAClB,SAAS,CACZ,CAAC;KACL;AAED;;;AAGG;IACI,MAAM,0BAA0B,CACnC,OAAgC,EAAA;;QAGhC,IAAI,CAAC,OAAO,EAAE;AACV,YAAA,MAAM,8BAA8B,CAChCC,iBAA+C,CAClD,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,4CAA4C,EAC9D,OAAO,CAAC,aAAa,CACxB,CAAC;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,YAAA,MAAM,qBAAqB,CACvBC,wBAA6C,CAChD,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAC9C,OAAO,CAAC,OAAO,CAAC,WAAW,CAC9B,CAAC;;AAGF,QAAA,IAAI,MAAM,EAAE;YACR,IAAI;AACA,gBAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACpB,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,iBAAiB,GACnB,CAAC,YAAY,4BAA4B;AACzC,oBAAA,CAAC,CAAC,SAAS;wBACPC,aAA+C,CAAC;AACxD,gBAAA,MAAM,+BAA+B,GACjC,CAAC,YAAY,WAAW;AACxB,oBAAA,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,mBAAmB;AAC1C,oBAAA,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,qBAAqB,CAAC;;gBAGhD,IAAI,iBAAiB,IAAI,+BAA+B,EAAE;AACtD,oBAAA,OAAO,WAAW,CACd,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;AAErB,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;AACJ,aAAA;AACJ,SAAA;;AAED,QAAA,OAAO,WAAW,CACd,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,oDAAoD,EACtE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACrB;AAED;;;AAGG;AACK,IAAA,MAAM,kCAAkC,CAC5C,OAAgC,EAChC,IAAa,EAAA;AAEb,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,oDAAoD,EACtE,OAAO,CAAC,aAAa,CACxB,CAAC;;QAGF,MAAM,YAAY,GAAG,MAAM,CACvB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EACzD,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,CAAC,OAAO,EACf,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,IAAI,CAAC,YAAY,EAAE;AACf,YAAA,MAAM,kCAAkC,CACpCA,aAA+C,CAClD,CAAC;AACL,SAAA;QAED,IACI,YAAY,CAAC,SAAS;YACtBC,cAAwB,CACpB,YAAY,CAAC,SAAS,EACtB,OAAO,CAAC,mCAAmC;AACvC,gBAAA,+CAA+C,CACtD,EACH;YACE,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B,EAAE,aAAa,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EACjD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,YAAA,MAAM,kCAAkC,CACpCC,mBAAqD,CACxD,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,mBAAmB,GAA8B;AACnD,YAAA,GAAG,OAAO;YACV,YAAY,EAAE,YAAY,CAAC,MAAM;AACjC,YAAA,oBAAoB,EAChB,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM;AAC/D,YAAA,aAAa,EAAE;AACX,gBAAA,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;gBACzC,IAAI,EAAE,iBAAiB,CAAC,eAAe;AAC1C,aAAA;SACJ,CAAC;QAEF,IAAI;AACA,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5B,iBAAiB,CAAC,8BAA8B,EAChD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,mBAAmB,CAAC,CAAC;AAC1B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,4BAA4B,EAAE;gBAC3C,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B,EAAE,aAAa,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EACjD,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,gBAAA,IAAI,CAAC,CAAC,QAAQ,KAAKC,QAA0C,EAAE;;AAE3D,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sEAAsE,CACzE,CAAC;AACF,oBAAA,MAAM,kBAAkB,GACpB,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACxC,oBAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AAC5D,iBAAA;AACJ,aAAA;AAED,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,OAAkC,EAClC,SAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,qCAAqC,EACvD,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,wCAAwC,EAC1D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,CAAC;QACX,MAAM,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAClE,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,oBAAoB,CACnC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CACV,CAAC;QAEF,OAAO,WAAW,CACd,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1C,iBAAiB,CAAC,4CAA4C,EAC9D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,iBAAiB,CAAC,4CAA4C,CACjE,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAkC,EAAA;AAElC,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,wCAAwC,EAC1D,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,QAAAC,WAAmC,CAC/B,UAAU,EACV,OAAO,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,mBAAmB,GAAGC,SAA4B,CAAC;AAC3D,YAAA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACvC,CAAC;QAEF,IAAI,OAAO,CAAC,WAAW,EAAE;YACrBC,cAAsC,CAClC,UAAU,EACV,OAAO,CAAC,WAAW,CACtB,CAAC;AACL,SAAA;QAEDC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CACvE,CAAC;QAEFC,YAAoC,CAChC,UAAU,EACV,SAAS,CAAC,mBAAmB,CAChC,CAAC;AAEF,QAAAC,aAAqC,CAAC,UAAU,CAAC,CAAC;QAElDC,cAAsC,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAAC,uBAA+C,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACF,QAAAC,aAAqC,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjEC,kBAA0C,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;QAEDC,eAAuC,CACnC,UAAU,EACV,OAAO,CAAC,YAAY,CACvB,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;AAC5C,YAAAC,eAAuC,CACnC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAElDC,kBAA0C,CACtC,UAAU,EACV,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;YACFC,sBAA8C,CAC1C,UAAU,EACV,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;AAED,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;AAC3D,YAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC3C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AAEF,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAExB,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D,aAAA;;AAGD,YAAAC,WAAmC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC/D,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;YAClE,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChBC,SAAiC,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACjE,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,aAAA;AACJ,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAAC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IACI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB;YAC9C,OAAO,CAAC,aAAa,EACvB;AACE,YAAA,QAAQ,OAAO,CAAC,aAAa,CAAC,IAAI;gBAC9B,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,aAAa,CAAC,UAAU,CACnC,CAAC;AACF,wBAAAC,SAAiC,CAC7B,UAAU,EACV,UAAU,CACb,CAAC;AACL,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD;AAC9C,4BAAA,CAAC,CACR,CAAC;AACL,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;oBACtBC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,aAAa,CAAC,UAAU,CACnC,CAAC;oBACF,MAAM;AACb,aAAA;AACJ,SAAA;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1BC,mBAA2C,CACvC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CACtC,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC7BC,uBAA+C,CAC3C,UAAU,EACV,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;AAED,QAAAC,sBAA8C,CAC1C,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACF,QAAA,OAAOC,gBAAyB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}