{"version": 3, "file": "operationOptionHelpers.js", "sourceRoot": "", "sources": ["../../../src/client/operationOptionHelpers.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AASlC,kFAWC;AAhBD;;;;GAIG;AACH,SAAgB,mCAAmC,CAAC,OAAyB;;IAC3E,OAAO;QACL,uBAAuB,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,uBAAuB;QACxE,OAAO,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO;QACxC,eAAe,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,eAAe;QACxD,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,gBAAgB,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,gBAAgB;QAC1D,kBAAkB,EAAE,MAAA,OAAO,CAAC,cAAc,0CAAE,kBAAkB;QAC9D,OAAO,oBAAO,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,CAAE;QAC/C,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions, RequestParameters } from \"./common.js\";\n\n/**\n * Helper function to convert OperationOptions to RequestParameters\n * @param options - the options that are used by Modular layer to send the request\n * @returns the result of the conversion in RequestParameters of RLC layer\n */\nexport function operationOptionsToRequestParameters(options: OperationOptions): RequestParameters {\n  return {\n    allowInsecureConnection: options.requestOptions?.allowInsecureConnection,\n    timeout: options.requestOptions?.timeout,\n    skipUrlEncoding: options.requestOptions?.skipUrlEncoding,\n    abortSignal: options.abortSignal,\n    onUploadProgress: options.requestOptions?.onUploadProgress,\n    onDownloadProgress: options.requestOptions?.onDownloadProgress,\n    headers: { ...options.requestOptions?.headers },\n    onResponse: options.onResponse,\n  };\n}\n"]}