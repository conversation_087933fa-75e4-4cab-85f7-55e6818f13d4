{"version": 3, "file": "azurePipelinesCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azurePipelinesCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Optional parameters for the {@link AzurePipelinesCredential} class.\n */\nexport interface AzurePipelinesCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {}\n"]}