{"version": 3, "file": "CacheError.mjs", "sources": ["../../src/error/CacheError.ts"], "sourcesContent": [null], "names": ["CacheErrorCodes.cacheQuotaExceededErrorCode", "CacheErrorCodes.cacheUnknownErrorCode"], "mappings": ";;;;;;AAAA;;;AAGG;AAKU,MAAA,kBAAkB,GAAG;AAC9B,IAAA,CAACA,2BAA2C,GACxC,kCAAkC;AACtC,IAAA,CAACC,qBAAqC,GAClC,qDAAqD;EAC3D;AAEF;;AAEG;AACG,MAAO,UAAW,SAAQ,KAAK,CAAA;IAWjC,WAAY,CAAA,SAAiB,EAAE,YAAqB,EAAA;QAChD,MAAM,OAAO,GACT,YAAY;aACX,kBAAkB,CAAC,SAAS,CAAC;AAC1B,kBAAE,kBAAkB,CAAC,SAAS,CAAC;kBAC7B,kBAAkB,CAACA,qBAAqC,CAAC,CAAC,CAAC;AAErE,QAAA,KAAK,CAAC,CAAG,EAAA,SAAS,KAAK,OAAO,CAAA,CAAE,CAAC,CAAC;QAClC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;AAElD,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;KAC/B;AACJ;;;;"}