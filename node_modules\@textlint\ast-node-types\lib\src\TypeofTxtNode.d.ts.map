{"version": 3, "file": "TypeofTxtNode.d.ts", "sourceRoot": "", "sources": ["../../src/TypeofTxtNode.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,EACR,UAAU,EACV,iBAAiB,EACjB,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,eAAe,EACf,eAAe,EACf,aAAa,EACb,qBAAqB,EACrB,WAAW,EACX,YAAY,EACZ,qBAAqB,EACrB,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,oBAAoB,EACpB,eAAe,EACf,WAAW,EACX,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EAClB,MAAM,YAAY,CAAC;AAEpB;;;;;;;;GAQG;AACH,MAAM,MAAM,aAAa,CAAC,CAAC,SAAS,YAAY,GAAG,MAAM,IAErD,CAAC,SAAS,YAAY,CAAC,QAAQ,GACzB,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,YAAY,GACnC,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,gBAAgB,GAChB,CAAC,SAAS,YAAY,CAAC,aAAa,GACpC,gBAAgB,GAChB,CAAC,SAAS,YAAY,CAAC,UAAU,GACjC,iBAAiB,GACjB,CAAC,SAAS,YAAY,CAAC,cAAc,GACrC,iBAAiB,GACjB,CAAC,SAAS,YAAY,CAAC,IAAI,GAC3B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,YAAY,GACnC,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,MAAM,GAC7B,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,UAAU,GACjC,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,SAAS,GAKhC,gBAAgB,GAChB,CAAC,SAAS,YAAY,CAAC,aAAa,GACpC,gBAAgB,GAChB,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,aAAa,GACpC,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,IAAI,GAC3B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,aAAa,GACpC,oBAAoB,GACpB,CAAC,SAAS,YAAY,CAAC,iBAAiB,GACxC,oBAAoB,GACpB,CAAC,SAAS,YAAY,CAAC,MAAM,GAC7B,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,UAAU,GACjC,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,YAAY,GACnC,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,MAAM,GAC7B,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,UAAU,GACjC,aAAa,GACb,CAAC,SAAS,YAAY,CAAC,KAAK,GAC5B,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,KAAK,GAC5B,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,cAAc,GACrC,qBAAqB,GACrB,CAAC,SAAS,YAAY,CAAC,kBAAkB,GACzC,qBAAqB,GACrB,CAAC,SAAS,YAAY,CAAC,UAAU,GACjC,iBAAiB,GACjB,CAAC,SAAS,YAAY,CAAC,cAAc,GACrC,iBAAiB,GACjB,CAAC,SAAS,YAAY,CAAC,cAAc,GACrC,qBAAqB,GACrB,CAAC,SAAS,YAAY,CAAC,kBAAkB,GACzC,qBAAqB,GACrB,CAAC,SAAS,YAAY,CAAC,OAAO,GAC9B,cAAc,GACd,CAAC,SAAS,YAAY,CAAC,WAAW,GAClC,cAAc,GACd,CAAC,SAAS,YAAY,CAAC,GAAG,GAC1B,UAAU,GACV,CAAC,SAAS,YAAY,CAAC,OAAO,GAC9B,UAAU,GACV,CAAC,SAAS,YAAY,CAAC,IAAI,GAC3B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,IAAI,GAC3B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,WAAW,GACX,CAAC,SAAS,YAAY,CAAC,KAAK,GAC5B,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,YAAY,GACZ,CAAC,SAAS,YAAY,CAAC,QAAQ,GAC/B,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,YAAY,GACnC,eAAe,GACf,CAAC,SAAS,YAAY,CAAC,SAAS,GAChC,gBAAgB,GAChB,CAAC,SAAS,YAAY,CAAC,aAAa,GACpC,gBAAgB,GAChB,UAAU,CAAC"}