{"name": "hosted-git-info", "version": "4.1.0", "description": "Provides metadata and conversions from repository urls for GitHub, Bitbucket and GitLab", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "keywords": ["git", "github", "bitbucket", "gitlab"], "author": "<PERSON> <<EMAIL>> (http://re-becca.org)", "license": "ISC", "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "homepage": "https://github.com/npm/hosted-git-info", "scripts": {"posttest": "standard", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "test": "tap", "test:coverage": "tap --coverage-report=html"}, "dependencies": {"lru-cache": "^6.0.0"}, "devDependencies": {"standard": "^16.0.3", "standard-version": "^9.1.0", "tap": "^15.1.6"}, "files": ["index.js", "git-host.js", "git-host-info.js"], "engines": {"node": ">=10"}, "tap": {"color": 1, "coverage": true}}