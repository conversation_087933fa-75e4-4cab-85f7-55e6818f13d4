{"version": 3, "file": "usernamePasswordCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/usernamePasswordCredentialOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Defines options for the {@link UsernamePasswordCredential} class.\n * @deprecated UsernamePasswordCredential is deprecated. Use a more secure credential. See https://aka.ms/azsdk/identity/mfa for details.\n */\nexport interface UsernamePasswordCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {}\n"]}