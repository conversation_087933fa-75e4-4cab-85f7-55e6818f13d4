import * as vscode from 'vscode';
import { SettingsService, SettingsChangeEvent } from './SettingsService';
import { ApiKeyManager } from './ApiKeyManager';
import { DynamicProviderService } from './DynamicProviderService';

/**
 * Service registration interface for settings synchronization
 */
export interface SettingsAwareService {
    onSettingsChanged(event: SettingsChangeEvent): Promise<void> | void;
}

/**
 * Settings synchronization coordinator
 * Manages real-time settings updates across all extension services
 */
export class SettingsSynchronizationService {
    private _disposables: vscode.Disposable[] = [];
    private _registeredServices = new Set<SettingsAwareService>();
    private _isInitialized = false;
    
    constructor(
        private context: vscode.ExtensionContext,
        private settingsService: SettingsService,
        private apiKeyManager: ApiKeyManager,
        private dynamicProviderService: DynamicProviderService
    ) {
        this.initialize();
    }
    
    /**
     * Initialize the synchronization service
     */
    private initialize(): void {
        if (this._isInitialized) return;
        
        // Listen for settings changes
        this._disposables.push(
            this.settingsService.onDidChangeSettings(async (event) => {
                await this.handleSettingsChange(event);
            })
        );
        
        // Register core services
        this.registerCoreServices();
        
        this.context.subscriptions.push(...this._disposables);
        this._isInitialized = true;
        
        console.log('Settings synchronization service initialized');
    }
    
    /**
     * Register core extension services for settings synchronization
     */
    private registerCoreServices(): void {
        // Register services that need to respond to settings changes
        // Note: These would be actual service instances in a real implementation
        
        // Example registrations (these would be real service instances)
        // this.registerService(chatViewProvider);
        // this.registerService(autoApprovalManager);
        // this.registerService(performanceService);
    }
    
    /**
     * Register a service for settings synchronization
     */
    public registerService(service: SettingsAwareService): void {
        this._registeredServices.add(service);
        console.log(`Registered service for settings synchronization: ${service.constructor.name}`);
    }
    
    /**
     * Unregister a service from settings synchronization
     */
    public unregisterService(service: SettingsAwareService): void {
        this._registeredServices.delete(service);
        console.log(`Unregistered service from settings synchronization: ${service.constructor.name}`);
    }
    
    /**
     * Handle settings changes and notify all registered services
     */
    private async handleSettingsChange(event: SettingsChangeEvent): Promise<void> {
        console.log(`Settings changed: ${event.section}.${event.key}`, {
            oldValue: event.oldValue,
            newValue: event.newValue,
            affectedConfiguration: event.affectedConfiguration
        });
        
        // Handle specific settings changes with immediate effects
        await this.handleSpecificSettingsChanges(event);
        
        // Notify all registered services
        const notificationPromises = Array.from(this._registeredServices).map(async (service) => {
            try {
                await service.onSettingsChanged(event);
            } catch (error) {
                console.error(`Error notifying service of settings change:`, error);
                vscode.window.showErrorMessage(
                    `Failed to apply settings change in ${service.constructor.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
                );
            }
        });
        
        await Promise.all(notificationPromises);
        
        // Show user feedback for important changes
        this.showUserFeedback(event);
    }
    
    /**
     * Handle specific settings changes that require immediate action
     */
    private async handleSpecificSettingsChanges(event: SettingsChangeEvent): Promise<void> {
        switch (event.section) {
            case 'core':
                await this.handleCoreSettingsChange(event);
                break;
            case 'autoApproval':
                await this.handleAutoApprovalSettingsChange(event);
                break;
            case 'performance':
                await this.handlePerformanceSettingsChange(event);
                break;
            case 'ui':
                await this.handleUISettingsChange(event);
                break;
            case 'security':
                await this.handleSecuritySettingsChange(event);
                break;
            case 'contextEngine':
                await this.handleContextEngineSettingsChange(event);
                break;
        }
    }
    
    /**
     * Handle core settings changes (provider, model)
     */
    private async handleCoreSettingsChange(event: SettingsChangeEvent): Promise<void> {
        if (event.key === 'provider') {
            // Provider change is handled by DynamicProviderService
            console.log(`Provider changed from ${event.oldValue} to ${event.newValue}`);
            
            // Validate that the new provider is available
            const status = await this.dynamicProviderService.getProviderStatus(event.newValue);
            if (!status.isAvailable) {
                vscode.window.showWarningMessage(
                    `Provider ${event.newValue} is not available: ${status.error}. Please configure API key or check connection.`
                );
            }
        } else if (event.key === 'model') {
            console.log(`Model changed from ${event.oldValue} to ${event.newValue}`);
            
            // Validate that the new model is available for the current provider
            const currentProvider = this.settingsService.get('provider');
            const availableModels = await this.dynamicProviderService.getModelsForProvider(currentProvider);
            
            if (!availableModels.some(model => model.id === event.newValue)) {
                vscode.window.showWarningMessage(
                    `Model ${event.newValue} is not available for provider ${currentProvider}. Available models: ${availableModels.map(m => m.id).join(', ')}`
                );
            }
        }
    }
    
    /**
     * Handle auto-approval settings changes
     */
    private async handleAutoApprovalSettingsChange(event: SettingsChangeEvent): Promise<void> {
        // NO-OP: Auto-approval is hardcoded, no configuration changes needed
    }
    
    /**
     * Handle performance settings changes
     */
    private async handlePerformanceSettingsChange(event: SettingsChangeEvent): Promise<void> {
        console.log('Performance settings changed:', event);
        
        const config = this.settingsService.getPerformanceConfig();
        
        // Apply performance changes immediately
        if (event.key === 'workerPoolSize') {
            // Restart worker pool with new size
            console.log(`Worker pool size changed to ${event.newValue}`);
        } else if (event.key === 'enableStreaming') {
            console.log(`Streaming ${event.newValue ? 'enabled' : 'disabled'}`);
        } else if (event.key === 'streamingTimeout') {
            console.log(`Streaming timeout changed to ${event.newValue}ms`);
        }
    }
    
    /**
     * Handle UI settings changes
     */
    private async handleUISettingsChange(event: SettingsChangeEvent): Promise<void> {
        console.log('UI settings changed:', event);
        
        // UI changes typically require webview refresh
        // This would be handled by the ChatViewProvider or other UI components
        if (['theme', 'fontSize', 'showLineNumbers', 'enableSyntaxHighlighting', 'compactMode'].includes(event.key)) {
            // Notify UI components to refresh
            vscode.commands.executeCommand('v1b3-sama.refreshUI');
        }
    }
    
    /**
     * Handle security settings changes
     */
    private async handleSecuritySettingsChange(event: SettingsChangeEvent): Promise<void> {
        console.log('Security settings changed:', event);
        
        if (event.key === 'validateApiKeys' && event.newValue === false) {
            vscode.window.showWarningMessage(
                'API key validation has been disabled. This may reduce security.',
                'Understand'
            );
        }
    }
    
    /**
     * Handle context engine settings changes
     */
    private async handleContextEngineSettingsChange(event: SettingsChangeEvent): Promise<void> {
        console.log('Context engine settings changed:', event);
        
        if (event.key === 'enabled') {
            if (event.newValue) {
                console.log('Context engine enabled');
            } else {
                console.log('Context engine disabled');
            }
        }
    }
    
    /**
     * Validate auto-approval configuration using SettingsService
     */
    private validateAutoApprovalConfig(config: any): { isValid: boolean; errors: string[] } {
        // Use the centralized validation from SettingsService
        return this.settingsService.validateConfiguration(config);
    }
    
    /**
     * Show user feedback for important settings changes
     */
    private showUserFeedback(event: SettingsChangeEvent): void {
        // Only show feedback for user-visible changes
        const importantChanges = [
            'core.provider',
            'core.model',
            'autoApproval.enabled',
            'performance.enableStreaming'
        ];
        
        const changeKey = `${event.section}.${event.key}`;
        if (importantChanges.includes(changeKey)) {
            const message = this.getChangeMessage(event);
            if (message) {
                vscode.window.showInformationMessage(message);
            }
        }
    }
    
    /**
     * Get user-friendly message for settings changes
     */
    private getChangeMessage(event: SettingsChangeEvent): string | null {
        switch (`${event.section}.${event.key}`) {
            case 'core.provider':
                return `Switched to provider: ${this.apiKeyManager.getProviderDisplayName(event.newValue)}`;
            case 'core.model':
                return `Switched to model: ${event.newValue}`;
            case 'autoApproval.enabled':
                return event.newValue ? 'Auto-approval enabled' : 'Auto-approval disabled';
            case 'performance.enableStreaming':
                return event.newValue ? 'Streaming enabled' : 'Streaming disabled';
            default:
                return null;
        }
    }
    
    /**
     * Force refresh all settings-dependent services
     */
    public async forceRefreshAll(): Promise<void> {
        console.log('Force refreshing all settings-dependent services');
        
        // Create a synthetic change event to trigger refresh
        const refreshEvent: SettingsChangeEvent = {
            section: 'system',
            key: 'refresh',
            oldValue: null,
            newValue: null,
            affectedConfiguration: ['all']
        };
        
        await this.handleSettingsChange(refreshEvent);
    }
    
    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._registeredServices.clear();
        this._isInitialized = false;
    }
}
