import * as vscode from 'vscode';
import { SupportedProvider } from './ApiKeyManager';
import { ProviderStatusMonitoringService, ProviderStatusSummary } from './ProviderStatusMonitoringService';
import { SettingsService } from './SettingsService';

/**
 * Service for displaying provider status in VS Code UI
 */
export class ProviderStatusIndicatorService {
    private static instance: ProviderStatusIndicatorService;
    private _statusBarItem: vscode.StatusBarItem;
    private _disposables: vscode.Disposable[] = [];
    private _currentProvider?: SupportedProvider;
    private _currentStatus?: ProviderStatusSummary;

    public static getInstance(): ProviderStatusIndicatorService {
        if (!ProviderStatusIndicatorService.instance) {
            ProviderStatusIndicatorService.instance = new ProviderStatusIndicatorService();
        }
        return ProviderStatusIndicatorService.instance;
    }

    constructor() {
        // Create status bar item
        this._statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            100
        );
        this._statusBarItem.command = 'v1b3-sama.showProviderStatus';
    }

    /**
     * Initialize the status indicator
     */
    public async initialize(context: vscode.ExtensionContext): Promise<void> {
        // Listen for provider status changes
        const monitoringService = ProviderStatusMonitoringService.getInstance();
        this._disposables.push(
            monitoringService.onDidChangeProviderStatus(status => {
                if (status.provider === this._currentProvider) {
                    this.updateStatusDisplay(status);
                }
            })
        );

        // Listen for provider changes in settings
        // Note: SettingsService integration will be added when properly initialized

        // Register command to show detailed status
        this._disposables.push(
            vscode.commands.registerCommand('v1b3-sama.showProviderStatus', () => {
                this.showDetailedStatus();
            })
        );

        // Show status bar item
        this._statusBarItem.show();

        // Initial status update
        await this.refreshStatus();

        context.subscriptions.push(...this._disposables, this._statusBarItem);
    }

    /**
     * Update current provider and refresh status
     */
    private async updateCurrentProvider(provider: SupportedProvider): Promise<void> {
        this._currentProvider = provider;
        await this.refreshStatus();
    }

    /**
     * Refresh status display
     */
    private async refreshStatus(): Promise<void> {
        if (!this._currentProvider) {
            this._statusBarItem.text = '$(warning) No Provider';
            this._statusBarItem.tooltip = 'No LLM provider selected';
            this._statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            return;
        }

        try {
            // Get current provider status
            const monitoringService = ProviderStatusMonitoringService.getInstance();
            const details = monitoringService.getProviderStatusDetails(this._currentProvider);
            
            if (details.summary) {
                this.updateStatusDisplay(details.summary);
            } else {
                // No status available, show unknown
                this._statusBarItem.text = `$(question) ${this._currentProvider}`;
                this._statusBarItem.tooltip = `Provider: ${this._currentProvider} (Status unknown)`;
                this._statusBarItem.backgroundColor = undefined;
            }
        } catch (error) {
            console.error('Failed to refresh provider status:', error);
            this._statusBarItem.text = `$(error) ${this._currentProvider}`;
            this._statusBarItem.tooltip = `Provider: ${this._currentProvider} (Error getting status)`;
            this._statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
        }
    }

    /**
     * Update status bar display based on provider status
     */
    private updateStatusDisplay(status: ProviderStatusSummary): void {
        this._currentStatus = status;
        
        const { icon, color } = this.getStatusIconAndColor(status.status);
        const responseTimeText = status.responseTime ? ` (${status.responseTime}ms)` : '';
        
        this._statusBarItem.text = `${icon} ${status.provider}${responseTimeText}`;
        this._statusBarItem.backgroundColor = color;
        
        // Create detailed tooltip
        const tooltip = this.createStatusTooltip(status);
        this._statusBarItem.tooltip = tooltip;
    }

    /**
     * Get icon and color for status
     */
    private getStatusIconAndColor(status: string): { icon: string; color?: vscode.ThemeColor } {
        switch (status) {
            case 'online':
                return { 
                    icon: '$(check)', 
                    color: undefined // Default color
                };
            case 'degraded':
                return { 
                    icon: '$(warning)', 
                    color: new vscode.ThemeColor('statusBarItem.warningBackground')
                };
            case 'offline':
                return { 
                    icon: '$(error)', 
                    color: new vscode.ThemeColor('statusBarItem.errorBackground')
                };
            case 'unknown':
            default:
                return { 
                    icon: '$(question)', 
                    color: undefined
                };
        }
    }

    /**
     * Create detailed tooltip for status
     */
    private createStatusTooltip(status: ProviderStatusSummary): string {
        const lines = [
            `Provider: ${status.provider}`,
            `Status: ${status.status.toUpperCase()}`,
            `Last Checked: ${status.lastChecked.toLocaleTimeString()}`
        ];

        if (status.responseTime) {
            lines.push(`Response Time: ${status.responseTime}ms`);
        }

        if (status.uptime > 0) {
            lines.push(`Uptime: ${status.uptime.toFixed(1)}%`);
        }

        if (status.errorCount > 0) {
            lines.push(`Recent Errors: ${status.errorCount}`);
        }

        if (status.isPreferred) {
            lines.push('✓ Preferred Provider');
        }

        lines.push('', 'Click for detailed status');

        return lines.join('\n');
    }

    /**
     * Show detailed status in a webview or quick pick
     */
    private async showDetailedStatus(): Promise<void> {
        try {
            const monitoringService = ProviderStatusMonitoringService.getInstance();
            const allStatuses = await monitoringService.getAllProviderStatuses();
            const stats = monitoringService.getMonitoringStats();
            
            // Create quick pick items for each provider
            const items: vscode.QuickPickItem[] = allStatuses.map(status => {
                const { icon } = this.getStatusIconAndColor(status.status);
                const responseTime = status.responseTime ? ` (${status.responseTime}ms)` : '';
                const uptime = status.uptime > 0 ? ` - ${status.uptime.toFixed(1)}% uptime` : '';
                
                return {
                    label: `${icon} ${status.provider}${responseTime}`,
                    description: `${status.status.toUpperCase()}${uptime}`,
                    detail: `Last checked: ${status.lastChecked.toLocaleTimeString()}${status.errorCount > 0 ? ` | ${status.errorCount} recent errors` : ''}`,
                    picked: status.provider === this._currentProvider
                };
            });

            // Add overall statistics
            items.unshift({
                label: '$(graph) Overall Statistics',
                description: `${stats.onlineProviders}/${stats.totalProviders} online | ${stats.overallUptime.toFixed(1)}% uptime`,
                detail: `Average response time: ${stats.averageResponseTime.toFixed(0)}ms | Total checks: ${stats.totalChecks}`,
                kind: vscode.QuickPickItemKind.Separator
            });

            // Show quick pick
            const selected = await vscode.window.showQuickPick(items, {
                title: 'Provider Status Overview',
                placeHolder: 'Select a provider to switch to it',
                canPickMany: false
            });

            if (selected && selected.label.includes('$(')) {
                // Extract provider name from label
                const providerMatch = selected.label.match(/\$\([^)]+\)\s+(\w+)/);
                if (providerMatch) {
                    const provider = providerMatch[1] as SupportedProvider;
                    if (provider !== this._currentProvider) {
                        // Note: Provider switching requires SettingsService integration
                        vscode.window.showInformationMessage(`Provider switching not yet implemented: ${provider}`);
                    }
                }
            }

        } catch (error) {
            console.error('Failed to show detailed status:', error);
            vscode.window.showErrorMessage('Failed to load provider status details');
        }
    }

    /**
     * Show provider health notification
     */
    public showHealthNotification(status: ProviderStatusSummary): void {
        if (status.provider !== this._currentProvider) {
            return; // Only show notifications for current provider
        }

        switch (status.status) {
            case 'offline':
                vscode.window.showErrorMessage(
                    `Provider ${status.provider} is offline`,
                    'Check Status',
                    'Switch Provider'
                ).then(action => {
                    if (action === 'Check Status') {
                        this.showDetailedStatus();
                    } else if (action === 'Switch Provider') {
                        vscode.commands.executeCommand('v1b3-sama.showProviderStatus');
                    }
                });
                break;

            case 'degraded':
                vscode.window.showWarningMessage(
                    `Provider ${status.provider} is experiencing issues`,
                    'Check Status'
                ).then(action => {
                    if (action === 'Check Status') {
                        this.showDetailedStatus();
                    }
                });
                break;

            case 'online':
                // Only show recovery notification if we were previously offline
                if (this._currentStatus && this._currentStatus.status === 'offline') {
                    vscode.window.showInformationMessage(
                        `Provider ${status.provider} is back online`
                    );
                }
                break;
        }
    }

    /**
     * Force refresh status
     */
    public async forceRefresh(): Promise<void> {
        if (this._currentProvider) {
            try {
                const monitoringService = ProviderStatusMonitoringService.getInstance();
                const status = await monitoringService.forceHealthCheck(this._currentProvider);
                this.updateStatusDisplay(status);
            } catch (error) {
                console.error('Failed to force refresh status:', error);
            }
        }
    }

    /**
     * Hide status bar item
     */
    public hide(): void {
        this._statusBarItem.hide();
    }

    /**
     * Show status bar item
     */
    public show(): void {
        this._statusBarItem.show();
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._statusBarItem.dispose();
    }
}
