{"version": 3, "file": "delay.js", "sourceRoot": "", "sources": ["../../src/delay.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,yBAAyB,EAAE,MAAM,yCAAyC,CAAC;AAEpF,MAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAOtD;;;;;GAKG;AACH,MAAM,UAAU,KAAK,CAAC,QAAgB,EAAE,OAAsB;IAC5D,IAAI,KAAoC,CAAC;IACzC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IACrD,OAAO,sBAAsB,CAC3B,CAAC,OAAO,EAAE,EAAE;QACV,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC,EACD;QACE,kBAAkB,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC;QAC7C,WAAW;QACX,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,oBAAoB;KACrD,CACF,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CACjC,YAAoB,EACpB,MAGC;IAED,6CAA6C;IAC7C,MAAM,gBAAgB,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAE3E,yCAAyC;IACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAE1E,gFAAgF;IAChF,mEAAmE;IACnE,MAAM,cAAc,GAAG,YAAY,GAAG,CAAC,GAAG,yBAAyB,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IAEzF,OAAO,EAAE,cAAc,EAAE,CAAC;AAC5B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortOptions } from \"./aborterUtils.js\";\nimport { createAbortablePromise } from \"./createAbortablePromise.js\";\nimport { getRandomIntegerInclusive } from \"@typespec/ts-http-runtime/internal/util\";\n\nconst StandardAbortMessage = \"The delay was aborted.\";\n\n/**\n * Options for support abort functionality for the delay method\n */\nexport interface DelayOptions extends AbortOptions {}\n\n/**\n * A wrapper for setTimeout that resolves a promise after timeInMs milliseconds.\n * @param timeInMs - The number of milliseconds to be delayed.\n * @param options - The options for delay - currently abort options\n * @returns Promise that is resolved after timeInMs\n */\nexport function delay(timeInMs: number, options?: DelayOptions): Promise<void> {\n  let token: ReturnType<typeof setTimeout>;\n  const { abortSignal, abortErrorMsg } = options ?? {};\n  return createAbortablePromise(\n    (resolve) => {\n      token = setTimeout(resolve, timeInMs);\n    },\n    {\n      cleanupBeforeAbort: () => clearTimeout(token),\n      abortSignal,\n      abortErrorMsg: abortErrorMsg ?? StandardAbortMessage,\n    },\n  );\n}\n\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n * @param retryAttempt - The current retry attempt number.\n * @param config - The exponential retry configuration.\n * @returns An object containing the calculated retry delay.\n */\nexport function calculateRetryDelay(\n  retryAttempt: number,\n  config: {\n    retryDelayInMs: number;\n    maxRetryDelayInMs: number;\n  },\n): { retryAfterInMs: number } {\n  // Exponentially increase the delay each time\n  const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);\n\n  // Don't let the delay exceed the maximum\n  const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);\n\n  // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n  // that retries across multiple clients don't occur simultaneously.\n  const retryAfterInMs = clampedDelay / 2 + getRandomIntegerInclusive(0, clampedDelay / 2);\n\n  return { retryAfterInMs };\n}\n"]}