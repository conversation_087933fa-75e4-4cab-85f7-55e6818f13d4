{"name": "@secretlint/secretlint-formatter-sarif", "version": "9.3.4", "description": "A secretlint formatter for SARIF format", "keywords": ["sarif", "secretlint", "formatter"], "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/@secretlint/secretlint-formatter-sarif/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./module/index.d.ts", "default": "./module/index.js"}, "default": "./module/index.js"}, "./package.json": "./package.json"}, "main": "./module/index.js", "module": "module/index.js", "types": "./module/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "module/", "src/"], "scripts": {"build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublishOnly": "npm run clean && npm run build", "test": "node --loader ts-node/esm --test test/index.test.ts", "updateSnapshot": "UPDATE_SNAPSHOT=1 npm test", "watch": "tsc --build --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4, "trailingComma": "none"}, "dependencies": {"node-sarif-builder": "^2.0.3"}, "devDependencies": {"@secretlint/types": "^9.3.4", "@types/mocha": "^10.0.10", "@types/node": "^20.17.51", "escape-string-regexp": "^5.0.0", "mocha": "^10.8.2", "prettier": "^2.8.1", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "packageManager": "yarn@1.22.22", "publishConfig": {"access": "public"}, "gitHead": "1b0bebd91380d94ee0a756900ce9b39c8b3cfd1b"}