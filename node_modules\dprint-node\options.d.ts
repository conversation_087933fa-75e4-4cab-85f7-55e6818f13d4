/* tslint:disable */
/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */

export type UseTabs = boolean;
export type UseTabs1 = true | false;
export type SemiColons = "always" | "prefer" | "asi";
export type SemiColons1 = string;
export type QuoteStyle = "alwaysDouble" | "alwaysSingle" | "preferDouble" | "preferSingle";
export type QuoteStyle1 = string;
export type QuoteProps = "preserve" | "asNeeded";
export type QuoteProps1 = string;
export type NewLineKind = "auto" | "crlf" | "lf" | "system";
export type NewLineKind1 = string;
export type UseBraces = "maintain" | "whenNotSingleLine" | "always" | "preferNone";
export type UseBraces1 = string;
export type BracePosition = "maintain" | "sameLine" | "nextLine" | "sameLineUnlessHanging";
export type BracePosition1 = string;
export type SingleBodyPosition = "maintain" | "sameLine" | "nextLine";
export type SingleBodyPosition1 = string;
export type NextControlFlowPosition = "maintain" | "sameLine" | "nextLine";
export type NextControlFlowPosition1 = string;
export type TrailingCommas = "never" | "always" | "onlyMultiLine";
export type TrailingCommas1 = string;
export type OperatorPosition = "maintain" | "sameLine" | "nextLine";
export type OperatorPosition1 = string;
export type PreferHanging = boolean;
export type PreferHanging1 = true | false;
export type PreferSingleLine = boolean;
export type PreferSingleLine1 = true | false;
export type Deno = boolean;
export type Deno1 = true | false;
export type ArrowFunctionUseParentheses = "force" | "maintain" | "preferNone";
export type ArrowFunctionUseParentheses1 = string;
export type BinaryExpressionLinePerExpression = boolean;
export type BinaryExpressionLinePerExpression1 = true | false;
export type JsxQuoteStyle = "preferDouble" | "preferSingle";
export type JsxQuoteStyle1 = string;
export type JsxMultiLineParens = "never" | "prefer" | "always";
export type JsxMultiLineParens1 = string;
export type MemberExpressionLinePerExpression = boolean;
export type MemberExpressionLinePerExpression1 = true | false;
export type TypeLiteralSeparatorKind = "semiColon" | "comma";
export type TypeLiteralSeparatorKind1 = string;
export type EnumDeclarationMemberSpacing = "newLine" | "blankLine" | "maintain";
export type EnumDeclarationMemberSpacing1 = string;
export type SpaceAround = boolean;
export type SpaceAround1 = true | false;
export type SpaceSurroundingProperties = boolean;
export type SpaceSurroundingProperties1 = true | false;
export type ObjectExpressionSpaceSurroundingProperties = boolean;
export type ObjectExpressionSpaceSurroundingProperties1 = true | false;
export type ObjectPatternSpaceSurroundingProperties = boolean;
export type ObjectPatternSpaceSurroundingProperties1 = true | false;
export type TypeLiteralSpaceSurroundingProperties = boolean;
export type TypeLiteralSpaceSurroundingProperties1 = true | false;
export type BinaryExpressionSpaceSurroundingBitwiseAndArithmeticOperator = boolean;
export type BinaryExpressionSpaceSurroundingBitwiseAndArithmeticOperator1 = true | false;
export type CommentLineForceSpaceAfterSlashes = boolean;
export type CommentLineForceSpaceAfterSlashes1 = true | false;
export type ConstructorSpaceBeforeParentheses = boolean;
export type ConstructorSpaceBeforeParentheses1 = true | false;
export type ConstructorTypeSpaceAfterNewKeyword = boolean;
export type ConstructorTypeSpaceAfterNewKeyword1 = true | false;
export type ConstructSignatureSpaceAfterNewKeyword = boolean;
export type ConstructSignatureSpaceAfterNewKeyword1 = true | false;
export type DoWhileStatementSpaceAfterWhileKeyword = boolean;
export type DoWhileStatementSpaceAfterWhileKeyword1 = true | false;
export type ExportDeclarationSpaceSurroundingNamedExports = boolean;
export type ExportDeclarationSpaceSurroundingNamedExports1 = true | false;
export type ForInStatementSpaceAfterForKeyword = boolean;
export type ForInStatementSpaceAfterForKeyword1 = true | false;
export type ForOfStatementSpaceAfterForKeyword = boolean;
export type ForOfStatementSpaceAfterForKeyword1 = true | false;
export type ForStatementSpaceAfterForKeyword = boolean;
export type ForStatementSpaceAfterForKeyword1 = true | false;
export type ForStatementSpaceAfterSemiColons = boolean;
export type ForStatementSpaceAfterSemiColons1 = true | false;
export type FunctionDeclarationSpaceBeforeParentheses = boolean;
export type FunctionDeclarationSpaceBeforeParentheses1 = true | false;
export type FunctionExpressionSpaceBeforeParentheses = boolean;
export type FunctionExpressionSpaceBeforeParentheses1 = true | false;
export type FunctionExpressionSpaceAfterFunctionKeyword = boolean;
export type FunctionExpressionSpaceAfterFunctionKeyword1 = true | false;
export type GetAccessorSpaceBeforeParentheses = boolean;
export type GetAccessorSpaceBeforeParentheses1 = true | false;
export type IfStatementSpaceAfterIfKeyword = boolean;
export type IfStatementSpaceAfterIfKeyword1 = true | false;
export type ImportDeclarationSpaceSurroundingNamedImports = boolean;
export type ImportDeclarationSpaceSurroundingNamedImports1 = true | false;
export type JsxElementSpaceBeforeSelfClosingTagSlash = boolean;
export type JsxElementSpaceBeforeSelfClosingTagSlash1 = true | false;
export type JsxExpressionContainerSpaceSurroundingExpression = boolean;
export type JsxExpressionContainerSpaceSurroundingExpression1 = true | false;
export type MethodSpaceBeforeParentheses = boolean;
export type MethodSpaceBeforeParentheses1 = true | false;
export type SetAccessorSpaceBeforeParentheses = boolean;
export type SetAccessorSpaceBeforeParentheses1 = true | false;
export type TaggedTemplateSpaceBeforeLiteral = boolean;
export type TaggedTemplateSpaceBeforeLiteral1 = true | false;
export type TypeAnnotationSpaceBeforeColon = boolean;
export type TypeAnnotationSpaceBeforeColon1 = true | false;
export type TypeAssertionSpaceBeforeExpression = boolean;
export type TypeAssertionSpaceBeforeExpression1 = true | false;
export type WhileStatementSpaceAfterWhileKeyword = boolean;
export type WhileStatementSpaceAfterWhileKeyword1 = true | false;
export type SortOrder = "maintain" | "caseSensitive" | "caseInsensitive";
export type SortOrder1 = string;
export type ForceSingleLine = boolean;
export type ForceSingleLine1 = true | false;

export interface Options {
  /**
   * Whether the configuration is not allowed to be overridden or extended.
   */
  locked?: boolean;
  /**
   * The width of a line the printer will try to stay under. Note that the printer may exceed this width in certain cases.
   */
  lineWidth?: number;
  /**
   * The number of columns for an indent.
   */
  indentWidth?: number;
  /**
   * Whether to use tabs (true) or spaces (false).
   */
  useTabs?: UseTabs & UseTabs1;
  /**
   * How semi-colons should be used.
   */
  semiColons?: SemiColons & SemiColons1;
  /**
   * How to use single or double quotes.
   */
  quoteStyle?: QuoteStyle & QuoteStyle1;
  /**
   * Change when properties in objects are quoted.
   */
  quoteProps?: QuoteProps & QuoteProps1;
  /**
   * The kind of newline to use.
   */
  newLineKind?: NewLineKind & NewLineKind1;
  /**
   * If braces should be used or not.
   */
  useBraces?: UseBraces & UseBraces1;
  /**
   * Where to place the opening brace.
   */
  bracePosition?: BracePosition & BracePosition1;
  /**
   * Where to place the expression of a statement that could possibly be on one line (ex. `if (true) console.log(5);`).
   */
  singleBodyPosition?: SingleBodyPosition & SingleBodyPosition1;
  /**
   * Where to place the next control flow within a control flow statement.
   */
  nextControlFlowPosition?: NextControlFlowPosition & NextControlFlowPosition1;
  /**
   * If trailing commas should be used.
   */
  trailingCommas?: TrailingCommas & TrailingCommas1;
  /**
   * Where to place the operator for expressions that span multiple lines.
   */
  operatorPosition?: OperatorPosition & OperatorPosition1;
  /**
   * Set to prefer hanging indentation when exceeding the line width instead of making code split up on multiple lines.
   */
  preferHanging?: PreferHanging & PreferHanging1;
  /**
   * If code should revert back from being on multiple lines to being on a single line when able.
   */
  preferSingleLine?: PreferSingleLine & PreferSingleLine1;
  /**
   * Top level configuration that sets the configuration to what is used in Deno.
   */
  deno?: Deno & Deno1;
  /**
   * Whether to use parentheses around a single parameter in an arrow function.
   */
  "arrowFunction.useParentheses"?: ArrowFunctionUseParentheses & ArrowFunctionUseParentheses1;
  /**
   * Whether to force a line per expression when spanning multiple lines.
   */
  "binaryExpression.linePerExpression"?: BinaryExpressionLinePerExpression & BinaryExpressionLinePerExpression1;
  /**
   * How to use single or double quotes in JSX attributes.
   */
  "jsx.quoteStyle"?: JsxQuoteStyle & JsxQuoteStyle1;
  /**
   * Surrounds the top-most JSX element or fragment in parentheses when it spans multiple lines.
   */
  "jsx.multiLineParens"?: JsxMultiLineParens & JsxMultiLineParens1;
  /**
   * Whether to force a line per expression when spanning multiple lines.
   */
  "memberExpression.linePerExpression"?: MemberExpressionLinePerExpression & MemberExpressionLinePerExpression1;
  /**
   * The kind of separator to use in type literals.
   */
  "typeLiteral.separatorKind"?: TypeLiteralSeparatorKind & TypeLiteralSeparatorKind1;
  "typeLiteral.separatorKind.singleLine"?: TypeLiteralSeparatorKind & TypeLiteralSeparatorKind1;
  "typeLiteral.separatorKind.multiLine"?: TypeLiteralSeparatorKind & TypeLiteralSeparatorKind1;
  /**
   * How to space the members of an enum.
   */
  "enumDeclaration.memberSpacing"?: EnumDeclarationMemberSpacing & EnumDeclarationMemberSpacing1;
  /**
   * Whether to place spaces around enclosed expressions.
   */
  spaceAround?: SpaceAround & SpaceAround1;
  "arguments.spaceAround"?: SpaceAround & SpaceAround1;
  "arrayExpression.spaceAround"?: SpaceAround & SpaceAround1;
  "arrayPattern.spaceAround"?: SpaceAround & SpaceAround1;
  "doWhileStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "forInStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "forOfStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "forStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "ifStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "parameters.spaceAround"?: SpaceAround & SpaceAround1;
  "switchStatement.spaceAround"?: SpaceAround & SpaceAround1;
  "tupleType.spaceAround"?: SpaceAround & SpaceAround1;
  "whileStatement.spaceAround"?: SpaceAround & SpaceAround1;
  /**
   * Whether to add a space surrounding the properties of single line object-like nodes.
   */
  spaceSurroundingProperties?: SpaceSurroundingProperties & SpaceSurroundingProperties1;
  /**
   * Whether to add a space surrounding the properties of a single line object expression.
   */
  "objectExpression.spaceSurroundingProperties"?: ObjectExpressionSpaceSurroundingProperties &
    ObjectExpressionSpaceSurroundingProperties1;
  /**
   * Whether to add a space surrounding the properties of a single line object pattern.
   */
  "objectPattern.spaceSurroundingProperties"?: ObjectPatternSpaceSurroundingProperties &
    ObjectPatternSpaceSurroundingProperties1;
  /**
   * Whether to add a space surrounding the properties of a single line type literal.
   */
  "typeLiteral.spaceSurroundingProperties"?: TypeLiteralSpaceSurroundingProperties &
    TypeLiteralSpaceSurroundingProperties1;
  /**
   * Whether to surround the operator in a binary expression with spaces.
   */
  "binaryExpression.spaceSurroundingBitwiseAndArithmeticOperator"?: BinaryExpressionSpaceSurroundingBitwiseAndArithmeticOperator &
    BinaryExpressionSpaceSurroundingBitwiseAndArithmeticOperator1;
  /**
   * Forces a space after the double slash in a comment line.
   */
  "commentLine.forceSpaceAfterSlashes"?: CommentLineForceSpaceAfterSlashes & CommentLineForceSpaceAfterSlashes1;
  /**
   * Whether to add a space before the parentheses of a constructor.
   */
  "constructor.spaceBeforeParentheses"?: ConstructorSpaceBeforeParentheses & ConstructorSpaceBeforeParentheses1;
  /**
   * Whether to add a space after the `new` keyword in a constructor type.
   */
  "constructorType.spaceAfterNewKeyword"?: ConstructorTypeSpaceAfterNewKeyword & ConstructorTypeSpaceAfterNewKeyword1;
  /**
   * Whether to add a space after the `new` keyword in a construct signature.
   */
  "constructSignature.spaceAfterNewKeyword"?: ConstructSignatureSpaceAfterNewKeyword &
    ConstructSignatureSpaceAfterNewKeyword1;
  /**
   * Whether to add a space after the `while` keyword in a do while statement.
   */
  "doWhileStatement.spaceAfterWhileKeyword"?: DoWhileStatementSpaceAfterWhileKeyword &
    DoWhileStatementSpaceAfterWhileKeyword1;
  /**
   * Whether to add spaces around named exports in an export declaration.
   */
  "exportDeclaration.spaceSurroundingNamedExports"?: ExportDeclarationSpaceSurroundingNamedExports &
    ExportDeclarationSpaceSurroundingNamedExports1;
  /**
   * Whether to add a space after the `for` keyword in a "for in" statement.
   */
  "forInStatement.spaceAfterForKeyword"?: ForInStatementSpaceAfterForKeyword & ForInStatementSpaceAfterForKeyword1;
  /**
   * Whether to add a space after the `for` keyword in a "for of" statement.
   */
  "forOfStatement.spaceAfterForKeyword"?: ForOfStatementSpaceAfterForKeyword & ForOfStatementSpaceAfterForKeyword1;
  /**
   * Whether to add a space after the `for` keyword in a "for" statement.
   */
  "forStatement.spaceAfterForKeyword"?: ForStatementSpaceAfterForKeyword & ForStatementSpaceAfterForKeyword1;
  /**
   * Whether to add a space after the semi-colons in a "for" statement.
   */
  "forStatement.spaceAfterSemiColons"?: ForStatementSpaceAfterSemiColons & ForStatementSpaceAfterSemiColons1;
  /**
   * Whether to add a space before the parentheses of a function declaration.
   */
  "functionDeclaration.spaceBeforeParentheses"?: FunctionDeclarationSpaceBeforeParentheses &
    FunctionDeclarationSpaceBeforeParentheses1;
  /**
   * Whether to add a space before the parentheses of a function expression.
   */
  "functionExpression.spaceBeforeParentheses"?: FunctionExpressionSpaceBeforeParentheses &
    FunctionExpressionSpaceBeforeParentheses1;
  /**
   * Whether to add a space after the function keyword of a function expression.
   */
  "functionExpression.spaceAfterFunctionKeyword"?: FunctionExpressionSpaceAfterFunctionKeyword &
    FunctionExpressionSpaceAfterFunctionKeyword1;
  /**
   * Whether to add a space before the parentheses of a get accessor.
   */
  "getAccessor.spaceBeforeParentheses"?: GetAccessorSpaceBeforeParentheses & GetAccessorSpaceBeforeParentheses1;
  /**
   * Whether to add a space after the `if` keyword in an "if" statement.
   */
  "ifStatement.spaceAfterIfKeyword"?: IfStatementSpaceAfterIfKeyword & IfStatementSpaceAfterIfKeyword1;
  /**
   * Whether to add spaces around named imports in an import declaration.
   */
  "importDeclaration.spaceSurroundingNamedImports"?: ImportDeclarationSpaceSurroundingNamedImports &
    ImportDeclarationSpaceSurroundingNamedImports1;
  /**
   * Whether to add a space before a JSX element's slash when self closing.
   */
  "jsxElement.spaceBeforeSelfClosingTagSlash"?: JsxElementSpaceBeforeSelfClosingTagSlash &
    JsxElementSpaceBeforeSelfClosingTagSlash1;
  /**
   * Whether to add a space surrounding the expression of a JSX container.
   */
  "jsxExpressionContainer.spaceSurroundingExpression"?: JsxExpressionContainerSpaceSurroundingExpression &
    JsxExpressionContainerSpaceSurroundingExpression1;
  /**
   * Whether to add a space before the parentheses of a method.
   */
  "method.spaceBeforeParentheses"?: MethodSpaceBeforeParentheses & MethodSpaceBeforeParentheses1;
  /**
   * Whether to add a space before the parentheses of a set accessor.
   */
  "setAccessor.spaceBeforeParentheses"?: SetAccessorSpaceBeforeParentheses & SetAccessorSpaceBeforeParentheses1;
  /**
   * Whether to add a space before the literal in a tagged template.
   */
  "taggedTemplate.spaceBeforeLiteral"?: TaggedTemplateSpaceBeforeLiteral & TaggedTemplateSpaceBeforeLiteral1;
  /**
   * Whether to add a space before the colon of a type annotation.
   */
  "typeAnnotation.spaceBeforeColon"?: TypeAnnotationSpaceBeforeColon & TypeAnnotationSpaceBeforeColon1;
  /**
   * Whether to add a space before the expression in a type assertion.
   */
  "typeAssertion.spaceBeforeExpression"?: TypeAssertionSpaceBeforeExpression & TypeAssertionSpaceBeforeExpression1;
  /**
   * Whether to add a space after the `while` keyword in a while statement.
   */
  "whileStatement.spaceAfterWhileKeyword"?: WhileStatementSpaceAfterWhileKeyword &
    WhileStatementSpaceAfterWhileKeyword1;
  /**
   * The kind of sort ordering to use.
   */
  "module.sortImportDeclarations"?: SortOrder & SortOrder1;
  "module.sortExportDeclarations"?: SortOrder & SortOrder1;
  "exportDeclaration.sortNamedExports"?: SortOrder & SortOrder1;
  "importDeclaration.sortNamedImports"?: SortOrder & SortOrder1;
  /**
   * The text to use for an ignore comment (ex. `// dprint-ignore`).
   */
  ignoreNodeCommentText?: string;
  /**
   * The text to use for a file ignore comment (ex. `// dprint-ignore-file`).
   */
  ignoreFileCommentText?: string;
  "forInStatement.useBraces"?: UseBraces & UseBraces1;
  "forOfStatement.useBraces"?: UseBraces & UseBraces1;
  "forStatement.useBraces"?: UseBraces & UseBraces1;
  "ifStatement.useBraces"?: UseBraces & UseBraces1;
  "whileStatement.useBraces"?: UseBraces & UseBraces1;
  "arrowFunction.bracePosition"?: BracePosition & BracePosition1;
  "classDeclaration.bracePosition"?: BracePosition & BracePosition1;
  "classExpression.bracePosition"?: BracePosition & BracePosition1;
  "constructor.bracePosition"?: BracePosition & BracePosition1;
  "doWhileStatement.bracePosition"?: BracePosition & BracePosition1;
  "enumDeclaration.bracePosition"?: BracePosition & BracePosition1;
  "forInStatement.bracePosition"?: BracePosition & BracePosition1;
  "forOfStatement.bracePosition"?: BracePosition & BracePosition1;
  "forStatement.bracePosition"?: BracePosition & BracePosition1;
  "functionDeclaration.bracePosition"?: BracePosition & BracePosition1;
  "functionExpression.bracePosition"?: BracePosition & BracePosition1;
  "getAccessor.bracePosition"?: BracePosition & BracePosition1;
  "ifStatement.bracePosition"?: BracePosition & BracePosition1;
  "interfaceDeclaration.bracePosition"?: BracePosition & BracePosition1;
  "moduleDeclaration.bracePosition"?: BracePosition & BracePosition1;
  "method.bracePosition"?: BracePosition & BracePosition1;
  "setAccessor.bracePosition"?: BracePosition & BracePosition1;
  "staticBlock.bracePosition"?: BracePosition & BracePosition1;
  "switchStatement.bracePosition"?: BracePosition & BracePosition1;
  "switchCase.bracePosition"?: BracePosition & BracePosition1;
  "tryStatement.bracePosition"?: BracePosition & BracePosition1;
  "whileStatement.bracePosition"?: BracePosition & BracePosition1;
  "forInStatement.singleBodyPosition"?: SingleBodyPosition & SingleBodyPosition1;
  "forOfStatement.singleBodyPosition"?: SingleBodyPosition & SingleBodyPosition1;
  "forStatement.singleBodyPosition"?: SingleBodyPosition & SingleBodyPosition1;
  "ifStatement.singleBodyPosition"?: SingleBodyPosition & SingleBodyPosition1;
  "whileStatement.singleBodyPosition"?: SingleBodyPosition & SingleBodyPosition1;
  "ifStatement.nextControlFlowPosition"?: NextControlFlowPosition & NextControlFlowPosition1;
  "tryStatement.nextControlFlowPosition"?: NextControlFlowPosition & NextControlFlowPosition1;
  "arguments.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "parameters.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "arrayExpression.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "arrayPattern.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "enumDeclaration.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "exportDeclaration.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "importDeclaration.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "objectExpression.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "objectPattern.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "tupleType.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "typeLiteral.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "typeParameters.trailingCommas"?: TrailingCommas & TrailingCommas1;
  "binaryExpression.operatorPosition"?: OperatorPosition & OperatorPosition1;
  "conditionalExpression.operatorPosition"?: OperatorPosition & OperatorPosition1;
  "arguments.preferHanging"?: PreferHanging & PreferHanging1;
  "arrayExpression.preferHanging"?: PreferHanging & PreferHanging1;
  "arrayPattern.preferHanging"?: PreferHanging & PreferHanging1;
  "doWhileStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "exportDeclaration.preferHanging"?: PreferHanging & PreferHanging1;
  "extendsClause.preferHanging"?: PreferHanging & PreferHanging1;
  "forInStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "forOfStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "forStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "ifStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "implementsClause.preferHanging"?: PreferHanging & PreferHanging1;
  "importDeclaration.preferHanging"?: PreferHanging & PreferHanging1;
  "jsxAttributes.preferHanging"?: PreferHanging & PreferHanging1;
  "objectExpression.preferHanging"?: PreferHanging & PreferHanging1;
  "objectPattern.preferHanging"?: PreferHanging & PreferHanging1;
  "parameters.preferHanging"?: PreferHanging & PreferHanging1;
  "sequenceExpression.preferHanging"?: PreferHanging & PreferHanging1;
  "switchStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "tupleType.preferHanging"?: PreferHanging & PreferHanging1;
  "typeLiteral.preferHanging"?: PreferHanging & PreferHanging1;
  "typeParameters.preferHanging"?: PreferHanging & PreferHanging1;
  "unionAndIntersectionType.preferHanging"?: PreferHanging & PreferHanging1;
  "variableStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "whileStatement.preferHanging"?: PreferHanging & PreferHanging1;
  "arrayExpression.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "arrayPattern.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "arguments.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "binaryExpression.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "computed.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "conditionalExpression.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "conditionalType.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "decorators.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "exportDeclaration.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "forStatement.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "importDeclaration.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "jsxAttributes.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "jsxElement.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "mappedType.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "memberExpression.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "objectExpression.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "objectPattern.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "parameters.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "parentheses.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "tupleType.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "typeLiteral.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "typeParameters.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "unionAndIntersectionType.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  "variableStatement.preferSingleLine"?: PreferSingleLine & PreferSingleLine1;
  /**
   * If code should be forced to be on a single line if able.
   */
  "exportDeclaration.forceSingleLine"?: ForceSingleLine & ForceSingleLine1;
  "importDeclaration.forceSingleLine"?: ForceSingleLine & ForceSingleLine1;
  [k: string]: unknown;
}
