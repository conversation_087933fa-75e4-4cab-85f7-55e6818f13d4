import * as vscode from 'vscode';
import { DirectFileGenerationService, DualViewResult } from './DirectFileGenerationService';
import { ExecutionStateManager } from './ExecutionStateManager';
import { LLMResponseParser, ParsedResponse } from './LLMResponseParser';
import { ExecutionPlan, ExecutionStep } from '../interfaces/ILLMService';
import { ExecutionPlanContextService } from './ExecutionPlanContextService';
import { ContextEngineService } from './ContextEngineService';

export interface WorkflowRequest {
    userPrompt: string;
    targetFiles?: string[];
    workspaceContext?: any;
}

export interface WorkflowResult {
    success: boolean;
    textResponse: string;
    dualViewResults: DualViewResult[];
    executionPlan?: ExecutionPlan;
    error?: string;
    metadata?: {
        filesProcessed: number;
        filesCreated: number;
        filesModified: number;
        totalChanges: number;
    };
}

export interface DualViewOperation {
    type: 'create' | 'modify' | 'create_directory';
    path: string;
    content: string;
    language?: string;
    priority: number;
}

/**
 * TASK 3: Unified Workflow Service
 * Orchestrates the complete end-to-end workflow for dual-view file operations
 * Coordinates between AI response parsing, file generation, diff preview, and execution state management
 */
export class UnifiedWorkflowService {
    private _directFileService: DirectFileGenerationService;
    private _executionStateManager: ExecutionStateManager;
    private _executionPlanContextService?: ExecutionPlanContextService;
    private _outputChannel: vscode.OutputChannel;

    constructor(
        directFileService: DirectFileGenerationService,
        executionStateManager: ExecutionStateManager,
        contextEngine?: ContextEngineService
    ) {
        this._directFileService = directFileService;
        this._executionStateManager = executionStateManager;
        this._outputChannel = vscode.window.createOutputChannel('V1b3-Sama Unified Workflow');

        // Initialize execution plan context service if context engine is provided
        if (contextEngine) {
            this._executionPlanContextService = new ExecutionPlanContextService({
                contextEngine,
                executionStateManager
            });
        }
    }

    /**
     * Process AI response and execute unified dual-view workflow
     * This is the main entry point for Task 3 functionality
     */
    public async processAIResponseWithDualView(
        aiResponse: string,
        userPrompt: string
    ): Promise<WorkflowResult> {
        try {
            this._outputChannel.appendLine(`🚀 Starting unified workflow for: "${userPrompt}"`);

            // 1. Parse AI response to extract file operations and execution plans
            const parsedResponse = LLMResponseParser.parseResponse(aiResponse);

            // 2. If execution plan exists, use plan-based workflow
            if (parsedResponse.hasExecutionPlan && parsedResponse.executionPlan) {
                return await this._processExecutionPlanWorkflow(parsedResponse, userPrompt);
            }

            // 3. If file operations exist, use direct dual-view workflow
            if (parsedResponse.hasFileOperations) {
                return await this._processDirectDualViewWorkflow(parsedResponse, userPrompt);
            }

            // 4. No file operations - return text-only response
            return {
                success: true,
                textResponse: parsedResponse.textContent,
                dualViewResults: [],
                metadata: {
                    filesProcessed: 0,
                    filesCreated: 0,
                    filesModified: 0,
                    totalChanges: 0
                }
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Unified workflow failed: ${errorMsg}`);
            
            return {
                success: false,
                textResponse: aiResponse,
                dualViewResults: [],
                error: errorMsg
            };
        }
    }

    /**
     * Process execution plan workflow (complex multi-step operations)
     */
    private async _processExecutionPlanWorkflow(
        parsedResponse: ParsedResponse,
        userPrompt: string
    ): Promise<WorkflowResult> {
        if (!parsedResponse.executionPlan) {
            throw new Error('Execution plan is missing');
        }

        this._outputChannel.appendLine(`📋 Processing execution plan: ${parsedResponse.executionPlan.description}`);

        // Add execution plan to context engine if available
        if (this._executionPlanContextService) {
            try {
                await this._executionPlanContextService.contextEngine.addExecutionPlan(
                    parsedResponse.executionPlan,
                    userPrompt
                );
                this._outputChannel.appendLine(`📊 Execution plan added to context engine`);
            } catch (error) {
                console.error('Failed to add execution plan to context engine:', error);
            }
        }

        // Reset execution state if needed before setting new plan
        const currentState = this._executionStateManager.state;
        if (currentState !== 'idle') {
            console.log(`UnifiedWorkflowService: Resetting execution state from ${currentState} to idle for new plan`);
            this._executionStateManager.stopPlan();
        }

        // Set the execution plan in state manager (triggers awaitingApproval state)
        this._executionStateManager.setPlan(parsedResponse.executionPlan);

        // Convert execution steps to dual-view operations for preview
        const dualViewResults: DualViewResult[] = [];
        const fileSteps = parsedResponse.executionPlan.steps.filter(step => 
            step.type === 'file_create' || step.type === 'file_modify'
        );

        // Generate dual-view previews for all file operations
        for (const step of fileSteps) {
            if (step.path && step.content) {
                const dualViewResult = await this._directFileService.generateWithDualView(
                    step.path,
                    step.content,
                    this._inferLanguageFromPath(step.path)
                );
                dualViewResults.push(dualViewResult);
            }
        }

        const metadata = this._calculateMetadata(dualViewResults);
        this._outputChannel.appendLine(`📊 Plan preview generated: ${metadata?.filesProcessed || 0} files, ${metadata?.totalChanges || 0} changes`);

        return {
            success: true,
            textResponse: parsedResponse.textContent,
            dualViewResults,
            executionPlan: parsedResponse.executionPlan,
            metadata
        };
    }

    /**
     * Process direct dual-view workflow (simple file operations)
     */
    private async _processDirectDualViewWorkflow(
        parsedResponse: ParsedResponse,
        userPrompt: string
    ): Promise<WorkflowResult> {
        this._outputChannel.appendLine(`📁 Processing ${parsedResponse.fileOperations.length} direct file operations`);

        const dualViewResults: DualViewResult[] = [];

        // Process each file operation with dual-view
        for (const operation of parsedResponse.fileOperations) {
            try {
                const dualViewResult = await this._directFileService.generateWithDualView(
                    operation.path,
                    operation.content,
                    operation.language
                );

                dualViewResults.push(dualViewResult);

                if (dualViewResult.success) {
                    this._outputChannel.appendLine(`✅ Dual-view generated for: ${operation.path}`);
                } else {
                    this._outputChannel.appendLine(`❌ Failed dual-view for: ${operation.path} - ${dualViewResult.error}`);
                }

            } catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Unknown error';
                this._outputChannel.appendLine(`❌ Error processing ${operation.path}: ${errorMsg}`);
                
                // Add failed result
                dualViewResults.push({
                    success: false,
                    filePath: operation.path,
                    error: errorMsg,
                    diffResult: null,
                    editorResult: undefined
                });
            }
        }

        const metadata = this._calculateMetadata(dualViewResults);
        this._outputChannel.appendLine(`📊 Direct workflow completed: ${metadata?.filesProcessed || 0} files processed`);

        return {
            success: true,
            textResponse: parsedResponse.textContent,
            dualViewResults,
            metadata
        };
    }

    /**
     * Calculate workflow metadata from dual-view results
     */
    private _calculateMetadata(dualViewResults: DualViewResult[]): WorkflowResult['metadata'] {
        let filesCreated = 0;
        let filesModified = 0;
        let totalChanges = 0;

        for (const result of dualViewResults) {
            if (result.success) {
                if (result.editorResult?.isNewFile) {
                    filesCreated++;
                } else {
                    filesModified++;
                }

                if (result.diffResult) {
                    totalChanges += (result.diffResult.additions || 0) + (result.diffResult.deletions || 0);
                }
            }
        }

        return {
            filesProcessed: dualViewResults.length,
            filesCreated,
            filesModified,
            totalChanges
        };
    }

    /**
     * Infer programming language from file path
     */
    private _inferLanguageFromPath(filePath: string): string {
        const extension = filePath.split('.').pop()?.toLowerCase();
        
        const languageMap: { [key: string]: string } = {
            'js': 'javascript', 'jsx': 'jsx', 'ts': 'typescript', 'tsx': 'tsx',
            'py': 'python', 'java': 'java', 'cpp': 'cpp', 'c': 'c', 'h': 'c',
            'cs': 'csharp', 'php': 'php', 'rb': 'ruby', 'go': 'go', 'rs': 'rust',
            'html': 'html', 'css': 'css', 'scss': 'scss', 'json': 'json',
            'yaml': 'yaml', 'yml': 'yaml', 'xml': 'xml', 'md': 'markdown'
        };

        return languageMap[extension || ''] || 'text';
    }

    /**
     * Get execution plan recommendations based on current context
     */
    public async getExecutionPlanRecommendations(userPrompt: string): Promise<{
        suggestedFiles: string[];
        potentialConflicts: string[];
        relatedPlans: any[];
        complexity: 'low' | 'medium' | 'high';
    }> {
        if (this._executionPlanContextService) {
            return this._executionPlanContextService.getExecutionPlanRecommendations(userPrompt);
        }

        return {
            suggestedFiles: [],
            potentialConflicts: [],
            relatedPlans: [],
            complexity: 'low'
        };
    }

    /**
     * Get contextual execution plans
     */
    public async getContextualExecutionPlans() {
        if (this._executionPlanContextService) {
            return this._executionPlanContextService.getContextualExecutionPlans();
        }
        return [];
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._outputChannel.dispose();
        if (this._executionPlanContextService) {
            this._executionPlanContextService.dispose();
        }
    }
}
