{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,UAAU,MAAM,yCAAyC,CAAC;AAEtE,OAAO,EAGL,qBAAqB,GACtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAEL,sBAAsB,GACvB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAqB,KAAK,EAAE,MAAM,YAAY,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAEvF;;;;;;;;GAQG;AACH,MAAM,UAAU,mBAAmB,CACjC,YAAoB,EACpB,MAGC;IAID,OAAO,UAAU,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAe,EAAE,QAA0B;IAC3E,OAAO,UAAU,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzD,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,iBAAiB,CAC/B,GAAW,EACX,YAAoB,EACpB,QAA0B;IAE1B,OAAO,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AACnE,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,yBAAyB,CAAC,GAAW,EAAE,GAAW;IAChE,OAAO,UAAU,CAAC,yBAAyB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,CAAU;IAChC,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,UAAU,CAAC,UAAU,EAAE,CAAC;AACjC,CAAC;AAwBD;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAY,UAAU,CAAC,SAAS,CAAC;AACvD;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAY,UAAU,CAAC,KAAK,CAAC;AAC/C;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAY,UAAU,CAAC,MAAM,CAAC;AACjD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,MAAM,GAAY,UAAU,CAAC,UAAU,CAAC;AACrD;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAY,UAAU,CAAC,UAAU,CAAC;AACzD;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAY,UAAU,CAAC,aAAa,CAAC;AAC/D;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAY,UAAU,CAAC,aAAa,CAAC;AAC/D;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAY,UAAU,CAAC,WAAW,CAAC;AAK3D;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAiB,EAAE,MAAoB;IACxE,OAAO,UAAU,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAa,EAAE,MAAoB;IACpE,OAAO,UAAU,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as tspRuntime from \"@typespec/ts-http-runtime/internal/util\";\n\nexport {\n  type AbortOptions,\n  type AbortablePromiseBuilder,\n  cancelablePromiseRace,\n} from \"./aborterUtils.js\";\nexport {\n  type CreateAbortablePromiseOptions,\n  createAbortablePromise,\n} from \"./createAbortablePromise.js\";\nexport { type DelayOptions, delay } from \"./delay.js\";\nexport { getErrorMessage } from \"./error.js\";\nexport { isDefined, isObjectWithProperties, objectHasProperty } from \"./typeGuards.js\";\n\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n *\n * @param retryAttempt - The current retry attempt number.\n *\n * @param config - The exponential retry configuration.\n *\n * @returns An object containing the calculated retry delay.\n */\nexport function calculateRetryDelay(\n  retryAttempt: number,\n  config: {\n    retryDelayInMs: number;\n    maxRetryDelayInMs: number;\n  },\n): {\n  retryAfterInMs: number;\n} {\n  return tspRuntime.calculateRetryDelay(retryAttempt, config);\n}\n\n/**\n * Generates a SHA-256 hash.\n *\n * @param content - The data to be included in the hash.\n *\n * @param encoding - The textual encoding to use for the returned hash.\n */\nexport function computeSha256Hash(content: string, encoding: \"base64\" | \"hex\"): Promise<string> {\n  return tspRuntime.computeSha256Hash(content, encoding);\n}\n\n/**\n * Generates a SHA-256 HMAC signature.\n *\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n *\n * @param stringToSign - The data to be signed.\n *\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nexport function computeSha256Hmac(\n  key: string,\n  stringToSign: string,\n  encoding: \"base64\" | \"hex\",\n): Promise<string> {\n  return tspRuntime.computeSha256Hmac(key, stringToSign, encoding);\n}\n\n/**\n * Returns a random integer value between a lower and upper bound, inclusive of both bounds. Note that this uses Math.random and isn't secure. If you need to use this for any kind of security purpose, find a better source of random.\n *\n * @param min - The smallest integer value allowed.\n *\n * @param max - The largest integer value allowed.\n */\nexport function getRandomIntegerInclusive(min: number, max: number): number {\n  return tspRuntime.getRandomIntegerInclusive(min, max);\n}\n\n/**\n * Typeguard for an error object shape (has name and message)\n *\n * @param e - Something caught by a catch clause.\n */\nexport function isError(e: unknown): e is Error {\n  return tspRuntime.isError(e);\n}\n\n/**\n * Helper to determine when an input is a generic JS object.\n *\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nexport function isObject(input: unknown): input is UnknownObject {\n  return tspRuntime.isObject(input);\n}\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return tspRuntime.randomUUID();\n}\n\n/**\n * Supported HTTP methods to use when making requests.\n *\n * @public\n */\nexport type HttpMethods =\n  | \"GET\"\n  | \"PUT\"\n  | \"POST\"\n  | \"DELETE\"\n  | \"PATCH\"\n  | \"HEAD\"\n  | \"OPTIONS\"\n  | \"TRACE\";\n\n/**\n * A generic shape for a plain JS object.\n */\nexport type UnknownObject = {\n  [s: string]: unknown;\n};\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Browser.\n */\nexport const isBrowser: boolean = tspRuntime.isBrowser;\n/**\n * A constant that indicates whether the environment the code is running is Bun.sh.\n */\nexport const isBun: boolean = tspRuntime.isBun;\n/**\n * A constant that indicates whether the environment the code is running is Deno.\n */\nexport const isDeno: boolean = tspRuntime.isDeno;\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n *\n * @deprecated\n *\n * Use `isNodeLike` instead.\n */\nexport const isNode: boolean = tspRuntime.isNodeLike;\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n */\nexport const isNodeLike: boolean = tspRuntime.isNodeLike;\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNodeRuntime: boolean = tspRuntime.isNodeRuntime;\n/**\n * A constant that indicates whether the environment the code is running is in React-Native.\n */\nexport const isReactNative: boolean = tspRuntime.isReactNative;\n/**\n * A constant that indicates whether the environment the code is running is a Web Worker.\n */\nexport const isWebWorker: boolean = tspRuntime.isWebWorker;\n\n/** The supported character encoding type */\nexport type EncodingType = \"utf-8\" | \"base64\" | \"base64url\" | \"hex\";\n\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nexport function uint8ArrayToString(bytes: Uint8Array, format: EncodingType): string {\n  return tspRuntime.uint8ArrayToString(bytes, format);\n}\n\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nexport function stringToUint8Array(value: string, format: EncodingType): Uint8Array {\n  return tspRuntime.stringToUint8Array(value, format);\n}\n"]}