/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
__webpack_require__.r(__webpack_exports__);
// Web Worker for heavy FileSystem operations
// Runs in background thread to keep main extension responsive
// Note: This worker runs in a web worker context and doesn't have access to Node.js APIs
class FileSystemWorker {
    constructor() {
        // Listen for messages from main thread
        if (typeof self !== 'undefined') {
            self.onmessage = this.handleMessage.bind(this);
        }
    }
    async handleMessage(event) {
        const { id, type, payload } = event.data;
        try {
            let result;
            switch (type) {
                case 'diff':
                    result = await this.generateDiff(payload);
                    break;
                case 'search':
                    result = await this.searchFiles(payload);
                    break;
                case 'index':
                    result = await this.indexDirectory(payload);
                    break;
                case 'analyze':
                    result = await this.analyzeFile(payload);
                    break;
                default:
                    throw new Error(`Unknown operation type: ${type}`);
            }
            this.postMessage({
                id,
                type: 'success',
                payload: result
            });
        }
        catch (error) {
            this.postMessage({
                id,
                type: 'error',
                payload: {
                    message: error instanceof Error ? error.message : 'Unknown error',
                    stack: error instanceof Error ? error.stack : undefined
                }
            });
        }
    }
    async generateDiff(request) {
        const { original, modified, filename = 'file' } = request;
        // Use a more efficient diff algorithm for large files
        const lines1 = original.split('\n');
        const lines2 = modified.split('\n');
        const diff = this.computeUnifiedDiff(lines1, lines2, filename);
        const { additions, deletions } = this.countDiffLines(diff);
        return {
            additions,
            deletions,
            diff,
            originalLines: lines1.length,
            modifiedLines: lines2.length
        };
    }
    async searchFiles(request) {
        // File search operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('File search operations must be handled by the main thread');
    }
    async indexDirectory(request) {
        // Directory indexing operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('Directory indexing operations must be handled by the main thread');
    }
    async analyzeFile(request) {
        // File analysis operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('File analysis operations must be handled by the main thread');
    }
    computeUnifiedDiff(lines1, lines2, filename) {
        // Simplified unified diff implementation
        // In production, you might want to use a more sophisticated algorithm
        const result = [];
        result.push(`--- a/${filename}`);
        result.push(`+++ b/${filename}`);
        let i = 0, j = 0;
        while (i < lines1.length || j < lines2.length) {
            if (i < lines1.length && j < lines2.length && lines1[i] === lines2[j]) {
                result.push(` ${lines1[i]}`);
                i++;
                j++;
            }
            else if (i < lines1.length && (j >= lines2.length || lines1[i] !== lines2[j])) {
                result.push(`-${lines1[i]}`);
                i++;
            }
            else {
                result.push(`+${lines2[j]}`);
                j++;
            }
        }
        return result.join('\n');
    }
    countDiffLines(diff) {
        const lines = diff.split('\n');
        let additions = 0;
        let deletions = 0;
        for (const line of lines) {
            if (line.startsWith('+') && !line.startsWith('+++')) {
                additions++;
            }
            else if (line.startsWith('-') && !line.startsWith('---')) {
                deletions++;
            }
        }
        return { additions, deletions };
    }
    analyzeComplexity(content, lines) {
        // Simple complexity analysis
        const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
        const cognitiveComplexity = this.calculateCognitiveComplexity(content);
        return {
            cyclomaticComplexity,
            cognitiveComplexity,
            linesOfCode: lines.length,
            nonEmptyLines: lines.filter(line => line.trim().length > 0).length,
            commentLines: lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*')).length
        };
    }
    analyzeDependencies(content, filePath) {
        const imports = [];
        const requires = [];
        const exports = [];
        // Extract imports/requires (simplified)
        const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
        const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
        const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            imports.push(match[1]);
        }
        while ((match = requireRegex.exec(content)) !== null) {
            requires.push(match[1]);
        }
        while ((match = exportRegex.exec(content)) !== null) {
            exports.push(match[1]);
        }
        // Get file extension without using path module
        const lastDot = filePath.lastIndexOf('.');
        const fileExtension = lastDot > 0 ? filePath.substring(lastDot) : '';
        return {
            imports,
            requires,
            exports,
            totalDependencies: imports.length + requires.length,
            fileExtension
        };
    }
    analyzeMetrics(content, lines) {
        return {
            fileSize: new TextEncoder().encode(content).length, // Use TextEncoder instead of Buffer
            lineCount: lines.length,
            characterCount: content.length,
            wordCount: content.split(/\s+/).length,
            averageLineLength: content.length / lines.length,
            maxLineLength: Math.max(...lines.map(line => line.length))
        };
    }
    calculateCyclomaticComplexity(content) {
        // Simplified cyclomatic complexity calculation
        const keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?'];
        let complexity = 1; // Base complexity
        for (const keyword of keywords) {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        }
        return complexity;
    }
    calculateCognitiveComplexity(content) {
        // Simplified cognitive complexity calculation
        let complexity = 0;
        let nestingLevel = 0;
        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            // Increase nesting for blocks
            if (trimmed.includes('{')) {
                nestingLevel++;
            }
            if (trimmed.includes('}')) {
                nestingLevel = Math.max(0, nestingLevel - 1);
            }
            // Add complexity for control structures
            if (/\b(if|while|for|switch)\b/.test(trimmed)) {
                complexity += 1 + nestingLevel;
            }
        }
        return complexity;
    }
    postMessage(response) {
        if (typeof self !== 'undefined') {
            self.postMessage(response);
        }
    }
}
// Initialize worker
new FileSystemWorker();


var __webpack_export_target__ = self;
for(var __webpack_i__ in __webpack_exports__) __webpack_export_target__[__webpack_i__] = __webpack_exports__[__webpack_i__];
if(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, "__esModule", { value: true });
/******/ })()
;