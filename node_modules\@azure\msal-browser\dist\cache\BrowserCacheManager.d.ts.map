{"version": 3, "file": "BrowserCacheManager.d.ts", "sourceRoot": "", "sources": ["../../src/cache/BrowserCacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,iBAAiB,EACjB,aAAa,EACb,WAAW,EAEX,iBAAiB,EAEjB,uBAAuB,EAGvB,YAAY,EACZ,WAAW,EAEX,6BAA6B,EAG7B,cAAc,EAEd,OAAO,EACP,aAAa,EAEb,kBAAkB,EAClB,MAAM,EAGN,kBAAkB,EAClB,qBAAqB,EACrB,sBAAsB,EACtB,YAAY,EAEZ,gBAAgB,EAEhB,SAAS,EACZ,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAK1D,OAAO,EAGH,gBAAgB,EAGnB,MAAM,8BAA8B,CAAC;AAGtC,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,+CAA+C,CAAC;AACpF,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAG1D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAGnD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAIxD;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,YAAY;IAEjD,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE9C,SAAS,CAAC,cAAc,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,eAAe,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAEjD,SAAS,CAAC,qBAAqB,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAExD,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;IAEvC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IAEzB,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;IAEhD,OAAO,CAAC,YAAY,CAAe;gBAG/B,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,EACnC,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,kBAAkB,EACrC,YAAY,EAAE,YAAY,EAC1B,sBAAsB,CAAC,EAAE,sBAAsB;IAwB7C,UAAU,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKtD;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAmB3B;;;OAGG;IACH,SAAS,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAiBhE;;;;;OAKG;IACH,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAoBpD;;;OAGG;IACG,UAAU,CACZ,OAAO,EAAE,aAAa,EACtB,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IA2BhB;;;OAGG;IACH,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC;IAI/B;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAyBxC;;;OAGG;IACH,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAuB1C;;;OAGG;IACG,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAK/C;;;OAGG;IACG,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBjE;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAKhC;;;OAGG;IACG,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAKnD;;;OAGG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAKrC;;;OAGG;IACH,YAAY,IAAI,SAAS;IAIzB;;;;OAIG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI;IA4CpD;;;;OAIG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI;IAoEvD;;;OAGG;IACH,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;IAyB9D;;;OAGG;IACG,oBAAoB,CACtB,OAAO,EAAE,aAAa,EACtB,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAchB;;;OAGG;IACH,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IA2B1E;;;OAGG;IACG,wBAAwB,CAC1B,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAehB;;;OAGG;IACH,yBAAyB,CACrB,eAAe,EAAE,MAAM,GACxB,kBAAkB,GAAG,IAAI;IA2B5B;;;OAGG;IACG,yBAAyB,CAC3B,YAAY,EAAE,kBAAkB,EAChC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IAgBhB;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAwBhE;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI;IASpD;;;OAGG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI;IA0B/B;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,GACvC,IAAI;IAQP;;OAEG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI;IAqBjE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC;IAOzC;;;;OAIG;IACH,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,IAAI;IAQpE;;OAEG;IACH,kBAAkB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;IAUtC;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,uBAAuB,GAAG,IAAI;IAKxE;;OAEG;IACH,gBAAgB,IAAI,WAAW,GAAG,IAAI;IAgCtC;;;OAGG;IACH,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI;IAwBnD;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IA2BvE;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,GAClC,IAAI;IAQP;;;;OAIG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,IAAI;IAsCzE;;;;;;OAMG;IACH,iBAAiB,CACb,QAAQ,EAAE,MAAM,EAChB,KAAK,EAAE,MAAM,EACb,WAAW,CAAC,EAAE,OAAO,GACtB,IAAI;IAiBP;;;OAGG;IACH,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAI7B;;;;OAIG;IACH,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAUtC;;OAEG;IACH,OAAO,IAAI,MAAM,EAAE;IAInB;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IA4B5B;;;;;OAKG;IACG,4BAA4B,CAC9B,iBAAiB,EAAE,kBAAkB,EACrC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC;IA6BhB;;;;OAIG;IACH,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAYrC;;;OAGG;IACH,iBAAiB,IAAI,IAAI;IAqBzB,qBAAqB,CACjB,eAAe,EAAE,6BAA6B,EAC9C,YAAY,CAAC,EAAE,MAAM,GACtB,IAAI;IAoBP;;OAEG;IACH,gBAAgB,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC;IAqC3D;;OAEG;IACH,sBAAsB,IAAI,mBAAmB,GAAG,IAAI;IA0BpD,uBAAuB,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO;IAUzD,wBAAwB,IAAI;QACxB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,gBAAgB,CAAC;KAC1B,GAAG,IAAI;IAiBR,wBAAwB,CACpB,UAAU,EAAE,OAAO,EACnB,IAAI,GAAE,gBAA0C,GACjD,IAAI;IAwBP;;;;OAIG;IACG,YAAY,CACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,EACD,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,YAAY,GACnB,OAAO,CAAC,IAAI,CAAC;IAoDhB;;;;;OAKG;IACG,eAAe,CACjB,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,YAAY,GAC5B,OAAO,CAAC,IAAI,CAAC;CA8BnB;AA6BD,eAAO,MAAM,6BAA6B,aAC5B,MAAM,UACR,MAAM,qBACK,kBAAkB,gBACvB,YAAY,KAC3B,mBAiBF,CAAC"}