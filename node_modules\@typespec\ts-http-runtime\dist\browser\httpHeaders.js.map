{"version": 3, "file": "httpHeaders.js", "sourceRoot": "", "sources": ["../../src/httpHeaders.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC,SAAS,aAAa,CAAC,IAAY;IACjC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5B,CAAC;AAED,QAAQ,CAAC,CAAC,cAAc,CAAC,GAA6B;IACpD,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;QACjC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAED,MAAM,eAAe;IAGnB,YAAY,UAAiD;QAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAuB,CAAC;QAClD,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjD,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,IAAY,EAAE,KAAgC;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,IAAY;;QACrB,OAAO,MAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,0CAAE,KAAK,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAY;QACxB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAsC,EAAE;QACpD,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACnC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvD,MAAM,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,OAAO,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,UAAgC;IAChE,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpHeaders, RawHttpHeaders, RawHttpHeadersInput } from \"./interfaces.js\";\n\ninterface HeaderEntry {\n  name: string;\n  value: string;\n}\n\nfunction normalizeName(name: string): string {\n  return name.toLowerCase();\n}\n\nfunction* headerIterator(map: Map<string, HeaderEntry>): IterableIterator<[string, string]> {\n  for (const entry of map.values()) {\n    yield [entry.name, entry.value];\n  }\n}\n\nclass HttpHeadersImpl implements HttpHeaders {\n  private readonly _headersMap: Map<string, HeaderEntry>;\n\n  constructor(rawHeaders?: RawHttpHeaders | RawHttpHeadersInput) {\n    this._headersMap = new Map<string, HeaderEntry>();\n    if (rawHeaders) {\n      for (const headerName of Object.keys(rawHeaders)) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   * @param value - The value of the header to set.\n   */\n  public set(name: string, value: string | number | boolean): void {\n    this._headersMap.set(normalizeName(name), { name, value: String(value).trim() });\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param name - The name of the header. This value is case-insensitive.\n   */\n  public get(name: string): string | undefined {\n    return this._headersMap.get(normalizeName(name))?.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   */\n  public has(name: string): boolean {\n    return this._headersMap.has(normalizeName(name));\n  }\n\n  /**\n   * Remove the header with the provided headerName.\n   * @param name - The name of the header to remove.\n   */\n  public delete(name: string): void {\n    this._headersMap.delete(normalizeName(name));\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJSON(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const entry of this._headersMap.values()) {\n        result[entry.name] = entry.value;\n      }\n    } else {\n      for (const [normalizedName, entry] of this._headersMap) {\n        result[normalizedName] = entry.value;\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJSON({ preserveCase: true }));\n  }\n\n  /**\n   * Iterate over tuples of header [name, value] pairs.\n   */\n  [Symbol.iterator](): Iterator<[string, string]> {\n    return headerIterator(this._headersMap);\n  }\n}\n\n/**\n * Creates an object that satisfies the `HttpHeaders` interface.\n * @param rawHeaders - A simple object representing initial headers\n */\nexport function createHttpHeaders(rawHeaders?: RawHttpHeadersInput): HttpHeaders {\n  return new HttpHeadersImpl(rawHeaders);\n}\n"]}