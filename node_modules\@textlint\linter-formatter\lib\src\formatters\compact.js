/**
 * @fileoverview Compact reporter
 * <AUTHOR>
 */
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//------------------------------------------------------------------------------
// Helper Functions
//------------------------------------------------------------------------------
function getMessageType(message) {
    if (message.fatal || message.severity === 2) {
        return "Error";
    }
    else {
        return "Warning";
    }
}
//------------------------------------------------------------------------------
// Public Interface
//------------------------------------------------------------------------------
function formatter(results) {
    let output = "", total = 0;
    results.forEach(function (result) {
        const messages = result.messages;
        total += messages.length;
        messages.forEach(function (message) {
            output += result.filePath + ": ";
            output += "line " + (message.line || 0);
            output += ", col " + (message.column || 0);
            output += ", " + getMessageType(message);
            output += " - " + message.message;
            output += message.ruleId ? " (" + message.ruleId + ")" : "";
            output += "\n";
        });
    });
    if (total > 0) {
        output += "\n" + total + " problem" + (total !== 1 ? "s" : "");
    }
    return output;
}
exports.default = formatter;
//# sourceMappingURL=compact.js.map