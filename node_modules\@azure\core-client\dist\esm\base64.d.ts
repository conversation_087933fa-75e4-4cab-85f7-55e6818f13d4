/**
 * Encodes a string in base64 format.
 * @param value - the string to encode
 * @internal
 */
export declare function encodeString(value: string): string;
/**
 * Encodes a byte array in base64 format.
 * @param value - the Uint8Aray to encode
 * @internal
 */
export declare function encodeByteArray(value: Uint8Array): string;
/**
 * Decodes a base64 string into a byte array.
 * @param value - the base64 string to decode
 * @internal
 */
export declare function decodeString(value: string): Uint8Array;
/**
 * Decodes a base64 string into a string.
 * @param value - the base64 string to decode
 * @internal
 */
export declare function decodeStringToString(value: string): string;
//# sourceMappingURL=base64.d.ts.map