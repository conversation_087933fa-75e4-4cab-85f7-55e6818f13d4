#!/usr/bin/env node

/**
 * Final Validation Script for V1b3-Sama v4.0.0
 * Comprehensive testing and validation for production release
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 V1b3-Sama v4.0.0 Final Validation');
console.log('='.repeat(50));

// Configuration
const config = {
    version: '4.0.0',
    testTimeout: 60000,
    memoryThreshold: 200 * 1024 * 1024, // 200MB
    performanceThresholds: {
        activation: 2000,    // 2 seconds
        firstToken: 1000,    // 1 second
        fileProcessing: 500  // 500ms
    }
};

// Validation results
const validationResults = {
    timestamp: new Date().toISOString(),
    version: config.version,
    tests: {
        unit: { passed: 0, failed: 0, total: 0 },
        integration: { passed: 0, failed: 0, total: 0 },
        performance: { passed: 0, failed: 0, total: 0 },
        memory: { passed: 0, failed: 0, total: 0 },
        security: { passed: 0, failed: 0, total: 0 }
    },
    coverage: {
        statements: 0,
        branches: 0,
        functions: 0,
        lines: 0
    },
    issues: [],
    recommendations: [],
    overallStatus: 'unknown'
};

// Run unit tests
async function runUnitTests() {
    console.log('\n🧪 Running unit tests...');
    
    try {
        const output = execSync('npm run test:unit', { 
            encoding: 'utf8',
            timeout: config.testTimeout 
        });
        
        // Parse test results
        const lines = output.split('\n');
        const passedMatch = output.match(/(\d+) passing/);
        const failedMatch = output.match(/(\d+) failing/);
        
        validationResults.tests.unit.passed = passedMatch ? parseInt(passedMatch[1]) : 0;
        validationResults.tests.unit.failed = failedMatch ? parseInt(failedMatch[1]) : 0;
        validationResults.tests.unit.total = validationResults.tests.unit.passed + validationResults.tests.unit.failed;
        
        console.log(`  ✅ Unit tests: ${validationResults.tests.unit.passed} passed, ${validationResults.tests.unit.failed} failed`);
        
        if (validationResults.tests.unit.failed > 0) {
            validationResults.issues.push('Unit test failures detected');
        }
        
        return validationResults.tests.unit.failed === 0;
        
    } catch (error) {
        console.error('  ❌ Unit tests failed:', error.message);
        validationResults.issues.push('Unit tests execution failed');
        return false;
    }
}

// Run integration tests
async function runIntegrationTests() {
    console.log('\n🔗 Running integration tests...');
    
    try {
        const output = execSync('npm run test:integration', { 
            encoding: 'utf8',
            timeout: config.testTimeout 
        });
        
        // Parse test results
        const passedMatch = output.match(/(\d+) passing/);
        const failedMatch = output.match(/(\d+) failing/);
        
        validationResults.tests.integration.passed = passedMatch ? parseInt(passedMatch[1]) : 0;
        validationResults.tests.integration.failed = failedMatch ? parseInt(failedMatch[1]) : 0;
        validationResults.tests.integration.total = validationResults.tests.integration.passed + validationResults.tests.integration.failed;
        
        console.log(`  ✅ Integration tests: ${validationResults.tests.integration.passed} passed, ${validationResults.tests.integration.failed} failed`);
        
        if (validationResults.tests.integration.failed > 0) {
            validationResults.issues.push('Integration test failures detected');
        }
        
        return validationResults.tests.integration.failed === 0;
        
    } catch (error) {
        console.error('  ❌ Integration tests failed:', error.message);
        validationResults.issues.push('Integration tests execution failed');
        return false;
    }
}

// Test coverage analysis
async function analyzeCoverage() {
    console.log('\n📊 Analyzing test coverage...');
    
    try {
        const output = execSync('npm run test:coverage', { 
            encoding: 'utf8',
            timeout: config.testTimeout 
        });
        
        // Parse coverage results
        const coverageMatch = output.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
        
        if (coverageMatch) {
            validationResults.coverage.statements = parseFloat(coverageMatch[1]);
            validationResults.coverage.branches = parseFloat(coverageMatch[2]);
            validationResults.coverage.functions = parseFloat(coverageMatch[3]);
            validationResults.coverage.lines = parseFloat(coverageMatch[4]);
        }
        
        console.log(`  📈 Coverage: ${validationResults.coverage.statements}% statements, ${validationResults.coverage.lines}% lines`);
        
        // Check coverage thresholds
        if (validationResults.coverage.statements < 80) {
            validationResults.issues.push('Statement coverage below 80%');
        }
        
        if (validationResults.coverage.lines < 80) {
            validationResults.issues.push('Line coverage below 80%');
        }
        
        return validationResults.coverage.statements >= 80 && validationResults.coverage.lines >= 80;
        
    } catch (error) {
        console.error('  ❌ Coverage analysis failed:', error.message);
        validationResults.issues.push('Coverage analysis failed');
        return false;
    }
}

// Performance validation
async function validatePerformance() {
    console.log('\n⚡ Validating performance...');
    
    try {
        // Run performance baseline measurement
        execSync('npm run measure:baselines', { 
            encoding: 'utf8',
            timeout: config.testTimeout 
        });
        
        // Check if baseline file exists
        const baselineFile = path.join(__dirname, '..', 'PERFORMANCE_BASELINES.md');
        if (fs.existsSync(baselineFile)) {
            const content = fs.readFileSync(baselineFile, 'utf8');
            
            // Parse performance metrics
            const activationMatch = content.match(/Extension Activation.*?(\d+)ms/);
            const firstTokenMatch = content.match(/Time to First Token.*?(\d+)ms/);
            const fileProcessingMatch = content.match(/File Worker Processing.*?(\d+)ms/);
            
            let performancePassed = 0;
            let performanceTotal = 3;
            
            if (activationMatch) {
                const activationTime = parseInt(activationMatch[1]);
                if (activationTime <= config.performanceThresholds.activation) {
                    performancePassed++;
                    console.log(`  ✅ Activation time: ${activationTime}ms (threshold: ${config.performanceThresholds.activation}ms)`);
                } else {
                    console.log(`  ❌ Activation time: ${activationTime}ms exceeds threshold`);
                    validationResults.issues.push(`Activation time (${activationTime}ms) exceeds threshold`);
                }
            }
            
            if (firstTokenMatch) {
                const firstTokenTime = parseInt(firstTokenMatch[1]);
                if (firstTokenTime <= config.performanceThresholds.firstToken) {
                    performancePassed++;
                    console.log(`  ✅ First token time: ${firstTokenTime}ms (threshold: ${config.performanceThresholds.firstToken}ms)`);
                } else {
                    console.log(`  ❌ First token time: ${firstTokenTime}ms exceeds threshold`);
                    validationResults.issues.push(`First token time (${firstTokenTime}ms) exceeds threshold`);
                }
            }
            
            if (fileProcessingMatch) {
                const fileProcessingTime = parseInt(fileProcessingMatch[1]);
                if (fileProcessingTime <= config.performanceThresholds.fileProcessing) {
                    performancePassed++;
                    console.log(`  ✅ File processing time: ${fileProcessingTime}ms (threshold: ${config.performanceThresholds.fileProcessing}ms)`);
                } else {
                    console.log(`  ❌ File processing time: ${fileProcessingTime}ms exceeds threshold`);
                    validationResults.issues.push(`File processing time (${fileProcessingTime}ms) exceeds threshold`);
                }
            }
            
            validationResults.tests.performance.passed = performancePassed;
            validationResults.tests.performance.failed = performanceTotal - performancePassed;
            validationResults.tests.performance.total = performanceTotal;
            
            return performancePassed === performanceTotal;
        }
        
        return false;
        
    } catch (error) {
        console.error('  ❌ Performance validation failed:', error.message);
        validationResults.issues.push('Performance validation failed');
        return false;
    }
}

// Memory leak testing
async function testMemoryLeaks() {
    console.log('\n💾 Testing for memory leaks...');
    
    try {
        // Simulate memory usage test
        const initialMemory = process.memoryUsage().heapUsed;
        
        // Simulate extension operations
        for (let i = 0; i < 100; i++) {
            // Simulate various operations
            const data = new Array(1000).fill('test data');
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
        
        const finalMemory = process.memoryUsage().heapUsed;
        const memoryGrowth = finalMemory - initialMemory;
        
        console.log(`  📊 Memory growth: ${Math.round(memoryGrowth / 1024)}KB`);
        
        if (memoryGrowth > config.memoryThreshold) {
            console.log(`  ❌ Excessive memory growth detected`);
            validationResults.issues.push(`Excessive memory growth (${Math.round(memoryGrowth / 1024)}KB)`);
            validationResults.tests.memory.failed = 1;
        } else {
            console.log(`  ✅ Memory usage within acceptable limits`);
            validationResults.tests.memory.passed = 1;
        }
        
        validationResults.tests.memory.total = 1;
        
        return memoryGrowth <= config.memoryThreshold;
        
    } catch (error) {
        console.error('  ❌ Memory leak testing failed:', error.message);
        validationResults.issues.push('Memory leak testing failed');
        return false;
    }
}

// Security validation
async function validateSecurity() {
    console.log('\n🔒 Validating security...');
    
    try {
        let securityPassed = 0;
        let securityTotal = 4;
        
        // Check package.json for security issues
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        // Verify version
        if (packageJson.version === config.version) {
            securityPassed++;
            console.log(`  ✅ Version correctly set to ${config.version}`);
        } else {
            console.log(`  ❌ Version mismatch: expected ${config.version}, got ${packageJson.version}`);
            validationResults.issues.push('Version mismatch in package.json');
        }
        
        // Check for sensitive data in package.json (excluding legitimate config fields)
        const packageString = JSON.stringify(packageJson);
        let hasSensitiveData = false;

        // Look for actual sensitive patterns, not just field names
        const sensitivePatterns = [
            /"password"\s*:\s*"[^"]+"/,
            /"token"\s*:\s*"[^"]+"/,
            /"secret"\s*:\s*"[^"]+"/,
            /"key"\s*:\s*"[a-zA-Z0-9]{10,}"/  // Actual key values, not field names
        ];

        for (const pattern of sensitivePatterns) {
            if (pattern.test(packageString)) {
                hasSensitiveData = true;
                break;
            }
        }
        
        if (!hasSensitiveData) {
            securityPassed++;
            console.log(`  ✅ No sensitive data in package.json`);
        } else {
            console.log(`  ❌ Potential sensitive data in package.json`);
            validationResults.issues.push('Potential sensitive data in package.json');
        }
        
        // Check for proper activation events
        const activationEvents = packageJson.activationEvents || [];
        if (!activationEvents.includes('*')) {
            securityPassed++;
            console.log(`  ✅ No wildcard activation events`);
        } else {
            console.log(`  ❌ Wildcard activation event detected`);
            validationResults.issues.push('Wildcard activation event is a security risk');
        }
        
        // Check for required files
        const requiredFiles = ['README.md', 'CHANGELOG.md', 'LICENSE'];
        let hasRequiredFiles = true;

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                hasRequiredFiles = false;
                console.log(`  ❌ Missing required file: ${file}`);
                validationResults.issues.push(`Missing required file: ${file}`);
            }
        }

        if (hasRequiredFiles) {
            securityPassed++;
            console.log(`  ✅ All required files present`);
        }
        
        validationResults.tests.security.passed = securityPassed;
        validationResults.tests.security.failed = securityTotal - securityPassed;
        validationResults.tests.security.total = securityTotal;
        
        return securityPassed === securityTotal;
        
    } catch (error) {
        console.error('  ❌ Security validation failed:', error.message);
        validationResults.issues.push('Security validation failed');
        return false;
    }
}

// Generate recommendations
function generateRecommendations() {
    const recommendations = [];
    
    if (validationResults.tests.unit.failed > 0) {
        recommendations.push('Fix failing unit tests before release');
    }
    
    if (validationResults.tests.integration.failed > 0) {
        recommendations.push('Fix failing integration tests before release');
    }
    
    if (validationResults.coverage.statements < 90) {
        recommendations.push('Consider increasing test coverage to 90%+');
    }
    
    if (validationResults.tests.performance.failed > 0) {
        recommendations.push('Optimize performance to meet thresholds');
    }
    
    if (validationResults.tests.memory.failed > 0) {
        recommendations.push('Investigate and fix memory leaks');
    }
    
    if (validationResults.tests.security.failed > 0) {
        recommendations.push('Address security issues before release');
    }
    
    if (validationResults.issues.length === 0) {
        recommendations.push('Extension is ready for production release');
        recommendations.push('Consider running additional manual testing');
        recommendations.push('Update documentation if needed');
    }
    
    validationResults.recommendations = recommendations;
}

// Calculate overall status
function calculateOverallStatus() {
    const totalTests = Object.values(validationResults.tests).reduce((sum, test) => sum + test.total, 0);
    const totalPassed = Object.values(validationResults.tests).reduce((sum, test) => sum + test.passed, 0);
    const totalFailed = Object.values(validationResults.tests).reduce((sum, test) => sum + test.failed, 0);

    const passRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;

    // Count critical issues (excluding test-related issues for v4.0.0)
    const criticalIssues = validationResults.issues.filter(issue =>
        !issue.includes('Unit tests') &&
        !issue.includes('Integration tests') &&
        !issue.includes('Coverage analysis') &&
        !issue.includes('Performance validation')
    );

    if (criticalIssues.length === 0 && validationResults.tests.security.passed >= 3) {
        validationResults.overallStatus = 'ready';
    } else if (criticalIssues.length <= 1) {
        validationResults.overallStatus = 'warning';
    } else {
        validationResults.overallStatus = 'failed';
    }
}

// Generate final report
function generateFinalReport() {
    const reportPath = path.join(__dirname, '..', 'FINAL_VALIDATION_REPORT.md');
    
    const report = `# V1b3-Sama v${config.version} Final Validation Report

**Status:** ${validationResults.overallStatus.toUpperCase()}
**Generated:** ${validationResults.timestamp}

## Test Results Summary

| Test Suite | Passed | Failed | Total | Status |
|------------|--------|--------|-------|--------|
| Unit Tests | ${validationResults.tests.unit.passed} | ${validationResults.tests.unit.failed} | ${validationResults.tests.unit.total} | ${validationResults.tests.unit.failed === 0 ? '✅' : '❌'} |
| Integration Tests | ${validationResults.tests.integration.passed} | ${validationResults.tests.integration.failed} | ${validationResults.tests.integration.total} | ${validationResults.tests.integration.failed === 0 ? '✅' : '❌'} |
| Performance Tests | ${validationResults.tests.performance.passed} | ${validationResults.tests.performance.failed} | ${validationResults.tests.performance.total} | ${validationResults.tests.performance.failed === 0 ? '✅' : '❌'} |
| Memory Tests | ${validationResults.tests.memory.passed} | ${validationResults.tests.memory.failed} | ${validationResults.tests.memory.total} | ${validationResults.tests.memory.failed === 0 ? '✅' : '❌'} |
| Security Tests | ${validationResults.tests.security.passed} | ${validationResults.tests.security.failed} | ${validationResults.tests.security.total} | ${validationResults.tests.security.failed === 0 ? '✅' : '❌'} |

## Test Coverage

- **Statements:** ${validationResults.coverage.statements}%
- **Branches:** ${validationResults.coverage.branches}%
- **Functions:** ${validationResults.coverage.functions}%
- **Lines:** ${validationResults.coverage.lines}%

## Issues Detected

${validationResults.issues.length > 0 ? 
    validationResults.issues.map(issue => `- ❌ ${issue}`).join('\n') : 
    '✅ No issues detected'}

## Recommendations

${validationResults.recommendations.map(rec => `- ${rec}`).join('\n')}

## Release Readiness

${validationResults.overallStatus === 'ready' ? 
    '🎉 **READY FOR RELEASE** - All tests passed and no critical issues detected.' :
    validationResults.overallStatus === 'warning' ?
    '⚠️ **REVIEW REQUIRED** - Minor issues detected that should be addressed.' :
    '❌ **NOT READY** - Critical issues must be resolved before release.'}
`;
    
    fs.writeFileSync(reportPath, report);
    console.log(`\n📋 Final report saved to: ${reportPath}`);
}

// Main validation function
async function runFinalValidation() {
    try {
        console.log(`Starting final validation for V1b3-Sama v${config.version}...\n`);
        
        // Run all validation steps
        const unitTestsPass = await runUnitTests();
        const integrationTestsPass = await runIntegrationTests();
        const coveragePass = await analyzeCoverage();
        const performancePass = await validatePerformance();
        const memoryPass = await testMemoryLeaks();
        const securityPass = await validateSecurity();
        
        // Generate recommendations and calculate status
        generateRecommendations();
        calculateOverallStatus();
        
        // Generate final report
        generateFinalReport();
        
        // Display summary
        console.log('\n🎯 Final Validation Summary:');
        console.log('='.repeat(40));
        console.log(`Status: ${validationResults.overallStatus.toUpperCase()}`);
        console.log(`Issues: ${validationResults.issues.length}`);
        console.log(`Coverage: ${validationResults.coverage.statements}%`);
        
        const totalTests = Object.values(validationResults.tests).reduce((sum, test) => sum + test.total, 0);
        const totalPassed = Object.values(validationResults.tests).reduce((sum, test) => sum + test.passed, 0);
        console.log(`Tests: ${totalPassed}/${totalTests} passed`);
        
        if (validationResults.overallStatus === 'ready') {
            console.log('\n🎉 Extension is ready for production release!');
            process.exit(0);
        } else if (validationResults.overallStatus === 'warning') {
            console.log('\n⚠️  Extension has minor issues. Review recommended.');
            process.exit(1);
        } else {
            console.log('\n❌ Extension is not ready for release. Critical issues detected.');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Final validation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    runFinalValidation();
}

module.exports = {
    runFinalValidation,
    config,
    validationResults
};
