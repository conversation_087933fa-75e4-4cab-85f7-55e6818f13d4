{"version": 3, "file": "concat.common.js", "sourceRoot": "", "sources": ["../../../src/util/concat.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtD;;;GAGG;AACH,SAAS,KAAK,CAAC,MAAkC;IAC/C,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;AACrC,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,MAAsD;IAEtD,IAAI,MAAM,YAAY,IAAI,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QAC3D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CACb,8FAA8F,CAC/F,CAAC;IACJ,CAAC;AACH,CAAC;AASD;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,MAAM,CAC1B,OAAgD;IAEhD,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,KAAK,CAAC,IAAI,CAAC,MAAM,UAAU,CAAC,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isWebReadableStream } from \"./typeGuards.js\";\n\n/**\n * Drain the content of the given ReadableStream into a Blob.\n * The blob's content may end up in memory or on disk dependent on size.\n */\nfunction drain(stream: ReadableStream<Uint8Array>): Promise<Blob> {\n  return new Response(stream).blob();\n}\n\nasync function toBlobPart(\n  source: ReadableStream<Uint8Array> | Blob | Uint8Array,\n): Promise<BlobPart> {\n  if (source instanceof Blob || source instanceof Uint8Array) {\n    return source;\n  }\n\n  if (isWebReadableStream(source)) {\n    return drain(source);\n  } else {\n    throw new Error(\n      \"Unsupported source type. Only Blob, Uint8Array, and ReadableStream are supported in browser.\",\n    );\n  }\n}\n\n/**\n * Accepted binary data types for concat\n *\n * @internal\n */\ntype ConcatSource = ReadableStream<Uint8Array> | Blob | Uint8Array;\n\n/**\n * Utility function that concatenates a set of binary inputs into one combined output.\n *\n * @param sources - array of sources for the concatenation\n * @returns - in Node, a (() =\\> NodeJS.ReadableStream) which, when read, produces a concatenation of all the inputs.\n *           In browser, returns a `Blob` representing all the concatenated inputs.\n *\n * @internal\n */\nexport async function concat(\n  sources: (ConcatSource | (() => ConcatSource))[],\n): Promise<(() => NodeJS.ReadableStream) | Blob> {\n  const parts = [];\n  for (const source of sources) {\n    parts.push(await toBlobPart(typeof source === \"function\" ? source() : source));\n  }\n\n  return new Blob(parts);\n}\n"]}