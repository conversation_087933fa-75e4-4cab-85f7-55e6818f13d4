{"version": 3, "file": "pretty-error.js", "sourceRoot": "", "sources": ["../../../src/formatters/pretty-error.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,0DAA0D;AAC1D,gBAAgB;AAChB,YAAY,CAAC;;;;;;AAIb,kDAA0B;AAC1B,4BAA4B;AAC5B,mEAAsC;AACtC,4BAA4B;AAC5B,qEAAsC;AACtC,4DAAmC;AACnC,4BAA4B;AAC5B,0DAAkC;AAElC,aAAa;AACb,gEAAyC;AAEzC,YAAY;AACZ,2BAAkC;AAElC,MAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,MAAM,UAAU,GAAG,OAAO,CAAC;AAC3B,MAAM,QAAQ,GAAG,IAAA,sBAAK,EAClB,uCAAuC;IACnC,2BAA2B;IAC3B,2CAA2C;IAC3C,qDAAqD;IACrD,oDAAoD;IACpD,6CAA6C;IAC7C,2CAA2C;IAC3C,EAAE,CACT,CAAC;AAEF;;;;;GAKG;AACH,SAAS,WAAW,CAAC,IAAY,EAAE,OAAwB;IACvD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;IACzB,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3C,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;aACjB,CAAC,CAAC;YACH,SAAS;QACb,CAAC;QAED,MAAM,CAAC,IAAI,CAAC;YACR,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,GAAG,EAAE,OAAO,CAAC,MAAM;YACnB,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YACd,MAAM,EAAE,IAAI;SACf,CAAC,CAAC;IACP,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,UAAU,CAAC,KAAa,EAAE,EAAU;IACzC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,UAAU,GAAQ,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IACxD,MAAM,KAAK,GAAG,IAAA,sBAAa,EAAC,MAAM,CAAC,CAAC;IACpC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACb,OAAO,EAAE,CAAC;IACd,CAAC;IACD,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAElB,OAAO,CAAC,EAAE,EAAE,CAAC;QACT,MAAM,IAAI,GAAG,CAAC;IAClB,CAAC;IAED,OAAO,MAAM,GAAG,EAAE,CAAC;AACvB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,WAAW,CAAC,IAAY,EAAE,QAAgB,EAAE,OAAwB;IACzE,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,OAAO;IACX,CAAC;IACD,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9C,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IAC1F,OAAO,IAAA,qBAAM,EAAC,QAAQ,EAAE;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,KAAK,EAAE,OAAO,CAAC,OAAO;QACtB,QAAQ,EAAE,GAAG,QAAQ,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;QACzD,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QAClD,cAAc,EAAE,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACjD,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;QAC5B,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,aAAa,EAAE,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC/C,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;QAC3B,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QAC9C,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACzC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;QACxB,gBAAgB,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACvC,GAAG,EAAE,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;QAC5B,CAAC,EAAE,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;KAC7B,CAAC,CAAC;AACP,CAAC;AAsEQ,kCAAW;AApEpB;;;;;GAKG;AACH,SAAS,SAAS,CAAC,OAAyB,EAAE,OAAyB;IACnE,gBAAgB;IAChB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;QAC5B,MAAM,IAAI,GAAG,IAAA,iBAAY,EAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QACD,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;QACzB,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO;YAC9B,UAAU;YACV,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,eAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;YACnB,CAAC;YACD,IAAK,OAAe,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACnD,MAAM,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACJ,QAAQ,EAAE,CAAC;YACf,CAAC;YACD,MAAM,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,EAAE,CAAC;gBACJ,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC;YACvB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,eAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAC9B;YACI,SAAS;YACT,KAAK;YACL,IAAA,mBAAS,EAAC,UAAU,EAAE,KAAK,CAAC;YAC5B,IAAI;YACJ,MAAM;YACN,IAAA,mBAAS,EAAC,QAAQ,EAAE,MAAM,CAAC;YAC3B,IAAI;YACJ,QAAQ;YACR,IAAA,mBAAS,EAAC,UAAU,EAAE,QAAQ,CAAC;YAC/B,KAAK;SACR,CAAC,IAAI,CAAC,EAAE,CAAC,CACb,CAAC;IACN,CAAC;IAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACnB,MAAM,IAAI,eAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,YAAY,YAAY,IAAA,mBAAS,EAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;QACvG,MAAM,IAAI,iBAAiB,eAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC;IAC5E,CAAC;IAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,IAAA,oBAAS,EAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,kBAAe,SAAS,CAAC"}