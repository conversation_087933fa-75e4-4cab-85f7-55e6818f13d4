{"version": 3, "file": "typeGuards.js", "sourceRoot": "", "sources": ["../../src/typeGuards.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAI,KAA2B;IACtD,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC;AACxD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,sBAAsB,CACpC,KAAY,EACZ,UAA0B;IAE1B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAC/B,KAAY,EACZ,QAAsB;IAEtB,OAAO,CACL,SAAS,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAK,KAAiC,CAChG,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Helper TypeGuard that checks if something is defined or not.\n * @param thing - Anything\n */\nexport function isDefined<T>(thing: T | undefined | null): thing is T {\n  return typeof thing !== \"undefined\" && thing !== null;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified properties.\n * @param thing - Anything.\n * @param properties - The name of the properties that should appear in the object.\n */\nexport function isObjectWithProperties<Thing, PropertyName extends string>(\n  thing: Thing,\n  properties: PropertyName[],\n): thing is Thing & Record<PropertyName, unknown> {\n  if (!isDefined(thing) || typeof thing !== \"object\") {\n    return false;\n  }\n\n  for (const property of properties) {\n    if (!objectHasProperty(thing, property)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Helper TypeGuard that checks if the input is an object with the specified property.\n * @param thing - Any object.\n * @param property - The name of the property that should appear in the object.\n */\nexport function objectHasProperty<Thing, PropertyName extends string>(\n  thing: Thing,\n  property: PropertyName,\n): thing is Thing & Record<PropertyName, unknown> {\n  return (\n    isDefined(thing) && typeof thing === \"object\" && property in (thing as Record<string, unknown>)\n  );\n}\n"]}