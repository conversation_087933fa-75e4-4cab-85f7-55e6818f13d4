{"version": 3, "file": "browserCustomizationOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/browserCustomizationOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Shared configuration options for browser customization\n */\nexport interface BrowserCustomizationOptions {\n  /**\n   * Shared configuration options for browser customization\n   */\n  browserCustomizationOptions?: {\n    /**\n     * Format for error messages for display in browser\n     */\n    errorMessage?: string;\n    /**\n     * Format for success messages for display in browser\n     */\n    successMessage?: string;\n  };\n}\n"]}