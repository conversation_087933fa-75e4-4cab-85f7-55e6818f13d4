{"name": "@secretlint/secretlint-rule-preset-recommend", "version": "9.3.4", "description": "Recommended rule preset of secretlint.", "keywords": ["secretlint", "rule", "rule-preset"], "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/@secretlint/secretlint-rule-preset-recommend/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "type": "module", "exports": {".": {"import": {"types": "./module/index.d.ts", "default": "./module/index.js"}, "default": "./module/index.js"}, "./package.json": "./package.json"}, "main": "./module/index.js", "types": "./module/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "module/", "src/"], "scripts": {"build": "rollup --config", "build:tsc": "tsc -p . --noEmit", "clean": "rimraf module/", "sync-canary": "cp ../secretlint-rule-preset-canary/src/index.ts src/ && sed -i '' -e 's/\\@secretlint\\/secretlint-rule-preset-canary/\\@secretlint\\/secretlint-rule-preset-recommend/' src/index.ts && cp -r ../secretlint-rule-preset-canary/test/ test && sed -i '' -e 's/\\@secretlint\\/secretlint-rule-preset-canary/\\@secretlint\\/secretlint-rule-preset-recommend/' test/index.test.ts && node cp-canary-deps.cjs && yarn install && npm run build && npm run updateSnapshot", "prepublishOnly": "npm run clean && npm run build", "prettier": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublish": "npm run --if-present build", "import-test": "node import-tests.js && npm run build && npm run updateSnapshot", "test": "node --loader ts-node/esm --test test/index.test.ts", "updateSnapshot": "UPDATE_SNAPSHOT=1 npm test", "watch": "tsc --build --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-typescript": "^11.1.6", "@secretlint/secretlint-rule-1password": "^9.3.4", "@secretlint/secretlint-rule-aws": "^9.3.4", "@secretlint/secretlint-rule-azure": "^9.3.4", "@secretlint/secretlint-rule-basicauth": "^9.3.4", "@secretlint/secretlint-rule-filter-comments": "^9.3.4", "@secretlint/secretlint-rule-gcp": "^9.3.4", "@secretlint/secretlint-rule-github": "^9.3.4", "@secretlint/secretlint-rule-linear": "^9.3.4", "@secretlint/secretlint-rule-npm": "^9.3.4", "@secretlint/secretlint-rule-openai": "^9.3.4", "@secretlint/secretlint-rule-privatekey": "^9.3.4", "@secretlint/secretlint-rule-sendgrid": "^9.3.4", "@secretlint/secretlint-rule-shopify": "^9.3.4", "@secretlint/secretlint-rule-slack": "^9.3.4", "@secretlint/tester": "^9.3.4", "@secretlint/types": "^9.3.4", "@types/node": "^20.17.51", "prettier": "^2.8.1", "rimraf": "^6.0.1", "rollup": "^3.29.5", "rollup-plugin-polyfill-node": "^0.13.0", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "packageManager": "yarn@1.22.22", "engines": {"node": "^14.13.1 || >=16.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "1b0bebd91380d94ee0a756900ce9b39c8b3cfd1b"}