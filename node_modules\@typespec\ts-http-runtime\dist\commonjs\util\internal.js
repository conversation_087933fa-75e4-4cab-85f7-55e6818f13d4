"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Sanitizer = exports.uint8ArrayToString = exports.stringToUint8Array = exports.isWebWorker = exports.isReactNative = exports.isDeno = exports.isNodeRuntime = exports.isNodeLike = exports.isBun = exports.isBrowser = exports.randomUUID = exports.computeSha256Hmac = exports.computeSha256Hash = exports.isError = exports.isObject = exports.getRandomIntegerInclusive = exports.calculateRetryDelay = void 0;
var delay_js_1 = require("./delay.js");
Object.defineProperty(exports, "calculateRetryDelay", { enumerable: true, get: function () { return delay_js_1.calculateRetryDelay; } });
var random_js_1 = require("./random.js");
Object.defineProperty(exports, "getRandomIntegerInclusive", { enumerable: true, get: function () { return random_js_1.getRandomIntegerInclusive; } });
var object_js_1 = require("./object.js");
Object.defineProperty(exports, "isObject", { enumerable: true, get: function () { return object_js_1.isObject; } });
var error_js_1 = require("./error.js");
Object.defineProperty(exports, "isError", { enumerable: true, get: function () { return error_js_1.isError; } });
var sha256_js_1 = require("./sha256.js");
Object.defineProperty(exports, "computeSha256Hash", { enumerable: true, get: function () { return sha256_js_1.computeSha256Hash; } });
Object.defineProperty(exports, "computeSha256Hmac", { enumerable: true, get: function () { return sha256_js_1.computeSha256Hmac; } });
var uuidUtils_js_1 = require("./uuidUtils.js");
Object.defineProperty(exports, "randomUUID", { enumerable: true, get: function () { return uuidUtils_js_1.randomUUID; } });
var checkEnvironment_js_1 = require("./checkEnvironment.js");
Object.defineProperty(exports, "isBrowser", { enumerable: true, get: function () { return checkEnvironment_js_1.isBrowser; } });
Object.defineProperty(exports, "isBun", { enumerable: true, get: function () { return checkEnvironment_js_1.isBun; } });
Object.defineProperty(exports, "isNodeLike", { enumerable: true, get: function () { return checkEnvironment_js_1.isNodeLike; } });
Object.defineProperty(exports, "isNodeRuntime", { enumerable: true, get: function () { return checkEnvironment_js_1.isNodeRuntime; } });
Object.defineProperty(exports, "isDeno", { enumerable: true, get: function () { return checkEnvironment_js_1.isDeno; } });
Object.defineProperty(exports, "isReactNative", { enumerable: true, get: function () { return checkEnvironment_js_1.isReactNative; } });
Object.defineProperty(exports, "isWebWorker", { enumerable: true, get: function () { return checkEnvironment_js_1.isWebWorker; } });
var bytesEncoding_js_1 = require("./bytesEncoding.js");
Object.defineProperty(exports, "stringToUint8Array", { enumerable: true, get: function () { return bytesEncoding_js_1.stringToUint8Array; } });
Object.defineProperty(exports, "uint8ArrayToString", { enumerable: true, get: function () { return bytesEncoding_js_1.uint8ArrayToString; } });
var sanitizer_js_1 = require("./sanitizer.js");
Object.defineProperty(exports, "Sanitizer", { enumerable: true, get: function () { return sanitizer_js_1.Sanitizer; } });
//# sourceMappingURL=internal.js.map