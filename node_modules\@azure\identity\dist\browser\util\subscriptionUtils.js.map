{"version": 3, "file": "subscriptionUtils.js", "sourceRoot": "", "sources": ["../../../src/util/subscriptionUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE3C;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAwB,EAAE,YAAoB;IAC9E,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,uLAAuL,CACxL,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CredentialLogger } from \"./logging.js\";\nimport { formatError } from \"./logging.js\";\n\n/**\n * @internal\n */\nexport function checkSubscription(logger: CredentialLogger, subscription: string): void {\n  if (!subscription.match(/^[0-9a-zA-Z-._ ]+$/)) {\n    const error = new Error(\n      \"Invalid subscription provided. You can locate your subscription by following the instructions listed here: https://learn.microsoft.com/azure/azure-portal/get-subscription-tenant-id.\",\n    );\n    logger.info(formatError(\"\", error));\n    throw error;\n  }\n}\n"]}