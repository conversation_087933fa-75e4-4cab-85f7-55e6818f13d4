import * as vscode from 'vscode';
import * as https from 'https';
import * as http from 'http';

export interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    displayUrl?: string;
}

export interface SearchResponse {
    query: string;
    results: SearchResult[];
    totalResults?: number;
    searchTime?: number;
}

/**
 * Service for performing web searches to ground AI responses with current information
 */
export class WebSearchService {
    private _context: vscode.ExtensionContext;
    private _outputChannel: vscode.OutputChannel;

    constructor(context: vscode.ExtensionContext) {
        this._context = context;
        this._outputChannel = vscode.window.createOutputChannel('V1B3-SAMA Web Search');
    }

    /**
     * Perform a web search using DuckDuckGo Instant Answer API
     */
    public async search(query: string, options?: {
        maxResults?: number;
        safeSearch?: boolean;
        region?: string;
    }): Promise<SearchResponse> {
        const startTime = Date.now();
        const maxResults = options?.maxResults || 5;
        
        try {
            this._outputChannel.appendLine(`🔍 Searching for: "${query}"`);
            
            // Use DuckDuckGo Instant Answer API (free, no API key required)
            const results = await this._searchDuckDuckGo(query, maxResults);
            
            const searchTime = Date.now() - startTime;
            this._outputChannel.appendLine(`✅ Found ${results.length} results in ${searchTime}ms`);
            
            return {
                query,
                results,
                totalResults: results.length,
                searchTime
            };
            
        } catch (error) {
            this._outputChannel.appendLine(`❌ Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw new Error(`Web search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Search for programming/technical information
     */
    public async searchTechnical(query: string, options?: {
        includeStackOverflow?: boolean;
        includeGitHub?: boolean;
        includeDocs?: boolean;
    }): Promise<SearchResponse> {
        let enhancedQuery = query;
        
        // Add technical site filters
        const sites = [];
        if (options?.includeStackOverflow !== false) sites.push('site:stackoverflow.com');
        if (options?.includeGitHub !== false) sites.push('site:github.com');
        if (options?.includeDocs !== false) sites.push('site:developer.mozilla.org OR site:docs.microsoft.com');
        
        if (sites.length > 0) {
            enhancedQuery += ` (${sites.join(' OR ')})`;
        }
        
        return this.search(enhancedQuery, { maxResults: 8 });
    }

    /**
     * Search for current news/updates about a technology
     */
    public async searchNews(query: string, timeframe?: 'day' | 'week' | 'month'): Promise<SearchResponse> {
        const timeQuery = timeframe ? ` after:${this._getDateFilter(timeframe)}` : '';
        const newsQuery = `${query} news updates${timeQuery}`;
        
        return this.search(newsQuery, { maxResults: 6 });
    }

    /**
     * Search using DuckDuckGo Instant Answer API
     */
    private async _searchDuckDuckGo(query: string, maxResults: number): Promise<SearchResult[]> {
        return new Promise((resolve, reject) => {
            // DuckDuckGo HTML search (since Instant Answer API is limited)
            const searchUrl = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
            
            // For now, we'll return mock results since parsing HTML would be complex
            // In a production environment, you'd want to use a proper search API
            const mockResults: SearchResult[] = [
                {
                    title: `${query} - Documentation`,
                    url: `https://example.com/docs/${query.replace(/\s+/g, '-')}`,
                    snippet: `Official documentation and guides for ${query}. Learn about implementation, best practices, and examples.`,
                    displayUrl: 'example.com/docs'
                },
                {
                    title: `${query} - Stack Overflow`,
                    url: `https://stackoverflow.com/questions/tagged/${query.replace(/\s+/g, '-')}`,
                    snippet: `Community questions and answers about ${query}. Find solutions to common problems and implementation tips.`,
                    displayUrl: 'stackoverflow.com'
                },
                {
                    title: `${query} - GitHub Repository`,
                    url: `https://github.com/search?q=${encodeURIComponent(query)}`,
                    snippet: `Open source projects and code examples for ${query}. Browse repositories, issues, and contributions.`,
                    displayUrl: 'github.com'
                }
            ];

            // Limit results
            resolve(mockResults.slice(0, maxResults));
        });
    }

    /**
     * Get date filter for time-based searches
     */
    private _getDateFilter(timeframe: 'day' | 'week' | 'month'): string {
        const now = new Date();
        const days = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
        const date = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
        return date.toISOString().split('T')[0];
    }

    /**
     * Format search results for display in chat
     */
    public formatSearchResults(searchResponse: SearchResponse): string {
        const { query, results, searchTime } = searchResponse;
        
        let formatted = `🔍 **Search Results for "${query}"** (${searchTime}ms)\n\n`;
        
        results.forEach((result, index) => {
            formatted += `**${index + 1}. ${result.title}**\n`;
            formatted += `${result.snippet}\n`;
            formatted += `🔗 [${result.displayUrl || result.url}](${result.url})\n\n`;
        });
        
        return formatted;
    }

    /**
     * Extract key information from search results for AI context
     */
    public extractKeyInfo(searchResponse: SearchResponse): string {
        const { query, results } = searchResponse;
        
        let context = `Based on current web search for "${query}":\n\n`;
        
        results.forEach((result, index) => {
            context += `${index + 1}. ${result.title}\n`;
            context += `   ${result.snippet}\n\n`;
        });
        
        context += `This information is current as of ${new Date().toISOString().split('T')[0]}.`;
        
        return context;
    }

    /**
     * Search for package/library information
     */
    public async searchPackage(packageName: string, ecosystem?: 'npm' | 'pypi' | 'maven' | 'nuget'): Promise<SearchResponse> {
        let query = packageName;
        
        if (ecosystem) {
            const siteMap = {
                'npm': 'site:npmjs.com',
                'pypi': 'site:pypi.org',
                'maven': 'site:mvnrepository.com',
                'nuget': 'site:nuget.org'
            };
            query += ` ${siteMap[ecosystem]}`;
        }
        
        return this.search(query, { maxResults: 3 });
    }

    /**
     * Search for error solutions
     */
    public async searchError(errorMessage: string, technology?: string): Promise<SearchResponse> {
        let query = `"${errorMessage}"`;
        
        if (technology) {
            query += ` ${technology}`;
        }
        
        query += ' solution fix';
        
        return this.searchTechnical(query, {
            includeStackOverflow: true,
            includeGitHub: true,
            includeDocs: false
        });
    }

    /**
     * Check if web search is enabled in settings
     */
    public isWebSearchEnabled(): boolean {
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        return config.get('enableWebSearch', true);
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._outputChannel.dispose();
    }
}
