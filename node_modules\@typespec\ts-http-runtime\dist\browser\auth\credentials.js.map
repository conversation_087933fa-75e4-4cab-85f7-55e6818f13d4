{"version": 3, "file": "credentials.js", "sourceRoot": "", "sources": ["../../../src/auth/credentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AA0ElC;;GAEG;AACH,MAAM,UAAU,uBAAuB,CACrC,UAA4B;IAE5B,OAAO,gBAAgB,IAAI,UAAU,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CACrC,UAA4B;IAE5B,OAAO,gBAAgB,IAAI,UAAU,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,UAA4B;IAC5D,OAAO,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAA4B;IAC7D,OAAO,KAAK,IAAI,UAAU,CAAC;AAC7B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"./oauth2Flows.js\";\n\n/**\n * Options used when creating and sending get OAuth 2 requests for this operation.\n */\nexport interface GetOAuth2TokenOptions {\n  /** Abort signal for the request */\n  abortSignal?: AbortSignal;\n}\n\n/**\n * Options used when creating and sending get bearer token requests for this operation.\n */\nexport interface GetBearerTokenOptions {\n  /** Abort signal for the request */\n  abortSignal?: AbortSignal;\n}\n\n/**\n * Credential for OAuth2 authentication flows.\n */\nexport interface OAuth2TokenCredential<TFlows extends OAuth2Flow> {\n  /**\n   * Gets an OAuth2 token for the specified flows.\n   * @param flows - The OAuth2 flows to use.\n   * @param options - Options for the request.\n   * @returns - a valid access token which was obtained through one of the flows specified in `flows`.\n   */\n  getOAuth2Token(flows: TFlows[], options?: GetOAuth2TokenOptions): Promise<string>;\n}\n\n/**\n * Credential for Bearer token authentication.\n */\nexport interface BearerTokenCredential {\n  /**\n   * Gets a Bearer token for the specified flows.\n   * @param options - Options for the request.\n   * @returns - a valid access token.\n   */\n  getBearerToken(options?: GetBearerTokenOptions): Promise<string>;\n}\n\n/**\n * Credential for HTTP Basic authentication.\n * Provides username and password for basic authentication headers.\n */\nexport interface BasicCredential {\n  /** The username for basic authentication. */\n  username: string;\n  /** The password for basic authentication. */\n  password: string;\n}\n\n/**\n * Credential for API Key authentication.\n * Provides an API key that will be used in the request headers.\n */\nexport interface ApiKeyCredential {\n  /** The API key for authentication. */\n  key: string;\n}\n\n/**\n * Union type of all supported authentication credentials.\n */\nexport type ClientCredential =\n  | OAuth2TokenCredential<OAuth2Flow>\n  | BearerTokenCredential\n  | BasicCredential\n  | ApiKeyCredential;\n\n/**\n * Type guard to check if a credential is an OAuth2 token credential.\n */\nexport function isOAuth2TokenCredential(\n  credential: ClientCredential,\n): credential is OAuth2TokenCredential<OAuth2Flow> {\n  return \"getOAuth2Token\" in credential;\n}\n\n/**\n * Type guard to check if a credential is a Bearer token credential.\n */\nexport function isBearerTokenCredential(\n  credential: ClientCredential,\n): credential is BearerTokenCredential {\n  return \"getBearerToken\" in credential;\n}\n\n/**\n * Type guard to check if a credential is a Basic auth credential.\n */\nexport function isBasicCredential(credential: ClientCredential): credential is BasicCredential {\n  return \"username\" in credential && \"password\" in credential;\n}\n\n/**\n * Type guard to check if a credential is an API key credential.\n */\nexport function isApiKeyCredential(credential: ClientCredential): credential is ApiKeyCredential {\n  return \"key\" in credential;\n}\n"]}