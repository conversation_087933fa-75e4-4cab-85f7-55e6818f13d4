// Error Monitoring Service for production error tracking and reporting
// Privacy-conscious local error logging with optional telemetry

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface ErrorReport {
    id: string;
    timestamp: number;
    error: {
        name: string;
        message: string;
        stack?: string;
    };
    context: {
        operation: string;
        component: string;
        userId?: string;
        sessionId: string;
    };
    environment: {
        vscodeVersion: string;
        extensionVersion: string;
        platform: string;
        nodeVersion: string;
    };
    metadata?: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ErrorStats {
    totalErrors: number;
    errorsByComponent: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: ErrorReport[];
    topErrors: Array<{ message: string; count: number; lastSeen: number }>;
}

export class ErrorMonitoringService {
    private context: vscode.ExtensionContext;
    private sessionId: string;
    private errorLog: ErrorReport[] = [];
    private maxLogSize = 1000;
    private logFilePath: string;
    private isEnabled = true;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.sessionId = this.generateSessionId();
        this.logFilePath = path.join(context.globalStorageUri?.fsPath || '', 'error-log.json');
        
        this.loadPersistedErrors();
        this.setupGlobalErrorHandlers();
        this.startPeriodicCleanup();
    }

    /**
     * Report an error to the monitoring service
     */
    public reportError(
        error: Error,
        context: {
            operation: string;
            component: string;
            metadata?: any;
        },
        severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
    ): string {
        if (!this.isEnabled) {
            return '';
        }

        const errorReport: ErrorReport = {
            id: this.generateErrorId(),
            timestamp: Date.now(),
            error: {
                name: error.name,
                message: error.message,
                stack: this.sanitizeStackTrace(error.stack)
            },
            context: {
                operation: context.operation,
                component: context.component,
                sessionId: this.sessionId
            },
            environment: {
                vscodeVersion: vscode.version,
                extensionVersion: this.getExtensionVersion(),
                platform: process.platform,
                nodeVersion: process.version
            },
            metadata: context.metadata,
            severity
        };

        this.addErrorToLog(errorReport);
        this.logErrorToConsole(errorReport);
        
        // Persist critical errors immediately
        if (severity === 'critical') {
            this.persistErrors();
        }

        return errorReport.id;
    }

    /**
     * Report an unhandled exception
     */
    public reportUnhandledException(
        error: Error,
        component: string = 'unknown',
        metadata?: any
    ): string {
        return this.reportError(error, {
            operation: 'unhandled_exception',
            component,
            metadata
        }, 'critical');
    }

    /**
     * Report a handled error with context
     */
    public reportHandledError(
        error: Error,
        operation: string,
        component: string,
        metadata?: any
    ): string {
        return this.reportError(error, {
            operation,
            component,
            metadata
        }, 'medium');
    }

    /**
     * Get error statistics
     */
    public getErrorStats(): ErrorStats {
        const now = Date.now();
        const last24Hours = now - (24 * 60 * 60 * 1000);
        const recentErrors = this.errorLog.filter(e => e.timestamp > last24Hours);

        // Count errors by component
        const errorsByComponent: Record<string, number> = {};
        recentErrors.forEach(error => {
            errorsByComponent[error.context.component] = 
                (errorsByComponent[error.context.component] || 0) + 1;
        });

        // Count errors by severity
        const errorsBySeverity: Record<string, number> = {};
        recentErrors.forEach(error => {
            errorsBySeverity[error.severity] = 
                (errorsBySeverity[error.severity] || 0) + 1;
        });

        // Find top errors
        const errorCounts: Record<string, { count: number; lastSeen: number }> = {};
        recentErrors.forEach(error => {
            const key = `${error.error.name}: ${error.error.message}`;
            if (!errorCounts[key]) {
                errorCounts[key] = { count: 0, lastSeen: 0 };
            }
            errorCounts[key].count++;
            errorCounts[key].lastSeen = Math.max(errorCounts[key].lastSeen, error.timestamp);
        });

        const topErrors = Object.entries(errorCounts)
            .map(([message, data]) => ({ message, ...data }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        return {
            totalErrors: recentErrors.length,
            errorsByComponent,
            errorsBySeverity,
            recentErrors: recentErrors.slice(-20), // Last 20 errors
            topErrors
        };
    }

    /**
     * Clear error log
     */
    public clearErrors(): void {
        this.errorLog = [];
        this.persistErrors();
    }

    /**
     * Enable or disable error monitoring
     */
    public setEnabled(enabled: boolean): void {
        this.isEnabled = enabled;
    }

    /**
     * Export error log for analysis
     */
    public exportErrors(): string {
        const exportData = {
            sessionId: this.sessionId,
            exportTime: new Date().toISOString(),
            errors: this.errorLog,
            stats: this.getErrorStats()
        };
        
        return JSON.stringify(exportData, null, 2);
    }

    /**
     * Show error report in VS Code
     */
    public async showErrorReport(): Promise<void> {
        const stats = this.getErrorStats();
        const reportContent = this.formatErrorReport(stats);

        const panel = vscode.window.createWebviewPanel(
            'v1b3-error-report',
            'V1b3-Sama Error Report',
            vscode.ViewColumn.One,
            { enableScripts: false }
        );

        panel.webview.html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Error Report</title>
                <style>
                    body { 
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                        padding: 20px; 
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-editor-foreground);
                    }
                    .error-card { 
                        background: var(--vscode-editor-inactiveSelectionBackground);
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 4px;
                        padding: 15px; 
                        margin: 10px 0; 
                    }
                    .severity-critical { border-left: 4px solid #ff4757; }
                    .severity-high { border-left: 4px solid #ffa502; }
                    .severity-medium { border-left: 4px solid #3742fa; }
                    .severity-low { border-left: 4px solid #2ed573; }
                    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
                    .stat-card { 
                        background: var(--vscode-button-background);
                        padding: 15px; 
                        border-radius: 4px; 
                        text-align: center; 
                    }
                    .stat-number { font-size: 2em; font-weight: bold; }
                    pre { 
                        background: var(--vscode-textCodeBlock-background);
                        padding: 10px; 
                        border-radius: 4px; 
                        overflow-x: auto; 
                        font-size: 12px;
                    }
                </style>
            </head>
            <body>
                <h1>V1b3-Sama Error Report</h1>
                ${reportContent}
            </body>
            </html>
        `;
    }

    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }

    private generateErrorId(): string {
        return `error_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }

    private getExtensionVersion(): string {
        try {
            const packageJson = require('../../package.json');
            return packageJson.version || 'unknown';
        } catch {
            return 'unknown';
        }
    }

    private sanitizeStackTrace(stack?: string): string | undefined {
        if (!stack) return undefined;
        
        // Remove sensitive file paths and replace with relative paths
        return stack
            .split('\n')
            .map(line => {
                // Remove absolute paths, keep only relative paths
                return line.replace(/\/.*?\/([^\/]+\.js)/g, './$1');
            })
            .join('\n');
    }

    private addErrorToLog(errorReport: ErrorReport): void {
        this.errorLog.push(errorReport);
        
        // Trim log if it gets too large
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(-this.maxLogSize);
        }
    }

    private logErrorToConsole(errorReport: ErrorReport): void {
        const logLevel = this.getLogLevel(errorReport.severity);
        const message = `[${errorReport.context.component}] ${errorReport.error.name}: ${errorReport.error.message}`;
        
        console[logLevel](`V1b3-Sama Error [${errorReport.id}]:`, message, {
            operation: errorReport.context.operation,
            severity: errorReport.severity,
            timestamp: new Date(errorReport.timestamp).toISOString()
        });
    }

    private getLogLevel(severity: string): 'error' | 'warn' | 'info' {
        switch (severity) {
            case 'critical':
            case 'high':
                return 'error';
            case 'medium':
                return 'warn';
            default:
                return 'info';
        }
    }

    private setupGlobalErrorHandlers(): void {
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            const error = reason instanceof Error ? reason : new Error(String(reason));
            this.reportUnhandledException(error, 'global', { 
                type: 'unhandledRejection',
                promise: promise.toString()
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            this.reportUnhandledException(error, 'global', { 
                type: 'uncaughtException'
            });
        });
    }

    private loadPersistedErrors(): void {
        try {
            if (fs.existsSync(this.logFilePath)) {
                const data = fs.readFileSync(this.logFilePath, 'utf8');
                const parsed = JSON.parse(data);
                this.errorLog = parsed.errors || [];
                
                // Keep only recent errors (last 7 days)
                const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
                this.errorLog = this.errorLog.filter(e => e.timestamp > weekAgo);
            }
        } catch (error) {
            console.warn('Failed to load persisted errors:', error);
            this.errorLog = [];
        }
    }

    private persistErrors(): void {
        try {
            // Ensure directory exists
            const dir = path.dirname(this.logFilePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            const data = {
                sessionId: this.sessionId,
                lastUpdated: new Date().toISOString(),
                errors: this.errorLog
            };

            fs.writeFileSync(this.logFilePath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Failed to persist errors:', error);
        }
    }

    private startPeriodicCleanup(): void {
        // Clean up old errors every hour
        setInterval(() => {
            const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
            const originalLength = this.errorLog.length;
            this.errorLog = this.errorLog.filter(e => e.timestamp > weekAgo);
            
            if (this.errorLog.length !== originalLength) {
                this.persistErrors();
            }
        }, 60 * 60 * 1000); // 1 hour
    }

    private formatErrorReport(stats: ErrorStats): string {
        return `
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${stats.totalErrors}</div>
                    <div>Total Errors (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(stats.errorsByComponent).length}</div>
                    <div>Affected Components</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.errorsBySeverity.critical || 0}</div>
                    <div>Critical Errors</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.topErrors.length}</div>
                    <div>Unique Error Types</div>
                </div>
            </div>

            <h2>Top Errors</h2>
            ${stats.topErrors.map(error => `
                <div class="error-card">
                    <strong>${error.message}</strong><br>
                    <small>Count: ${error.count} | Last seen: ${new Date(error.lastSeen).toLocaleString()}</small>
                </div>
            `).join('')}

            <h2>Recent Errors</h2>
            ${stats.recentErrors.slice(-10).map(error => `
                <div class="error-card severity-${error.severity}">
                    <strong>[${error.context.component}] ${error.error.name}</strong><br>
                    ${error.error.message}<br>
                    <small>
                        ${new Date(error.timestamp).toLocaleString()} | 
                        Operation: ${error.context.operation} | 
                        Severity: ${error.severity}
                    </small>
                    ${error.error.stack ? `<pre>${error.error.stack}</pre>` : ''}
                </div>
            `).join('')}
        `;
    }

    /**
     * Dispose of the error monitoring service
     */
    public dispose(): void {
        this.persistErrors();
        this.isEnabled = false;
    }
}
