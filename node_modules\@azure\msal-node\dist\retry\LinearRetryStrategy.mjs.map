{"version": 3, "file": "LinearRetryStrategy.mjs", "sources": ["../../src/retry/LinearRetryStrategy.ts"], "sourcesContent": [null], "names": [], "mappings": ";;AAAA;;;AAGG;MAIU,mBAAmB,CAAA;AAC5B;;;;;;;AAOG;IACI,cAAc,CACjB,WAA+C,EAC/C,YAAoB,EAAA;QAEpB,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;;AAGD,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC;AAE/D;;;AAGG;AACH,QAAA,IAAI,KAAK,CAAC,aAAa,CAAC,EAAE;;YAEtB,aAAa;AACT,gBAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;AAC9D,SAAA;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;KAChD;AACJ;;;;"}