{"version": 3, "file": "Backoff.js", "sourceRoot": "", "sources": ["../../src/backoff/Backoff.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAoBA,oDAAkC;AAClC,oDAAkC;AAClC,uDAAqC;AACrC,iEAA+C;AAC/C,oDAAkC", "sourcesContent": ["/**\n * A generic type that returns backoff intervals.\n */\nexport interface IBackoffFactory<T> {\n  /**\n   * Returns the first backoff duration.\n   */\n  next(context: T): IBackoff<T>;\n}\n\n/**\n * A generic type that returns backoff intervals.\n */\nexport interface IBackoff<T> extends IBackoffFactory<T> {\n  /**\n   * Returns the number of milliseconds to wait for this backoff attempt.\n   */\n  readonly duration: number;\n}\n\nexport * from './ConstantBackoff';\nexport * from './DelegateBackoff';\nexport * from './ExponentialBackoff';\nexport * from './ExponentialBackoffGenerators';\nexport * from './IterableBackoff';\n"]}