// Mock service implementations for testing
// Provides complete mock implementations of all service interfaces

import * as sinon from 'sinon';
import { ILLMService, LLMMessage, LLMResponse } from '../../interfaces/ILLMService';
import { IFileSystemService, FileChange, FileStats, SearchOptions, DiffResult } from '../../interfaces/IFileSystemService';
import { ITerminalService, CommandResult, CommandOptions, ProcessInfo, SystemInfo } from '../../interfaces/ITerminalService';
import { ICacheService, CacheEntry, CacheOptions, CacheStats } from '../../interfaces/ICacheService';
import { IMcpClientService, McpConnectionConfig, McpContextData, McpToolCall, McpToolResult, McpResourceContent } from '../../interfaces/IMcpClientService';

export class MockLLMService implements ILLMService {
    public sendMessage = sinon.stub().resolves({
        content: 'Mock response',
        tokensUsed: { input: 10, output: 20 },
        cost: 0.001,
        model: 'mock-model'
    } as LLMResponse);

    public sendMessageStream = sinon.stub().resolves({
        content: 'Mock streaming response',
        tokensUsed: { input: 10, output: 20 },
        cost: 0.001,
        model: 'mock-model'
    } as LLMResponse);

    public stopStream = sinon.stub();
    public pauseStream = sinon.stub();
    public resumeStream = sinon.stub();
    public cancelStream = sinon.stub();
    public getActiveStreams = sinon.stub().returns([]);

    public getConversationHistory = sinon.stub().returns([]);
    public clearConversationHistory = sinon.stub();
    public addToConversationHistory = sinon.stub();
    public updateConversationHistory = sinon.stub();
    public getConversationCost = sinon.stub().returns(0);
    public getConversationTokens = sinon.stub().returns({ input: 0, output: 0 });
}

export class MockFileSystemService implements IFileSystemService {
    public readFile = sinon.stub().resolves('mock file content');
    public writeFile = sinon.stub().resolves();
    public createFile = sinon.stub().resolves();
    public deleteFile = sinon.stub().resolves();
    public fileExists = sinon.stub().resolves(true);
    public listFiles = sinon.stub().resolves(['file1.txt', 'file2.txt']);
    public getFileStats = sinon.stub().resolves({
        size: 1024,
        created: new Date(),
        modified: new Date(),
        isDirectory: false,
        isFile: true
    } as FileStats);
    public watchFiles = sinon.stub().returns('mock-watcher-id');
    public stopWatching = sinon.stub();
    public generateDiff = sinon.stub().returns({
        additions: 1,
        deletions: 0,
        diff: '+mock diff'
    } as DiffResult);
    public generateFileDiff = sinon.stub().resolves({
        path: 'mock-file.txt',
        diff: '+mock diff',
        additions: 1,
        deletions: 0
    });
    public searchFiles = sinon.stub().resolves(['match1.txt', 'match2.txt']);
    public getWorkspaceRoot = sinon.stub().returns('/mock/workspace');
    public getRelativePath = sinon.stub().returns('relative/path');
    public createDirectory = sinon.stub().resolves();
    public dispose = sinon.stub();
}

export class MockTerminalService implements ITerminalService {
    public executeCommand = sinon.stub().resolves({
        stdout: 'mock output',
        stderr: '',
        exitCode: 0,
        command: 'mock command',
        duration: 100
    } as CommandResult);

    public executeCommandStream = sinon.stub().resolves({
        stdout: 'mock output',
        stderr: '',
        exitCode: 0,
        command: 'mock command',
        duration: 100
    } as CommandResult);

    public killProcess = sinon.stub().resolves();
    public checkCommandExists = sinon.stub().resolves(true);
    public getSystemInfo = sinon.stub().resolves({
        platform: 'test',
        arch: 'x64',
        nodeVersion: '16.0.0',
        vsCodeVersion: '1.60.0',
        extensionVersion: '1.0.0',
        workspaceRoot: '/mock/workspace'
    } as SystemInfo);

    public getRunningProcesses = sinon.stub().returns([]);
    public createTerminal = sinon.stub().returns('mock-terminal-id');
    public sendToTerminal = sinon.stub();
    public showTerminal = sinon.stub();
    public disposeTerminal = sinon.stub();
    public dispose = sinon.stub();
}

export class MockCacheService implements ICacheService {
    public get = sinon.stub().returns(undefined);
    public set = sinon.stub();
    public delete = sinon.stub().returns(true);
    public clear = sinon.stub();
    public has = sinon.stub().returns(false);
    public size = sinon.stub().returns(0);
    public getOrSet = sinon.stub().resolves(undefined);
    public invalidatePattern = sinon.stub();
    public cleanup = sinon.stub();
    public keys = sinon.stub().returns([]);
    public getStats = sinon.stub().returns({
        hits: 0,
        misses: 0,
        size: 0,
        maxSize: 1000,
        hitRate: 0,
        oldestEntry: Date.now(),
        newestEntry: Date.now()
    } as CacheStats);
    public dispose = sinon.stub();
}

export class MockMcpClientService implements IMcpClientService {
    public connect = sinon.stub().resolves();
    public disconnect = sinon.stub().resolves();
    public isConnected = sinon.stub().returns(false);
    public discoverTools = sinon.stub().resolves([]);
    public callTool = sinon.stub().resolves({
        success: true,
        result: 'mock result'
    } as McpToolResult);
    public getResources = sinon.stub().resolves([]);
    public getServers = sinon.stub().resolves({ mcpServers: [] });
    public toggleServer = sinon.stub().resolves({ mcpServers: [] });
    public updateTimeout = sinon.stub().resolves({ mcpServers: [] });
    public addRemoteServer = sinon.stub().resolves({ mcpServers: [] });
    public downloadServer = sinon.stub().resolves();
    public getResourceTemplates = sinon.stub().resolves([]);
    public readResource = sinon.stub().resolves({
        uri: 'mock://resource',
        content: 'mock content',
        mimeType: 'text/plain'
    } as McpResourceContent);
    public sendContext = sinon.stub().resolves();
    public getServerStatus = sinon.stub().returns(undefined);
    public onServerStatusChange = sinon.stub().returns(() => {});
    public onToolsDiscovered = sinon.stub().returns(() => {});
    public dispose = sinon.stub();
}

// Import real services for test factory
import { LLMService } from '../../services/llmService';
import { FileSystemService } from '../../services/fileSystemService';
import { TerminalService } from '../../services/terminalService';
import { CacheService } from '../../services/CacheService';
import { McpClientService } from '../../services/McpClientService';
import * as vscode from 'vscode';

/**
 * Create a complete set of mock services for testing (DEPRECATED - use createTestServices instead)
 * @deprecated Use createTestServices for real service instances configured for testing
 */
export function createMockServices() {
    return {
        llmService: new MockLLMService(),
        fileSystemService: new MockFileSystemService(),
        terminalService: new MockTerminalService(),
        cacheService: new MockCacheService(),
        mcpClientService: new MockMcpClientService()
    };
}

/**
 * Create real services configured for test environment
 * This replaces mock services with actual implementations for more realistic testing
 */
export function createTestServices(context: vscode.ExtensionContext) {
    // Configure cache service for testing with shorter TTL and smaller size
    const cacheService = new CacheService({
        ttl: 1000, // 1 second for fast test cleanup
        maxSize: 10, // Small cache for tests
        cleanupInterval: 500 // Frequent cleanup
    });

    // Create real LLM service (will use test API keys if available)
    const llmService = new LLMService();

    // Create real file system service
    const fileSystemService = new FileSystemService(context);

    // Create real terminal service
    const terminalService = new TerminalService();

    // Create real MCP client service
    const mcpClientService = new McpClientService();

    return {
        llmService,
        fileSystemService,
        terminalService,
        cacheService,
        mcpClientService,
        // Helper method to configure test-specific settings
        configureForTesting: () => {
            // Set test-specific configurations
            // For example, disable actual file operations in tests if needed
            // This can be expanded based on specific test requirements
        }
    };
}

/**
 * Reset all mock services to their initial state
 * @deprecated Use disposeTestServices for real service cleanup
 */
export function resetMockServices(services: ReturnType<typeof createMockServices>) {
    Object.values(services).forEach(service => {
        Object.values(service).forEach(method => {
            if (typeof method === 'object' && method && 'reset' in method) {
                (method as any).reset();
            }
        });
    });
}

/**
 * Properly dispose of test services and clean up resources
 */
export function disposeTestServices(services: ReturnType<typeof createTestServices>) {
    // Dispose cache service
    if (services.cacheService && typeof services.cacheService.dispose === 'function') {
        services.cacheService.dispose();
    }

    // Dispose MCP client service
    if (services.mcpClientService && typeof services.mcpClientService.disconnect === 'function') {
        // Disconnect all servers (if any are connected)
        // Note: In a real implementation, you'd iterate through connected servers
        // For test cleanup, we'll just call disconnect with a dummy server name
        try {
            services.mcpClientService.disconnect('test-server').catch(console.error);
        } catch (error) {
            // Ignore errors during test cleanup
        }
    }

    // Clean up any other resources as needed
}
