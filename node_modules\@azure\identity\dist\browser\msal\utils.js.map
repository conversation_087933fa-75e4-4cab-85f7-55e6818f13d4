{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/msal/utils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,2BAA2B,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAEvF,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAC1F,OAAO,EAAE,UAAU,IAAI,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAEpF,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAGrD,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAMvC;;GAEG;AACH,MAAM,MAAM,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AAEjD;;;GAGG;AACH,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEhD;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAClC,MAAyB,EACzB,SAA4B,EAC5B,eAAiC;IAEjC,MAAM,KAAK,GAAG,CAAC,OAAe,EAAS,EAAE;QACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,OAAO,IAAI,2BAA2B,CAAC;YACrC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACjD,eAAe;YACf,OAAO;SACR,CAAC,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACzB,MAAM,KAAK,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC3B,MAAM,KAAK,CAAC,yCAAyC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAoC;IACnE,IAAI,aAAa,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC;IAE3C,IAAI,CAAC,aAAa,IAAI,UAAU,EAAE,CAAC;QACjC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;IACnD,CAAC;IAED,OAAO,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,oBAAoB,CAAC;AAC/C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,QAAgB,EAAE,IAAa;IAC1D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,IAAI,GAAG,oBAAoB,CAAC;IAC9B,CAAC;IACD,IAAI,IAAI,MAAM,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,GAAG,QAAQ,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,IAAI,IAAI,QAAQ,EAAE,CAAC;IAC/B,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CACjC,QAAgB,EAChB,aAAqB,EACrB,wBAAkC;IAElC,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,aAAa,CAAC,IAAI,wBAAwB,EAAE,CAAC;QACvE,OAAO,CAAC,aAAa,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAIhC,CAAC,UAA4B,EAAE,WAA+B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAC7F,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAQ,EAAE;IACpC,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO;IACT,CAAC;IACD,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK;YAC5B,UAAU,CAAC,IAAI,CAAC,QAAQ,QAAQ,cAAc,OAAO,EAAE,CAAC,CAAC;YACzD,OAAO;QACT,KAAK,UAAU,CAAC,QAAQ,CAAC,IAAI;YAC3B,UAAU,CAAC,IAAI,CAAC,QAAQ,QAAQ,qBAAqB,OAAO,EAAE,CAAC,CAAC;YAChE,OAAO;QACT,KAAK,UAAU,CAAC,QAAQ,CAAC,OAAO;YAC9B,UAAU,CAAC,IAAI,CAAC,QAAQ,QAAQ,wBAAwB,OAAO,EAAE,CAAC,CAAC;YACnE,OAAO;QACT,KAAK,UAAU,CAAC,QAAQ,CAAC,OAAO;YAC9B,UAAU,CAAC,IAAI,CAAC,QAAQ,QAAQ,gBAAgB,OAAO,EAAE,CAAC,CAAC;YAC3D,OAAO;IACX,CAAC;AACH,CAAC,CAAC;AAEJ;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,QAAmC;IACjE,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,OAAO;YACV,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;QACnC,KAAK,MAAM;YACT,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;QAClC,KAAK,SAAS;YACZ,OAAO,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QACrC,KAAK,SAAS;YACZ,OAAO,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QACrC;YACE,4CAA4C;YAC5C,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;IACpC,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,cAAc,EAAE,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,MAAgB,EAChB,KAAY,EACZ,eAAiC;IAEjC,IACE,KAAK,CAAC,IAAI,KAAK,WAAW;QAC1B,KAAK,CAAC,IAAI,KAAK,iBAAiB;QAChC,KAAK,CAAC,IAAI,KAAK,kBAAkB,EACjC,CAAC;QACD,MAAM,SAAS,GAAG,KAA6B,CAAC;QAChD,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC;YAC5B,KAAK,4BAA4B;gBAC/B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAChD,OAAO,IAAI,0BAA0B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvD,KAAK,+BAA+B;gBAClC,OAAO,IAAI,UAAU,CAAC,oDAAoD,CAAC,CAAC;YAC9E,KAAK,kBAAkB,CAAC;YACxB,KAAK,sBAAsB,CAAC;YAC5B,KAAK,gBAAgB;gBACnB,MAAM,CAAC,IAAI,CACT,WAAW,CAAC,MAAM,EAAE,qCAAqC,SAAS,CAAC,SAAS,EAAE,CAAC,CAChF,CAAC;gBACF,MAAM;YACR;gBACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC9E,MAAM;QACV,CAAC;IACH,CAAC;IACD,IACE,KAAK,CAAC,IAAI,KAAK,0BAA0B;QACzC,KAAK,CAAC,IAAI,KAAK,+BAA+B;QAC9C,KAAK,CAAC,IAAI,KAAK,YAAY;QAC3B,KAAK,CAAC,IAAI,KAAK,qBAAqB,EACpC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,MAAM,CAAC,IAAI,CACT,WAAW,CACT,MAAM,EACN,iCAAiC,KAAK,CAAC,OAAO,sBAC3C,KAAa,CAAC,UACjB,EAAE,CACH,CACF,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,2BAA2B,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AAC9F,CAAC;AAED,kBAAkB;AAClB,MAAM,UAAU,YAAY,CAAC,OAA6B;IACxD,OAAO;QACL,cAAc,EAAE,OAAO,CAAC,aAAa;QACrC,WAAW,EAAE,OAAO,CAAC,SAAS;QAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ;KAC3B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,QAAgB,EAAE,OAAwB;;IACrE,MAAM,MAAM,GAAG;QACb,SAAS,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,gBAAgB;QAClD,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,eAAe;QAC7C,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,QAAQ;QACR,OAAO,EAAE,iCAAiC;KAC3C,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,6BAA6B,CAAC,MAA4B;IACxE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,+BAA+B,CAAC,gBAAwB;IACtE,MAAM,MAAM,GAAgD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAEzF,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,KAAK,iCAAiC,EAAE,CAAC;QAC3E,MAAM,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthenticationR<PERSON>ord, MsalAccountInfo, MsalToken, ValidMsalToken } from \"./types.js\";\nimport { AuthenticationRequiredError, CredentialUnavailableError } from \"../errors.js\";\nimport type { CredentialLogger } from \"../util/logging.js\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\nimport { DefaultAuthority, DefaultAuthorityHost, DefaultTenantId } from \"../constants.js\";\nimport { randomUUID as coreRandomUUID, isNode, isNodeLike } from \"@azure/core-util\";\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport type { AzureLogLevel } from \"@azure/logger\";\nimport type { GetTokenOptions } from \"@azure/core-auth\";\nimport { msalCommon } from \"./msal.js\";\n\nexport interface ILoggerCallback {\n  (level: msalCommon.LogLevel, message: string, containsPii: boolean): void;\n}\n\n/**\n * @internal\n */\nconst logger = credentialLogger(\"IdentityUtils\");\n\n/**\n * Latest AuthenticationRecord version\n * @internal\n */\nconst LatestAuthenticationRecordVersion = \"1.0\";\n\n/**\n * Ensures the validity of the MSAL token\n * @internal\n */\nexport function ensureValidMsalToken(\n  scopes: string | string[],\n  msalToken?: MsalToken | null,\n  getTokenOptions?: GetTokenOptions,\n): asserts msalToken is ValidMsalToken {\n  const error = (message: string): Error => {\n    logger.getToken.info(message);\n    return new AuthenticationRequiredError({\n      scopes: Array.isArray(scopes) ? scopes : [scopes],\n      getTokenOptions,\n      message,\n    });\n  };\n  if (!msalToken) {\n    throw error(\"No response\");\n  }\n  if (!msalToken.expiresOn) {\n    throw error(`Response had no \"expiresOn\" property.`);\n  }\n  if (!msalToken.accessToken) {\n    throw error(`Response had no \"accessToken\" property.`);\n  }\n}\n\n/**\n * Returns the authority host from either the options bag or the AZURE_AUTHORITY_HOST environment variable.\n *\n * Defaults to {@link DefaultAuthorityHost}.\n * @internal\n */\nexport function getAuthorityHost(options?: { authorityHost?: string }): string {\n  let authorityHost = options?.authorityHost;\n\n  if (!authorityHost && isNodeLike) {\n    authorityHost = process.env.AZURE_AUTHORITY_HOST;\n  }\n\n  return authorityHost ?? DefaultAuthorityHost;\n}\n\n/**\n * Generates a valid authority by combining a host with a tenantId.\n * @internal\n */\nexport function getAuthority(tenantId: string, host?: string): string {\n  if (!host) {\n    host = DefaultAuthorityHost;\n  }\n  if (new RegExp(`${tenantId}/?$`).test(host)) {\n    return host;\n  }\n  if (host.endsWith(\"/\")) {\n    return host + tenantId;\n  } else {\n    return `${host}/${tenantId}`;\n  }\n}\n\n/**\n * Generates the known authorities.\n * If the Tenant Id is `adfs`, the authority can't be validated since the format won't match the expected one.\n * For that reason, we have to force MSAL to disable validating the authority\n * by sending it within the known authorities in the MSAL configuration.\n * @internal\n */\nexport function getKnownAuthorities(\n  tenantId: string,\n  authorityHost: string,\n  disableInstanceDiscovery?: boolean,\n): string[] {\n  if ((tenantId === \"adfs\" && authorityHost) || disableInstanceDiscovery) {\n    return [authorityHost];\n  }\n  return [];\n}\n\n/**\n * Generates a logger that can be passed to the MSAL clients.\n * @param credLogger - The logger of the credential.\n * @internal\n */\nexport const defaultLoggerCallback: (\n  logger: CredentialLogger,\n  platform?: \"Node\" | \"Browser\",\n) => ILoggerCallback =\n  (credLogger: CredentialLogger, platform: \"Node\" | \"Browser\" = isNode ? \"Node\" : \"Browser\") =>\n  (level, message, containsPii): void => {\n    if (containsPii) {\n      return;\n    }\n    switch (level) {\n      case msalCommon.LogLevel.Error:\n        credLogger.info(`MSAL ${platform} V2 error: ${message}`);\n        return;\n      case msalCommon.LogLevel.Info:\n        credLogger.info(`MSAL ${platform} V2 info message: ${message}`);\n        return;\n      case msalCommon.LogLevel.Verbose:\n        credLogger.info(`MSAL ${platform} V2 verbose message: ${message}`);\n        return;\n      case msalCommon.LogLevel.Warning:\n        credLogger.info(`MSAL ${platform} V2 warning: ${message}`);\n        return;\n    }\n  };\n\n/**\n * @internal\n */\nexport function getMSALLogLevel(logLevel: AzureLogLevel | undefined): msalCommon.LogLevel {\n  switch (logLevel) {\n    case \"error\":\n      return msalCommon.LogLevel.Error;\n    case \"info\":\n      return msalCommon.LogLevel.Info;\n    case \"verbose\":\n      return msalCommon.LogLevel.Verbose;\n    case \"warning\":\n      return msalCommon.LogLevel.Warning;\n    default:\n      // default msal logging level should be Info\n      return msalCommon.LogLevel.Info;\n  }\n}\n\n/**\n * Wraps core-util's randomUUID in order to allow for mocking in tests.\n * This prepares the library for the upcoming core-util update to ESM.\n *\n * @internal\n * @returns A string containing a random UUID\n */\nexport function randomUUID(): string {\n  return coreRandomUUID();\n}\n\n/**\n * Handles MSAL errors.\n */\nexport function handleMsalError(\n  scopes: string[],\n  error: Error,\n  getTokenOptions?: GetTokenOptions,\n): Error {\n  if (\n    error.name === \"AuthError\" ||\n    error.name === \"ClientAuthError\" ||\n    error.name === \"BrowserAuthError\"\n  ) {\n    const msalError = error as msalCommon.AuthError;\n    switch (msalError.errorCode) {\n      case \"endpoints_resolution_error\":\n        logger.info(formatError(scopes, error.message));\n        return new CredentialUnavailableError(error.message);\n      case \"device_code_polling_cancelled\":\n        return new AbortError(\"The authentication has been aborted by the caller.\");\n      case \"consent_required\":\n      case \"interaction_required\":\n      case \"login_required\":\n        logger.info(\n          formatError(scopes, `Authentication returned errorCode ${msalError.errorCode}`),\n        );\n        break;\n      default:\n        logger.info(formatError(scopes, `Failed to acquire token: ${error.message}`));\n        break;\n    }\n  }\n  if (\n    error.name === \"ClientConfigurationError\" ||\n    error.name === \"BrowserConfigurationAuthError\" ||\n    error.name === \"AbortError\" ||\n    error.name === \"AuthenticationError\"\n  ) {\n    return error;\n  }\n  if (error.name === \"NativeAuthError\") {\n    logger.info(\n      formatError(\n        scopes,\n        `Error from the native broker: ${error.message} with status code: ${\n          (error as any).statusCode\n        }`,\n      ),\n    );\n    return error;\n  }\n  return new AuthenticationRequiredError({ scopes, getTokenOptions, message: error.message });\n}\n\n// transformations\nexport function publicToMsal(account: AuthenticationRecord): msalCommon.AccountInfo {\n  return {\n    localAccountId: account.homeAccountId,\n    environment: account.authority,\n    username: account.username,\n    homeAccountId: account.homeAccountId,\n    tenantId: account.tenantId,\n  };\n}\n\nexport function msalToPublic(clientId: string, account: MsalAccountInfo): AuthenticationRecord {\n  const record = {\n    authority: account.environment ?? DefaultAuthority,\n    homeAccountId: account.homeAccountId,\n    tenantId: account.tenantId || DefaultTenantId,\n    username: account.username,\n    clientId,\n    version: LatestAuthenticationRecordVersion,\n  };\n  return record;\n}\n\n/**\n * Serializes an `AuthenticationRecord` into a string.\n *\n * The output of a serialized authentication record will contain the following properties:\n *\n * - \"authority\"\n * - \"homeAccountId\"\n * - \"clientId\"\n * - \"tenantId\"\n * - \"username\"\n * - \"version\"\n *\n * To later convert this string to a serialized `AuthenticationRecord`, please use the exported function `deserializeAuthenticationRecord()`.\n */\nexport function serializeAuthenticationRecord(record: AuthenticationRecord): string {\n  return JSON.stringify(record);\n}\n\n/**\n * Deserializes a previously serialized authentication record from a string into an object.\n *\n * The input string must contain the following properties:\n *\n * - \"authority\"\n * - \"homeAccountId\"\n * - \"clientId\"\n * - \"tenantId\"\n * - \"username\"\n * - \"version\"\n *\n * If the version we receive is unsupported, an error will be thrown.\n *\n * At the moment, the only available version is: \"1.0\", which is always set when the authentication record is serialized.\n *\n * @param serializedRecord - Authentication record previously serialized into string.\n * @returns AuthenticationRecord.\n */\nexport function deserializeAuthenticationRecord(serializedRecord: string): AuthenticationRecord {\n  const parsed: AuthenticationRecord & { version?: string } = JSON.parse(serializedRecord);\n\n  if (parsed.version && parsed.version !== LatestAuthenticationRecordVersion) {\n    throw Error(\"Unsupported AuthenticationRecord version\");\n  }\n\n  return parsed;\n}\n"]}