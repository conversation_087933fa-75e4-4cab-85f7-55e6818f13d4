{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/errors.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAExD;;;;;;;GAOG;AACH,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IAEzB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IAEtB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;;GAGG;AACH,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,MAAM,CAAC;IACd,iBAAiB,EAAE,MAAM,CAAC;IAC1B,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAUD;;GAEG;AACH,eAAO,MAAM,8BAA8B,+BAA+B,CAAC;AAE3E;;;;GAIG;AACH,qBAAa,0BAA2B,SAAQ,KAAK;gBACvC,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE;CAK5D;AAED;;GAEG;AACH,eAAO,MAAM,uBAAuB,wBAAwB,CAAC;AAE7D;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,KAAK;IAC5C;;OAEG;IACH,SAAgB,UAAU,EAAE,MAAM,CAAC;IAEnC;;OAEG;IACH,SAAgB,aAAa,EAAE,aAAa,CAAC;gBAG3C,UAAU,EAAE,MAAM,EAClB,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI,EAC7C,OAAO,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE;CA8ChC;AAED;;GAEG;AACH,eAAO,MAAM,gCAAgC,iCAAiC,CAAC;AAE/E;;;GAGG;AACH,qBAAa,4BAA6B,SAAQ,KAAK;IACrD;;;OAGG;IACI,MAAM,EAAE,GAAG,EAAE,CAAC;gBAET,MAAM,EAAE,GAAG,EAAE,EAAE,YAAY,CAAC,EAAE,MAAM;CAQjD;AAaD;;GAEG;AACH,MAAM,WAAW,kCAAkC;IACjD;;OAEG;IACH,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB;;OAEG;IACH,eAAe,CAAC,EAAE,eAAe,CAAC;IAClC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB;AAED;;GAEG;AACH,qBAAa,2BAA4B,SAAQ,KAAK;IACpD;;OAEG;IACI,MAAM,EAAE,MAAM,EAAE,CAAC;IACxB;;OAEG;IACI,eAAe,CAAC,EAAE,eAAe,CAAC;;IAGvC;;OAEG;IACH,OAAO,EAAE,kCAAkC;CAW9C"}