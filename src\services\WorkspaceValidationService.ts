import * as vscode from 'vscode';
import * as path from 'path';

/**
 * Centralized workspace validation service for V1b3-Sama extension
 * Ensures all file operations are strictly scoped to the current VS Code workspace
 */
export class WorkspaceValidationService {
    private static instance: WorkspaceValidationService;
    private _workspaceFolder: vscode.WorkspaceFolder | undefined;
    private _workspaceWatcher: vscode.Disposable | undefined;

    private constructor() {
        this.initialize();
    }

    public static getInstance(): WorkspaceValidationService {
        if (!WorkspaceValidationService.instance) {
            WorkspaceValidationService.instance = new WorkspaceValidationService();
        }
        return WorkspaceValidationService.instance;
    }

    private initialize(): void {
        // Set initial workspace folder
        this.updateWorkspaceFolder();

        // Watch for workspace changes
        this._workspaceWatcher = vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.updateWorkspaceFolder();
        });
    }

    private updateWorkspaceFolder(): void {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        this._workspaceFolder = workspaceFolders && workspaceFolders.length > 0 ? workspaceFolders[0] : undefined;
    }

    /**
     * Get the current workspace folder
     */
    public getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
        return this._workspaceFolder;
    }

    /**
     * Get the workspace root path
     */
    public getWorkspaceRoot(): string | undefined {
        return this._workspaceFolder?.uri.fsPath;
    }

    /**
     * Check if a workspace is currently open (with real-time detection)
     */
    public hasWorkspace(): boolean {
        // Always check current state in case of timing issues
        const currentWorkspaceFolders = vscode.workspace.workspaceFolders;
        const hasCurrentWorkspace = currentWorkspaceFolders && currentWorkspaceFolders.length > 0;

        // If we don't have a cached workspace but VS Code shows one, update our cache
        if (!this._workspaceFolder && hasCurrentWorkspace) {
            this.updateWorkspaceFolder();
        }

        return this._workspaceFolder !== undefined;
    }

    /**
     * Force refresh workspace detection
     */
    public refreshWorkspaceDetection(): void {
        this.updateWorkspaceFolder();
    }

    /**
     * Validate that a workspace is open and throw error if not
     */
    public requireWorkspace(): vscode.WorkspaceFolder {
        // Force refresh workspace detection before checking
        const currentWorkspaceFolders = vscode.workspace.workspaceFolders;

        if (currentWorkspaceFolders && currentWorkspaceFolders.length > 0) {
            // Update our cache if VS Code has a workspace but we don't
            if (!this._workspaceFolder) {
                this._workspaceFolder = currentWorkspaceFolders[0];
            }

            return this._workspaceFolder;
        }

        throw new Error('No workspace folder is open. Please open a folder in VS Code to perform file operations.');
    }

    /**
     * Validate a file path is within workspace boundaries
     */
    public validateFilePath(filePath: string): { valid: boolean; error?: string; resolvedPath?: string } {
        try {
            // Require workspace
            const workspaceFolder = this.requireWorkspace();

            // Basic path validation
            if (!filePath || filePath.trim() === '') {
                return { valid: false, error: 'File path cannot be empty' };
            }

            // Check for dangerous path patterns
            if (filePath.includes('..')) {
                return { valid: false, error: 'Path traversal (..) is not allowed for security reasons' };
            }

            if (path.isAbsolute(filePath)) {
                return { valid: false, error: 'Absolute paths are not allowed. Use relative paths within the workspace.' };
            }

            // Resolve the full path
            const resolvedPath = path.resolve(workspaceFolder.uri.fsPath, filePath);

            // Ensure the resolved path is within workspace boundaries
            if (!resolvedPath.startsWith(workspaceFolder.uri.fsPath)) {
                return {
                    valid: false,
                    error: `File path '${filePath}' resolves outside the workspace boundary. All operations must be within the workspace folder.`
                };
            }

            // Additional security checks for malicious patterns
            // Note: We don't check normalizedPath !== filePath because path separators
            // may differ between platforms (/ vs \) and this causes false positives
            const suspiciousPatterns = [
                /[\x00-\x1f\x7f]/,  // Control characters
                /[<>:"|?*]/,        // Invalid filename characters on Windows
                /^\s|\s$/,          // Leading/trailing whitespace
                /\/{2,}/,           // Multiple consecutive slashes
                /\\{2,}/            // Multiple consecutive backslashes
            ];

            for (const pattern of suspiciousPatterns) {
                if (pattern.test(filePath)) {
                    return {
                        valid: false,
                        error: `File path contains invalid characters or sequences. Use clean relative paths.`
                    };
                }
            }

            return { valid: true, resolvedPath };

        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : 'Unknown validation error' 
            };
        }
    }

    /**
     * Validate multiple file paths at once
     */
    public validateFilePaths(filePaths: string[]): { valid: boolean; errors: string[]; validPaths: string[] } {
        const errors: string[] = [];
        const validPaths: string[] = [];

        for (const filePath of filePaths) {
            const validation = this.validateFilePath(filePath);
            if (validation.valid && validation.resolvedPath) {
                validPaths.push(validation.resolvedPath);
            } else {
                errors.push(`${filePath}: ${validation.error}`);
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            validPaths
        };
    }

    /**
     * Get workspace-relative path from absolute path
     */
    public getRelativePath(absolutePath: string): string {
        const workspaceRoot = this.getWorkspaceRoot();
        if (workspaceRoot && absolutePath.startsWith(workspaceRoot)) {
            return path.relative(workspaceRoot, absolutePath);
        }
        return absolutePath;
    }

    /**
     * Get workspace context information for UI display
     */
    public getWorkspaceContext(): {
        hasWorkspace: boolean;
        workspaceName?: string;
        workspacePath?: string;
        fileCount?: number;
    } {
        if (!this._workspaceFolder) {
            return { hasWorkspace: false };
        }

        return {
            hasWorkspace: true,
            workspaceName: this._workspaceFolder.name,
            workspacePath: this._workspaceFolder.uri.fsPath,
            // File count could be added later if needed
        };
    }

    /**
     * Create a user-friendly error message for workspace issues
     */
    public createWorkspaceErrorMessage(operation: string): string {
        if (!this.hasWorkspace()) {
            return `Cannot ${operation}: No workspace folder is open. Please open a folder in VS Code first.`;
        }
        return `Cannot ${operation}: Operation failed workspace validation.`;
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        if (this._workspaceWatcher) {
            this._workspaceWatcher.dispose();
            this._workspaceWatcher = undefined;
        }
    }
}

// Export singleton instance
export const workspaceValidation = WorkspaceValidationService.getInstance();
