{"version": 3, "file": "ExponentialBackoffGenerators.js", "sourceRoot": "", "sources": ["../../../src/backoff/ExponentialBackoffGenerators.ts"], "names": [], "mappings": "AAUA;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAwB,CAAC,QAAQ,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;IAC/E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,QAAQ,CAAC;IAChE,QAAQ,GAAG,CAAC;CACb,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAwB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACzE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAwB,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;IAC5E,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,GAAG,GAAG,CAAC;AAEpB;;;;;GAKG;AACH,MAAM,eAAe,GAAG,CAAC,GAAG,GAAG,CAAC;AAEhC;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAkC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IAC3F,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,MAAM,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/E,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACnF,OAAO;QACL,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,eAAe,GAAG,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC;QAC1F,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;KACpB,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { IExponentialBackoffOptions } from '../backoff/ExponentialBackoff';\n\n/**\n * Function used to get the next delay.\n */\nexport type GeneratorFn<S> = (\n  state: S | undefined,\n  options: IExponentialBackoffOptions<S>,\n) => [number, S];\n\n/**\n * Generator that creates a backoff with no jitter.\n */\nexport const noJitterGenerator: GeneratorFn<number> = (attempts = 0, options) => [\n  Math.min(options.maxDelay, options.initialDelay * 2 ** attempts),\n  attempts + 1,\n];\n\n/**\n * Generator that randomizes an exponential backoff between [0, delay).\n */\nexport const fullJitterGenerator: GeneratorFn<number> = (state, options) => {\n  const [delay, next] = noJitterGenerator(state, options);\n  return [Math.floor(Math.random() * delay), next];\n};\n\n/**\n * Generator that randomizes an exponential backoff between [0, delay).\n */\nexport const halfJitterGenerator: GeneratorFn<number> = (attempts, options) => {\n  const [delay, next] = noJitterGenerator(attempts, options);\n  return [Math.floor((delay + Math.random() * delay) / 2), next];\n};\n\n/**\n * A factor used within the formula to help smooth the first calculated delay.\n */\nconst pFactor = 4.0;\n\n/**\n *  A factor used to scale the median values of the retry times generated by\n * the formula to be _near_ whole seconds, to aid user comprehension. This\n * factor allows the median values to fall approximately at 1, 2, 4 etc\n * seconds, instead of 1.4, 2.8, 5.6, 11.2.\n */\nconst rpScalingFactor = 1 / 1.4;\n\n/**\n * Decorrelated jitter. This should be considered the optimal Jitter stategy\n * for most scenarios, as battle-tested in Polly.\n *\n * @see https://github.com/App-vNext/Polly/issues/530\n * @see https://github.com/Polly-Contrib/Polly.Contrib.WaitAndRetry/blob/24cb116a2a320e82b01f57e13bfeaeff2725ccbf/src/Polly.Contrib.WaitAndRetry/Backoff.DecorrelatedJitterV2.cs\n */\nexport const decorrelatedJitterGenerator: GeneratorFn<[number, number]> = (state, options) => {\n  const [attempt, prev] = state || [0, 0];\n  const t = attempt + Math.random();\n  const next = Math.pow(options.exponent, t) * Math.tanh(Math.sqrt(pFactor * t));\n  const formulaIntrinsicValue = isFinite(next) ? Math.max(0, next - prev) : Infinity;\n  return [\n    Math.min(formulaIntrinsicValue * rpScalingFactor * options.initialDelay, options.maxDelay),\n    [attempt + 1, next],\n  ];\n};\n"]}