export { AddEventOptions, Instrumenter, InstrumenterSpanOptions, OperationTracingOptions, OptionsWithTracingContext, Resolved, SpanStatus, SpanStatusError, SpanStatusSuccess, TracingClient, TracingClientOptions, TracingContext, TracingSpan, TracingSpanKind, TracingSpanLink, TracingSpanOptions, } from "./interfaces.js";
export { useInstrumenter } from "./instrumenter.js";
export { createTracingClient } from "./tracingClient.js";
//# sourceMappingURL=index.d.ts.map