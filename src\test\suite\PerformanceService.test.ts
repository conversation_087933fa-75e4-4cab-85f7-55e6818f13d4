import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import { PerformanceService, PerformanceMetric, PerformanceAlert } from '../../services/PerformanceService';

suite('PerformanceService Test Suite', () => {
    let performanceService: PerformanceService;
    let mockContext: vscode.ExtensionContext;
    let sandbox: sinon.SinonSandbox;
    let clock: sinon.SinonFakeTimers;

    setup(() => {
        sandbox = sinon.createSandbox();
        clock = sandbox.useFakeTimers();
        
        // Mock VS Code extension context
        mockContext = {
            globalState: {
                get: sandbox.stub().returns({}),
                update: sandbox.stub().resolves()
            }
        } as any;

        // Mock performance.now()
        global.performance = {
            now: sandbox.stub().returns(1000)
        } as any;

        performanceService = new PerformanceService(mockContext);
    });

    teardown(() => {
        clock.restore();
        sandbox.restore();
    });

    test('should initialize with default baselines', () => {
        const report = performanceService.getPerformanceReport();
        
        assert.ok(report.baselines);
        assert.ok(report.baselines.extensionActivation);
        assert.strictEqual(report.baselines.extensionActivation.baseline, 1200);
        assert.strictEqual(report.baselines.extensionActivation.threshold, 1380);
    });

    test('should start and end metrics correctly', () => {
        const metricId = performanceService.startMetric('test_operation', { test: true });
        
        assert.ok(metricId);
        assert.ok(metricId.includes('test_operation'));
        
        // Advance time
        (global.performance.now as sinon.SinonStub).returns(1500);
        
        const metric = performanceService.endMetric(metricId, { result: 'success' });
        
        assert.ok(metric);
        assert.strictEqual(metric.name, 'test_operation');
        assert.strictEqual(metric.duration, 500);
        assert.ok(metric.metadata);
        assert.strictEqual(metric.metadata.test, true);
        assert.strictEqual(metric.metadata.result, 'success');
    });

    test('should track operations with automatic timing', async () => {
        let operationExecuted = false;
        
        const result = await performanceService.trackOperation(
            'async_operation',
            async () => {
                operationExecuted = true;
                // Simulate async work
                await new Promise(resolve => setTimeout(resolve, 100));
                return 'operation_result';
            },
            { context: 'test' }
        );
        
        assert.strictEqual(result, 'operation_result');
        assert.ok(operationExecuted);
        
        const report = performanceService.getPerformanceReport();
        assert.ok(report.totalOperations > 0);
    });

    test('should handle operation failures in trackOperation', async () => {
        const testError = new Error('Test operation failed');
        
        try {
            await performanceService.trackOperation(
                'failing_operation',
                async () => {
                    throw testError;
                }
            );
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.strictEqual(error, testError);
        }
        
        // Should still record the metric with failure metadata
        const report = performanceService.getPerformanceReport();
        assert.ok(report.totalOperations > 0);
    });

    test('should record key metrics and check thresholds', () => {
        const consoleSpy = sandbox.spy(console, 'warn');
        
        // Record metric above threshold
        performanceService.recordKeyMetric('extension_activation', 1500);
        
        const report = performanceService.getPerformanceReport();
        assert.ok(report.alerts.length > 0);
        
        const alert = report.alerts[0];
        assert.strictEqual(alert.metric, 'extension_activation');
        assert.strictEqual(alert.current, 1500);
        assert.strictEqual(alert.baseline, 1200);
        assert.strictEqual(alert.threshold, 1380);
        assert.ok(alert.message.includes('Performance degradation'));
    });

    test('should not generate alerts for metrics within threshold', () => {
        // Record metric within threshold
        performanceService.recordKeyMetric('extension_activation', 1100);
        
        const report = performanceService.getPerformanceReport();
        assert.strictEqual(report.alerts.length, 0);
    });

    test('should calculate performance trends correctly', () => {
        // Add historical data points showing improvement
        const metricType = 'time_to_first_token';
        const values = [600, 580, 560, 540, 520]; // Improving trend
        
        values.forEach(value => {
            performanceService.recordKeyMetric(metricType, value);
        });
        
        const trend = performanceService.getPerformanceTrend(metricType);
        
        assert.strictEqual(trend.direction, 'improving');
        assert.ok(trend.confidence > 0);
        assert.ok(trend.change > 0);
        assert.strictEqual(trend.dataPoints, 5);
    });

    test('should handle insufficient data for trends', () => {
        const trend = performanceService.getPerformanceTrend('unknown_metric');
        
        assert.strictEqual(trend.trend, 'insufficient_data');
        assert.strictEqual(trend.direction, 'unknown');
        assert.strictEqual(trend.change, 0);
        assert.strictEqual(trend.confidence, 0);
    });

    test('should provide session summary', () => {
        // Record some metrics
        performanceService.recordKeyMetric('extension_activation', 1100);
        performanceService.recordKeyMetric('time_to_first_token', 450);
        
        // Advance time to simulate session duration
        clock.tick(60000); // 1 minute
        
        const summary = performanceService.getSessionSummary();
        
        assert.ok(summary.sessionDuration > 0);
        assert.ok(summary.totalOperations >= 2);
        assert.ok(summary.averageResponseTime >= 0);
        assert.ok(Array.isArray(summary.performanceTrends));
    });

    test('should update baselines based on recent measurements', () => {
        const metricType = 'extension_activation';
        const originalBaseline = performanceService.getPerformanceReport().baselines.extensionActivation.baseline;
        
        // Add many measurements to trigger baseline update
        for (let i = 0; i < 15; i++) {
            performanceService.recordKeyMetric(metricType, 1000 + i * 10);
        }
        
        performanceService.updateBaselines();
        
        const updatedBaseline = performanceService.getPerformanceReport().baselines.extensionActivation.baseline;
        
        // Baseline should be updated based on 90th percentile
        assert.notStrictEqual(updatedBaseline, originalBaseline);
    });

    test('should export metrics correctly', () => {
        performanceService.recordKeyMetric('test_metric', 100);
        
        const exportedData = performanceService.exportMetrics();
        const parsed = JSON.parse(exportedData);
        
        assert.ok(parsed.report);
        assert.ok(parsed.rawMetrics);
        assert.ok(parsed.timestamp);
        assert.ok(Array.isArray(parsed.rawMetrics));
    });

    test('should clear metrics', () => {
        performanceService.recordKeyMetric('test_metric', 100);
        
        let report = performanceService.getPerformanceReport();
        assert.ok(report.totalOperations > 0);
        
        performanceService.clearMetrics();
        
        report = performanceService.getPerformanceReport();
        assert.strictEqual(report.totalOperations, 0);
    });

    test('should get metrics by category', () => {
        performanceService.recordKeyMetric('streaming_operation', 200);
        performanceService.recordKeyMetric('file_operation', 150);
        performanceService.recordKeyMetric('streaming_another', 180);
        
        const streamingMetrics = performanceService.getMetricsByCategory('streaming');
        assert.strictEqual(streamingMetrics.length, 2);
        
        const fileMetrics = performanceService.getMetricsByCategory('file');
        assert.strictEqual(fileMetrics.length, 1);
    });

    test('should handle streaming metrics tracking', () => {
        const streamId = 'test_stream_123';
        
        // Track streaming events
        performanceService.trackStreamingMetrics(streamId, 'start', { provider: 'openai' });
        performanceService.trackStreamingMetrics(streamId, 'firstToken', { latency: 200 });
        performanceService.trackStreamingMetrics(streamId, 'token', { content: 'Hello' });
        performanceService.trackStreamingMetrics(streamId, 'complete', { totalTokens: 50 });
        
        const report = performanceService.getPerformanceReport();
        assert.ok(report.streamingMetrics.totalStreams >= 0);
    });

    test('should persist and load metrics correctly', () => {
        const updateSpy = mockContext.globalState.update as sinon.SinonStub;
        
        // Record enough metrics to trigger persistence
        for (let i = 0; i < 10; i++) {
            performanceService.recordKeyMetric('test_metric', 100 + i);
        }
        
        // Should have called update to persist metrics
        assert.ok(updateSpy.called);
    });

    test('should handle performance alert severity correctly', () => {
        // Test different severity levels
        const baseline = 1000;
        
        // Low severity (15-25% degradation)
        performanceService.recordKeyMetric('test_metric', baseline * 1.2);
        
        // Medium severity (25-50% degradation)
        performanceService.recordKeyMetric('test_metric', baseline * 1.4);
        
        // High severity (>50% degradation)
        performanceService.recordKeyMetric('test_metric', baseline * 1.6);
        
        const report = performanceService.getPerformanceReport();
        const alerts = report.alerts;
        
        assert.ok(alerts.length > 0);
        
        // Check that different severity levels are assigned
        const severities = alerts.map(alert => alert.severity);
        assert.ok(severities.includes('low') || severities.includes('medium') || severities.includes('high'));
    });

    test('should show performance report in webview', async () => {
        const createWebviewPanelSpy = sandbox.stub(vscode.window, 'createWebviewPanel').returns({
            webview: { html: '' }
        } as any);
        
        await performanceService.showPerformanceReport();
        
        assert.ok(createWebviewPanelSpy.calledOnce);
        assert.ok(createWebviewPanelSpy.calledWith('v1b3-performance'));
    });
});
