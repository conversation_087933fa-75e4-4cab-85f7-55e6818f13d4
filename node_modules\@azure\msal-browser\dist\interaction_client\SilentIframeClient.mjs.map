{"version": 3, "file": "SilentIframeClient.mjs", "sources": ["../../src/interaction_client/SilentIframeClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.preconnect", "ResponseHandler.deserializeResponse", "Authorize.handleResponseEAR", "BrowserAuthErrorCodes.silentLogoutUnsupported", "Authorize.getAuthCodeRequestUrl", "Authorize.handleResponseCode"], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAGG;AA4CG,MAAO,kBAAmB,SAAQ,yBAAyB,CAAA;IAI7D,WACI,CAAA,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,KAAY,EACZ,iBAAqC,EACrC,iBAAsC,EACtC,oBAA2C,EAC3C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;KAC1C;AAED;;;AAGG;IACH,MAAM,YAAY,CACd,OAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,8BAA8B,EAChD,OAAO,CAAC,aAAa,CACxB,CAAC;;QAEF,IACI,CAAC,OAAO,CAAC,SAAS;YAClB,CAAC,OAAO,CAAC,GAAG;AACZ,aAAC,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EACjD;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qGAAqG,CACxG,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,YAAY,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QACpC,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,YAAA,IACI,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI;AACxC,gBAAA,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,UAAU,EAChD;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAgD,6CAAA,EAAA,YAAY,CAAC,MAAM,SAAS,WAAW,CAAC,IAAI,CAAA,CAAE,CACjG,CAAC;AACF,gBAAA,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC;AAC1C,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC;AAC1C,SAAA;;AAGD,QAAA,MAAM,aAAa,GAAkC,MAAM,WAAW,CAClE,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QACxC,aAAa,CAAC,cAAc,GAAG,qBAAqB,CAChD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,EACzB,aAAa,CAAC,oBAAoB,CACrC,CAAC;AACF,QAAAA,UAAuB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;AACpD,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAC7C,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC9C,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,eAAe,CACjB,OAAsC,EAAA;AAEtC,QAAA,IAAI,UAA+C,CAAC;QACpD,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,IAAI;;AAEA,YAAA,UAAU,GAAG,MAAM,WAAW,CAC1B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,OAAO,CAAC,SAAS;gBACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;gBACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;gBACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,aAAA,CAAC,CAAC;AAEH,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC1B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AAED,YAAA,IACI,CAAC,UAAU;AACX,gBAAA,EAAE,CAAC,YAAY,SAAS,CAAC;AACzB,gBAAA,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC,mBAAmB,EACtD;AACE,gBAAA,MAAM,CAAC,CAAC;AACX,aAAA;AAED,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B;gBACI,UAAU,EAAE,CAAC,CAAC,SAAS;AAC1B,aAAA,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC1B,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,cAAc,CAChB,OAAsC,EAAA;AAEtC,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAC5C,QAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;YACE,gBAAgB,EAAE,OAAO,CAAC,SAAS;YACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;YACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;YACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,SAAA,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,WAAW,CAC5B,cAAc,EACd,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,EAAE,CAAC;AACJ,QAAA,MAAM,aAAa,GAAG;AAClB,YAAA,GAAG,OAAO;AACV,YAAA,MAAM,EAAE,MAAM;SACjB,CAAC;AACF,QAAA,MAAM,SAAS,GAAG,MAAM,WAAW,CAC/B,kBAAkB,EAClB,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,aAAa,EACb,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;;QAErE,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,oBAAoB,EACpB,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,SAAS,EACT,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,EACpC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,EAC3C,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,YAAY,CACf,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,MAAM,CACvBC,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7C,OAAO,WAAW,CACdC,iBAA2B,EAC3B,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,aAAa,EACb,YAAY,EACZ,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;KACL;AAED;;AAEG;IACH,MAAM,GAAA;;QAEF,OAAO,OAAO,CAAC,MAAM,CACjB,sBAAsB,CAClBC,uBAA6C,CAChD,CACJ,CAAC;KACL;AAED;;;;;AAKG;AACO,IAAA,MAAM,iBAAiB,CAC7B,UAAmC,EACnC,OAAsC,EAAA;AAEtC,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,6BAA6B,EAC/C,aAAa,CAChB,CAAC;AACF,QAAA,MAAM,SAAS,GAAG,MAAM,WAAW,CAC/B,iBAAiB,EACjB,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AAEtD,QAAA,MAAM,aAAa,GAAG;AAClB,YAAA,GAAG,OAAO;YACV,aAAa,EAAE,SAAS,CAAC,SAAS;SACrC,CAAC;;AAEF,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjCC,qBAA+B,EAC/B,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,IAAI,CAAC,MAAM,EACX,UAAU,CAAC,SAAS,EACpB,aAAa,EACb,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,QAAA,MAAM,SAAS,GAAG,MAAM,WAAW,CAC/B,mBAAmB,EACnB,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,WAAW,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CACvC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;;QAErE,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,oBAAoB,EACpB,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,SAAS,EACT,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,EACpC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,EAC3C,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,YAAY,CACf,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,MAAM,CACvBH,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAE7C,QAAA,OAAO,WAAW,CACdI,kBAA4B,EAC5B,iBAAiB,CAAC,kBAAkB,EACpC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,OAAO,EACP,YAAY,EACZ,SAAS,CAAC,QAAQ,EAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,EACX,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;KACL;AACJ;;;;"}