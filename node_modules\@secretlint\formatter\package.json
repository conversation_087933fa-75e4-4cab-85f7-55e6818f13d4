{"name": "@secretlint/formatter", "version": "9.3.4", "description": "A formatter collection for Secretlint.", "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/@secretlint/formatter/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "type": "module", "exports": {".": {"import": {"types": "./module/index.d.ts", "default": "./module/index.js"}, "default": "./module/index.js"}, "./module/": {"import": "./module/", "types": "./module/"}, "./package.json": "./package.json"}, "main": "./module/index.js", "types": "./module/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "module/", "src/"], "scripts": {"build": "tsc --build", "clean": "tsc --build --clean", "prepublishOnly": "npm run clean && npm run build", "prettier": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublish": "npm run --if-present build", "test": "mocha", "updateSnapshot": "UPDATE_SNAPSHOT=1 npm test", "watch": "tsc --build --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "dependencies": {"@secretlint/resolver": "^9.3.4", "@secretlint/types": "^9.3.4", "@textlint/linter-formatter": "^14.7.2", "@textlint/module-interop": "^14.7.2", "@textlint/types": "^14.7.2", "chalk": "^4.1.2", "debug": "^4.4.1", "pluralize": "^8.0.0", "strip-ansi": "^6.0.1", "table": "^6.9.0", "terminal-link": "^2.1.1"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^20.17.51", "@types/pluralize": "0.0.33", "escape-string-regexp": "^4.0.0", "mocha": "^10.8.2", "prettier": "^2.8.1", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "packageManager": "yarn@1.22.22", "engines": {"node": "^14.13.1 || >=16.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "1b0bebd91380d94ee0a756900ce9b39c8b3cfd1b"}