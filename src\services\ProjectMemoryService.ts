/**
 * Project Memory Service
 * Manages project-specific memories and patterns
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { 
    IProjectMemoryService, 
    ProjectMemory 
} from '../interfaces/IEnhancedMemorySystem';

export class ProjectMemoryService implements IProjectMemoryService {
    private static readonly PROJECT_MEMORIES_KEY = 'v1b3sama.projectMemories';
    private memories: Map<string, ProjectMemory> = new Map();
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadMemories();
    }

    public async getProjectMemories(projectId: string, category?: string): Promise<ProjectMemory[]> {
        const projectMemories = Array.from(this.memories.values())
            .filter(m => m.projectId === projectId);
        
        if (category) {
            return projectMemories.filter(m => m.category === category);
        }
        
        return projectMemories.sort((a, b) => b.relevance - a.relevance);
    }

    public async addProjectMemory(
        projectId: string, 
        category: string, 
        key: string, 
        value: any, 
        description?: string
    ): Promise<void> {
        const id = `${projectId}:${category}:${key}`;
        const existing = this.memories.get(id);
        
        if (existing) {
            // Update existing memory
            existing.value = value;
            existing.description = description || existing.description;
            existing.relevance = Math.min(1.0, existing.relevance + 0.1);
            existing.lastAccessed = Date.now();
            existing.accessCount += 1;
        } else {
            // Create new memory
            const memory: ProjectMemory = {
                id,
                projectId,
                category: category as any,
                key,
                value,
                description,
                relevance: 0.8, // Start with high relevance
                lastAccessed: Date.now(),
                accessCount: 1,
                relatedFiles: [],
                tags: []
            };
            this.memories.set(id, memory);
        }
        
        await this.saveMemories();
    }

    public async updateMemoryRelevance(id: string, relevance: number): Promise<void> {
        const memory = this.memories.get(id);
        if (memory) {
            memory.relevance = Math.max(0, Math.min(1, relevance));
            memory.lastAccessed = Date.now();
            await this.saveMemories();
        }
    }

    public async findRelevantMemories(projectId: string, query: string): Promise<ProjectMemory[]> {
        const projectMemories = await this.getProjectMemories(projectId);
        const queryLower = query.toLowerCase();
        
        // Score memories by relevance to query
        const scoredMemories = projectMemories.map(memory => {
            let score = memory.relevance * 0.4; // Base score from relevance
            
            // Check key/value/description match
            if (memory.key.toLowerCase().includes(queryLower)) {
                score += 0.3;
            }
            if (String(memory.value).toLowerCase().includes(queryLower)) {
                score += 0.2;
            }
            if (memory.description?.toLowerCase().includes(queryLower)) {
                score += 0.2;
            }
            
            // Check tags
            if (memory.tags?.some(tag => tag.toLowerCase().includes(queryLower))) {
                score += 0.1;
            }
            
            // Boost recently accessed memories
            const daysSinceAccess = (Date.now() - memory.lastAccessed) / (1000 * 60 * 60 * 24);
            if (daysSinceAccess < 7) {
                score += 0.1;
            }
            
            // Boost frequently accessed memories
            if (memory.accessCount > 5) {
                score += 0.1;
            }
            
            return { memory, score };
        });
        
        return scoredMemories
            .filter(sm => sm.score > 0.3)
            .sort((a, b) => b.score - a.score)
            .map(sm => sm.memory);
    }

    public async analyzeProjectPatterns(projectId: string): Promise<ProjectMemory[]> {
        const workspaceRoot = this.getWorkspaceRoot();
        if (!workspaceRoot) {
            return [];
        }
        
        const patterns: ProjectMemory[] = [];
        
        try {
            // Analyze package.json for dependencies
            const packageJsonPath = path.join(workspaceRoot, 'package.json');
            try {
                const packageJson = JSON.parse(
                    await vscode.workspace.fs.readFile(vscode.Uri.file(packageJsonPath))
                        .then(data => Buffer.from(data).toString())
                );
                
                if (packageJson.dependencies) {
                    await this.addProjectMemory(
                        projectId, 
                        'dependencies', 
                        'npm_dependencies', 
                        Object.keys(packageJson.dependencies),
                        'NPM dependencies from package.json'
                    );
                }
                
                if (packageJson.scripts) {
                    await this.addProjectMemory(
                        projectId, 
                        'conventions', 
                        'npm_scripts', 
                        packageJson.scripts,
                        'Available NPM scripts'
                    );
                }
            } catch {
                // package.json doesn't exist or is invalid
            }
            
            // Analyze project structure
            const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**');
            const fileExtensions = new Map<string, number>();
            const directories = new Set<string>();
            
            for (const file of files) {
                const ext = path.extname(file.fsPath);
                if (ext) {
                    fileExtensions.set(ext, (fileExtensions.get(ext) || 0) + 1);
                }
                
                const dir = path.dirname(path.relative(workspaceRoot, file.fsPath));
                if (dir && dir !== '.') {
                    directories.add(dir.split(path.sep)[0]); // Top-level directory
                }
            }
            
            // Store file type analysis
            const topExtensions = Array.from(fileExtensions.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);
            
            await this.addProjectMemory(
                projectId,
                'patterns',
                'file_types',
                topExtensions,
                'Most common file types in project'
            );
            
            // Store directory structure
            await this.addProjectMemory(
                projectId,
                'architecture',
                'directory_structure',
                Array.from(directories),
                'Top-level directories in project'
            );
            
            // Detect framework patterns
            const frameworks = this.detectFrameworks(Array.from(fileExtensions.keys()), Array.from(directories));
            if (frameworks.length > 0) {
                await this.addProjectMemory(
                    projectId,
                    'patterns',
                    'detected_frameworks',
                    frameworks,
                    'Detected frameworks and libraries'
                );
            }
            
        } catch (error) {
            console.error('Failed to analyze project patterns:', error);
        }
        
        return await this.getProjectMemories(projectId);
    }

    private detectFrameworks(extensions: string[], directories: string[]): string[] {
        const frameworks: string[] = [];
        
        // React detection
        if (extensions.includes('.jsx') || extensions.includes('.tsx') || 
            directories.includes('components') || directories.includes('hooks')) {
            frameworks.push('React');
        }
        
        // Vue detection
        if (extensions.includes('.vue')) {
            frameworks.push('Vue.js');
        }
        
        // Angular detection
        if (directories.includes('app') && extensions.includes('.ts') && 
            (directories.includes('services') || directories.includes('components'))) {
            frameworks.push('Angular');
        }
        
        // Express/Node.js detection
        if (directories.includes('routes') || directories.includes('middleware') ||
            directories.includes('controllers')) {
            frameworks.push('Express.js');
        }
        
        // Python frameworks
        if (extensions.includes('.py')) {
            if (directories.includes('templates') || directories.includes('static')) {
                frameworks.push('Django/Flask');
            }
        }
        
        return frameworks;
    }

    private getWorkspaceRoot(): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        return workspaceFolders && workspaceFolders.length > 0 
            ? workspaceFolders[0].uri.fsPath 
            : undefined;
    }

    private getCurrentProjectId(): string {
        const workspaceRoot = this.getWorkspaceRoot();
        return workspaceRoot ? path.basename(workspaceRoot) : 'default';
    }

    private async loadMemories(): Promise<void> {
        try {
            const stored = this.context.workspaceState.get<ProjectMemory[]>(
                ProjectMemoryService.PROJECT_MEMORIES_KEY, 
                []
            );
            
            this.memories.clear();
            for (const memory of stored) {
                this.memories.set(memory.id, memory);
            }
            
            console.log(`Project Memory: Loaded ${this.memories.size} memories`);
        } catch (error) {
            console.error('Failed to load project memories:', error);
        }
    }

    private async saveMemories(): Promise<void> {
        try {
            const memoriesArray = Array.from(this.memories.values());
            await this.context.workspaceState.update(
                ProjectMemoryService.PROJECT_MEMORIES_KEY, 
                memoriesArray
            );
        } catch (error) {
            console.error('Failed to save project memories:', error);
        }
    }

    /**
     * Get project memories as context string for LLM prompts
     */
    public async getProjectMemoriesAsContext(projectId?: string): Promise<string> {
        const currentProjectId = projectId || this.getCurrentProjectId();
        const memories = await this.getProjectMemories(currentProjectId);
        
        if (memories.length === 0) {
            return '';
        }
        
        let context = '\n\n## Project Memory (Learned Patterns & Architecture)\n\n';
        
        const groupedMemories = memories.reduce((groups, memory) => {
            if (!groups[memory.category]) {
                groups[memory.category] = [];
            }
            groups[memory.category].push(memory);
            return groups;
        }, {} as Record<string, ProjectMemory[]>);
        
        for (const [category, mems] of Object.entries(groupedMemories)) {
            context += `**${category.toUpperCase()}:**\n`;
            for (const memory of mems.slice(0, 5)) { // Limit to top 5 per category
                context += `- ${memory.key}: ${JSON.stringify(memory.value)}\n`;
                if (memory.description) {
                    context += `  ${memory.description}\n`;
                }
            }
            context += '\n';
        }
        
        return context;
    }

    /**
     * Auto-analyze current project and store insights
     */
    public async autoAnalyzeCurrentProject(): Promise<void> {
        const projectId = this.getCurrentProjectId();
        await this.analyzeProjectPatterns(projectId);
        console.log(`Project Memory: Auto-analyzed project ${projectId}`);
    }
}
