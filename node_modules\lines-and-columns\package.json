{"name": "lines-and-columns", "version": "2.0.4", "description": "Maps lines and columns to character offsets and back.", "keywords": ["lines", "columns", "parser"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "repository": {"type": "git", "url": "https://github.com/eventualbuddha/lines-and-columns.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "type": "module", "main": "./build/index.cjs", "exports": {"import": "./build/index.mjs", "require": "./build/index.cjs", "types": "./build/index.d.ts"}, "types": "./build/index.d.ts", "files": ["build"], "scripts": {"prebuild": "rm -rf build", "build": "tsc --project tsconfig.build.json && mv build/index.js build/index.mjs && tsc --project tsconfig.build.json --module commonjs && mv build/index.js build/index.cjs", "build:watch": "tsc --project tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepublishOnly": "npm run lint && npm run build", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}