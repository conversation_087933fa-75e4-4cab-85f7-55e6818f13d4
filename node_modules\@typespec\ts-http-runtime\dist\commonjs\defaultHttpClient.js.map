{"version": 3, "file": "defaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAQlC,0DAEC;AAPD,2DAA2D;AAE3D;;GAEG;AACH,SAAgB,uBAAuB;IACrC,OAAO,IAAA,wCAAoB,GAAE,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createNodeHttpClient } from \"./nodeHttpClient.js\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createNodeHttpClient();\n}\n"]}