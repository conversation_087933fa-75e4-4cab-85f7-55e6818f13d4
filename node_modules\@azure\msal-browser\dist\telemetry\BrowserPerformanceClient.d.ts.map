{"version": 3, "file": "BrowserPerformanceClient.d.ts", "sourceRoot": "", "sources": ["../../src/telemetry/BrowserPerformanceClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAEH,0BAA0B,EAC1B,kBAAkB,EAElB,iBAAiB,EAEjB,iBAAiB,EAGpB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAmD3D,qBAAa,wBACT,SAAQ,iBACR,YAAW,kBAAkB;gBAGzB,aAAa,EAAE,aAAa,EAC5B,SAAS,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EACvB,aAAa,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAqBvC,UAAU,IAAI,MAAM;IAIpB,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,+BAA+B;IA0BvC;;;;;;;OAOG;IACH,gBAAgB,CACZ,WAAW,EAAE,MAAM,EACnB,aAAa,CAAC,EAAE,MAAM,GACvB,0BAA0B;IAuD7B;;;;;OAKG;IACH,eAAe,CACX,SAAS,EAAE,iBAAiB,EAC5B,aAAa,CAAC,EAAE,MAAM,GACvB,IAAI;IAuCP;;;;;;;;OAQG;IACH,mBAAmB,CACf,SAAS,EAAE,MAAM,EACjB,aAAa,CAAC,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,OAAO,GAC5B,IAAI;CA+BV"}