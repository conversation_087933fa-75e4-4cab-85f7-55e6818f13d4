/**
 * Checkpoint Manager - Interactive session history and restoration system
 * Merges conversation history and action history into unified checkpoint system
 */

import * as vscode from 'vscode';
import { ConversationData } from './ConversationPersistenceService';
import { ExecutionPlan } from '../interfaces/ILLMService';

export interface SessionCheckpoint {
    id: string;
    timestamp: string;
    name: string;
    conversationData: ConversationData;
    executionPlan?: ExecutionPlan;
    executionState?: 'idle' | 'awaitingApproval' | 'executing' | 'paused' | 'completed' | 'failed';
    metadata: {
        messageCount: number;
        totalTokens: number;
        totalCost: number;
        provider: string;
        model: string;
        lastUserMessage: string;
    };
}

export interface CheckpointSummary {
    id: string;
    name: string;
    timestamp: string;
    messageCount: number;
    provider: string;
    model: string;
    lastUserMessage: string;
    hasExecutionPlan: boolean;
    executionState?: string;
}

/**
 * Unified checkpoint system for conversation and execution state management
 */
export class CheckpointManager {
    private _context: vscode.ExtensionContext;
    private _panel: vscode.WebviewPanel | undefined;
    private _checkpoints: SessionCheckpoint[] = [];
    private _maxCheckpoints = 50; // Maximum checkpoints to keep

    constructor(context: vscode.ExtensionContext) {
        this._context = context;

        // Load saved checkpoints
        this._checkpoints = context.globalState.get('sessionCheckpoints', []);
        console.log(`CheckpointManager: Loaded ${this._checkpoints.length} checkpoints from storage`);

        // Debug: Log checkpoint data
        if (this._checkpoints.length > 0) {
            console.log('CheckpointManager: Loaded checkpoints:', this._checkpoints.map(cp => ({
                id: cp.id,
                name: cp.name,
                timestamp: cp.timestamp,
                messageCount: cp.metadata?.messageCount
            })));
        }
    }

    /**
     * Save current session as a checkpoint
     */
    public async saveCheckpoint(
        conversationData: ConversationData,
        executionPlan?: ExecutionPlan,
        executionState?: string,
        customName?: string
    ): Promise<string> {
        console.log('CheckpointManager: Saving checkpoint with data:', {
            messageCount: conversationData?.messages?.length || 0,
            provider: conversationData?.provider,
            model: conversationData?.model,
            executionState,
            customName
        });

        const checkpoint: SessionCheckpoint = {
            id: this._generateId(),
            timestamp: new Date().toISOString(),
            name: customName || this._generateCheckpointName(conversationData),
            conversationData: { ...conversationData },
            executionPlan: executionPlan ? { ...executionPlan } : undefined,
            executionState: executionState as any,
            metadata: {
                messageCount: conversationData.messages.length,
                totalTokens: conversationData.totalTokens.input + conversationData.totalTokens.output,
                totalCost: conversationData.totalCost,
                provider: conversationData.provider,
                model: conversationData.model,
                lastUserMessage: this._getLastUserMessage(conversationData),
            }
        };

        this._checkpoints.unshift(checkpoint); // Add to beginning (most recent first)

        // Trim old checkpoints
        if (this._checkpoints.length > this._maxCheckpoints) {
            this._checkpoints = this._checkpoints.slice(0, this._maxCheckpoints);
        }

        console.log(`CheckpointManager: Saving ${this._checkpoints.length} checkpoints to storage`);
        await this._context.globalState.update('sessionCheckpoints', this._checkpoints);

        // Update panel if open
        if (this._panel) {
            this._panel.webview.html = this._getHtmlForWebview();
        }

        console.log(`CheckpointManager: Successfully saved checkpoint ${checkpoint.id}`);
        return checkpoint.id;
    }

    /**
     * Get all checkpoint summaries
     */
    public getCheckpointSummaries(): CheckpointSummary[] {
        return this._checkpoints.map(cp => ({
            id: cp.id,
            name: cp.name,
            timestamp: cp.timestamp,
            messageCount: cp.metadata.messageCount,
            provider: cp.metadata.provider,
            model: cp.metadata.model,
            lastUserMessage: cp.metadata.lastUserMessage,
            hasExecutionPlan: !!cp.executionPlan,
            executionState: cp.executionState
        }));
    }

    /**
     * Get full checkpoint data by ID
     */
    public getCheckpoint(checkpointId: string): SessionCheckpoint | undefined {
        return this._checkpoints.find(cp => cp.id === checkpointId);
    }

    /**
     * Show checkpoint management panel
     */
    public showCheckpointPanel(): void {
        // Create and show panel
        this._panel = vscode.window.createWebviewPanel(
            'sessionCheckpoints',
            'Session Checkpoints',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [this._context.extensionUri]
            }
        );

        // Set the webview's HTML content
        this._panel.webview.html = this._getHtmlForWebview();

        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'restoreCheckpoint':
                        this._restoreCheckpoint(message.checkpointId);
                        return;
                    case 'deleteCheckpoint':
                        this._deleteCheckpoint(message.checkpointId);
                        return;
                    case 'renameCheckpoint':
                        this._renameCheckpoint(message.checkpointId, message.newName);
                        return;
                    case 'clearAllCheckpoints':
                        this._clearAllCheckpoints();
                        return;
                }
            },
            undefined,
            this._context.subscriptions
        );
    }

    /**
     * Show quick pick for checkpoint selection
     */
    public async showCheckpointQuickPick(): Promise<SessionCheckpoint | undefined> {
        if (this._checkpoints.length === 0) {
            vscode.window.showInformationMessage('No checkpoints available');
            return undefined;
        }

        const items = this._checkpoints.map(cp => ({
            label: cp.name,
            description: `${new Date(cp.timestamp).toLocaleString()} • ${cp.metadata.messageCount} messages`,
            detail: cp.metadata.lastUserMessage,
            checkpoint: cp
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a checkpoint to restore',
            matchOnDescription: true,
            matchOnDetail: true
        });

        return selected?.checkpoint;
    }

    /**
     * Delete a checkpoint
     */
    public async deleteCheckpoint(checkpointId: string): Promise<void> {
        this._checkpoints = this._checkpoints.filter(cp => cp.id !== checkpointId);
        await this._context.globalState.update('sessionCheckpoints', this._checkpoints);

        if (this._panel) {
            this._panel.webview.html = this._getHtmlForWebview();
        }
    }

    /**
     * Clear all checkpoints
     */
    public async clearAllCheckpoints(): Promise<void> {
        this._checkpoints = [];
        await this._context.globalState.update('sessionCheckpoints', []);

        if (this._panel) {
            this._panel.webview.html = this._getHtmlForWebview();
        }

        vscode.window.showInformationMessage('All checkpoints cleared');
    }

    /**
     * Generate checkpoint name from conversation data
     */
    private _generateCheckpointName(conversationData: ConversationData): string {
        const lastUserMessage = this._getLastUserMessage(conversationData);
        if (lastUserMessage) {
            // Take first 40 characters and add ellipsis if longer
            return lastUserMessage.length > 40 
                ? lastUserMessage.substring(0, 37) + '...' 
                : lastUserMessage;
        }
        return `Checkpoint - ${new Date().toLocaleDateString()}`;
    }

    /**
     * Get last user message from conversation
     */
    private _getLastUserMessage(conversationData: ConversationData): string {
        const userMessages = conversationData.messages.filter(m => m.role === 'user');
        return userMessages.length > 0 ? userMessages[userMessages.length - 1].content : '';
    }

    /**
     * Restore checkpoint (emit event for ChatViewProvider to handle)
     */
    private _restoreCheckpoint(checkpointId: string): void {
        const checkpoint = this._checkpoints.find(cp => cp.id === checkpointId);
        if (!checkpoint) {
            vscode.window.showErrorMessage('Checkpoint not found');
            return;
        }

        // Emit command for ChatViewProvider to handle
        vscode.commands.executeCommand('v1b3-sama.restoreCheckpoint', checkpoint);
        
        if (this._panel) {
            this._panel.dispose();
        }
    }

    /**
     * Delete checkpoint from webview
     */
    private async _deleteCheckpoint(checkpointId: string): Promise<void> {
        await this.deleteCheckpoint(checkpointId);
        vscode.window.showInformationMessage('Checkpoint deleted');
    }

    /**
     * Rename checkpoint
     */
    private async _renameCheckpoint(checkpointId: string, newName: string): Promise<void> {
        const checkpoint = this._checkpoints.find(cp => cp.id === checkpointId);
        if (checkpoint) {
            checkpoint.name = newName;
            await this._context.globalState.update('sessionCheckpoints', this._checkpoints);
            
            if (this._panel) {
                this._panel.webview.html = this._getHtmlForWebview();
            }
        }
    }

    /**
     * Clear all checkpoints from webview
     */
    private async _clearAllCheckpoints(): Promise<void> {
        await this.clearAllCheckpoints();
    }

    /**
     * Generate HTML for webview
     */
    private _getHtmlForWebview(): string {
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Session Checkpoints</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 20px;
                        margin: 0;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    h1 {
                        color: var(--vscode-foreground);
                        border-bottom: 1px solid var(--vscode-panel-border);
                        padding-bottom: 10px;
                        margin-bottom: 20px;
                    }
                    .checkpoint-item {
                        background-color: var(--vscode-editor-background);
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 6px;
                        padding: 15px;
                        margin-bottom: 15px;
                        transition: background-color 0.2s;
                    }
                    .checkpoint-item:hover {
                        background-color: var(--vscode-list-hoverBackground);
                    }
                    .checkpoint-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 10px;
                    }
                    .checkpoint-name {
                        font-weight: bold;
                        font-size: 1.1em;
                        color: var(--vscode-textLink-foreground);
                    }
                    .checkpoint-meta {
                        font-size: 0.9em;
                        color: var(--vscode-descriptionForeground);
                        margin-bottom: 8px;
                    }
                    .checkpoint-preview {
                        font-style: italic;
                        color: var(--vscode-descriptionForeground);
                        margin-bottom: 10px;
                        max-height: 40px;
                        overflow: hidden;
                    }
                    .checkpoint-actions {
                        display: flex;
                        gap: 10px;
                    }
                    .button {
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 0.9em;
                    }
                    .button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    .button.secondary {
                        background-color: var(--vscode-button-secondaryBackground);
                        color: var(--vscode-button-secondaryForeground);
                    }
                    .button.secondary:hover {
                        background-color: var(--vscode-button-secondaryHoverBackground);
                    }
                    .button.danger {
                        background-color: var(--vscode-errorForeground);
                        color: var(--vscode-editor-background);
                    }
                    .empty-state {
                        text-align: center;
                        color: var(--vscode-descriptionForeground);
                        padding: 40px;
                    }
                    .actions {
                        margin-top: 20px;
                        text-align: right;
                    }
                    .execution-badge {
                        display: inline-block;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 0.8em;
                        font-weight: bold;
                        margin-left: 8px;
                    }
                    .execution-badge.awaiting { background-color: #ffa500; color: #000; }
                    .execution-badge.completed { background-color: #28a745; color: #fff; }
                    .execution-badge.failed { background-color: #dc3545; color: #fff; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>📚 Session Checkpoints</h1>
                    ${this._generateCheckpointsList()}
                    <div class="actions">
                        <button class="button danger" onclick="clearAllCheckpoints()">Clear All Checkpoints</button>
                    </div>
                </div>

                <script>
                    const vscode = acquireVsCodeApi();

                    function restoreCheckpoint(checkpointId) {
                        vscode.postMessage({
                            command: 'restoreCheckpoint',
                            checkpointId: checkpointId
                        });
                    }

                    function deleteCheckpoint(checkpointId) {
                        if (confirm('Are you sure you want to delete this checkpoint?')) {
                            vscode.postMessage({
                                command: 'deleteCheckpoint',
                                checkpointId: checkpointId
                            });
                        }
                    }

                    function renameCheckpoint(checkpointId) {
                        const newName = prompt('Enter new checkpoint name:');
                        if (newName && newName.trim()) {
                            vscode.postMessage({
                                command: 'renameCheckpoint',
                                checkpointId: checkpointId,
                                newName: newName.trim()
                            });
                        }
                    }

                    function clearAllCheckpoints() {
                        if (confirm('Are you sure you want to clear all checkpoints? This cannot be undone.')) {
                            vscode.postMessage({
                                command: 'clearAllCheckpoints'
                            });
                        }
                    }
                </script>
            </body>
            </html>`;
    }

    /**
     * Generate checkpoints list HTML
     */
    private _generateCheckpointsList(): string {
        // Debug: Log checkpoint count and data
        console.log(`CheckpointManager: Generating list with ${this._checkpoints.length} checkpoints`);
        console.log('CheckpointManager: Checkpoint data:', this._checkpoints);

        if (this._checkpoints.length === 0) {
            return `<div class="empty-state">
                <h3>No checkpoints saved yet</h3>
                <p>Checkpoints are automatically created during conversations.</p>
                <p><strong>Debug Info:</strong></p>
                <ul>
                    <li>Loaded checkpoints: ${this._checkpoints.length}</li>
                    <li>Storage key: sessionCheckpoints</li>
                    <li>Extension context: ${this._context ? 'Available' : 'Missing'}</li>
                </ul>
            </div>`;
        }

        let html = '';
        for (const checkpoint of this._checkpoints) {
            const executionBadge = checkpoint.executionPlan 
                ? `<span class="execution-badge ${checkpoint.executionState || 'awaiting'}">${checkpoint.executionState || 'Plan Available'}</span>`
                : '';

            html += `
                <div class="checkpoint-item">
                    <div class="checkpoint-header">
                        <div class="checkpoint-name">${checkpoint.name}${executionBadge}</div>
                        <div class="checkpoint-meta">${new Date(checkpoint.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="checkpoint-meta">
                        ${checkpoint.metadata.messageCount} messages • ${checkpoint.metadata.provider}/${checkpoint.metadata.model} • $${checkpoint.metadata.totalCost.toFixed(4)}
                    </div>
                    <div class="checkpoint-preview">${checkpoint.metadata.lastUserMessage}</div>
                    <div class="checkpoint-actions">
                        <button class="button" onclick="restoreCheckpoint('${checkpoint.id}')">🔄 Restore</button>
                        <button class="button secondary" onclick="renameCheckpoint('${checkpoint.id}')">✏️ Rename</button>
                        <button class="button secondary" onclick="deleteCheckpoint('${checkpoint.id}')">🗑️ Delete</button>
                    </div>
                </div>
            `;
        }
        return html;
    }

    /**
     * Generate unique ID
     */
    private _generateId(): string {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
}
