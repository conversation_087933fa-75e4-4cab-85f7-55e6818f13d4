{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../../src/logger/logger.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,MAAM,YAAY,CAAC;AAiG/B,MAAM,2BAA2B,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAI5E,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,GAAG;IACZ,IAAI,EAAE,GAAG;IACT,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;CACX,CAAC;AAEF,SAAS,cAAc,CACrB,MAAmC,EACnC,KAAyD;IAEzD,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;QACtB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAAC,KAAa;IAC9C,OAAO,2BAA2B,CAAC,QAAQ,CAAC,KAAY,CAAC,CAAC;AAC5D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAAmC;IACrE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAwB,CAAC;IAC1D,MAAM,eAAe,GACnB,CAAC,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC1F,SAAS,CAAC;IAEZ,IAAI,QAA6C,CAAC;IAElD,MAAM,YAAY,GAAgC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3E,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;QAC7B,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,SAAS,kBAAkB,CAAC,KAA+B;QACzD,IAAI,KAAK,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CACb,sBAAsB,KAAK,yBAAyB,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC5F,CAAC;QACJ,CAAC;QACD,QAAQ,GAAG,KAAK,CAAC;QAEjB,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;YACvC,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,eAAe,EAAE,CAAC;QACpB,0FAA0F;QAC1F,IAAI,yBAAyB,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CACX,GAAG,OAAO,CAAC,kBAAkB,8BAA8B,eAAe,iDAAiD,2BAA2B,CAAC,IAAI,CACzJ,IAAI,CACL,GAAG,CACL,CAAC;QACJ,CAAC;IACH,CAAC;IAED,SAAS,YAAY,CAAC,MAA4B;QAChD,OAAO,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,SAAS,YAAY,CACnB,MAAmC,EACnC,KAA8B;QAE9B,MAAM,MAAM,GAAyB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvE,KAAK;SACN,CAAC,CAAC;QAEH,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE/B,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YAC1C,KAAK,CAAC,MAAM,CAAC,iBAAiB,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC;QAED,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,kBAAkB;QACzB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,SAAS,yBAAyB,CAAC,SAAiB;QAClD,MAAM,gBAAgB,GAAgC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrF,cAAc,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAC/C,OAAO;YACL,KAAK,EAAE,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC;YAC9C,OAAO,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;YAClD,IAAI,EAAE,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC;YAC5C,OAAO,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;SACnD,CAAC;IACJ,CAAC;IAED,OAAO;QACL,WAAW,EAAE,kBAAkB;QAC/B,WAAW,EAAE,kBAAkB;QAC/B,kBAAkB,EAAE,yBAAyB;QAC7C,MAAM,EAAE,YAAY;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,GAAG,mBAAmB,CAAC;IAClC,kBAAkB,EAAE,4BAA4B;IAChD,SAAS,EAAE,iBAAiB;CAC7B,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,2DAA2D;AAC3D,MAAM,CAAC,MAAM,qBAAqB,GAAgC,OAAO,CAAC,MAAM,CAAC;AAEjF;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,QAAkC;IAC5D,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAC,SAAiB;IAClD,OAAO,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC/C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport debug from \"./debug.js\";\n\nimport type { Debugger } from \"./debug.js\";\nexport type { Debugger };\n\n/**\n * The log levels supported by the logger.\n * The log levels in order of most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport type TypeSpecRuntimeLogLevel = \"verbose\" | \"info\" | \"warning\" | \"error\";\n\n/**\n * A TypeSpecRuntimeClientLogger is a function that can log to an appropriate severity level.\n */\nexport type TypeSpecRuntimeClientLogger = Debugger;\n\n/**\n * Defines the methods available on the SDK-facing logger.\n */\nexport interface TypeSpecRuntimeLogger {\n  /**\n   * Used for failures the program is unlikely to recover from,\n   * such as Out of Memory.\n   */\n  error: Debugger;\n  /**\n   * Used when a function fails to perform its intended task.\n   * Usually this means the function will throw an exception.\n   * Not used for self-healing events (e.g. automatic retry)\n   */\n  warning: Debugger;\n  /**\n   * Used when a function operates normally.\n   */\n  info: Debugger;\n  /**\n   * Used for detailed troubleshooting scenarios. This is\n   * intended for use by developers / system administrators\n   * for diagnosing specific failures.\n   */\n  verbose: Debugger;\n}\n\n/**\n * todo doc\n */\nexport interface LoggerContext {\n  /**\n   * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n   * @param level - The log level to enable for logging.\n   * Options from most verbose to least verbose are:\n   * - verbose\n   * - info\n   * - warning\n   * - error\n   */\n  setLogLevel(logLevel?: TypeSpecRuntimeLogLevel): void;\n\n  /**\n   * Retrieves the currently specified log level.\n   */\n  getLogLevel(): TypeSpecRuntimeLogLevel | undefined;\n\n  /**\n   * Creates a logger for use by the SDKs that inherits from `TypeSpecRuntimeLogger`.\n   * @param namespace - The name of the SDK package.\n   * @hidden\n   */\n  createClientLogger(namespace: string): TypeSpecRuntimeLogger;\n\n  /**\n   * The TypeSpecRuntimeClientLogger provides a mechanism for overriding where logs are output to.\n   * By default, logs are sent to stderr.\n   * Override the `log` method to redirect logs to another location.\n   */\n  logger: TypeSpecRuntimeClientLogger;\n}\n\n/**\n * Option for creating a TypeSpecRuntimeLoggerContext.\n */\nexport interface CreateLoggerContextOptions {\n  /**\n   * The name of the environment variable to check for the log level.\n   */\n  logLevelEnvVarName: string;\n\n  /**\n   * The namespace of the logger.\n   */\n  namespace: string;\n}\n\nconst TYPESPEC_RUNTIME_LOG_LEVELS = [\"verbose\", \"info\", \"warning\", \"error\"];\n\ntype DebuggerWithLogLevel = Debugger & { level: TypeSpecRuntimeLogLevel };\n\nconst levelMap = {\n  verbose: 400,\n  info: 300,\n  warning: 200,\n  error: 100,\n};\n\nfunction patchLogMethod(\n  parent: TypeSpecRuntimeClientLogger,\n  child: TypeSpecRuntimeClientLogger | DebuggerWithLogLevel,\n): void {\n  child.log = (...args) => {\n    parent.log(...args);\n  };\n}\n\nfunction isTypeSpecRuntimeLogLevel(level: string): level is TypeSpecRuntimeLogLevel {\n  return TYPESPEC_RUNTIME_LOG_LEVELS.includes(level as any);\n}\n\n/**\n * Creates a logger context base on the provided options.\n * @param options - The options for creating a logger context.\n * @returns The logger context.\n */\nexport function createLoggerContext(options: CreateLoggerContextOptions): LoggerContext {\n  const registeredLoggers = new Set<DebuggerWithLogLevel>();\n  const logLevelFromEnv =\n    (typeof process !== \"undefined\" && process.env && process.env[options.logLevelEnvVarName]) ||\n    undefined;\n\n  let logLevel: TypeSpecRuntimeLogLevel | undefined;\n\n  const clientLogger: TypeSpecRuntimeClientLogger = debug(options.namespace);\n  clientLogger.log = (...args) => {\n    debug.log(...args);\n  };\n\n  function contextSetLogLevel(level?: TypeSpecRuntimeLogLevel): void {\n    if (level && !isTypeSpecRuntimeLogLevel(level)) {\n      throw new Error(\n        `Unknown log level '${level}'. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(\",\")}`,\n      );\n    }\n    logLevel = level;\n\n    const enabledNamespaces = [];\n    for (const logger of registeredLoggers) {\n      if (shouldEnable(logger)) {\n        enabledNamespaces.push(logger.namespace);\n      }\n    }\n\n    debug.enable(enabledNamespaces.join(\",\"));\n  }\n\n  if (logLevelFromEnv) {\n    // avoid calling setLogLevel because we don't want a mis-set environment variable to crash\n    if (isTypeSpecRuntimeLogLevel(logLevelFromEnv)) {\n      contextSetLogLevel(logLevelFromEnv);\n    } else {\n      console.error(\n        `${options.logLevelEnvVarName} set to unknown log level '${logLevelFromEnv}'; logging is not enabled. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(\n          \", \",\n        )}.`,\n      );\n    }\n  }\n\n  function shouldEnable(logger: DebuggerWithLogLevel): boolean {\n    return Boolean(logLevel && levelMap[logger.level] <= levelMap[logLevel]);\n  }\n\n  function createLogger(\n    parent: TypeSpecRuntimeClientLogger,\n    level: TypeSpecRuntimeLogLevel,\n  ): DebuggerWithLogLevel {\n    const logger: DebuggerWithLogLevel = Object.assign(parent.extend(level), {\n      level,\n    });\n\n    patchLogMethod(parent, logger);\n\n    if (shouldEnable(logger)) {\n      const enabledNamespaces = debug.disable();\n      debug.enable(enabledNamespaces + \",\" + logger.namespace);\n    }\n\n    registeredLoggers.add(logger);\n\n    return logger;\n  }\n\n  function contextGetLogLevel(): TypeSpecRuntimeLogLevel | undefined {\n    return logLevel;\n  }\n\n  function contextCreateClientLogger(namespace: string): TypeSpecRuntimeLogger {\n    const clientRootLogger: TypeSpecRuntimeClientLogger = clientLogger.extend(namespace);\n    patchLogMethod(clientLogger, clientRootLogger);\n    return {\n      error: createLogger(clientRootLogger, \"error\"),\n      warning: createLogger(clientRootLogger, \"warning\"),\n      info: createLogger(clientRootLogger, \"info\"),\n      verbose: createLogger(clientRootLogger, \"verbose\"),\n    };\n  }\n\n  return {\n    setLogLevel: contextSetLogLevel,\n    getLogLevel: contextGetLogLevel,\n    createClientLogger: contextCreateClientLogger,\n    logger: clientLogger,\n  };\n}\n\nconst context = createLoggerContext({\n  logLevelEnvVarName: \"TYPESPEC_RUNTIME_LOG_LEVEL\",\n  namespace: \"typeSpecRuntime\",\n});\n\n/**\n * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n * @param level - The log level to enable for logging.\n * Options from most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const TypeSpecRuntimeLogger: TypeSpecRuntimeClientLogger = context.logger;\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function setLogLevel(logLevel?: TypeSpecRuntimeLogLevel): void {\n  context.setLogLevel(logLevel);\n}\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function getLogLevel(): TypeSpecRuntimeLogLevel | undefined {\n  return context.getLogLevel();\n}\n\n/**\n * Creates a logger for use by the SDKs that inherits from `TypeSpecRuntimeLogger`.\n * @param namespace - The name of the SDK package.\n * @hidden\n */\nexport function createClientLogger(namespace: string): TypeSpecRuntimeLogger {\n  return context.createClientLogger(namespace);\n}\n"]}