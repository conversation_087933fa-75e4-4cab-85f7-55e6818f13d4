/*! @azure/msal-browser v4.13.1 2025-06-10 */
"use strict";!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).msal={})}(this,(function(e){
/*! @azure/msal-common v15.7.0 2025-06-10 */
const t={LIBRARY_NAME:"MSAL.JS",SKU:"msal.js.common",CACHE_PREFIX:"msal",DEFAULT_AUTHORITY:"https://login.microsoftonline.com/common/",DEFAULT_AUTHORITY_HOST:"login.microsoftonline.com",DEFAULT_COMMON_TENANT:"common",ADFS:"adfs",DSTS:"dstsv2",AAD_INSTANCE_DISCOVERY_ENDPT:"https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=",CIAM_AUTH_URL:".ciamlogin.com",AAD_TENANT_DOMAIN_SUFFIX:".onmicrosoft.com",RESOURCE_DELIM:"|",NO_ACCOUNT:"NO_ACCOUNT",CLAIMS:"claims",CONSUMER_UTID:"9188040d-6c67-4c5b-b112-36a304b66dad",OPENID_SCOPE:"openid",PROFILE_SCOPE:"profile",OFFLINE_ACCESS_SCOPE:"offline_access",EMAIL_SCOPE:"email",CODE_GRANT_TYPE:"authorization_code",RT_GRANT_TYPE:"refresh_token",S256_CODE_CHALLENGE_METHOD:"S256",URL_FORM_CONTENT_TYPE:"application/x-www-form-urlencoded;charset=utf-8",AUTHORIZATION_PENDING:"authorization_pending",NOT_DEFINED:"not_defined",EMPTY_STRING:"",NOT_APPLICABLE:"N/A",NOT_AVAILABLE:"Not Available",FORWARD_SLASH:"/",IMDS_ENDPOINT:"http://***************/metadata/instance/compute/location",IMDS_VERSION:"2020-06-01",IMDS_TIMEOUT:2e3,AZURE_REGION_AUTO_DISCOVER_FLAG:"TryAutoDetect",REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX:"login.microsoft.com",KNOWN_PUBLIC_CLOUDS:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"],SHR_NONCE_VALIDITY:240,INVALID_INSTANCE:"invalid_instance"},r=200,n=400,o=400,i=499,a=500,s=599,c=[t.OPENID_SCOPE,t.PROFILE_SCOPE,t.OFFLINE_ACCESS_SCOPE],l=[...c,t.EMAIL_SCOPE],h="Content-Type",d="Content-Length",u="Retry-After",g="X-AnchorMailbox",p="WWW-Authenticate",m="Authentication-Info",f="x-ms-request-id",y="x-ms-httpver",C="active-account-filters",v="common",w="organizations",I="consumers",T="access_token",A="xms_cc",k={LOGIN:"login",SELECT_ACCOUNT:"select_account",CONSENT:"consent",NONE:"none",CREATE:"create",NO_SESSION:"no_session"},S={PLAIN:"plain",S256:"S256"},b="code",E="id_token token refresh_token",R={QUERY:"query",FRAGMENT:"fragment"},_="query",P="authorization_code",M="refresh_token",O="MSSTS",N="ADFS",q="Generic",L="-",U=".",H={ID_TOKEN:"IdToken",ACCESS_TOKEN:"AccessToken",ACCESS_TOKEN_WITH_AUTH_SCHEME:"AccessToken_With_AuthScheme",REFRESH_TOKEN:"RefreshToken"},x="appmetadata",D="1",B="authority-metadata",F=86400,K="config",z="cache",G="network",$="hardcoded_values",Q={SCHEMA_VERSION:5,MAX_CUR_HEADER_BYTES:80,MAX_LAST_HEADER_BYTES:330,MAX_CACHED_ERRORS:50,CACHE_KEY:"server-telemetry",CATEGORY_SEPARATOR:"|",VALUE_SEPARATOR:",",OVERFLOW_TRUE:"1",OVERFLOW_FALSE:"0",UNKNOWN_ERROR:"unknown_error"},j={BEARER:"Bearer",POP:"pop",SSH:"ssh-cert"},W=60,V=3600,J="throttling",Y="retry-after, h429",X="invalid_grant",Z="client_mismatch",ee="1",te="3",re="4",ne="2",oe="4",ie="5",ae="0",se="1",ce="2",le="3",he="4",de={Jwt:"JWT",Jwk:"JWK",Pop:"pop"},ue="unexpected_error",ge="post_request_failed";var pe=Object.freeze({__proto__:null,postRequestFailed:ge,unexpectedError:ue});
/*! @azure/msal-common v15.7.0 2025-06-10 */const me={[ue]:"Unexpected error in authentication.",[ge]:"Post request failed from the network, could be a 4xx/5xx or a network unavailability. Please check the exact error code for details."},fe={unexpectedError:{code:ue,desc:me[ue]},postRequestFailed:{code:ge,desc:me[ge]}};class ye extends Error{constructor(e,r,n){super(r?`${e}: ${r}`:e),Object.setPrototypeOf(this,ye.prototype),this.errorCode=e||t.EMPTY_STRING,this.errorMessage=r||t.EMPTY_STRING,this.subError=n||t.EMPTY_STRING,this.name="AuthError"}setCorrelationId(e){this.correlationId=e}}function Ce(e,t){return new ye(e,t?`${me[e]} ${t}`:me[e])}
/*! @azure/msal-common v15.7.0 2025-06-10 */const ve="client_info_decoding_error",we="client_info_empty_error",Ie="token_parsing_error",Te="null_or_empty_token",Ae="endpoints_resolution_error",ke="network_error",Se="openid_config_error",be="hash_not_deserialized",Ee="invalid_state",Re="state_mismatch",_e="state_not_found",Pe="nonce_mismatch",Me="auth_time_not_found",Oe="max_age_transpired",Ne="multiple_matching_tokens",qe="multiple_matching_accounts",Le="multiple_matching_appMetadata",Ue="request_cannot_be_made",He="cannot_remove_empty_scope",xe="cannot_append_scopeset",De="empty_input_scopeset",Be="device_code_polling_cancelled",Fe="device_code_expired",Ke="device_code_unknown_error",ze="no_account_in_silent_request",Ge="invalid_cache_record",$e="invalid_cache_environment",Qe="no_account_found",je="no_crypto_object",We="unexpected_credential_type",Ve="invalid_assertion",Je="invalid_client_credential",Ye="token_refresh_required",Xe="user_timeout_reached",Ze="token_claims_cnf_required_for_signedjwt",et="authorization_code_missing_from_server_response",tt="binding_key_not_removed",rt="end_session_endpoint_not_supported",nt="key_id_missing",ot="no_network_connectivity",it="user_canceled",at="missing_tenant_id_error",st="method_not_implemented",ct="nested_app_auth_bridge_disabled";var lt=Object.freeze({__proto__:null,authTimeNotFound:Me,authorizationCodeMissingFromServerResponse:et,bindingKeyNotRemoved:tt,cannotAppendScopeSet:xe,cannotRemoveEmptyScope:He,clientInfoDecodingError:ve,clientInfoEmptyError:we,deviceCodeExpired:Fe,deviceCodePollingCancelled:Be,deviceCodeUnknownError:Ke,emptyInputScopeSet:De,endSessionEndpointNotSupported:rt,endpointResolutionError:Ae,hashNotDeserialized:be,invalidAssertion:Ve,invalidCacheEnvironment:$e,invalidCacheRecord:Ge,invalidClientCredential:Je,invalidState:Ee,keyIdMissing:nt,maxAgeTranspired:Oe,methodNotImplemented:st,missingTenantIdError:at,multipleMatchingAccounts:qe,multipleMatchingAppMetadata:Le,multipleMatchingTokens:Ne,nestedAppAuthBridgeDisabled:ct,networkError:ke,noAccountFound:Qe,noAccountInSilentRequest:ze,noCryptoObject:je,noNetworkConnectivity:ot,nonceMismatch:Pe,nullOrEmptyToken:Te,openIdConfigError:Se,requestCannotBeMade:Ue,stateMismatch:Re,stateNotFound:_e,tokenClaimsCnfRequiredForSignedJwt:Ze,tokenParsingError:Ie,tokenRefreshRequired:Ye,unexpectedCredentialType:We,userCanceled:it,userTimeoutReached:Xe});
/*! @azure/msal-common v15.7.0 2025-06-10 */const ht={[ve]:"The client info could not be parsed/decoded correctly",[we]:"The client info was empty",[Ie]:"Token cannot be parsed",[Te]:"The token is null or empty",[Ae]:"Endpoints cannot be resolved",[ke]:"Network request failed",[Se]:"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.",[be]:"The hash parameters could not be deserialized",[Ee]:"State was not the expected format",[Re]:"State mismatch error",[_e]:"State not found",[Pe]:"Nonce mismatch error",[Me]:"Max Age was requested and the ID token is missing the auth_time variable. auth_time is an optional claim and is not enabled by default - it must be enabled. See https://aka.ms/msaljs/optional-claims for more information.",[Oe]:"Max Age is set to 0, or too much time has elapsed since the last end-user authentication.",[Ne]:"The cache contains multiple tokens satisfying the requirements. Call AcquireToken again providing more requirements such as authority or account.",[qe]:"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account",[Le]:"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata",[Ue]:"Token request cannot be made without authorization code or refresh token.",[He]:"Cannot remove null or empty scope from ScopeSet",[xe]:"Cannot append ScopeSet",[De]:"Empty input ScopeSet cannot be processed",[Be]:"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.",[Fe]:"Device code is expired.",[Ke]:"Device code stopped polling for unknown reasons.",[ze]:"Please pass an account object, silent flow is not supported without account information",[Ge]:"Cache record object was null or undefined.",[$e]:"Invalid environment when attempting to create cache entry",[Qe]:"No account found in cache for given key.",[je]:"No crypto object detected.",[We]:"Unexpected credential type.",[Ve]:"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515",[Je]:"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential",[Ye]:"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.",[Xe]:"User defined timeout for device code polling reached",[Ze]:"Cannot generate a POP jwt if the token_claims are not populated",[et]:"Server response does not contain an authorization code to proceed",[tt]:"Could not remove the credential's binding key from storage.",[rt]:"The provided authority does not support logout",[nt]:"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.",[ot]:"No network connectivity. Check your internet connection.",[it]:"User cancelled the flow.",[at]:"A tenant id - not common, organizations, or consumers - must be specified when using the client_credentials flow.",[st]:"This method has not been implemented",[ct]:"The nested app auth bridge is disabled"},dt={clientInfoDecodingError:{code:ve,desc:ht[ve]},clientInfoEmptyError:{code:we,desc:ht[we]},tokenParsingError:{code:Ie,desc:ht[Ie]},nullOrEmptyToken:{code:Te,desc:ht[Te]},endpointResolutionError:{code:Ae,desc:ht[Ae]},networkError:{code:ke,desc:ht[ke]},unableToGetOpenidConfigError:{code:Se,desc:ht[Se]},hashNotDeserialized:{code:be,desc:ht[be]},invalidStateError:{code:Ee,desc:ht[Ee]},stateMismatchError:{code:Re,desc:ht[Re]},stateNotFoundError:{code:_e,desc:ht[_e]},nonceMismatchError:{code:Pe,desc:ht[Pe]},authTimeNotFoundError:{code:Me,desc:ht[Me]},maxAgeTranspired:{code:Oe,desc:ht[Oe]},multipleMatchingTokens:{code:Ne,desc:ht[Ne]},multipleMatchingAccounts:{code:qe,desc:ht[qe]},multipleMatchingAppMetadata:{code:Le,desc:ht[Le]},tokenRequestCannotBeMade:{code:Ue,desc:ht[Ue]},removeEmptyScopeError:{code:He,desc:ht[He]},appendScopeSetError:{code:xe,desc:ht[xe]},emptyInputScopeSetError:{code:De,desc:ht[De]},DeviceCodePollingCancelled:{code:Be,desc:ht[Be]},DeviceCodeExpired:{code:Fe,desc:ht[Fe]},DeviceCodeUnknownError:{code:Ke,desc:ht[Ke]},NoAccountInSilentRequest:{code:ze,desc:ht[ze]},invalidCacheRecord:{code:Ge,desc:ht[Ge]},invalidCacheEnvironment:{code:$e,desc:ht[$e]},noAccountFound:{code:Qe,desc:ht[Qe]},noCryptoObj:{code:je,desc:ht[je]},unexpectedCredentialType:{code:We,desc:ht[We]},invalidAssertion:{code:Ve,desc:ht[Ve]},invalidClientCredential:{code:Je,desc:ht[Je]},tokenRefreshRequired:{code:Ye,desc:ht[Ye]},userTimeoutReached:{code:Xe,desc:ht[Xe]},tokenClaimsRequired:{code:Ze,desc:ht[Ze]},noAuthorizationCodeFromServer:{code:et,desc:ht[et]},bindingKeyNotRemovedError:{code:tt,desc:ht[tt]},logoutNotSupported:{code:rt,desc:ht[rt]},keyIdMissing:{code:nt,desc:ht[nt]},noNetworkConnectivity:{code:ot,desc:ht[ot]},userCanceledError:{code:it,desc:ht[it]},missingTenantIdError:{code:at,desc:ht[at]},nestedAppAuthBridgeDisabled:{code:ct,desc:ht[ct]}};class ut extends ye{constructor(e,t){super(e,t?`${ht[e]}: ${t}`:ht[e]),this.name="ClientAuthError",Object.setPrototypeOf(this,ut.prototype)}}function gt(e,t){return new ut(e,t)}
/*! @azure/msal-common v15.7.0 2025-06-10 */const pt={createNewGuid:()=>{throw gt(st)},base64Decode:()=>{throw gt(st)},base64Encode:()=>{throw gt(st)},base64UrlEncode:()=>{throw gt(st)},encodeKid:()=>{throw gt(st)},async getPublicKeyThumbprint(){throw gt(st)},async removeTokenBindingKey(){throw gt(st)},async clearKeystore(){throw gt(st)},async signJwt(){throw gt(st)},async hashString(){throw gt(st)}};
/*! @azure/msal-common v15.7.0 2025-06-10 */var mt;e.LogLevel=void 0,(mt=e.LogLevel||(e.LogLevel={}))[mt.Error=0]="Error",mt[mt.Warning=1]="Warning",mt[mt.Info=2]="Info",mt[mt.Verbose=3]="Verbose",mt[mt.Trace=4]="Trace";class ft{constructor(r,n,o){this.level=e.LogLevel.Info;const i=r||ft.createDefaultLoggerOptions();this.localCallback=i.loggerCallback||(()=>{}),this.piiLoggingEnabled=i.piiLoggingEnabled||!1,this.level="number"==typeof i.logLevel?i.logLevel:e.LogLevel.Info,this.correlationId=i.correlationId||t.EMPTY_STRING,this.packageName=n||t.EMPTY_STRING,this.packageVersion=o||t.EMPTY_STRING}static createDefaultLoggerOptions(){return{loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info}}clone(e,t,r){return new ft({loggerCallback:this.localCallback,piiLoggingEnabled:this.piiLoggingEnabled,logLevel:this.level,correlationId:r||this.correlationId},e,t)}logMessage(t,r){if(r.logLevel>this.level||!this.piiLoggingEnabled&&r.containsPii)return;const n=`${`[${(new Date).toUTCString()}] : [${r.correlationId||this.correlationId||""}]`} : ${this.packageName}@${this.packageVersion} : ${e.LogLevel[r.logLevel]} - ${t}`;this.executeCallback(r.logLevel,n,r.containsPii||!1)}executeCallback(e,t,r){this.localCallback&&this.localCallback(e,t,r)}error(r,n){this.logMessage(r,{logLevel:e.LogLevel.Error,containsPii:!1,correlationId:n||t.EMPTY_STRING})}errorPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Error,containsPii:!0,correlationId:n||t.EMPTY_STRING})}warning(r,n){this.logMessage(r,{logLevel:e.LogLevel.Warning,containsPii:!1,correlationId:n||t.EMPTY_STRING})}warningPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Warning,containsPii:!0,correlationId:n||t.EMPTY_STRING})}info(r,n){this.logMessage(r,{logLevel:e.LogLevel.Info,containsPii:!1,correlationId:n||t.EMPTY_STRING})}infoPii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Info,containsPii:!0,correlationId:n||t.EMPTY_STRING})}verbose(r,n){this.logMessage(r,{logLevel:e.LogLevel.Verbose,containsPii:!1,correlationId:n||t.EMPTY_STRING})}verbosePii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Verbose,containsPii:!0,correlationId:n||t.EMPTY_STRING})}trace(r,n){this.logMessage(r,{logLevel:e.LogLevel.Trace,containsPii:!1,correlationId:n||t.EMPTY_STRING})}tracePii(r,n){this.logMessage(r,{logLevel:e.LogLevel.Trace,containsPii:!0,correlationId:n||t.EMPTY_STRING})}isPiiLoggingEnabled(){return this.piiLoggingEnabled||!1}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const yt="@azure/msal-common",Ct="15.7.0",vt={None:"none",AzurePublic:"https://login.microsoftonline.com",AzurePpe:"https://login.windows-ppe.net",AzureChina:"https://login.chinacloudapi.cn",AzureGermany:"https://login.microsoftonline.de",AzureUsGovernment:"https://login.microsoftonline.us"};
/*! @azure/msal-common v15.7.0 2025-06-10 */
function wt(e,t){const r=function(e){if(!e)throw gt(Te);const t=/^([^\.\s]*)\.([^\.\s]+)\.([^\.\s]*)$/.exec(e);if(!t||t.length<4)throw gt(Ie);return t[2]}(e);try{const e=t(r);return JSON.parse(e)}catch(e){throw gt(Ie)}}function It(e,t){if(0===t||Date.now()-3e5>e+t)throw gt(Oe)}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Tt(){return Math.round((new Date).getTime()/1e3)}function At(e){return e.getTime()/1e3}function kt(e){return e?new Date(1e3*Number(e)):new Date}function St(e,t){const r=Number(e)||0;return Tt()+t>r}function bt(e){return Number(e)>Tt()}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Et(e){return[Ot(e),Nt(e),qt(e),Lt(e),Ut(e)].join(L).toLowerCase()}function Rt(e,t,r,n,o){return{credentialType:H.ID_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r,realm:o}}function _t(e,t,r,n,o,i,a,s,c,l,h,d,u,g,p){const m={homeAccountId:e,credentialType:H.ACCESS_TOKEN,secret:r,cachedAt:Tt().toString(),expiresOn:a.toString(),extendedExpiresOn:s.toString(),environment:t,clientId:n,realm:o,target:i,tokenType:h||j.BEARER};if(d&&(m.userAssertionHash=d),l&&(m.refreshOn=l.toString()),g&&(m.requestedClaims=g,m.requestedClaimsHash=p),m.tokenType?.toLowerCase()!==j.BEARER.toLowerCase())switch(m.credentialType=H.ACCESS_TOKEN_WITH_AUTH_SCHEME,m.tokenType){case j.POP:const e=wt(r,c);if(!e?.cnf?.kid)throw gt(Ze);m.keyId=e.cnf.kid;break;case j.SSH:m.keyId=u}return m}function Pt(e,t,r,n,o,i,a){const s={credentialType:H.REFRESH_TOKEN,homeAccountId:e,environment:t,clientId:n,secret:r};return i&&(s.userAssertionHash=i),o&&(s.familyId=o),a&&(s.expiresOn=a.toString()),s}function Mt(e){return e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("credentialType")&&e.hasOwnProperty("clientId")&&e.hasOwnProperty("secret")}function Ot(e){return[e.homeAccountId,e.environment].join(L).toLowerCase()}function Nt(e){const t=e.credentialType===H.REFRESH_TOKEN&&e.familyId||e.clientId;return[e.credentialType,t,e.realm||""].join(L).toLowerCase()}function qt(e){return(e.target||"").toLowerCase()}function Lt(e){return(e.requestedClaimsHash||"").toLowerCase()}function Ut(e){return e.tokenType&&e.tokenType.toLowerCase()!==j.BEARER.toLowerCase()?e.tokenType.toLowerCase():""}function Ht(){return Tt()+F}function xt(e,t,r){e.authorization_endpoint=t.authorization_endpoint,e.token_endpoint=t.token_endpoint,e.end_session_endpoint=t.end_session_endpoint,e.issuer=t.issuer,e.endpointsFromNetwork=r,e.jwks_uri=t.jwks_uri}function Dt(e,t,r){e.aliases=t.aliases,e.preferred_cache=t.preferred_cache,e.preferred_network=t.preferred_network,e.aliasesFromNetwork=r}function Bt(e){return e.expiresAt<=Tt()}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Ft="redirect_uri_empty",Kt="claims_request_parsing_error",zt="authority_uri_insecure",Gt="url_parse_error",$t="empty_url_error",Qt="empty_input_scopes_error",jt="invalid_prompt_value",Wt="invalid_claims",Vt="token_request_empty",Jt="logout_request_empty",Yt="invalid_code_challenge_method",Xt="pkce_params_missing",Zt="invalid_cloud_discovery_metadata",er="invalid_authority_metadata",tr="untrusted_authority",rr="missing_ssh_jwk",nr="missing_ssh_kid",or="missing_nonce_authentication_header",ir="invalid_authentication_header",ar="cannot_set_OIDCOptions",sr="cannot_allow_platform_broker",cr="authority_mismatch";var lr=Object.freeze({__proto__:null,authorityMismatch:cr,authorityUriInsecure:zt,cannotAllowPlatformBroker:sr,cannotSetOIDCOptions:ar,claimsRequestParsingError:Kt,emptyInputScopesError:Qt,invalidAuthenticationHeader:ir,invalidAuthorityMetadata:er,invalidClaims:Wt,invalidCloudDiscoveryMetadata:Zt,invalidCodeChallengeMethod:Yt,invalidPromptValue:jt,logoutRequestEmpty:Jt,missingNonceAuthenticationHeader:or,missingSshJwk:rr,missingSshKid:nr,pkceParamsMissing:Xt,redirectUriEmpty:Ft,tokenRequestEmpty:Vt,untrustedAuthority:tr,urlEmptyError:$t,urlParseError:Gt});
/*! @azure/msal-common v15.7.0 2025-06-10 */const hr={[Ft]:"A redirect URI is required for all calls, and none has been set.",[Kt]:"Could not parse the given claims request object.",[zt]:"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options",[Gt]:"URL could not be parsed into appropriate segments.",[$t]:"URL was empty or null.",[Qt]:"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.",[jt]:"Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest",[Wt]:"Given claims parameter must be a stringified JSON object.",[Vt]:"Token request was empty and not found in cache.",[Jt]:"The logout request was null or undefined.",[Yt]:'code_challenge_method passed is invalid. Valid values are "plain" and "S256".',[Xt]:"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request",[Zt]:"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields",[er]:"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.",[tr]:"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.",[rr]:"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.",[nr]:"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.",[or]:"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.",[ir]:"Invalid authentication header provided",[ar]:"Cannot set OIDCOptions parameter. Please change the protocol mode to OIDC or use a non-Microsoft authority.",[sr]:"Cannot set allowPlatformBroker parameter to true when not in AAD protocol mode.",[cr]:"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority."},dr={redirectUriNotSet:{code:Ft,desc:hr[Ft]},claimsRequestParsingError:{code:Kt,desc:hr[Kt]},authorityUriInsecure:{code:zt,desc:hr[zt]},urlParseError:{code:Gt,desc:hr[Gt]},urlEmptyError:{code:$t,desc:hr[$t]},emptyScopesError:{code:Qt,desc:hr[Qt]},invalidPrompt:{code:jt,desc:hr[jt]},invalidClaimsRequest:{code:Wt,desc:hr[Wt]},tokenRequestEmptyError:{code:Vt,desc:hr[Vt]},logoutRequestEmptyError:{code:Jt,desc:hr[Jt]},invalidCodeChallengeMethod:{code:Yt,desc:hr[Yt]},invalidCodeChallengeParams:{code:Xt,desc:hr[Xt]},invalidCloudDiscoveryMetadata:{code:Zt,desc:hr[Zt]},invalidAuthorityMetadata:{code:er,desc:hr[er]},untrustedAuthority:{code:tr,desc:hr[tr]},missingSshJwk:{code:rr,desc:hr[rr]},missingSshKid:{code:nr,desc:hr[nr]},missingNonceAuthenticationHeader:{code:or,desc:hr[or]},invalidAuthenticationHeader:{code:ir,desc:hr[ir]},cannotSetOIDCOptions:{code:ar,desc:hr[ar]},cannotAllowPlatformBroker:{code:sr,desc:hr[sr]},authorityMismatch:{code:cr,desc:hr[cr]}};class ur extends ye{constructor(e){super(e,hr[e]),this.name="ClientConfigurationError",Object.setPrototypeOf(this,ur.prototype)}}function gr(e){return new ur(e)}
/*! @azure/msal-common v15.7.0 2025-06-10 */class pr{static isEmptyObj(e){if(e)try{const t=JSON.parse(e);return 0===Object.keys(t).length}catch(e){}return!0}static startsWith(e,t){return 0===e.indexOf(t)}static endsWith(e,t){return e.length>=t.length&&e.lastIndexOf(t)===e.length-t.length}static queryStringToObject(e){const t={},r=e.split("&"),n=e=>decodeURIComponent(e.replace(/\+/g," "));return r.forEach((e=>{if(e.trim()){const[r,o]=e.split(/=(.+)/g,2);r&&o&&(t[n(r)]=n(o))}})),t}static trimArrayEntries(e){return e.map((e=>e.trim()))}static removeEmptyStringsFromArray(e){return e.filter((e=>!!e))}static jsonParseHelper(e){try{return JSON.parse(e)}catch(e){return null}}static matchPattern(e,t){return new RegExp(e.replace(/\\/g,"\\\\").replace(/\*/g,"[^ ]*").replace(/\?/g,"\\?")).test(t)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class mr{constructor(e){const t=e?pr.trimArrayEntries([...e]):[],r=t?pr.removeEmptyStringsFromArray(t):[];if(!r||!r.length)throw gr(Qt);this.scopes=new Set,r.forEach((e=>this.scopes.add(e)))}static fromString(e){const r=(e||t.EMPTY_STRING).split(" ");return new mr(r)}static createSearchScopes(e){const r=new mr(e);return r.containsOnlyOIDCScopes()?r.removeScope(t.OFFLINE_ACCESS_SCOPE):r.removeOIDCScopes(),r}containsScope(e){const t=this.printScopesLowerCase().split(" "),r=new mr(t);return!!e&&r.scopes.has(e.toLowerCase())}containsScopeSet(e){return!(!e||e.scopes.size<=0)&&(this.scopes.size>=e.scopes.size&&e.asArray().every((e=>this.containsScope(e))))}containsOnlyOIDCScopes(){let e=0;return l.forEach((t=>{this.containsScope(t)&&(e+=1)})),this.scopes.size===e}appendScope(e){e&&this.scopes.add(e.trim())}appendScopes(e){try{e.forEach((e=>this.appendScope(e)))}catch(e){throw gt(xe)}}removeScope(e){if(!e)throw gt(He);this.scopes.delete(e.trim())}removeOIDCScopes(){l.forEach((e=>{this.scopes.delete(e)}))}unionScopeSets(e){if(!e)throw gt(De);const t=new Set;return e.scopes.forEach((e=>t.add(e.toLowerCase()))),this.scopes.forEach((e=>t.add(e.toLowerCase()))),t}intersectingScopeSets(e){if(!e)throw gt(De);e.containsOnlyOIDCScopes()||e.removeOIDCScopes();const t=this.unionScopeSets(e),r=e.getScopeCount(),n=this.getScopeCount();return t.size<n+r}getScopeCount(){return this.scopes.size}asArray(){const e=[];return this.scopes.forEach((t=>e.push(t))),e}printScopes(){if(this.scopes){return this.asArray().join(" ")}return t.EMPTY_STRING}printScopesLowerCase(){return this.printScopes().toLowerCase()}}
/*! @azure/msal-common v15.7.0 2025-06-10 */function fr(e,t){if(!e)throw gt(we);try{const r=t(e);return JSON.parse(r)}catch(e){throw gt(ve)}}function yr(e){if(!e)throw gt(ve);const r=e.split(U,2);return{uid:r[0],utid:r.length<2?t.EMPTY_STRING:r[1]}}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Cr(e,t){return!!e&&!!t&&e===t.split(".")[1]}function vr(e,t,r,n){if(n){const{oid:t,sub:r,tid:o,name:i,tfp:a,acr:s}=n,c=o||a||s||"";return{tenantId:c,localAccountId:t||r||"",name:i,isHomeTenant:Cr(c,e)}}return{tenantId:r,localAccountId:t,isHomeTenant:Cr(r,e)}}function wr(e,t,r,n){let o=e;if(t){const{isHomeTenant:r,...n}=t;o={...e,...n}}if(r){const{isHomeTenant:t,...i}=vr(e.homeAccountId,e.localAccountId,e.tenantId,r);return o={...o,...i,idTokenClaims:r,idToken:n},o}return o}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Ir=0,Tr=1,Ar=2,kr=3;
/*! @azure/msal-common v15.7.0 2025-06-10 */function Sr(e){if(e){return e.tid||e.tfp||e.acr||null}return null}
/*! @azure/msal-common v15.7.0 2025-06-10 */const br={AAD:"AAD",OIDC:"OIDC",EAR:"EAR"};
/*! @azure/msal-common v15.7.0 2025-06-10 */class Er{generateAccountId(){return[this.homeAccountId,this.environment].join(L).toLowerCase()}generateAccountKey(){return Er.generateAccountCacheKey({homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId})}getAccountInfo(){return{homeAccountId:this.homeAccountId,environment:this.environment,tenantId:this.realm,username:this.username,localAccountId:this.localAccountId,name:this.name,nativeAccountId:this.nativeAccountId,authorityType:this.authorityType,tenantProfiles:new Map((this.tenantProfiles||[]).map((e=>[e.tenantId,e])))}}isSingleTenant(){return!this.tenantProfiles}static generateAccountCacheKey(e){const t=e.homeAccountId.split(".")[1];return[e.homeAccountId,e.environment||"",t||e.tenantId||""].join(L).toLowerCase()}static createAccount(e,t,r){const n=new Er;let o;t.authorityType===Tr?n.authorityType=N:t.protocolMode===br.OIDC?n.authorityType=q:n.authorityType=O,e.clientInfo&&r&&(o=fr(e.clientInfo,r)),n.clientInfo=e.clientInfo,n.homeAccountId=e.homeAccountId,n.nativeAccountId=e.nativeAccountId;const i=e.environment||t&&t.getPreferredCache();if(!i)throw gt($e);n.environment=i,n.realm=o?.utid||Sr(e.idTokenClaims)||"",n.localAccountId=o?.uid||e.idTokenClaims?.oid||e.idTokenClaims?.sub||"";const a=e.idTokenClaims?.preferred_username||e.idTokenClaims?.upn,s=e.idTokenClaims?.emails?e.idTokenClaims.emails[0]:null;if(n.username=a||s||"",n.name=e.idTokenClaims?.name||"",n.cloudGraphHostName=e.cloudGraphHostName,n.msGraphHost=e.msGraphHost,e.tenantProfiles)n.tenantProfiles=e.tenantProfiles;else{const t=vr(e.homeAccountId,n.localAccountId,n.realm,e.idTokenClaims);n.tenantProfiles=[t]}return n}static createFromAccountInfo(e,t,r){const n=new Er;return n.authorityType=e.authorityType||q,n.homeAccountId=e.homeAccountId,n.localAccountId=e.localAccountId,n.nativeAccountId=e.nativeAccountId,n.realm=e.tenantId,n.environment=e.environment,n.username=e.username,n.name=e.name,n.cloudGraphHostName=t,n.msGraphHost=r,n.tenantProfiles=Array.from(e.tenantProfiles?.values()||[]),n}static generateHomeAccountId(e,t,r,n,o){if(t!==Tr&&t!==Ar){if(e)try{const t=fr(e,n.base64Decode);if(t.uid&&t.utid)return`${t.uid}.${t.utid}`}catch(e){}r.warning("No client info in response")}return o?.sub||""}static isAccountEntity(e){return!!e&&(e.hasOwnProperty("homeAccountId")&&e.hasOwnProperty("environment")&&e.hasOwnProperty("realm")&&e.hasOwnProperty("localAccountId")&&e.hasOwnProperty("username")&&e.hasOwnProperty("authorityType"))}static accountInfoIsEqual(e,t,r){if(!e||!t)return!1;let n=!0;if(r){const r=e.idTokenClaims||{},o=t.idTokenClaims||{};n=r.iat===o.iat&&r.nonce===o.nonce}return e.homeAccountId===t.homeAccountId&&e.localAccountId===t.localAccountId&&e.username===t.username&&e.tenantId===t.tenantId&&e.environment===t.environment&&e.nativeAccountId===t.nativeAccountId&&n}}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Rr(e){return e.startsWith("#/")?e.substring(2):e.startsWith("#")||e.startsWith("?")?e.substring(1):e}function _r(e){if(!e||e.indexOf("=")<0)return null;try{const t=Rr(e),r=Object.fromEntries(new URLSearchParams(t));if(r.code||r.ear_jwe||r.error||r.error_description||r.state)return r}catch(e){throw gt(be)}return null}function Pr(e,t=!0,r){const n=new Array;return e.forEach(((e,o)=>{!t&&r&&o in r?n.push(`${o}=${e}`):n.push(`${o}=${encodeURIComponent(e)}`)})),n.join("&")}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Mr{get urlString(){return this._urlString}constructor(e){if(this._urlString=e,!this._urlString)throw gr($t);e.includes("#")||(this._urlString=Mr.canonicalizeUri(e))}static canonicalizeUri(e){if(e){let t=e.toLowerCase();return pr.endsWith(t,"?")?t=t.slice(0,-1):pr.endsWith(t,"?/")&&(t=t.slice(0,-2)),pr.endsWith(t,"/")||(t+="/"),t}return e}validateAsUri(){let e;try{e=this.getUrlComponents()}catch(e){throw gr(Gt)}if(!e.HostNameAndPort||!e.PathSegments)throw gr(Gt);if(!e.Protocol||"https:"!==e.Protocol.toLowerCase())throw gr(zt)}static appendQueryString(e,t){return t?e.indexOf("?")<0?`${e}?${t}`:`${e}&${t}`:e}static removeHashFromUrl(e){return Mr.canonicalizeUri(e.split("#")[0])}replaceTenantPath(e){const t=this.getUrlComponents(),r=t.PathSegments;return!e||0===r.length||r[0]!==v&&r[0]!==w||(r[0]=e),Mr.constructAuthorityUriFromObject(t)}getUrlComponents(){const e=RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?"),t=this.urlString.match(e);if(!t)throw gr(Gt);const r={Protocol:t[1],HostNameAndPort:t[4],AbsolutePath:t[5],QueryString:t[7]};let n=r.AbsolutePath.split("/");return n=n.filter((e=>e&&e.length>0)),r.PathSegments=n,r.QueryString&&r.QueryString.endsWith("/")&&(r.QueryString=r.QueryString.substring(0,r.QueryString.length-1)),r}static getDomainFromUrl(e){const t=RegExp("^([^:/?#]+://)?([^/?#]*)"),r=e.match(t);if(!r)throw gr(Gt);return r[2]}static getAbsoluteUrl(e,r){if(e[0]===t.FORWARD_SLASH){const t=new Mr(r).getUrlComponents();return t.Protocol+"//"+t.HostNameAndPort+e}return e}static constructAuthorityUriFromObject(e){return new Mr(e.Protocol+"//"+e.HostNameAndPort+"/"+e.PathSegments.join("/"))}static hashContainsKnownProperties(e){return!!_r(e)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Or={"login.microsoftonline.com":{token_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.com/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.com/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.com/{tenantid}/oauth2/v2.0/logout"},"login.chinacloudapi.cn":{token_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.chinacloudapi.cn/{tenantid}/discovery/v2.0/keys",issuer:"https://login.partner.microsoftonline.cn/{tenantid}/v2.0",authorization_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.chinacloudapi.cn/{tenantid}/oauth2/v2.0/logout"},"login.microsoftonline.us":{token_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/token",jwks_uri:"https://login.microsoftonline.us/{tenantid}/discovery/v2.0/keys",issuer:"https://login.microsoftonline.us/{tenantid}/v2.0",authorization_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/authorize",end_session_endpoint:"https://login.microsoftonline.us/{tenantid}/oauth2/v2.0/logout"}},Nr={tenant_discovery_endpoint:"https://{canonicalAuthority}/v2.0/.well-known/openid-configuration",metadata:[{preferred_network:"login.microsoftonline.com",preferred_cache:"login.windows.net",aliases:["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{preferred_network:"login.partner.microsoftonline.cn",preferred_cache:"login.partner.microsoftonline.cn",aliases:["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{preferred_network:"login.microsoftonline.de",preferred_cache:"login.microsoftonline.de",aliases:["login.microsoftonline.de"]},{preferred_network:"login.microsoftonline.us",preferred_cache:"login.microsoftonline.us",aliases:["login.microsoftonline.us","login.usgovcloudapi.net"]},{preferred_network:"login-us.microsoftonline.com",preferred_cache:"login-us.microsoftonline.com",aliases:["login-us.microsoftonline.com"]}]},qr=new Set;function Lr(e,t,r,n){if(n?.trace(`getAliasesFromMetadata called with source: ${r}`),e&&t){const o=Ur(t,e);if(o)return n?.trace(`getAliasesFromMetadata: found cloud discovery metadata in ${r}, returning aliases`),o.aliases;n?.trace(`getAliasesFromMetadata: did not find cloud discovery metadata in ${r}`)}return null}function Ur(e,t){for(let r=0;r<e.length;r++){const n=e[r];if(n.aliases.includes(t))return n}return null}
/*! @azure/msal-common v15.7.0 2025-06-10 */Nr.metadata.forEach((e=>{e.aliases.forEach((e=>{qr.add(e)}))}));const Hr="cache_quota_exceeded",xr="cache_error_unknown",Dr={[Hr]:"Exceeded cache storage capacity.",[xr]:"Unexpected error occurred when using cache storage."};class Br extends Error{constructor(e,t){const r=t||(Dr[e]?Dr[e]:Dr[xr]);super(`${e}: ${r}`),Object.setPrototypeOf(this,Br.prototype),this.name="CacheError",this.errorCode=e,this.errorMessage=r}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Fr{constructor(e,t,r,n){this.clientId=e,this.cryptoImpl=t,this.commonLogger=r.clone(yt,Ct),this.staticAuthorityOptions=n}getAllAccounts(e){return this.buildTenantProfiles(this.getAccountsFilteredBy(e||{}),e)}getAccountInfoFilteredBy(e){const t=this.getAllAccounts(e);if(t.length>1){return t.sort((e=>e.idTokenClaims?-1:1))[0]}return 1===t.length?t[0]:null}getBaseAccountInfo(e){const t=this.getAccountsFilteredBy(e);return t.length>0?t[0].getAccountInfo():null}buildTenantProfiles(e,t){return e.flatMap((e=>this.getTenantProfilesFromAccountEntity(e,t?.tenantId,t)))}getTenantedAccountInfoByFilter(e,t,r,n){let o,i=null;if(n&&!this.tenantProfileMatchesFilter(r,n))return null;const a=this.getIdToken(e,t,r.tenantId);return a&&(o=wt(a.secret,this.cryptoImpl.base64Decode),!this.idTokenClaimsMatchTenantProfileFilter(o,n))?null:(i=wr(e,r,o,a?.secret),i)}getTenantProfilesFromAccountEntity(e,t,r){const n=e.getAccountInfo();let o=n.tenantProfiles||new Map;const i=this.getTokenKeys();if(t){const e=o.get(t);if(!e)return[];o=new Map([[t,e]])}const a=[];return o.forEach((e=>{const t=this.getTenantedAccountInfoByFilter(n,i,e,r);t&&a.push(t)})),a}tenantProfileMatchesFilter(e,t){return!(t.localAccountId&&!this.matchLocalAccountIdFromTenantProfile(e,t.localAccountId))&&((!t.name||e.name===t.name)&&(void 0===t.isHomeTenant||e.isHomeTenant===t.isHomeTenant))}idTokenClaimsMatchTenantProfileFilter(e,t){if(t){if(t.localAccountId&&!this.matchLocalAccountIdFromTokenClaims(e,t.localAccountId))return!1;if(t.loginHint&&!this.matchLoginHintFromTokenClaims(e,t.loginHint))return!1;if(t.username&&!this.matchUsername(e.preferred_username,t.username))return!1;if(t.name&&!this.matchName(e,t.name))return!1;if(t.sid&&!this.matchSid(e,t.sid))return!1}return!0}async saveCacheRecord(e,t,r){if(!e)throw gt(Ge);try{e.account&&await this.setAccount(e.account,t),e.idToken&&!1!==r?.idToken&&await this.setIdTokenCredential(e.idToken,t),e.accessToken&&!1!==r?.accessToken&&await this.saveAccessToken(e.accessToken,t),e.refreshToken&&!1!==r?.refreshToken&&await this.setRefreshTokenCredential(e.refreshToken,t),e.appMetadata&&this.setAppMetadata(e.appMetadata)}catch(e){throw this.commonLogger?.error("CacheManager.saveCacheRecord: failed"),e instanceof Error?(this.commonLogger?.errorPii(`CacheManager.saveCacheRecord: ${e.message}`,t),"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name||e.message.includes("exceeded the quota")?(this.commonLogger?.error("CacheManager.saveCacheRecord: exceeded storage quota",t),new Br(Hr)):new Br(e.name,e.message)):(this.commonLogger?.errorPii(`CacheManager.saveCacheRecord: ${e}`,t),new Br(xr))}}async saveAccessToken(e,t){const r={clientId:e.clientId,credentialType:e.credentialType,environment:e.environment,homeAccountId:e.homeAccountId,realm:e.realm,tokenType:e.tokenType,requestedClaimsHash:e.requestedClaimsHash},n=this.getTokenKeys(),o=mr.fromString(e.target),i=[];n.accessToken.forEach((e=>{if(!this.accessTokenKeyMatchesFilter(e,r,!1))return;const t=this.getAccessTokenCredential(e);if(t&&this.credentialMatchesFilter(t,r)){mr.fromString(t.target).intersectingScopeSets(o)&&i.push(this.removeAccessToken(e))}})),await Promise.all(i),await this.setAccessTokenCredential(e,t)}getAccountsFilteredBy(e){const t=this.getAccountKeys(),r=[];return t.forEach((t=>{if(!this.isAccountKey(t,e.homeAccountId))return;const n=this.getAccount(t,this.commonLogger);if(!n)return;if(e.homeAccountId&&!this.matchHomeAccountId(n,e.homeAccountId))return;if(e.username&&!this.matchUsername(n.username,e.username))return;if(e.environment&&!this.matchEnvironment(n,e.environment))return;if(e.realm&&!this.matchRealm(n,e.realm))return;if(e.nativeAccountId&&!this.matchNativeAccountId(n,e.nativeAccountId))return;if(e.authorityType&&!this.matchAuthorityType(n,e.authorityType))return;const o={localAccountId:e?.localAccountId,name:e?.name},i=n.tenantProfiles?.filter((e=>this.tenantProfileMatchesFilter(e,o)));i&&0===i.length||r.push(n)})),r}isAccountKey(e,t,r){return!(e.split(L).length<3)&&(!(t&&!e.toLowerCase().includes(t.toLowerCase()))&&!(r&&!e.toLowerCase().includes(r.toLowerCase())))}isCredentialKey(e){if(e.split(L).length<6)return!1;const t=e.toLowerCase();if(-1===t.indexOf(H.ID_TOKEN.toLowerCase())&&-1===t.indexOf(H.ACCESS_TOKEN.toLowerCase())&&-1===t.indexOf(H.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase())&&-1===t.indexOf(H.REFRESH_TOKEN.toLowerCase()))return!1;if(t.indexOf(H.REFRESH_TOKEN.toLowerCase())>-1){const e=`${H.REFRESH_TOKEN}${L}${this.clientId}${L}`,r=`${H.REFRESH_TOKEN}${L}${D}${L}`;if(-1===t.indexOf(e.toLowerCase())&&-1===t.indexOf(r.toLowerCase()))return!1}else if(-1===t.indexOf(this.clientId.toLowerCase()))return!1;return!0}credentialMatchesFilter(e,t){if(t.clientId&&!this.matchClientId(e,t.clientId))return!1;if(t.userAssertionHash&&!this.matchUserAssertionHash(e,t.userAssertionHash))return!1;if("string"==typeof t.homeAccountId&&!this.matchHomeAccountId(e,t.homeAccountId))return!1;if(t.environment&&!this.matchEnvironment(e,t.environment))return!1;if(t.realm&&!this.matchRealm(e,t.realm))return!1;if(t.credentialType&&!this.matchCredentialType(e,t.credentialType))return!1;if(t.familyId&&!this.matchFamilyId(e,t.familyId))return!1;if(t.target&&!this.matchTarget(e,t.target))return!1;if((t.requestedClaimsHash||e.requestedClaimsHash)&&e.requestedClaimsHash!==t.requestedClaimsHash)return!1;if(e.credentialType===H.ACCESS_TOKEN_WITH_AUTH_SCHEME){if(t.tokenType&&!this.matchTokenType(e,t.tokenType))return!1;if(t.tokenType===j.SSH&&t.keyId&&!this.matchKeyId(e,t.keyId))return!1}return!0}getAppMetadataFilteredBy(e){const t=this.getKeys(),r={};return t.forEach((t=>{if(!this.isAppMetadata(t))return;const n=this.getAppMetadata(t);n&&(e.environment&&!this.matchEnvironment(n,e.environment)||e.clientId&&!this.matchClientId(n,e.clientId)||(r[t]=n))})),r}getAuthorityMetadataByAlias(e){const t=this.getAuthorityMetadataKeys();let r=null;return t.forEach((t=>{if(!this.isAuthorityMetadata(t)||-1===t.indexOf(this.clientId))return;const n=this.getAuthorityMetadata(t);n&&-1!==n.aliases.indexOf(e)&&(r=n)})),r}async removeAllAccounts(){const e=this.getAccountKeys(),t=[];e.forEach((e=>{t.push(this.removeAccount(e))})),await Promise.all(t)}async removeAccount(e){const t=this.getAccount(e,this.commonLogger);t&&(await this.removeAccountContext(t),this.removeItem(e))}async removeAccountContext(e){const t=this.getTokenKeys(),r=e.generateAccountId(),n=[];t.idToken.forEach((e=>{0===e.indexOf(r)&&this.removeIdToken(e)})),t.accessToken.forEach((e=>{0===e.indexOf(r)&&n.push(this.removeAccessToken(e))})),t.refreshToken.forEach((e=>{0===e.indexOf(r)&&this.removeRefreshToken(e)})),await Promise.all(n)}async removeAccessToken(e){const t=this.getAccessTokenCredential(e);if(t){if(t.credentialType.toLowerCase()===H.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()&&t.tokenType===j.POP){const e=t.keyId;if(e)try{await this.cryptoImpl.removeTokenBindingKey(e)}catch(e){throw gt(tt)}}return this.removeItem(e)}}removeAppMetadata(){return this.getKeys().forEach((e=>{this.isAppMetadata(e)&&this.removeItem(e)})),!0}readAccountFromCache(e){const t=Er.generateAccountCacheKey(e);return this.getAccount(t,this.commonLogger)}getIdToken(e,t,r,n,o){this.commonLogger.trace("CacheManager - getIdToken called");const i={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:H.ID_TOKEN,clientId:this.clientId,realm:r},a=this.getIdTokensByFilter(i,t),s=a.size;if(s<1)return this.commonLogger.info("CacheManager:getIdToken - No token found"),null;if(s>1){let t=a;if(!r){const r=new Map;a.forEach(((t,n)=>{t.realm===e.tenantId&&r.set(n,t)}));const n=r.size;if(n<1)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account but none match account entity tenant id, returning first result"),a.values().next().value;if(1===n)return this.commonLogger.info("CacheManager:getIdToken - Multiple ID tokens found for account, defaulting to home tenant profile"),r.values().next().value;t=r}return this.commonLogger.info("CacheManager:getIdToken - Multiple matching ID tokens found, clearing them"),t.forEach(((e,t)=>{this.removeIdToken(t)})),n&&o&&n.addFields({multiMatchedID:a.size},o),null}return this.commonLogger.info("CacheManager:getIdToken - Returning ID token"),a.values().next().value}getIdTokensByFilter(e,t){const r=t&&t.idToken||this.getTokenKeys().idToken,n=new Map;return r.forEach((t=>{if(!this.idTokenKeyMatchesFilter(t,{clientId:this.clientId,...e}))return;const r=this.getIdTokenCredential(t);r&&this.credentialMatchesFilter(r,e)&&n.set(t,r)})),n}idTokenKeyMatchesFilter(e,t){const r=e.toLowerCase();return(!t.clientId||-1!==r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase()))}removeIdToken(e){this.removeItem(e)}removeRefreshToken(e){this.removeItem(e)}getAccessToken(e,t,r,n,o,i){this.commonLogger.trace("CacheManager - getAccessToken called");const a=mr.createSearchScopes(t.scopes),s=t.authenticationScheme||j.BEARER,c=s&&s.toLowerCase()!==j.BEARER.toLowerCase()?H.ACCESS_TOKEN_WITH_AUTH_SCHEME:H.ACCESS_TOKEN,l={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:c,clientId:this.clientId,realm:n||e.tenantId,target:a,tokenType:s,keyId:t.sshKid,requestedClaimsHash:t.requestedClaimsHash},h=r&&r.accessToken||this.getTokenKeys().accessToken,d=[];h.forEach((e=>{if(this.accessTokenKeyMatchesFilter(e,l,!0)){const t=this.getAccessTokenCredential(e);t&&this.credentialMatchesFilter(t,l)&&d.push(t)}}));const u=d.length;return u<1?(this.commonLogger.info("CacheManager:getAccessToken - No token found"),null):u>1?(this.commonLogger.info("CacheManager:getAccessToken - Multiple access tokens found, clearing them"),d.forEach((e=>{this.removeAccessToken(Et(e))})),o&&i&&o.addFields({multiMatchedAT:d.length},i),null):(this.commonLogger.info("CacheManager:getAccessToken - Returning access token"),d[0])}accessTokenKeyMatchesFilter(e,t,r){const n=e.toLowerCase();if(t.clientId&&-1===n.indexOf(t.clientId.toLowerCase()))return!1;if(t.homeAccountId&&-1===n.indexOf(t.homeAccountId.toLowerCase()))return!1;if(t.realm&&-1===n.indexOf(t.realm.toLowerCase()))return!1;if(t.requestedClaimsHash&&-1===n.indexOf(t.requestedClaimsHash.toLowerCase()))return!1;if(t.target){const e=t.target.asArray();for(let t=0;t<e.length;t++){if(r&&!n.includes(e[t].toLowerCase()))return!1;if(!r&&n.includes(e[t].toLowerCase()))return!0}}return!0}getAccessTokensByFilter(e){const t=this.getTokenKeys(),r=[];return t.accessToken.forEach((t=>{if(!this.accessTokenKeyMatchesFilter(t,e,!0))return;const n=this.getAccessTokenCredential(t);n&&this.credentialMatchesFilter(n,e)&&r.push(n)})),r}getRefreshToken(e,t,r,n,o){this.commonLogger.trace("CacheManager - getRefreshToken called");const i=t?D:void 0,a={homeAccountId:e.homeAccountId,environment:e.environment,credentialType:H.REFRESH_TOKEN,clientId:this.clientId,familyId:i},s=r&&r.refreshToken||this.getTokenKeys().refreshToken,c=[];s.forEach((e=>{if(this.refreshTokenKeyMatchesFilter(e,a)){const t=this.getRefreshTokenCredential(e);t&&this.credentialMatchesFilter(t,a)&&c.push(t)}}));const l=c.length;return l<1?(this.commonLogger.info("CacheManager:getRefreshToken - No refresh token found."),null):(l>1&&n&&o&&n.addFields({multiMatchedRT:l},o),this.commonLogger.info("CacheManager:getRefreshToken - returning refresh token"),c[0])}refreshTokenKeyMatchesFilter(e,t){const r=e.toLowerCase();return(!t.familyId||-1!==r.indexOf(t.familyId.toLowerCase()))&&(!(!t.familyId&&t.clientId&&-1===r.indexOf(t.clientId.toLowerCase()))&&(!t.homeAccountId||-1!==r.indexOf(t.homeAccountId.toLowerCase())))}readAppMetadataFromCache(e){const t={environment:e,clientId:this.clientId},r=this.getAppMetadataFilteredBy(t),n=Object.keys(r).map((e=>r[e])),o=n.length;if(o<1)return null;if(o>1)throw gt(Le);return n[0]}isAppMetadataFOCI(e){const t=this.readAppMetadataFromCache(e);return!(!t||t.familyId!==D)}matchHomeAccountId(e,t){return!("string"!=typeof e.homeAccountId||t!==e.homeAccountId)}matchLocalAccountIdFromTokenClaims(e,t){return t===(e.oid||e.sub)}matchLocalAccountIdFromTenantProfile(e,t){return e.localAccountId===t}matchName(e,t){return!(t.toLowerCase()!==e.name?.toLowerCase())}matchUsername(e,t){return!(!e||"string"!=typeof e||t?.toLowerCase()!==e.toLowerCase())}matchUserAssertionHash(e,t){return!(!e.userAssertionHash||t!==e.userAssertionHash)}matchEnvironment(e,t){if(this.staticAuthorityOptions){const r=function(e,t){let r;const n=e.canonicalAuthority;if(n){const o=new Mr(n).getUrlComponents().HostNameAndPort;r=Lr(o,e.cloudDiscoveryMetadata?.metadata,K,t)||Lr(o,Nr.metadata,$,t)||e.knownAuthorities}return r||[]}(this.staticAuthorityOptions,this.commonLogger);if(r.includes(t)&&r.includes(e.environment))return!0}const r=this.getAuthorityMetadataByAlias(t);return!!(r&&r.aliases.indexOf(e.environment)>-1)}matchCredentialType(e,t){return e.credentialType&&t.toLowerCase()===e.credentialType.toLowerCase()}matchClientId(e,t){return!(!e.clientId||t!==e.clientId)}matchFamilyId(e,t){return!(!e.familyId||t!==e.familyId)}matchRealm(e,t){return!(e.realm?.toLowerCase()!==t.toLowerCase())}matchNativeAccountId(e,t){return!(!e.nativeAccountId||t!==e.nativeAccountId)}matchLoginHintFromTokenClaims(e,t){return e.login_hint===t||(e.preferred_username===t||e.upn===t)}matchSid(e,t){return e.sid===t}matchAuthorityType(e,t){return!(!e.authorityType||t.toLowerCase()!==e.authorityType.toLowerCase())}matchTarget(e,t){if(e.credentialType!==H.ACCESS_TOKEN&&e.credentialType!==H.ACCESS_TOKEN_WITH_AUTH_SCHEME||!e.target)return!1;return mr.fromString(e.target).containsScopeSet(t)}matchTokenType(e,t){return!(!e.tokenType||e.tokenType!==t)}matchKeyId(e,t){return!(!e.keyId||e.keyId!==t)}isAppMetadata(e){return-1!==e.indexOf(x)}isAuthorityMetadata(e){return-1!==e.indexOf(B)}generateAuthorityMetadataCacheKey(e){return`${B}-${this.clientId}-${e}`}static toObject(e,t){for(const r in t)e[r]=t[r];return e}}class Kr extends Fr{async setAccount(){throw gt(st)}getAccount(){throw gt(st)}async setIdTokenCredential(){throw gt(st)}getIdTokenCredential(){throw gt(st)}async setAccessTokenCredential(){throw gt(st)}getAccessTokenCredential(){throw gt(st)}async setRefreshTokenCredential(){throw gt(st)}getRefreshTokenCredential(){throw gt(st)}setAppMetadata(){throw gt(st)}getAppMetadata(){throw gt(st)}setServerTelemetry(){throw gt(st)}getServerTelemetry(){throw gt(st)}setAuthorityMetadata(){throw gt(st)}getAuthorityMetadata(){throw gt(st)}getAuthorityMetadataKeys(){throw gt(st)}setThrottlingCache(){throw gt(st)}getThrottlingCache(){throw gt(st)}removeItem(){throw gt(st)}getKeys(){throw gt(st)}getAccountKeys(){throw gt(st)}getTokenKeys(){throw gt(st)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const zr={tokenRenewalOffsetSeconds:300,preventCorsPreflight:!1},Gr={loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:e.LogLevel.Info,correlationId:t.EMPTY_STRING},$r={claimsBasedCachingEnabled:!1},Qr={async sendGetRequestAsync(){throw gt(st)},async sendPostRequestAsync(){throw gt(st)}},jr={sku:t.SKU,version:Ct,cpu:t.EMPTY_STRING,os:t.EMPTY_STRING},Wr={clientSecret:t.EMPTY_STRING,clientAssertion:void 0},Vr={azureCloudInstance:vt.None,tenant:`${t.DEFAULT_COMMON_TENANT}`},Jr={application:{appName:"",appVersion:""}};function Yr(e){return e.authOptions.authority.options.protocolMode===br.OIDC}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Xr="home_account_id",Zr="UPN",en="client_id",tn="redirect_uri",rn="token_type",nn="req_cnf",on="return_spa_code",an="x-client-xtra-sku",sn="brk_client_id",cn="brk_redirect_uri",ln="instance_aware";
/*! @azure/msal-common v15.7.0 2025-06-10 */
/*! @azure/msal-common v15.7.0 2025-06-10 */
function hn(e,t,r){if(!t)return;const n=e.get(en);n&&e.has(sn)&&r?.addFields({embeddedClientId:n,embeddedRedirectUri:e.get(tn)},t)}function dn(e,t){e.set("response_type",t)}function un(e,t,r=!0,n=c){!r||n.includes("openid")||t.includes("openid")||n.push("openid");const o=r?[...t||[],...n]:t||[],i=new mr(o);e.set("scope",i.printScopes())}function gn(e,t){e.set(en,t)}function pn(e,t){e.set(tn,t)}function mn(e,t){e.set("login_hint",t)}function fn(e,t){e.set(g,`UPN:${t}`)}function yn(e,t){e.set(g,`Oid:${t.uid}@${t.utid}`)}function Cn(e,t){e.set("sid",t)}function vn(e,t,r){const n=Mn(t,r);try{JSON.parse(n)}catch(e){throw gr(Wt)}e.set("claims",n)}function wn(e,t){e.set("client-request-id",t)}function In(e,t){e.set("x-client-SKU",t.sku),e.set("x-client-VER",t.version),t.os&&e.set("x-client-OS",t.os),t.cpu&&e.set("x-client-CPU",t.cpu)}function Tn(e,t){t?.appName&&e.set("x-app-name",t.appName),t?.appVersion&&e.set("x-app-ver",t.appVersion)}function An(e,t){t&&e.set("state",t)}function kn(e,t){e.set("client_secret",t)}function Sn(e,t){t&&e.set("client_assertion",t)}function bn(e,t){t&&e.set("client_assertion_type",t)}function En(e,t){e.set("grant_type",t)}function Rn(e){e.set("client_info","1")}function _n(e){e.has(ln)||e.set(ln,"true")}function Pn(e,t){Object.entries(t).forEach((([t,r])=>{!e.has(t)&&r&&e.set(t,r)}))}function Mn(e,t){let r;if(e)try{r=JSON.parse(e)}catch(e){throw gr(Wt)}else r={};return t&&t.length>0&&(r.hasOwnProperty(T)||(r[T]={}),r[T][A]={values:t}),JSON.stringify(r)}function On(e,t){t&&(e.set(rn,j.POP),e.set(nn,t))}function Nn(e,t){t&&(e.set(rn,j.SSH),e.set(nn,t))}function qn(e,t){e.set("x-client-current-telemetry",t.generateCurrentRequestHeaderValue()),e.set("x-client-last-telemetry",t.generateLastRequestHeaderValue())}function Ln(e){e.set("x-ms-lib-capability",Y)}function Un(e,t,r){e.has(sn)||e.set(sn,t),e.has(cn)||e.set(cn,r)}
/*! @azure/msal-common v15.7.0 2025-06-10 */
const Hn={AcquireTokenByCode:"acquireTokenByCode",AcquireTokenByRefreshToken:"acquireTokenByRefreshToken",AcquireTokenSilent:"acquireTokenSilent",AcquireTokenSilentAsync:"acquireTokenSilentAsync",AcquireTokenPopup:"acquireTokenPopup",AcquireTokenPreRedirect:"acquireTokenPreRedirect",AcquireTokenRedirect:"acquireTokenRedirect",CryptoOptsGetPublicKeyThumbprint:"cryptoOptsGetPublicKeyThumbprint",CryptoOptsSignJwt:"cryptoOptsSignJwt",SilentCacheClientAcquireToken:"silentCacheClientAcquireToken",SilentIframeClientAcquireToken:"silentIframeClientAcquireToken",AwaitConcurrentIframe:"awaitConcurrentIframe",SilentRefreshClientAcquireToken:"silentRefreshClientAcquireToken",SsoSilent:"ssoSilent",StandardInteractionClientGetDiscoveredAuthority:"standardInteractionClientGetDiscoveredAuthority",FetchAccountIdWithNativeBroker:"fetchAccountIdWithNativeBroker",NativeInteractionClientAcquireToken:"nativeInteractionClientAcquireToken",BaseClientCreateTokenRequestHeaders:"baseClientCreateTokenRequestHeaders",NetworkClientSendPostRequestAsync:"networkClientSendPostRequestAsync",RefreshTokenClientExecutePostToTokenEndpoint:"refreshTokenClientExecutePostToTokenEndpoint",AuthorizationCodeClientExecutePostToTokenEndpoint:"authorizationCodeClientExecutePostToTokenEndpoint",BrokerHandhshake:"brokerHandshake",AcquireTokenByRefreshTokenInBroker:"acquireTokenByRefreshTokenInBroker",AcquireTokenByBroker:"acquireTokenByBroker",RefreshTokenClientExecuteTokenRequest:"refreshTokenClientExecuteTokenRequest",RefreshTokenClientAcquireToken:"refreshTokenClientAcquireToken",RefreshTokenClientAcquireTokenWithCachedRefreshToken:"refreshTokenClientAcquireTokenWithCachedRefreshToken",RefreshTokenClientAcquireTokenByRefreshToken:"refreshTokenClientAcquireTokenByRefreshToken",RefreshTokenClientCreateTokenRequestBody:"refreshTokenClientCreateTokenRequestBody",AcquireTokenFromCache:"acquireTokenFromCache",SilentFlowClientAcquireCachedToken:"silentFlowClientAcquireCachedToken",SilentFlowClientGenerateResultFromCacheRecord:"silentFlowClientGenerateResultFromCacheRecord",AcquireTokenBySilentIframe:"acquireTokenBySilentIframe",InitializeBaseRequest:"initializeBaseRequest",InitializeSilentRequest:"initializeSilentRequest",InitializeClientApplication:"initializeClientApplication",InitializeCache:"initializeCache",SilentIframeClientTokenHelper:"silentIframeClientTokenHelper",SilentHandlerInitiateAuthRequest:"silentHandlerInitiateAuthRequest",SilentHandlerMonitorIframeForHash:"silentHandlerMonitorIframeForHash",SilentHandlerLoadFrame:"silentHandlerLoadFrame",SilentHandlerLoadFrameSync:"silentHandlerLoadFrameSync",StandardInteractionClientCreateAuthCodeClient:"standardInteractionClientCreateAuthCodeClient",StandardInteractionClientGetClientConfiguration:"standardInteractionClientGetClientConfiguration",StandardInteractionClientInitializeAuthorizationRequest:"standardInteractionClientInitializeAuthorizationRequest",GetAuthCodeUrl:"getAuthCodeUrl",GetStandardParams:"getStandardParams",HandleCodeResponseFromServer:"handleCodeResponseFromServer",HandleCodeResponse:"handleCodeResponse",HandleResponseEar:"handleResponseEar",HandleResponsePlatformBroker:"handleResponsePlatformBroker",HandleResponseCode:"handleResponseCode",UpdateTokenEndpointAuthority:"updateTokenEndpointAuthority",AuthClientAcquireToken:"authClientAcquireToken",AuthClientExecuteTokenRequest:"authClientExecuteTokenRequest",AuthClientCreateTokenRequestBody:"authClientCreateTokenRequestBody",PopTokenGenerateCnf:"popTokenGenerateCnf",PopTokenGenerateKid:"popTokenGenerateKid",HandleServerTokenResponse:"handleServerTokenResponse",DeserializeResponse:"deserializeResponse",AuthorityFactoryCreateDiscoveredInstance:"authorityFactoryCreateDiscoveredInstance",AuthorityResolveEndpointsAsync:"authorityResolveEndpointsAsync",AuthorityResolveEndpointsFromLocalSources:"authorityResolveEndpointsFromLocalSources",AuthorityGetCloudDiscoveryMetadataFromNetwork:"authorityGetCloudDiscoveryMetadataFromNetwork",AuthorityUpdateCloudDiscoveryMetadata:"authorityUpdateCloudDiscoveryMetadata",AuthorityGetEndpointMetadataFromNetwork:"authorityGetEndpointMetadataFromNetwork",AuthorityUpdateEndpointMetadata:"authorityUpdateEndpointMetadata",AuthorityUpdateMetadataWithRegionalInformation:"authorityUpdateMetadataWithRegionalInformation",RegionDiscoveryDetectRegion:"regionDiscoveryDetectRegion",RegionDiscoveryGetRegionFromIMDS:"regionDiscoveryGetRegionFromIMDS",RegionDiscoveryGetCurrentVersion:"regionDiscoveryGetCurrentVersion",AcquireTokenByCodeAsync:"acquireTokenByCodeAsync",GetEndpointMetadataFromNetwork:"getEndpointMetadataFromNetwork",GetCloudDiscoveryMetadataFromNetworkMeasurement:"getCloudDiscoveryMetadataFromNetworkMeasurement",HandleRedirectPromiseMeasurement:"handleRedirectPromise",HandleNativeRedirectPromiseMeasurement:"handleNativeRedirectPromise",UpdateCloudDiscoveryMetadataMeasurement:"updateCloudDiscoveryMetadataMeasurement",UsernamePasswordClientAcquireToken:"usernamePasswordClientAcquireToken",NativeMessageHandlerHandshake:"nativeMessageHandlerHandshake",NativeGenerateAuthResult:"nativeGenerateAuthResult",RemoveHiddenIframe:"removeHiddenIframe",ClearTokensAndKeysWithClaims:"clearTokensAndKeysWithClaims",CacheManagerGetRefreshToken:"cacheManagerGetRefreshToken",ImportExistingCache:"importExistingCache",SetUserData:"setUserData",LocalStorageUpdated:"localStorageUpdated",GeneratePkceCodes:"generatePkceCodes",GenerateCodeVerifier:"generateCodeVerifier",GenerateCodeChallengeFromVerifier:"generateCodeChallengeFromVerifier",Sha256Digest:"sha256Digest",GetRandomValues:"getRandomValues",GenerateHKDF:"generateHKDF",GenerateBaseKey:"generateBaseKey",Base64Decode:"base64Decode",UrlEncodeArr:"urlEncodeArr",Encrypt:"encrypt",Decrypt:"decrypt",GenerateEarKey:"generateEarKey",DecryptEarResponse:"decryptEarResponse"},xn=new Map([[Hn.AcquireTokenByCode,"ATByCode"],[Hn.AcquireTokenByRefreshToken,"ATByRT"],[Hn.AcquireTokenSilent,"ATS"],[Hn.AcquireTokenSilentAsync,"ATSAsync"],[Hn.AcquireTokenPopup,"ATPopup"],[Hn.AcquireTokenRedirect,"ATRedirect"],[Hn.CryptoOptsGetPublicKeyThumbprint,"CryptoGetPKThumb"],[Hn.CryptoOptsSignJwt,"CryptoSignJwt"],[Hn.SilentCacheClientAcquireToken,"SltCacheClientAT"],[Hn.SilentIframeClientAcquireToken,"SltIframeClientAT"],[Hn.SilentRefreshClientAcquireToken,"SltRClientAT"],[Hn.SsoSilent,"SsoSlt"],[Hn.StandardInteractionClientGetDiscoveredAuthority,"StdIntClientGetDiscAuth"],[Hn.FetchAccountIdWithNativeBroker,"FetchAccIdWithNtvBroker"],[Hn.NativeInteractionClientAcquireToken,"NtvIntClientAT"],[Hn.BaseClientCreateTokenRequestHeaders,"BaseClientCreateTReqHead"],[Hn.NetworkClientSendPostRequestAsync,"NetClientSendPost"],[Hn.RefreshTokenClientExecutePostToTokenEndpoint,"RTClientExecPost"],[Hn.AuthorizationCodeClientExecutePostToTokenEndpoint,"AuthCodeClientExecPost"],[Hn.BrokerHandhshake,"BrokerHandshake"],[Hn.AcquireTokenByRefreshTokenInBroker,"ATByRTInBroker"],[Hn.AcquireTokenByBroker,"ATByBroker"],[Hn.RefreshTokenClientExecuteTokenRequest,"RTClientExecTReq"],[Hn.RefreshTokenClientAcquireToken,"RTClientAT"],[Hn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,"RTClientATWithCachedRT"],[Hn.RefreshTokenClientAcquireTokenByRefreshToken,"RTClientATByRT"],[Hn.RefreshTokenClientCreateTokenRequestBody,"RTClientCreateTReqBody"],[Hn.AcquireTokenFromCache,"ATFromCache"],[Hn.SilentFlowClientAcquireCachedToken,"SltFlowClientATCached"],[Hn.SilentFlowClientGenerateResultFromCacheRecord,"SltFlowClientGenResFromCache"],[Hn.AcquireTokenBySilentIframe,"ATBySltIframe"],[Hn.InitializeBaseRequest,"InitBaseReq"],[Hn.InitializeSilentRequest,"InitSltReq"],[Hn.InitializeClientApplication,"InitClientApplication"],[Hn.InitializeCache,"InitCache"],[Hn.ImportExistingCache,"importCache"],[Hn.SetUserData,"setUserData"],[Hn.LocalStorageUpdated,"localStorageUpdated"],[Hn.SilentIframeClientTokenHelper,"SIClientTHelper"],[Hn.SilentHandlerInitiateAuthRequest,"SHandlerInitAuthReq"],[Hn.SilentHandlerMonitorIframeForHash,"SltHandlerMonitorIframeForHash"],[Hn.SilentHandlerLoadFrame,"SHandlerLoadFrame"],[Hn.SilentHandlerLoadFrameSync,"SHandlerLoadFrameSync"],[Hn.StandardInteractionClientCreateAuthCodeClient,"StdIntClientCreateAuthCodeClient"],[Hn.StandardInteractionClientGetClientConfiguration,"StdIntClientGetClientConf"],[Hn.StandardInteractionClientInitializeAuthorizationRequest,"StdIntClientInitAuthReq"],[Hn.GetAuthCodeUrl,"GetAuthCodeUrl"],[Hn.HandleCodeResponseFromServer,"HandleCodeResFromServer"],[Hn.HandleCodeResponse,"HandleCodeResp"],[Hn.HandleResponseEar,"HandleRespEar"],[Hn.HandleResponseCode,"HandleRespCode"],[Hn.HandleResponsePlatformBroker,"HandleRespPlatBroker"],[Hn.UpdateTokenEndpointAuthority,"UpdTEndpointAuth"],[Hn.AuthClientAcquireToken,"AuthClientAT"],[Hn.AuthClientExecuteTokenRequest,"AuthClientExecTReq"],[Hn.AuthClientCreateTokenRequestBody,"AuthClientCreateTReqBody"],[Hn.PopTokenGenerateCnf,"PopTGenCnf"],[Hn.PopTokenGenerateKid,"PopTGenKid"],[Hn.HandleServerTokenResponse,"HandleServerTRes"],[Hn.DeserializeResponse,"DeserializeRes"],[Hn.AuthorityFactoryCreateDiscoveredInstance,"AuthFactCreateDiscInst"],[Hn.AuthorityResolveEndpointsAsync,"AuthResolveEndpointsAsync"],[Hn.AuthorityResolveEndpointsFromLocalSources,"AuthResolveEndpointsFromLocal"],[Hn.AuthorityGetCloudDiscoveryMetadataFromNetwork,"AuthGetCDMetaFromNet"],[Hn.AuthorityUpdateCloudDiscoveryMetadata,"AuthUpdCDMeta"],[Hn.AuthorityGetEndpointMetadataFromNetwork,"AuthUpdCDMetaFromNet"],[Hn.AuthorityUpdateEndpointMetadata,"AuthUpdEndpointMeta"],[Hn.AuthorityUpdateMetadataWithRegionalInformation,"AuthUpdMetaWithRegInfo"],[Hn.RegionDiscoveryDetectRegion,"RegDiscDetectReg"],[Hn.RegionDiscoveryGetRegionFromIMDS,"RegDiscGetRegFromIMDS"],[Hn.RegionDiscoveryGetCurrentVersion,"RegDiscGetCurrentVer"],[Hn.AcquireTokenByCodeAsync,"ATByCodeAsync"],[Hn.GetEndpointMetadataFromNetwork,"GetEndpointMetaFromNet"],[Hn.GetCloudDiscoveryMetadataFromNetworkMeasurement,"GetCDMetaFromNet"],[Hn.HandleRedirectPromiseMeasurement,"HandleRedirectPromise"],[Hn.HandleNativeRedirectPromiseMeasurement,"HandleNtvRedirectPromise"],[Hn.UpdateCloudDiscoveryMetadataMeasurement,"UpdateCDMeta"],[Hn.UsernamePasswordClientAcquireToken,"UserPassClientAT"],[Hn.NativeMessageHandlerHandshake,"NtvMsgHandlerHandshake"],[Hn.NativeGenerateAuthResult,"NtvGenAuthRes"],[Hn.RemoveHiddenIframe,"RemoveHiddenIframe"],[Hn.ClearTokensAndKeysWithClaims,"ClearTAndKeysWithClaims"],[Hn.CacheManagerGetRefreshToken,"CacheManagerGetRT"],[Hn.GeneratePkceCodes,"GenPkceCodes"],[Hn.GenerateCodeVerifier,"GenCodeVerifier"],[Hn.GenerateCodeChallengeFromVerifier,"GenCodeChallengeFromVerifier"],[Hn.Sha256Digest,"Sha256Digest"],[Hn.GetRandomValues,"GetRandomValues"],[Hn.GenerateHKDF,"genHKDF"],[Hn.GenerateBaseKey,"genBaseKey"],[Hn.Base64Decode,"b64Decode"],[Hn.UrlEncodeArr,"urlEncArr"],[Hn.Encrypt,"encrypt"],[Hn.Decrypt,"decrypt"],[Hn.GenerateEarKey,"genEarKey"],[Hn.DecryptEarResponse,"decryptEarResp"]]),Dn=1,Bn=2,Fn=new Set(["accessTokenSize","durationMs","idTokenSize","matsSilentStatus","matsHttpStatus","refreshTokenSize","queuedTimeMs","startTimeMs","status","multiMatchedAT","multiMatchedID","multiMatchedRT","unencryptedCacheCount","encryptedCacheExpiredCount"]),Kn=(e,t,r,n,o)=>(...i)=>{r.trace(`Executing function ${t}`);const a=n?.startMeasurement(t,o);if(o){const e=t+"CallCount";n?.incrementFields({[e]:1},o)}try{const n=e(...i);return a?.end({success:!0}),r.trace(`Returning result from ${t}`),n}catch(e){r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e}},zn=(e,t,r,n,o)=>(...i)=>{r.trace(`Executing function ${t}`);const a=n?.startMeasurement(t,o);if(o){const e=t+"CallCount";n?.incrementFields({[e]:1},o)}return n?.setPreQueueTime(t,o),e(...i).then((e=>(r.trace(`Returning result from ${t}`),a?.end({success:!0}),e))).catch((e=>{r.trace(`Error occurred in ${t}`);try{r.trace(JSON.stringify(e))}catch(e){r.trace("Unable to print error message.")}throw a?.end({success:!1},e),e}))};
/*! @azure/msal-common v15.7.0 2025-06-10 */
class Gn{constructor(e,t,r,n){this.networkInterface=e,this.logger=t,this.performanceClient=r,this.correlationId=n}async detectRegion(e,n){this.performanceClient?.addQueueMeasurement(Hn.RegionDiscoveryDetectRegion,this.correlationId);let i=e;if(i)n.region_source=te;else{const e=Gn.IMDS_OPTIONS;try{const a=await zn(this.getRegionFromIMDS.bind(this),Hn.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(t.IMDS_VERSION,e);if(a.status===r&&(i=a.body,n.region_source=re),a.status===o){const t=await zn(this.getCurrentVersion.bind(this),Hn.RegionDiscoveryGetCurrentVersion,this.logger,this.performanceClient,this.correlationId)(e);if(!t)return n.region_source=ee,null;const o=await zn(this.getRegionFromIMDS.bind(this),Hn.RegionDiscoveryGetRegionFromIMDS,this.logger,this.performanceClient,this.correlationId)(t,e);o.status===r&&(i=o.body,n.region_source=re)}}catch(e){return n.region_source=ee,null}}return i||(n.region_source=ee),i||null}async getRegionFromIMDS(e,r){return this.performanceClient?.addQueueMeasurement(Hn.RegionDiscoveryGetRegionFromIMDS,this.correlationId),this.networkInterface.sendGetRequestAsync(`${t.IMDS_ENDPOINT}?api-version=${e}&format=text`,r,t.IMDS_TIMEOUT)}async getCurrentVersion(e){this.performanceClient?.addQueueMeasurement(Hn.RegionDiscoveryGetCurrentVersion,this.correlationId);try{const r=await this.networkInterface.sendGetRequestAsync(`${t.IMDS_ENDPOINT}?format=json`,e);return r.status===o&&r.body&&r.body["newest-versions"]&&r.body["newest-versions"].length>0?r.body["newest-versions"][0]:null}catch(e){return null}}}Gn.IMDS_OPTIONS={headers:{Metadata:"true"}};
/*! @azure/msal-common v15.7.0 2025-06-10 */
class $n{constructor(e,t,r,n,o,i,a,s){this.canonicalAuthority=e,this._canonicalAuthority.validateAsUri(),this.networkInterface=t,this.cacheManager=r,this.authorityOptions=n,this.regionDiscoveryMetadata={region_used:void 0,region_source:void 0,region_outcome:void 0},this.logger=o,this.performanceClient=a,this.correlationId=i,this.managedIdentity=s||!1,this.regionDiscovery=new Gn(t,this.logger,this.performanceClient,this.correlationId)}getAuthorityType(e){if(e.HostNameAndPort.endsWith(t.CIAM_AUTH_URL))return kr;const r=e.PathSegments;if(r.length)switch(r[0].toLowerCase()){case t.ADFS:return Tr;case t.DSTS:return Ar}return Ir}get authorityType(){return this.getAuthorityType(this.canonicalAuthorityUrlComponents)}get protocolMode(){return this.authorityOptions.protocolMode}get options(){return this.authorityOptions}get canonicalAuthority(){return this._canonicalAuthority.urlString}set canonicalAuthority(e){this._canonicalAuthority=new Mr(e),this._canonicalAuthority.validateAsUri(),this._canonicalAuthorityUrlComponents=null}get canonicalAuthorityUrlComponents(){return this._canonicalAuthorityUrlComponents||(this._canonicalAuthorityUrlComponents=this._canonicalAuthority.getUrlComponents()),this._canonicalAuthorityUrlComponents}get hostnameAndPort(){return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase()}get tenant(){return this.canonicalAuthorityUrlComponents.PathSegments[0]}get authorizationEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.authorization_endpoint);throw gt(Ae)}get tokenEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint);throw gt(Ae)}get deviceCodeEndpoint(){if(this.discoveryComplete())return this.replacePath(this.metadata.token_endpoint.replace("/token","/devicecode"));throw gt(Ae)}get endSessionEndpoint(){if(this.discoveryComplete()){if(!this.metadata.end_session_endpoint)throw gt(rt);return this.replacePath(this.metadata.end_session_endpoint)}throw gt(Ae)}get selfSignedJwtAudience(){if(this.discoveryComplete())return this.replacePath(this.metadata.issuer);throw gt(Ae)}get jwksUri(){if(this.discoveryComplete())return this.replacePath(this.metadata.jwks_uri);throw gt(Ae)}canReplaceTenant(e){return 1===e.PathSegments.length&&!$n.reservedTenantDomains.has(e.PathSegments[0])&&this.getAuthorityType(e)===Ir&&this.protocolMode!==br.OIDC}replaceTenant(e){return e.replace(/{tenant}|{tenantid}/g,this.tenant)}replacePath(e){let t=e;const r=new Mr(this.metadata.canonical_authority).getUrlComponents(),n=r.PathSegments;return this.canonicalAuthorityUrlComponents.PathSegments.forEach(((e,o)=>{let i=n[o];if(0===o&&this.canReplaceTenant(r)){const e=new Mr(this.metadata.authorization_endpoint).getUrlComponents().PathSegments[0];i!==e&&(this.logger.verbose(`Replacing tenant domain name ${i} with id ${e}`),i=e)}e!==i&&(t=t.replace(`/${i}/`,`/${e}/`))})),this.replaceTenant(t)}get defaultOpenIdConfigurationEndpoint(){const e=this.hostnameAndPort;return this.canonicalAuthority.endsWith("v2.0/")||this.authorityType===Tr||this.protocolMode===br.OIDC&&!this.isAliasOfKnownMicrosoftAuthority(e)?`${this.canonicalAuthority}.well-known/openid-configuration`:`${this.canonicalAuthority}v2.0/.well-known/openid-configuration`}discoveryComplete(){return!!this.metadata}async resolveEndpointsAsync(){this.performanceClient?.addQueueMeasurement(Hn.AuthorityResolveEndpointsAsync,this.correlationId);const e=this.getCurrentMetadataEntity(),t=await zn(this.updateCloudDiscoveryMetadata.bind(this),Hn.AuthorityUpdateCloudDiscoveryMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.canonicalAuthority=this.canonicalAuthority.replace(this.hostnameAndPort,e.preferred_network);const r=await zn(this.updateEndpointMetadata.bind(this),Hn.AuthorityUpdateEndpointMetadata,this.logger,this.performanceClient,this.correlationId)(e);this.updateCachedMetadata(e,t,{source:r}),this.performanceClient?.addFields({cloudDiscoverySource:t,authorityEndpointSource:r},this.correlationId)}getCurrentMetadataEntity(){let e=this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);return e||(e={aliases:[],preferred_cache:this.hostnameAndPort,preferred_network:this.hostnameAndPort,canonical_authority:this.canonicalAuthority,authorization_endpoint:"",token_endpoint:"",end_session_endpoint:"",issuer:"",aliasesFromNetwork:!1,endpointsFromNetwork:!1,expiresAt:Ht(),jwks_uri:""}),e}updateCachedMetadata(e,t,r){t!==z&&r?.source!==z&&(e.expiresAt=Ht(),e.canonical_authority=this.canonicalAuthority);const n=this.cacheManager.generateAuthorityMetadataCacheKey(e.preferred_cache);this.cacheManager.setAuthorityMetadata(n,e),this.metadata=e}async updateEndpointMetadata(e){this.performanceClient?.addQueueMeasurement(Hn.AuthorityUpdateEndpointMetadata,this.correlationId);const t=this.updateEndpointMetadataFromLocalSources(e);if(t){if(t.source===$&&this.authorityOptions.azureRegionConfiguration?.azureRegion&&t.metadata){xt(e,await zn(this.updateMetadataWithRegionalInformation.bind(this),Hn.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(t.metadata),!1),e.canonical_authority=this.canonicalAuthority}return t.source}let r=await zn(this.getEndpointMetadataFromNetwork.bind(this),Hn.AuthorityGetEndpointMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return this.authorityOptions.azureRegionConfiguration?.azureRegion&&(r=await zn(this.updateMetadataWithRegionalInformation.bind(this),Hn.AuthorityUpdateMetadataWithRegionalInformation,this.logger,this.performanceClient,this.correlationId)(r)),xt(e,r,!0),G;throw gt(Se,this.defaultOpenIdConfigurationEndpoint)}updateEndpointMetadataFromLocalSources(e){this.logger.verbose("Attempting to get endpoint metadata from authority configuration");const t=this.getEndpointMetadataFromConfig();if(t)return this.logger.verbose("Found endpoint metadata in authority configuration"),xt(e,t,!1),{source:K};if(this.logger.verbose("Did not find endpoint metadata in the config... Attempting to get endpoint metadata from the hardcoded values."),this.authorityOptions.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get endpoint metadata from the network metadata cache.");else{const t=this.getEndpointMetadataFromHardcodedValues();if(t)return xt(e,t,!1),{source:$,metadata:t};this.logger.verbose("Did not find endpoint metadata in hardcoded values... Attempting to get endpoint metadata from the network metadata cache.")}const r=Bt(e);return this.isAuthoritySameType(e)&&e.endpointsFromNetwork&&!r?(this.logger.verbose("Found endpoint metadata in the cache."),{source:z}):(r&&this.logger.verbose("The metadata entity is expired."),null)}isAuthoritySameType(e){return new Mr(e.canonical_authority).getUrlComponents().PathSegments.length===this.canonicalAuthorityUrlComponents.PathSegments.length}getEndpointMetadataFromConfig(){if(this.authorityOptions.authorityMetadata)try{return JSON.parse(this.authorityOptions.authorityMetadata)}catch(e){throw gr(er)}return null}async getEndpointMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(Hn.AuthorityGetEndpointMetadataFromNetwork,this.correlationId);const e={},t=this.defaultOpenIdConfigurationEndpoint;this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: attempting to retrieve OAuth endpoints from ${t}`);try{const r=await this.networkInterface.sendGetRequestAsync(t,e),n=
/*! @azure/msal-common v15.7.0 2025-06-10 */
function(e){return e.hasOwnProperty("authorization_endpoint")&&e.hasOwnProperty("token_endpoint")&&e.hasOwnProperty("issuer")&&e.hasOwnProperty("jwks_uri")}
/*! @azure/msal-common v15.7.0 2025-06-10 */(r.body);return n?r.body:(this.logger.verbose("Authority.getEndpointMetadataFromNetwork: could not parse response as OpenID configuration"),null)}catch(e){return this.logger.verbose(`Authority.getEndpointMetadataFromNetwork: ${e}`),null}}getEndpointMetadataFromHardcodedValues(){return this.hostnameAndPort in Or?Or[this.hostnameAndPort]:null}async updateMetadataWithRegionalInformation(e){this.performanceClient?.addQueueMeasurement(Hn.AuthorityUpdateMetadataWithRegionalInformation,this.correlationId);const r=this.authorityOptions.azureRegionConfiguration?.azureRegion;if(r){if(r!==t.AZURE_REGION_AUTO_DISCOVER_FLAG)return this.regionDiscoveryMetadata.region_outcome=ne,this.regionDiscoveryMetadata.region_used=r,$n.replaceWithRegionalInformation(e,r);const n=await zn(this.regionDiscovery.detectRegion.bind(this.regionDiscovery),Hn.RegionDiscoveryDetectRegion,this.logger,this.performanceClient,this.correlationId)(this.authorityOptions.azureRegionConfiguration?.environmentRegion,this.regionDiscoveryMetadata);if(n)return this.regionDiscoveryMetadata.region_outcome=oe,this.regionDiscoveryMetadata.region_used=n,$n.replaceWithRegionalInformation(e,n);this.regionDiscoveryMetadata.region_outcome=ie}return e}async updateCloudDiscoveryMetadata(e){this.performanceClient?.addQueueMeasurement(Hn.AuthorityUpdateCloudDiscoveryMetadata,this.correlationId);const t=this.updateCloudDiscoveryMetadataFromLocalSources(e);if(t)return t;const r=await zn(this.getCloudDiscoveryMetadataFromNetwork.bind(this),Hn.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.logger,this.performanceClient,this.correlationId)();if(r)return Dt(e,r,!0),G;throw gr(tr)}updateCloudDiscoveryMetadataFromLocalSources(e){this.logger.verbose("Attempting to get cloud discovery metadata  from authority configuration"),this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities||t.NOT_APPLICABLE}`),this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata||t.NOT_APPLICABLE}`),this.logger.verbosePii(`Canonical Authority: ${e.canonical_authority||t.NOT_APPLICABLE}`);const r=this.getCloudDiscoveryMetadataFromConfig();if(r)return this.logger.verbose("Found cloud discovery metadata in authority configuration"),Dt(e,r,!1),K;if(this.logger.verbose("Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the hardcoded values."),this.options.skipAuthorityMetadataCache)this.logger.verbose("Skipping hardcoded cloud discovery metadata cache since skipAuthorityMetadataCache is set to true. Attempting to get cloud discovery metadata from the network metadata cache.");else{const t=(n=this.hostnameAndPort,Ur(Nr.metadata,n));if(t)return this.logger.verbose("Found cloud discovery metadata from hardcoded values."),Dt(e,t,!1),$;this.logger.verbose("Did not find cloud discovery metadata in hardcoded values... Attempting to get cloud discovery metadata from the network metadata cache.")}var n;const o=Bt(e);return this.isAuthoritySameType(e)&&e.aliasesFromNetwork&&!o?(this.logger.verbose("Found cloud discovery metadata in the cache."),z):(o&&this.logger.verbose("The metadata entity is expired."),null)}getCloudDiscoveryMetadataFromConfig(){if(this.authorityType===kr)return this.logger.verbose("CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host."),$n.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);if(this.authorityOptions.cloudDiscoveryMetadata){this.logger.verbose("The cloud discovery metadata has been provided as a network response, in the config.");try{this.logger.verbose("Attempting to parse the cloud discovery metadata.");const e=Ur(JSON.parse(this.authorityOptions.cloudDiscoveryMetadata).metadata,this.hostnameAndPort);if(this.logger.verbose("Parsed the cloud discovery metadata."),e)return this.logger.verbose("There is returnable metadata attached to the parsed cloud discovery metadata."),e;this.logger.verbose("There is no metadata attached to the parsed cloud discovery metadata.")}catch(e){throw this.logger.verbose("Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error."),gr(Zt)}}return this.isInKnownAuthorities()?(this.logger.verbose("The host is included in knownAuthorities. Creating new cloud discovery metadata from the host."),$n.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)):null}async getCloudDiscoveryMetadataFromNetwork(){this.performanceClient?.addQueueMeasurement(Hn.AuthorityGetCloudDiscoveryMetadataFromNetwork,this.correlationId);const e=`${t.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`,r={};let n=null;try{const o=await this.networkInterface.sendGetRequestAsync(e,r);let i,a;if(function(e){return e.hasOwnProperty("tenant_discovery_endpoint")&&e.hasOwnProperty("metadata")}
/*! @azure/msal-common v15.7.0 2025-06-10 */(o.body))i=o.body,a=i.metadata,this.logger.verbosePii(`tenant_discovery_endpoint is: ${i.tenant_discovery_endpoint}`);else{if(!function(e){return e.hasOwnProperty("error")&&e.hasOwnProperty("error_description")}(o.body))return this.logger.error("AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse"),null;if(this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${o.status}`),i=o.body,i.error===t.INVALID_INSTANCE)return this.logger.error("The CloudInstanceDiscoveryErrorResponse error is invalid_instance."),null;this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${i.error}`),this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${i.error_description}`),this.logger.warning("Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []"),a=[]}this.logger.verbose("Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request."),n=Ur(a,this.hostnameAndPort)}catch(e){if(e instanceof ye)this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.\nError: ${e.errorCode}\nError Description: ${e.errorMessage}`);else{const t=e;this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.\nError: ${t.name}\nError Description: ${t.message}`)}return null}return n||(this.logger.warning("The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request."),this.logger.verbose("Creating custom Authority for custom domain scenario."),n=$n.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort)),n}isInKnownAuthorities(){return this.authorityOptions.knownAuthorities.filter((e=>e&&Mr.getDomainFromUrl(e).toLowerCase()===this.hostnameAndPort)).length>0}static generateAuthority(e,r){let n;if(r&&r.azureCloudInstance!==vt.None){const e=r.tenant?r.tenant:t.DEFAULT_COMMON_TENANT;n=`${r.azureCloudInstance}/${e}/`}return n||e}static createCloudDiscoveryMetadataFromHost(e){return{preferred_network:e,preferred_cache:e,aliases:[e]}}getPreferredCache(){if(this.managedIdentity)return t.DEFAULT_AUTHORITY_HOST;if(this.discoveryComplete())return this.metadata.preferred_cache;throw gt(Ae)}isAlias(e){return this.metadata.aliases.indexOf(e)>-1}isAliasOfKnownMicrosoftAuthority(e){return qr.has(e)}static isPublicCloudAuthority(e){return t.KNOWN_PUBLIC_CLOUDS.indexOf(e)>=0}static buildRegionalAuthorityString(e,r,n){const o=new Mr(e);o.validateAsUri();const i=o.getUrlComponents();let a=`${r}.${i.HostNameAndPort}`;this.isPublicCloudAuthority(i.HostNameAndPort)&&(a=`${r}.${t.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`);const s=Mr.constructAuthorityUriFromObject({...o.getUrlComponents(),HostNameAndPort:a}).urlString;return n?`${s}?${n}`:s}static replaceWithRegionalInformation(e,t){const r={...e};return r.authorization_endpoint=$n.buildRegionalAuthorityString(r.authorization_endpoint,t),r.token_endpoint=$n.buildRegionalAuthorityString(r.token_endpoint,t),r.end_session_endpoint&&(r.end_session_endpoint=$n.buildRegionalAuthorityString(r.end_session_endpoint,t)),r}static transformCIAMAuthority(e){let r=e;const n=new Mr(e).getUrlComponents();if(0===n.PathSegments.length&&n.HostNameAndPort.endsWith(t.CIAM_AUTH_URL)){r=`${r}${n.HostNameAndPort.split(".")[0]}${t.AAD_TENANT_DOMAIN_SUFFIX}`}return r}}function Qn(e){return e.endsWith(t.FORWARD_SLASH)?e:`${e}${t.FORWARD_SLASH}`}function jn(e){const t=e.cloudDiscoveryMetadata;let r;if(t)try{r=JSON.parse(t)}catch(e){throw gr(Zt)}return{canonicalAuthority:e.authority?Qn(e.authority):void 0,knownAuthorities:e.knownAuthorities,cloudDiscoveryMetadata:r}}
/*! @azure/msal-common v15.7.0 2025-06-10 */async function Wn(e,t,r,n,o,i,a){a?.addQueueMeasurement(Hn.AuthorityFactoryCreateDiscoveredInstance,i);const s=$n.transformCIAMAuthority(Qn(e)),c=new $n(s,t,r,n,o,i,a);try{return await zn(c.resolveEndpointsAsync.bind(c),Hn.AuthorityResolveEndpointsAsync,o,a,i)(),c}catch(e){throw gt(Ae)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */$n.reservedTenantDomains=new Set(["{tenant}","{tenantid}",v,I,w]);class Vn extends ye{constructor(e,t,r,n,o){super(e,t,r),this.name="ServerError",this.errorNo=n,this.status=o,Object.setPrototypeOf(this,Vn.prototype)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Jn(e,t,r){return{clientId:e,authority:t.authority,scopes:t.scopes,homeAccountIdentifier:r,claims:t.claims,authenticationScheme:t.authenticationScheme,resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,sshKid:t.sshKid,embeddedClientId:t.embeddedClientId||t.tokenBodyParameters?.clientId}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Yn{static generateThrottlingStorageKey(e){return`${J}.${JSON.stringify(e)}`}static preProcess(e,r){const n=Yn.generateThrottlingStorageKey(r),o=e.getThrottlingCache(n);if(o){if(o.throttleTime<Date.now())return void e.removeItem(n);throw new Vn(o.errorCodes?.join(" ")||t.EMPTY_STRING,o.errorMessage,o.subError)}}static postProcess(e,t,r){if(Yn.checkResponseStatus(r)||Yn.checkResponseForRetryAfter(r)){const n={throttleTime:Yn.calculateThrottleTime(parseInt(r.headers[u])),error:r.body.error,errorCodes:r.body.error_codes,errorMessage:r.body.error_description,subError:r.body.suberror};e.setThrottlingCache(Yn.generateThrottlingStorageKey(t),n)}}static checkResponseStatus(e){return 429===e.status||e.status>=500&&e.status<600}static checkResponseForRetryAfter(e){return!!e.headers&&(e.headers.hasOwnProperty(u)&&(e.status<200||e.status>=300))}static calculateThrottleTime(e){const t=e<=0?0:e,r=Date.now()/1e3;return Math.floor(1e3*Math.min(r+(t||W),r+V))}static removeThrottle(e,t,r,n){const o=Jn(t,r,n),i=this.generateThrottlingStorageKey(o);e.removeItem(i)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Xn extends ye{constructor(e,t,r){super(e.errorCode,e.errorMessage,e.subError),Object.setPrototypeOf(this,Xn.prototype),this.name="NetworkError",this.error=e,this.httpStatus=t,this.responseHeaders=r}}function Zn(e,t,r,n){return e.errorMessage=`${e.errorMessage}, additionalErrorInfo: error.name:${n?.name}, error.message:${n?.message}`,new Xn(e,t,r)}
/*! @azure/msal-common v15.7.0 2025-06-10 */class eo{constructor(e,t){this.config=function({authOptions:e,systemOptions:t,loggerOptions:r,cacheOptions:n,storageInterface:o,networkInterface:i,cryptoInterface:a,clientCredentials:s,libraryInfo:c,telemetry:l,serverTelemetryManager:h,persistencePlugin:d,serializableCache:u}){const g={...Gr,...r};return{authOptions:(p=e,{clientCapabilities:[],azureCloudOptions:Vr,skipAuthorityMetadataCache:!1,instanceAware:!1,encodeExtraQueryParams:!1,...p}),systemOptions:{...zr,...t},loggerOptions:g,cacheOptions:{...$r,...n},storageInterface:o||new Kr(e.clientId,pt,new ft(g)),networkInterface:i||Qr,cryptoInterface:a||pt,clientCredentials:s||Wr,libraryInfo:{...jr,...c},telemetry:{...Jr,...l},serverTelemetryManager:h||null,persistencePlugin:d||null,serializableCache:u||null};var p}(e),this.logger=new ft(this.config.loggerOptions,yt,Ct),this.cryptoUtils=this.config.cryptoInterface,this.cacheManager=this.config.storageInterface,this.networkClient=this.config.networkInterface,this.serverTelemetryManager=this.config.serverTelemetryManager,this.authority=this.config.authOptions.authority,this.performanceClient=t}createTokenRequestHeaders(e){const r={};if(r[h]=t.URL_FORM_CONTENT_TYPE,!this.config.systemOptions.preventCorsPreflight&&e)switch(e.type){case Xr:try{const t=yr(e.credential);r[g]=`Oid:${t.uid}@${t.utid}`}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case Zr:r[g]=`UPN: ${e.credential}`}return r}async executePostToTokenEndpoint(e,t,r,n,o,i){i&&this.performanceClient?.addQueueMeasurement(i,o);const a=await this.sendPostRequest(n,e,{body:t,headers:r},o);return this.config.serverTelemetryManager&&a.status<500&&429!==a.status&&this.config.serverTelemetryManager.clearTelemetryCache(),a}async sendPostRequest(e,t,r,n){let o;Yn.preProcess(this.cacheManager,e);try{o=await zn(this.networkClient.sendPostRequestAsync.bind(this.networkClient),Hn.NetworkClientSendPostRequestAsync,this.logger,this.performanceClient,n)(t,r);const e=o.headers||{};this.performanceClient?.addFields({refreshTokenSize:o.body.refresh_token?.length||0,httpVerToken:e[y]||"",requestId:e[f]||""},n)}catch(e){if(e instanceof Xn){const t=e.responseHeaders;throw t&&this.performanceClient?.addFields({httpVerToken:t[y]||"",requestId:t[f]||"",contentTypeHeader:t[h]||void 0,contentLengthHeader:t[d]||void 0,httpStatus:e.httpStatus},n),e.error}throw e instanceof ye?e:gt(ke)}return Yn.postProcess(this.cacheManager,e,o),o}async updateAuthority(e,t){this.performanceClient?.addQueueMeasurement(Hn.UpdateTokenEndpointAuthority,t);const r=`https://${e}/${this.authority.tenant}/`,n=await Wn(r,this.networkClient,this.cacheManager,this.authority.options,this.logger,t,this.performanceClient);this.authority=n}createTokenQueryParameters(e){const t=new Map;return e.embeddedClientId&&Un(t,this.config.authOptions.clientId,this.config.authOptions.redirectUri),e.tokenQueryParameters&&Pn(t,e.tokenQueryParameters),wn(t,e.correlationId),hn(t,e.correlationId,this.performanceClient),Pr(t)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const to="no_tokens_found",ro="native_account_unavailable",no="refresh_token_expired",oo="interaction_required",io="consent_required",ao="login_required",so="bad_token";var co=Object.freeze({__proto__:null,badToken:so,consentRequired:io,interactionRequired:oo,loginRequired:ao,nativeAccountUnavailable:ro,noTokensFound:to,refreshTokenExpired:no});
/*! @azure/msal-common v15.7.0 2025-06-10 */const lo=[oo,io,ao,so],ho=["message_only","additional_action","basic_action","user_password_expired","consent_required","bad_token"],uo={[to]:"No refresh token found in the cache. Please sign-in.",[ro]:"The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API.",[no]:"Refresh token has expired.",[so]:"Identity provider returned bad_token due to an expired or invalid refresh token. Please invoke an interactive API to resolve."},go={noTokensFoundError:{code:to,desc:uo[to]},native_account_unavailable:{code:ro,desc:uo[ro]},bad_token:{code:so,desc:uo[so]}};class po extends ye{constructor(e,r,n,o,i,a,s,c){super(e,r,n),Object.setPrototypeOf(this,po.prototype),this.timestamp=o||t.EMPTY_STRING,this.traceId=i||t.EMPTY_STRING,this.correlationId=a||t.EMPTY_STRING,this.claims=s||t.EMPTY_STRING,this.name="InteractionRequiredAuthError",this.errorNo=c}}function mo(e,t,r){const n=!!e&&lo.indexOf(e)>-1,o=!!r&&ho.indexOf(r)>-1,i=!!t&&lo.some((e=>t.indexOf(e)>-1));return n||i||o}function fo(e){return new po(e,uo[e])}
/*! @azure/msal-common v15.7.0 2025-06-10 */class yo{static setRequestState(e,r,n){const o=yo.generateLibraryState(e,n);return r?`${o}${t.RESOURCE_DELIM}${r}`:o}static generateLibraryState(e,t){if(!e)throw gt(je);const r={id:e.createNewGuid()};t&&(r.meta=t);const n=JSON.stringify(r);return e.base64Encode(n)}static parseRequestState(e,r){if(!e)throw gt(je);if(!r)throw gt(Ee);try{const n=r.split(t.RESOURCE_DELIM),o=n[0],i=n.length>1?n.slice(1).join(t.RESOURCE_DELIM):t.EMPTY_STRING,a=e.base64Decode(o),s=JSON.parse(a);return{userRequestState:i||t.EMPTY_STRING,libraryState:s}}catch(e){throw gt(Ee)}}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Co="sw";class vo{constructor(e,t){this.cryptoUtils=e,this.performanceClient=t}async generateCnf(e,t){this.performanceClient?.addQueueMeasurement(Hn.PopTokenGenerateCnf,e.correlationId);const r=await zn(this.generateKid.bind(this),Hn.PopTokenGenerateCnf,t,this.performanceClient,e.correlationId)(e),n=this.cryptoUtils.base64UrlEncode(JSON.stringify(r));return{kid:r.kid,reqCnfString:n}}async generateKid(e){this.performanceClient?.addQueueMeasurement(Hn.PopTokenGenerateKid,e.correlationId);return{kid:await this.cryptoUtils.getPublicKeyThumbprint(e),xms_ksl:Co}}async signPopToken(e,t,r){return this.signPayload(e,t,r)}async signPayload(e,t,r,n){const{resourceRequestMethod:o,resourceRequestUri:i,shrClaims:a,shrNonce:s,shrOptions:c}=r,l=i?new Mr(i):void 0,h=l?.getUrlComponents();return this.cryptoUtils.signJwt({at:e,ts:Tt(),m:o?.toUpperCase(),u:h?.HostNameAndPort,nonce:s||this.cryptoUtils.createNewGuid(),p:h?.AbsolutePath,q:h?.QueryString?[[],h.QueryString]:void 0,client_claims:a||void 0,...n},t,c,r.correlationId)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class wo{constructor(e,t){this.cache=e,this.hasChanged=t}get cacheHasChanged(){return this.hasChanged}get tokenCache(){return this.cache}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Io{constructor(e,t,r,n,o,i,a){this.clientId=e,this.cacheStorage=t,this.cryptoObj=r,this.logger=n,this.serializableCache=o,this.persistencePlugin=i,this.performanceClient=a}validateTokenResponse(e,r){if(e.error||e.error_description||e.suberror){const o=`Error(s): ${e.error_codes||t.NOT_AVAILABLE} - Timestamp: ${e.timestamp||t.NOT_AVAILABLE} - Description: ${e.error_description||t.NOT_AVAILABLE} - Correlation ID: ${e.correlation_id||t.NOT_AVAILABLE} - Trace ID: ${e.trace_id||t.NOT_AVAILABLE}`,c=e.error_codes?.length?e.error_codes[0]:void 0,l=new Vn(e.error,o,e.suberror,c,e.status);if(r&&e.status&&e.status>=a&&e.status<=s)return void this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently unavailable and the access token is unable to be refreshed.\n${l}`);if(r&&e.status&&e.status>=n&&e.status<=i)return void this.logger.warning(`executeTokenRequest:validateTokenResponse - AAD is currently available but is unable to refresh the access token.\n${l}`);if(mo(e.error,e.error_description,e.suberror))throw new po(e.error,e.error_description,e.suberror,e.timestamp||t.EMPTY_STRING,e.trace_id||t.EMPTY_STRING,e.correlation_id||t.EMPTY_STRING,e.claims||t.EMPTY_STRING,c);throw l}}async handleServerTokenResponse(e,r,n,o,i,a,s,c,l){let h,d;if(this.performanceClient?.addQueueMeasurement(Hn.HandleServerTokenResponse,e.correlation_id),e.id_token){if(h=wt(e.id_token||t.EMPTY_STRING,this.cryptoObj.base64Decode),i&&i.nonce&&h.nonce!==i.nonce)throw gt(Pe);if(o.maxAge||0===o.maxAge){const e=h.auth_time;if(!e)throw gt(Me);It(e,o.maxAge)}}this.homeAccountIdentifier=Er.generateHomeAccountId(e.client_info||t.EMPTY_STRING,r.authorityType,this.logger,this.cryptoObj,h),i&&i.state&&(d=yo.parseRequestState(this.cryptoObj,i.state)),e.key_id=e.key_id||o.sshKid||void 0;const u=this.generateCacheRecord(e,r,n,o,h,a,i);let g;try{if(this.persistencePlugin&&this.serializableCache&&(this.logger.verbose("Persistence enabled, calling beforeCacheAccess"),g=new wo(this.serializableCache,!0),await this.persistencePlugin.beforeCacheAccess(g)),s&&!c&&u.account){const e=u.account.generateAccountKey();if(!this.cacheStorage.getAccount(e))return this.logger.warning("Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache"),await Io.generateAuthenticationResult(this.cryptoObj,r,u,!1,o,h,d,void 0,l)}await this.cacheStorage.saveCacheRecord(u,o.correlationId,o.storeInCache)}finally{this.persistencePlugin&&this.serializableCache&&g&&(this.logger.verbose("Persistence enabled, calling afterCacheAccess"),await this.persistencePlugin.afterCacheAccess(g))}return Io.generateAuthenticationResult(this.cryptoObj,r,u,!1,o,h,d,e,l)}generateCacheRecord(e,t,r,n,o,i,a){const s=t.getPreferredCache();if(!s)throw gt($e);const c=Sr(o);let l,h;e.id_token&&o&&(l=Rt(this.homeAccountIdentifier,s,e.id_token,this.clientId,c||""),h=To(this.cacheStorage,t,this.homeAccountIdentifier,this.cryptoObj.base64Decode,o,e.client_info,s,c,a,void 0,this.logger));let d=null;if(e.access_token){const o=e.scope?mr.fromString(e.scope):new mr(n.scopes||[]),a=("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0,l=("string"==typeof e.ext_expires_in?parseInt(e.ext_expires_in,10):e.ext_expires_in)||0,h=("string"==typeof e.refresh_in?parseInt(e.refresh_in,10):e.refresh_in)||void 0,u=r+a,g=u+l,p=h&&h>0?r+h:void 0;d=_t(this.homeAccountIdentifier,s,e.access_token,this.clientId,c||t.tenant||"",o.printScopes(),u,g,this.cryptoObj.base64Decode,p,e.token_type,i,e.key_id,n.claims,n.requestedClaimsHash)}let u=null;if(e.refresh_token){let t;if(e.refresh_token_expires_in){t=r+("string"==typeof e.refresh_token_expires_in?parseInt(e.refresh_token_expires_in,10):e.refresh_token_expires_in)}u=Pt(this.homeAccountIdentifier,s,e.refresh_token,this.clientId,e.foci,i,t)}let g=null;return e.foci&&(g={clientId:this.clientId,environment:s,familyId:e.foci}),{account:h,idToken:l,accessToken:d,refreshToken:u,appMetadata:g}}static async generateAuthenticationResult(e,r,n,o,i,a,s,c,l){let h,d,u=t.EMPTY_STRING,g=[],p=null,m=t.EMPTY_STRING;if(n.accessToken){if(n.accessToken.tokenType!==j.POP||i.popKid)u=n.accessToken.secret;else{const t=new vo(e),{secret:r,keyId:o}=n.accessToken;if(!o)throw gt(nt);u=await t.signPopToken(r,o,i)}g=mr.fromString(n.accessToken.target).asArray(),p=kt(n.accessToken.expiresOn),h=kt(n.accessToken.extendedExpiresOn),n.accessToken.refreshOn&&(d=kt(n.accessToken.refreshOn))}n.appMetadata&&(m=n.appMetadata.familyId===D?D:"");const f=a?.oid||a?.sub||"",y=a?.tid||"";c?.spa_accountid&&n.account&&(n.account.nativeAccountId=c?.spa_accountid);const C=n.account?wr(n.account.getAccountInfo(),void 0,a,n.idToken?.secret):null;return{authority:r.canonicalAuthority,uniqueId:f,tenantId:y,scopes:g,account:C,idToken:n?.idToken?.secret||"",idTokenClaims:a||{},accessToken:u,fromCache:o,expiresOn:p,extExpiresOn:h,refreshOn:d,correlationId:i.correlationId,requestId:l||t.EMPTY_STRING,familyId:m,tokenType:n.accessToken?.tokenType||t.EMPTY_STRING,state:s?s.userRequestState:t.EMPTY_STRING,cloudGraphHostName:n.account?.cloudGraphHostName||t.EMPTY_STRING,msGraphHost:n.account?.msGraphHost||t.EMPTY_STRING,code:c?.spa_code,fromNativeBroker:!1}}}function To(e,t,r,n,o,i,a,s,c,l,h){h?.verbose("setCachedAccount called");const d=e.getAccountKeys().find((e=>e.startsWith(r)));let u=null;d&&(u=e.getAccount(d));const g=u||Er.createAccount({homeAccountId:r,idTokenClaims:o,clientInfo:i,environment:a,cloudGraphHostName:c?.cloud_graph_host_name,msGraphHost:c?.msgraph_host,nativeAccountId:l},t,n),p=g.tenantProfiles||[],m=s||g.realm;if(m&&!p.find((e=>e.tenantId===m))){const e=vr(r,g.localAccountId,m,o);p.push(e)}return g.tenantProfiles=p,g}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Ao{static validateRedirectUri(e){if(!e)throw gr(Ft)}static validatePrompt(e){const t=[];for(const e in k)t.push(k[e]);if(t.indexOf(e)<0)throw gr(jt)}static validateClaims(e){try{JSON.parse(e)}catch(e){throw gr(Wt)}}static validateCodeChallengeParams(e,t){if(!e||!t)throw gr(Xt);this.validateCodeChallengeMethod(t)}static validateCodeChallengeMethod(e){if([S.PLAIN,S.S256].indexOf(e)<0)throw gr(Yt)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */async function ko(e,t,r){if("string"==typeof e)return e;return e({clientId:t,tokenEndpoint:r})}
/*! @azure/msal-common v15.7.0 2025-06-10 */class So extends eo{constructor(e,t){super(e,t),this.includeRedirectUri=!0,this.oidcDefaultScopes=this.config.authOptions.authority.options.OIDCOptions?.defaultScopes}async acquireToken(e,t){if(this.performanceClient?.addQueueMeasurement(Hn.AuthClientAcquireToken,e.correlationId),!e.code)throw gt(Ue);const r=Tt(),n=await zn(this.executeTokenRequest.bind(this),Hn.AuthClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(this.authority,e),o=n.headers?.[f],i=new Io(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin,this.performanceClient);return i.validateTokenResponse(n.body),zn(i.handleServerTokenResponse.bind(i),Hn.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(n.body,this.authority,r,e,t,void 0,void 0,void 0,o)}getLogoutUri(e){if(!e)throw gr(Jt);const t=this.createLogoutUrlQueryString(e);return Mr.appendQueryString(this.authority.endSessionEndpoint,t)}async executeTokenRequest(e,t){this.performanceClient?.addQueueMeasurement(Hn.AuthClientExecuteTokenRequest,t.correlationId);const r=this.createTokenQueryParameters(t),n=Mr.appendQueryString(e.tokenEndpoint,r),o=await zn(this.createTokenRequestBody.bind(this),Hn.AuthClientCreateTokenRequestBody,this.logger,this.performanceClient,t.correlationId)(t);let i;if(t.clientInfo)try{const e=fr(t.clientInfo,this.cryptoUtils.base64Decode);i={credential:`${e.uid}${U}${e.utid}`,type:Xr}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}const a=this.createTokenRequestHeaders(i||t.ccsCredential),s=Jn(this.config.authOptions.clientId,t);return zn(this.executePostToTokenEndpoint.bind(this),Hn.AuthorizationCodeClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,t.correlationId)(n,o,a,s,t.correlationId,Hn.AuthorizationCodeClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){this.performanceClient?.addQueueMeasurement(Hn.AuthClientCreateTokenRequestBody,e.correlationId);const t=new Map;if(gn(t,e.embeddedClientId||e.tokenBodyParameters?.[en]||this.config.authOptions.clientId),this.includeRedirectUri?pn(t,e.redirectUri):Ao.validateRedirectUri(e.redirectUri),un(t,e.scopes,!0,this.oidcDefaultScopes),function(e,t){e.set("code",t)}(t,e.code),In(t,this.config.libraryInfo),Tn(t,this.config.telemetry.application),Ln(t),this.serverTelemetryManager&&!Yr(this.config)&&qn(t,this.serverTelemetryManager),e.codeVerifier&&function(e,t){e.set("code_verifier",t)}(t,e.codeVerifier),this.config.clientCredentials.clientSecret&&kn(t,this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const r=this.config.clientCredentials.clientAssertion;Sn(t,await ko(r.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),bn(t,r.assertionType)}if(En(t,P),Rn(t),e.authenticationScheme===j.POP){const r=new vo(this.cryptoUtils,this.performanceClient);let n;if(e.popKid)n=this.cryptoUtils.encodeKid(e.popKid);else{n=(await zn(r.generateCnf.bind(r),Hn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString}On(t,n)}else if(e.authenticationScheme===j.SSH){if(!e.sshJwk)throw gr(rr);Nn(t,e.sshJwk)}let r;if((!pr.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&vn(t,e.claims,this.config.authOptions.clientCapabilities),e.clientInfo)try{const t=fr(e.clientInfo,this.cryptoUtils.base64Decode);r={credential:`${t.uid}${U}${t.utid}`,type:Xr}}catch(e){this.logger.verbose("Could not parse client info for CCS Header: "+e)}else r=e.ccsCredential;if(this.config.systemOptions.preventCorsPreflight&&r)switch(r.type){case Xr:try{yn(t,yr(r.credential))}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case Zr:fn(t,r.credential)}return e.embeddedClientId&&Un(t,this.config.authOptions.clientId,this.config.authOptions.redirectUri),e.tokenBodyParameters&&Pn(t,e.tokenBodyParameters),!e.enableSpaAuthorizationCode||e.tokenBodyParameters&&e.tokenBodyParameters[on]||Pn(t,{[on]:"1"}),hn(t,e.correlationId,this.performanceClient),Pr(t)}createLogoutUrlQueryString(e){const t=new Map;return e.postLogoutRedirectUri&&function(e,t){e.set("post_logout_redirect_uri",t)}(t,e.postLogoutRedirectUri),e.correlationId&&wn(t,e.correlationId),e.idTokenHint&&function(e,t){e.set("id_token_hint",t)}(t,e.idTokenHint),e.state&&An(t,e.state),e.logoutHint&&function(e,t){e.set("logout_hint",t)}(t,e.logoutHint),e.extraQueryParameters&&Pn(t,e.extraQueryParameters),this.config.authOptions.instanceAware&&_n(t),Pr(t,this.config.authOptions.encodeExtraQueryParams,e.extraQueryParameters)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class bo extends eo{constructor(e,t){super(e,t)}async acquireToken(e){this.performanceClient?.addQueueMeasurement(Hn.RefreshTokenClientAcquireToken,e.correlationId);const t=Tt(),r=await zn(this.executeTokenRequest.bind(this),Hn.RefreshTokenClientExecuteTokenRequest,this.logger,this.performanceClient,e.correlationId)(e,this.authority),n=r.headers?.[f],o=new Io(this.config.authOptions.clientId,this.cacheManager,this.cryptoUtils,this.logger,this.config.serializableCache,this.config.persistencePlugin);return o.validateTokenResponse(r.body),zn(o.handleServerTokenResponse.bind(o),Hn.HandleServerTokenResponse,this.logger,this.performanceClient,e.correlationId)(r.body,this.authority,t,e,void 0,void 0,!0,e.forceCache,n)}async acquireTokenByRefreshToken(e){if(!e)throw gr(Vt);if(this.performanceClient?.addQueueMeasurement(Hn.RefreshTokenClientAcquireTokenByRefreshToken,e.correlationId),!e.account)throw gt(ze);if(this.cacheManager.isAppMetadataFOCI(e.account.environment))try{return await zn(this.acquireTokenWithCachedRefreshToken.bind(this),Hn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!0)}catch(t){const r=t instanceof po&&t.errorCode===to,n=t instanceof Vn&&t.errorCode===X&&t.subError===Z;if(r||n)return zn(this.acquireTokenWithCachedRefreshToken.bind(this),Hn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1);throw t}return zn(this.acquireTokenWithCachedRefreshToken.bind(this),Hn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,this.logger,this.performanceClient,e.correlationId)(e,!1)}async acquireTokenWithCachedRefreshToken(e,t){this.performanceClient?.addQueueMeasurement(Hn.RefreshTokenClientAcquireTokenWithCachedRefreshToken,e.correlationId);const r=Kn(this.cacheManager.getRefreshToken.bind(this.cacheManager),Hn.CacheManagerGetRefreshToken,this.logger,this.performanceClient,e.correlationId)(e.account,t,void 0,this.performanceClient,e.correlationId);if(!r)throw fo(to);if(r.expiresOn&&St(r.expiresOn,e.refreshTokenExpirationOffsetSeconds||300))throw this.performanceClient?.addFields({rtExpiresOnMs:Number(r.expiresOn)},e.correlationId),fo(no);const n={...e,refreshToken:r.secret,authenticationScheme:e.authenticationScheme||j.BEARER,ccsCredential:{credential:e.account.homeAccountId,type:Xr}};try{return await zn(this.acquireToken.bind(this),Hn.RefreshTokenClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(n)}catch(t){if(t instanceof po&&(this.performanceClient?.addFields({rtExpiresOnMs:Number(r.expiresOn)},e.correlationId),t.subError===so)){this.logger.verbose("acquireTokenWithRefreshToken: bad refresh token, removing from cache");const e=Et(r);this.cacheManager.removeRefreshToken(e)}throw t}}async executeTokenRequest(e,t){this.performanceClient?.addQueueMeasurement(Hn.RefreshTokenClientExecuteTokenRequest,e.correlationId);const r=this.createTokenQueryParameters(e),n=Mr.appendQueryString(t.tokenEndpoint,r),o=await zn(this.createTokenRequestBody.bind(this),Hn.RefreshTokenClientCreateTokenRequestBody,this.logger,this.performanceClient,e.correlationId)(e),i=this.createTokenRequestHeaders(e.ccsCredential),a=Jn(this.config.authOptions.clientId,e);return zn(this.executePostToTokenEndpoint.bind(this),Hn.RefreshTokenClientExecutePostToTokenEndpoint,this.logger,this.performanceClient,e.correlationId)(n,o,i,a,e.correlationId,Hn.RefreshTokenClientExecutePostToTokenEndpoint)}async createTokenRequestBody(e){this.performanceClient?.addQueueMeasurement(Hn.RefreshTokenClientCreateTokenRequestBody,e.correlationId);const t=new Map;if(gn(t,e.embeddedClientId||e.tokenBodyParameters?.[en]||this.config.authOptions.clientId),e.redirectUri&&pn(t,e.redirectUri),un(t,e.scopes,!0,this.config.authOptions.authority.options.OIDCOptions?.defaultScopes),En(t,M),Rn(t),In(t,this.config.libraryInfo),Tn(t,this.config.telemetry.application),Ln(t),this.serverTelemetryManager&&!Yr(this.config)&&qn(t,this.serverTelemetryManager),function(e,t){e.set("refresh_token",t)}(t,e.refreshToken),this.config.clientCredentials.clientSecret&&kn(t,this.config.clientCredentials.clientSecret),this.config.clientCredentials.clientAssertion){const r=this.config.clientCredentials.clientAssertion;Sn(t,await ko(r.assertion,this.config.authOptions.clientId,e.resourceRequestUri)),bn(t,r.assertionType)}if(e.authenticationScheme===j.POP){const r=new vo(this.cryptoUtils,this.performanceClient);let n;if(e.popKid)n=this.cryptoUtils.encodeKid(e.popKid);else{n=(await zn(r.generateCnf.bind(r),Hn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(e,this.logger)).reqCnfString}On(t,n)}else if(e.authenticationScheme===j.SSH){if(!e.sshJwk)throw gr(rr);Nn(t,e.sshJwk)}if((!pr.isEmptyObj(e.claims)||this.config.authOptions.clientCapabilities&&this.config.authOptions.clientCapabilities.length>0)&&vn(t,e.claims,this.config.authOptions.clientCapabilities),this.config.systemOptions.preventCorsPreflight&&e.ccsCredential)switch(e.ccsCredential.type){case Xr:try{yn(t,yr(e.ccsCredential.credential))}catch(e){this.logger.verbose("Could not parse home account ID for CCS Header: "+e)}break;case Zr:fn(t,e.ccsCredential.credential)}return e.embeddedClientId&&Un(t,this.config.authOptions.clientId,this.config.authOptions.redirectUri),e.tokenBodyParameters&&Pn(t,e.tokenBodyParameters),hn(t,e.correlationId,this.performanceClient),Pr(t)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Eo extends eo{constructor(e,t){super(e,t)}async acquireCachedToken(e){this.performanceClient?.addQueueMeasurement(Hn.SilentFlowClientAcquireCachedToken,e.correlationId);let t=ae;if(e.forceRefresh||!this.config.cacheOptions.claimsBasedCachingEnabled&&!pr.isEmptyObj(e.claims))throw this.setCacheOutcome(se,e.correlationId),gt(Ye);if(!e.account)throw gt(ze);const r=e.account.tenantId||function(e){const t=new Mr(e).getUrlComponents(),r=t.PathSegments.slice(-1)[0]?.toLowerCase();switch(r){case v:case w:case I:return;default:return r}}(e.authority),n=this.cacheManager.getTokenKeys(),o=this.cacheManager.getAccessToken(e.account,e,n,r,this.performanceClient,e.correlationId);if(!o)throw this.setCacheOutcome(ce,e.correlationId),gt(Ye);if(bt(o.cachedAt)||St(o.expiresOn,this.config.systemOptions.tokenRenewalOffsetSeconds))throw this.setCacheOutcome(le,e.correlationId),gt(Ye);o.refreshOn&&St(o.refreshOn,0)&&(t=he);const i=e.authority||this.authority.getPreferredCache(),a={account:this.cacheManager.readAccountFromCache(e.account),accessToken:o,idToken:this.cacheManager.getIdToken(e.account,n,r,this.performanceClient,e.correlationId),refreshToken:null,appMetadata:this.cacheManager.readAppMetadataFromCache(i)};return this.setCacheOutcome(t,e.correlationId),this.config.serverTelemetryManager&&this.config.serverTelemetryManager.incrementCacheHits(),[await zn(this.generateResultFromCacheRecord.bind(this),Hn.SilentFlowClientGenerateResultFromCacheRecord,this.logger,this.performanceClient,e.correlationId)(a,e),t]}setCacheOutcome(e,t){this.serverTelemetryManager?.setCacheOutcome(e),this.performanceClient?.addFields({cacheOutcome:e},t),e!==ae&&this.logger.info(`Token refresh is required due to cache outcome: ${e}`)}async generateResultFromCacheRecord(e,t){let r;if(this.performanceClient?.addQueueMeasurement(Hn.SilentFlowClientGenerateResultFromCacheRecord,t.correlationId),e.idToken&&(r=wt(e.idToken.secret,this.config.cryptoInterface.base64Decode)),t.maxAge||0===t.maxAge){const e=r?.auth_time;if(!e)throw gt(Me);It(e,t.maxAge)}return Io.generateAuthenticationResult(this.cryptoUtils,this.authority,e,!0,t,r)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const Ro={sendGetRequestAsync:()=>Promise.reject(gt(st)),sendPostRequestAsync:()=>Promise.reject(gt(st))};
/*! @azure/msal-common v15.7.0 2025-06-10 */function _o(e,t,r,n){const o=t.correlationId,i=new Map;gn(i,t.embeddedClientId||t.extraQueryParameters?.[en]||e.clientId);if(un(i,[...t.scopes||[],...t.extraScopesToConsent||[]],!0,e.authority.options.OIDCOptions?.defaultScopes),pn(i,t.redirectUri),wn(i,o),function(e,t){e.set("response_mode",t||_)}(i,t.responseMode),Rn(i),t.prompt&&(!function(e,t){e.set("prompt",t)}(i,t.prompt),n?.addFields({prompt:t.prompt},o)),t.domainHint&&(!function(e,t){e.set("domain_hint",t)}(i,t.domainHint),n?.addFields({domainHintFromRequest:!0},o)),t.prompt!==k.SELECT_ACCOUNT)if(t.sid&&t.prompt===k.NONE)r.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from request"),Cn(i,t.sid),n?.addFields({sidFromRequest:!0},o);else if(t.account){const e=(a=t.account,a.idTokenClaims?.sid||null);let s=function(e){return e.idTokenClaims?.login_hint||null}
/*! @azure/msal-common v15.7.0 2025-06-10 */(t.account);if(s&&t.domainHint&&(r.warning('AuthorizationCodeClient.createAuthCodeUrlQueryString: "domainHint" param is set, skipping opaque "login_hint" claim. Please consider not passing domainHint'),s=null),s){r.verbose("createAuthCodeUrlQueryString: login_hint claim present on account"),mn(i,s),n?.addFields({loginHintFromClaim:!0},o);try{yn(i,yr(t.account.homeAccountId))}catch(e){r.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(e&&t.prompt===k.NONE){r.verbose("createAuthCodeUrlQueryString: Prompt is none, adding sid from account"),Cn(i,e),n?.addFields({sidFromClaim:!0},o);try{yn(i,yr(t.account.homeAccountId))}catch(e){r.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}else if(t.loginHint)r.verbose("createAuthCodeUrlQueryString: Adding login_hint from request"),mn(i,t.loginHint),fn(i,t.loginHint),n?.addFields({loginHintFromRequest:!0},o);else if(t.account.username){r.verbose("createAuthCodeUrlQueryString: Adding login_hint from account"),mn(i,t.account.username),n?.addFields({loginHintFromUpn:!0},o);try{yn(i,yr(t.account.homeAccountId))}catch(e){r.verbose("createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header")}}}else t.loginHint&&(r.verbose("createAuthCodeUrlQueryString: No account, adding login_hint from request"),mn(i,t.loginHint),fn(i,t.loginHint),n?.addFields({loginHintFromRequest:!0},o));else r.verbose("createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints");var a;return t.nonce&&function(e,t){e.set("nonce",t)}(i,t.nonce),t.state&&An(i,t.state),(t.claims||e.clientCapabilities&&e.clientCapabilities.length>0)&&vn(i,t.claims,e.clientCapabilities),t.embeddedClientId&&Un(i,e.clientId,e.redirectUri),!e.instanceAware||t.extraQueryParameters&&Object.keys(t.extraQueryParameters).includes(ln)||_n(i),i}function Po(e,t,r,n){const o=Pr(t,r,n);return Mr.appendQueryString(e.authorizationEndpoint,o)}function Mo(e,t){if(!e.state||!t)throw e.state?gt(_e,"Cached State"):gt(_e,"Server State");let r,n;try{r=decodeURIComponent(e.state)}catch(t){throw gt(Ee,e.state)}try{n=decodeURIComponent(t)}catch(t){throw gt(Ee,e.state)}if(r!==n)throw gt(Re);if(e.error||e.error_description||e.suberror){const t=function(e){const t="code=",r=e.error_uri?.lastIndexOf(t);return r&&r>=0?e.error_uri?.substring(r+t.length):void 0}(e);if(mo(e.error,e.error_description,e.suberror))throw new po(e.error||"",e.error_description,e.suberror,e.timestamp||"",e.trace_id||"",e.correlation_id||"",e.claims||"",t);throw new Vn(e.error||"",e.error_description,e.suberror,t)}}function Oo(e){const{skus:t,libraryName:r,libraryVersion:n,extensionName:o,extensionVersion:i}=e,a=new Map([[0,[r,n]],[2,[o,i]]]);let s=[];if(t?.length){if(s=t.split(","),s.length<4)return t}else s=Array.from({length:4},(()=>"|"));return a.forEach(((e,t)=>{2===e.length&&e[0]?.length&&e[1]?.length&&function(e){const{skuArr:t,index:r,skuName:n,skuVersion:o}=e;if(r>=t.length)return;t[r]=[n,o].join("|")}({skuArr:s,index:t,skuName:e[0],skuVersion:e[1]})})),s.join(",")}class No{constructor(e,r){this.cacheOutcome=ae,this.cacheManager=r,this.apiId=e.apiId,this.correlationId=e.correlationId,this.wrapperSKU=e.wrapperSKU||t.EMPTY_STRING,this.wrapperVer=e.wrapperVer||t.EMPTY_STRING,this.telemetryCacheKey=Q.CACHE_KEY+L+e.clientId}generateCurrentRequestHeaderValue(){const e=`${this.apiId}${Q.VALUE_SEPARATOR}${this.cacheOutcome}`,t=[this.wrapperSKU,this.wrapperVer],r=this.getNativeBrokerErrorCode();r?.length&&t.push(`broker_error=${r}`);const n=t.join(Q.VALUE_SEPARATOR),o=[e,this.getRegionDiscoveryFields()].join(Q.VALUE_SEPARATOR);return[Q.SCHEMA_VERSION,o,n].join(Q.CATEGORY_SEPARATOR)}generateLastRequestHeaderValue(){const e=this.getLastRequests(),t=No.maxErrorsToSend(e),r=e.failedRequests.slice(0,2*t).join(Q.VALUE_SEPARATOR),n=e.errors.slice(0,t).join(Q.VALUE_SEPARATOR),o=e.errors.length,i=[o,t<o?Q.OVERFLOW_TRUE:Q.OVERFLOW_FALSE].join(Q.VALUE_SEPARATOR);return[Q.SCHEMA_VERSION,e.cacheHits,r,n,i].join(Q.CATEGORY_SEPARATOR)}cacheFailedRequest(e){const t=this.getLastRequests();t.errors.length>=Q.MAX_CACHED_ERRORS&&(t.failedRequests.shift(),t.failedRequests.shift(),t.errors.shift()),t.failedRequests.push(this.apiId,this.correlationId),e instanceof Error&&e&&e.toString()?e instanceof ye?e.subError?t.errors.push(e.subError):e.errorCode?t.errors.push(e.errorCode):t.errors.push(e.toString()):t.errors.push(e.toString()):t.errors.push(Q.UNKNOWN_ERROR),this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)}incrementCacheHits(){const e=this.getLastRequests();return e.cacheHits+=1,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e),e.cacheHits}getLastRequests(){return this.cacheManager.getServerTelemetry(this.telemetryCacheKey)||{failedRequests:[],errors:[],cacheHits:0}}clearTelemetryCache(){const e=this.getLastRequests(),t=No.maxErrorsToSend(e);if(t===e.errors.length)this.cacheManager.removeItem(this.telemetryCacheKey);else{const r={failedRequests:e.failedRequests.slice(2*t),errors:e.errors.slice(t),cacheHits:0};this.cacheManager.setServerTelemetry(this.telemetryCacheKey,r)}}static maxErrorsToSend(e){let r,n=0,o=0;const i=e.errors.length;for(r=0;r<i;r++){const i=e.failedRequests[2*r]||t.EMPTY_STRING,a=e.failedRequests[2*r+1]||t.EMPTY_STRING,s=e.errors[r]||t.EMPTY_STRING;if(o+=i.toString().length+a.toString().length+s.length+3,!(o<Q.MAX_LAST_HEADER_BYTES))break;n+=1}return n}getRegionDiscoveryFields(){const e=[];return e.push(this.regionUsed||t.EMPTY_STRING),e.push(this.regionSource||t.EMPTY_STRING),e.push(this.regionOutcome||t.EMPTY_STRING),e.join(",")}updateRegionDiscoveryMetadata(e){this.regionUsed=e.region_used,this.regionSource=e.region_source,this.regionOutcome=e.region_outcome}setCacheOutcome(e){this.cacheOutcome=e}setNativeBrokerErrorCode(e){const t=this.getLastRequests();t.nativeBrokerErrorCode=e,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,t)}getNativeBrokerErrorCode(){return this.getLastRequests().nativeBrokerErrorCode}clearNativeBrokerErrorCode(){const e=this.getLastRequests();delete e.nativeBrokerErrorCode,this.cacheManager.setServerTelemetry(this.telemetryCacheKey,e)}static makeExtraSkuString(e){return Oo(e)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */const qo="missing_kid_error",Lo="missing_alg_error",Uo={[qo]:"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.",[Lo]:"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."};class Ho extends ye{constructor(e,t){super(e,t),this.name="JoseHeaderError",Object.setPrototypeOf(this,Ho.prototype)}}function xo(e){return new Ho(e,Uo[e])}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Do{constructor(e){this.typ=e.typ,this.alg=e.alg,this.kid=e.kid}static getShrHeaderString(e){if(!e.kid)throw xo(qo);if(!e.alg)throw xo(Lo);const t=new Do({typ:e.typ||de.Pop,kid:e.kid,alg:e.alg});return JSON.stringify(t)}}
/*! @azure/msal-common v15.7.0 2025-06-10 */class Bo{startMeasurement(){}endMeasurement(){}flushMeasurement(){return null}}class Fo{generateId(){return"callback-id"}startMeasurement(e,t){return{end:()=>null,discard:()=>{},add:()=>{},increment:()=>{},event:{eventId:this.generateId(),status:Dn,authority:"",libraryName:"",libraryVersion:"",clientId:"",name:e,startTimeMs:Date.now(),correlationId:t||""},measurement:new Bo}}startPerformanceMeasurement(){return new Bo}calculateQueuedTime(){return 0}addQueueMeasurement(){}setPreQueueTime(){}endMeasurement(){return null}discardMeasurements(){}removePerformanceCallback(){return!0}addPerformanceCallback(){return""}emitEvents(){}addFields(){}incrementFields(){}cacheEventByCorrelationId(){}}
/*! @azure/msal-common v15.7.0 2025-06-10 */function Ko(e,t,r,n=5){if(e instanceof Error)return e instanceof ye?(r.errorCode=e.errorCode,r.subErrorCode=e.subError,void((e instanceof Vn||e instanceof po)&&(r.serverErrorNo=e.errorNo))):void(e instanceof Br?r.errorCode=e.errorCode:r.errorStack?.length?t.trace("PerformanceClient.addErrorStack: Stack already exist",r.correlationId):e.stack?.length?(e.stack&&(r.errorStack=function(e,t){if(t<0)return[];const r=e.split("\n")||[],n=[],o=r[0];o.startsWith("TypeError: Cannot read property")||o.startsWith("TypeError: Cannot read properties of")||o.startsWith("TypeError: Cannot set property")||o.startsWith("TypeError: Cannot set properties of")||o.endsWith("is not a function")?n.push(zo(o)):(o.startsWith("SyntaxError")||o.startsWith("TypeError"))&&n.push(zo(o.replace(/['].*[']|["].*["]/g,"<redacted>")));for(let e=1;e<r.length&&!(n.length>=t);e++){const t=r[e];n.push(zo(t))}return n}(e.stack,n)),r.errorName=e.name):t.trace("PerformanceClient.addErrorStack: Input stack is empty",r.correlationId));t.trace("PerformanceClient.addErrorStack: Input error is not instance of Error",r.correlationId)}function zo(e){const t=e.lastIndexOf(" ")+1;if(t<1)return e;const r=e.substring(t);let n=r.lastIndexOf("/");return n=n<0?r.lastIndexOf("\\"):n,n>=0?(e.substring(0,t)+"("+r.substring(n+1)+(")"===r.charAt(r.length-1)?"":")")).trimStart():e.trimStart()}class Go{constructor(e,t,r,n,o,i,a,s){this.authority=t,this.libraryName=n,this.libraryVersion=o,this.applicationTelemetry=i,this.clientId=e,this.logger=r,this.callbacks=new Map,this.eventsByCorrelationId=new Map,this.eventStack=new Map,this.queueMeasurements=new Map,this.preQueueTimeByCorrelationId=new Map,this.intFields=a||new Set;for(const e of Fn)this.intFields.add(e);this.abbreviations=s||new Map;for(const[e,t]of xn)this.abbreviations.set(e,t)}startPerformanceMeasurement(e,t){return{}}getPreQueueTime(e,t){const r=this.preQueueTimeByCorrelationId.get(t);if(r){if(r.name===e)return r.time;this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue time found for ${e}, unable to add queue measurement`)}else this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue times found for correlationId: ${t}, unable to add queue measurement`)}calculateQueuedTime(e,t){return e<1?(this.logger.trace(`PerformanceClient: preQueueTime should be a positive integer and not ${e}`),0):t<1?(this.logger.trace(`PerformanceClient: currentTime should be a positive integer and not ${t}`),0):t<e?(this.logger.trace("PerformanceClient: currentTime is less than preQueueTime, check how time is being retrieved"),0):t-e}addQueueMeasurement(e,t,r,n){if(!t)return void this.logger.trace(`PerformanceClient.addQueueMeasurement: correlationId not provided for ${e}, cannot add queue measurement`);if(0===r)this.logger.trace(`PerformanceClient.addQueueMeasurement: queue time provided for ${e} is ${r}`);else if(!r)return void this.logger.trace(`PerformanceClient.addQueueMeasurement: no queue time provided for ${e}`);const o={eventName:e,queueTime:n?0:r,manuallyCompleted:n},i=this.queueMeasurements.get(t);if(i)i.push(o),this.queueMeasurements.set(t,i);else{this.logger.trace(`PerformanceClient.addQueueMeasurement: adding correlationId ${t} to queue measurements`);const e=[o];this.queueMeasurements.set(t,e)}this.preQueueTimeByCorrelationId.delete(t)}startMeasurement(e,t){const r=t||this.generateId();t||this.logger.info(`PerformanceClient: No correlation id provided for ${e}, generating`,r),this.logger.trace(`PerformanceClient: Performance measurement started for ${e}`,r);const n={eventId:this.generateId(),status:Dn,authority:this.authority,libraryName:this.libraryName,libraryVersion:this.libraryVersion,clientId:this.clientId,name:e,startTimeMs:Date.now(),correlationId:r,appName:this.applicationTelemetry?.appName,appVersion:this.applicationTelemetry?.appVersion};var o,i,a;return this.cacheEventByCorrelationId(n),o=n,i=this.abbreviations,(a=this.eventStack.get(r))&&a.push({name:i.get(o.name)||o.name}),{end:(e,t)=>this.endMeasurement({...n,...e},t),discard:()=>this.discardMeasurements(n.correlationId),add:e=>this.addFields(e,n.correlationId),increment:e=>this.incrementFields(e,n.correlationId),event:n,measurement:new Bo}}endMeasurement(e,t){const r=this.eventsByCorrelationId.get(e.correlationId);if(!r)return this.logger.trace(`PerformanceClient: Measurement not found for ${e.eventId}`,e.correlationId),null;const n=e.eventId===r.eventId;let o={totalQueueTime:0,totalQueueCount:0,manuallyCompletedCount:0};e.durationMs=Math.round(e.durationMs||this.getDurationMs(e.startTimeMs));const i=JSON.stringify(function(e,t,r,n){if(!r?.length)return;const o=e=>e.length?e[e.length-1]:void 0,i=t.get(e.name)||e.name,a=o(r);if(a?.name!==i)return;const s=r?.pop();if(!s)return;const c=n instanceof ye?n.errorCode:n instanceof Error?n.name:void 0,l=n instanceof ye?n.subError:void 0;c&&s.childErr!==c&&(s.err=c,l&&(s.subErr=l)),delete s.name,delete s.childErr;const h={...s,dur:e.durationMs};e.success||(h.fail=1);const d=o(r);if(!d)return{[i]:h};let u;if(c&&(d.childErr=c),d[i]){const e=Object.keys(d).filter((e=>e.startsWith(i))).length;u=`${i}_${e+1}`}else u=i;return d[u]=h,d}(e,this.abbreviations,this.eventStack.get(r.correlationId),t));if(n?(o=this.getQueueInfo(e.correlationId),this.discardMeasurements(r.correlationId)):r.incompleteSubMeasurements?.delete(e.eventId),this.logger.trace(`PerformanceClient: Performance measurement ended for ${e.name}: ${e.durationMs} ms`,e.correlationId),t&&Ko(t,this.logger,r),!n)return r[e.name+"DurationMs"]=Math.floor(e.durationMs),{...r};n&&!t&&(r.errorCode||r.subErrorCode)&&(this.logger.trace(`PerformanceClient: Remove error and sub-error codes for root event ${e.name} as intermediate error was successfully handled`,e.correlationId),r.errorCode=void 0,r.subErrorCode=void 0);let a={...r,...e},s=0;return a.incompleteSubMeasurements?.forEach((t=>{this.logger.trace(`PerformanceClient: Incomplete submeasurement ${t.name} found for ${e.name}`,a.correlationId),s++})),a.incompleteSubMeasurements=void 0,a={...a,queuedTimeMs:o.totalQueueTime,queuedCount:o.totalQueueCount,queuedManuallyCompletedCount:o.manuallyCompletedCount,status:Bn,incompleteSubsCount:s,context:i},this.truncateIntegralFields(a),this.emitEvents([a],e.correlationId),a}addFields(e,t){this.logger.trace("PerformanceClient: Updating static fields");const r=this.eventsByCorrelationId.get(t);r?this.eventsByCorrelationId.set(t,{...r,...e}):this.logger.trace("PerformanceClient: Event not found for",t)}incrementFields(e,t){this.logger.trace("PerformanceClient: Updating counters");const r=this.eventsByCorrelationId.get(t);if(r)for(const t in e){if(r.hasOwnProperty(t)){if(isNaN(Number(r[t])))return}else r[t]=0;r[t]+=e[t]}else this.logger.trace("PerformanceClient: Event not found for",t)}cacheEventByCorrelationId(e){const t=this.eventsByCorrelationId.get(e.correlationId);t?(this.logger.trace(`PerformanceClient: Performance measurement for ${e.name} added/updated`,e.correlationId),t.incompleteSubMeasurements=t.incompleteSubMeasurements||new Map,t.incompleteSubMeasurements.set(e.eventId,{name:e.name,startTimeMs:e.startTimeMs})):(this.logger.trace(`PerformanceClient: Performance measurement for ${e.name} started`,e.correlationId),this.eventsByCorrelationId.set(e.correlationId,{...e}),this.eventStack.set(e.correlationId,[]))}getQueueInfo(e){const t=this.queueMeasurements.get(e);t||this.logger.trace(`PerformanceClient: no queue measurements found for for correlationId: ${e}`);let r=0,n=0,o=0;return t?.forEach((e=>{r+=e.queueTime,n++,o+=e.manuallyCompleted?1:0})),{totalQueueTime:r,totalQueueCount:n,manuallyCompletedCount:o}}discardMeasurements(e){this.logger.trace("PerformanceClient: Performance measurements discarded",e),this.eventsByCorrelationId.delete(e),this.logger.trace("PerformanceClient: QueueMeasurements discarded",e),this.queueMeasurements.delete(e),this.logger.trace("PerformanceClient: Pre-queue times discarded",e),this.preQueueTimeByCorrelationId.delete(e),this.logger.trace("PerformanceClient: Event stack discarded",e),this.eventStack.delete(e)}addPerformanceCallback(e){for(const[t,r]of this.callbacks)if(r.toString()===e.toString())return this.logger.warning(`PerformanceClient: Performance callback is already registered with id: ${t}`),t;const t=this.generateId();return this.callbacks.set(t,e),this.logger.verbose(`PerformanceClient: Performance callback registered with id: ${t}`),t}removePerformanceCallback(e){const t=this.callbacks.delete(e);return t?this.logger.verbose(`PerformanceClient: Performance callback ${e} removed.`):this.logger.verbose(`PerformanceClient: Performance callback ${e} not removed.`),t}emitEvents(e,t){this.logger.verbose("PerformanceClient: Emitting performance events",t),this.callbacks.forEach(((r,n)=>{this.logger.trace(`PerformanceClient: Emitting event to callback ${n}`,t),r.apply(null,[e])}))}truncateIntegralFields(e){this.intFields.forEach((t=>{t in e&&"number"==typeof e[t]&&(e[t]=Math.floor(e[t]))}))}getDurationMs(e){const t=Date.now()-e;return t<0?t:0}}const $o="pkce_not_created",Qo="ear_jwk_empty",jo="ear_jwe_empty",Wo="crypto_nonexistent",Vo="empty_navigate_uri",Jo="hash_empty_error",Yo="no_state_in_hash",Xo="hash_does_not_contain_known_properties",Zo="unable_to_parse_state",ei="state_interaction_type_mismatch",ti="interaction_in_progress",ri="popup_window_error",ni="empty_window_error",oi="user_cancelled",ii="monitor_popup_timeout",ai="monitor_window_timeout",si="redirect_in_iframe",ci="block_iframe_reload",li="block_nested_popups",hi="iframe_closed_prematurely",di="silent_logout_unsupported",ui="no_account_error",gi="silent_prompt_value_error",pi="no_token_request_cache_error",mi="unable_to_parse_token_request_cache_error",fi="auth_request_not_set_error",yi="invalid_cache_type",Ci="non_browser_environment",vi="database_not_open",wi="no_network_connectivity",Ii="post_request_failed",Ti="get_request_failed",Ai="failed_to_parse_response",ki="unable_to_load_token",Si="crypto_key_not_found",bi="auth_code_required",Ei="auth_code_or_nativeAccountId_required",Ri="spa_code_and_nativeAccountId_present",_i="database_unavailable",Pi="unable_to_acquire_token_from_native_platform",Mi="native_handshake_timeout",Oi="native_extension_not_installed",Ni="native_connection_not_established",qi="uninitialized_public_client_application",Li="native_prompt_not_supported",Ui="invalid_base64_string",Hi="invalid_pop_token_request",xi="failed_to_build_headers",Di="failed_to_parse_headers",Bi="failed_to_decrypt_ear_response";var Fi=Object.freeze({__proto__:null,authCodeOrNativeAccountIdRequired:Ei,authCodeRequired:bi,authRequestNotSetError:fi,blockIframeReload:ci,blockNestedPopups:li,cryptoKeyNotFound:Si,cryptoNonExistent:Wo,databaseNotOpen:vi,databaseUnavailable:_i,earJweEmpty:jo,earJwkEmpty:Qo,emptyNavigateUri:Vo,emptyWindowError:ni,failedToBuildHeaders:xi,failedToDecryptEarResponse:Bi,failedToParseHeaders:Di,failedToParseResponse:Ai,getRequestFailed:Ti,hashDoesNotContainKnownProperties:Xo,hashEmptyError:Jo,iframeClosedPrematurely:hi,interactionInProgress:ti,invalidBase64String:Ui,invalidCacheType:yi,invalidPopTokenRequest:Hi,monitorPopupTimeout:ii,monitorWindowTimeout:ai,nativeConnectionNotEstablished:Ni,nativeExtensionNotInstalled:Oi,nativeHandshakeTimeout:Mi,nativePromptNotSupported:Li,noAccountError:ui,noNetworkConnectivity:wi,noStateInHash:Yo,noTokenRequestCacheError:pi,nonBrowserEnvironment:Ci,pkceNotCreated:$o,popupWindowError:ri,postRequestFailed:Ii,redirectInIframe:si,silentLogoutUnsupported:di,silentPromptValueError:gi,spaCodeAndNativeAccountIdPresent:Ri,stateInteractionTypeMismatch:ei,unableToAcquireTokenFromNativePlatform:Pi,unableToLoadToken:ki,unableToParseState:Zo,unableToParseTokenRequestCacheError:mi,uninitializedPublicClientApplication:qi,userCancelled:oi});const Ki="For more visit: aka.ms/msaljs/browser-errors",zi={[$o]:"The PKCE code challenge and verifier could not be generated.",[Qo]:"No EAR encryption key provided. This is unexpected.",[jo]:"Server response does not contain ear_jwe property. This is unexpected.",[Wo]:"The crypto object or function is not available.",[Vo]:"Navigation URI is empty. Please check stack trace for more info.",[Jo]:`Hash value cannot be processed because it is empty. Please verify that your redirectUri is not clearing the hash. ${Ki}`,[Yo]:"Hash does not contain state. Please verify that the request originated from msal.",[Xo]:`Hash does not contain known properites. Please verify that your redirectUri is not changing the hash.  ${Ki}`,[Zo]:"Unable to parse state. Please verify that the request originated from msal.",[ei]:"Hash contains state but the interaction type does not match the caller.",[ti]:`Interaction is currently in progress. Please ensure that this interaction has been completed before calling an interactive API.   ${Ki}`,[ri]:"Error opening popup window. This can happen if you are using IE or if popups are blocked in the browser.",[ni]:"window.open returned null or undefined window object.",[oi]:"User cancelled the flow.",[ii]:`Token acquisition in popup failed due to timeout.  ${Ki}`,[ai]:`Token acquisition in iframe failed due to timeout.  ${Ki}`,[si]:"Redirects are not supported for iframed or brokered applications. Please ensure you are using MSAL.js in a top frame of the window if using the redirect APIs, or use the popup APIs.",[ci]:`Request was blocked inside an iframe because MSAL detected an authentication response.  ${Ki}`,[li]:"Request was blocked inside a popup because MSAL detected it was running in a popup.",[hi]:"The iframe being monitored was closed prematurely.",[di]:"Silent logout not supported. Please call logoutRedirect or logoutPopup instead.",[ui]:"No account object provided to acquireTokenSilent and no active account has been set. Please call setActiveAccount or provide an account on the request.",[gi]:"The value given for the prompt value is not valid for silent requests - must be set to 'none' or 'no_session'.",[pi]:"No token request found in cache.",[mi]:"The cached token request could not be parsed.",[fi]:"Auth Request not set. Please ensure initiateAuthRequest was called from the InteractionHandler",[yi]:"Invalid cache type",[Ci]:"Login and token requests are not supported in non-browser environments.",[vi]:"Database is not open!",[wi]:"No network connectivity. Check your internet connection.",[Ii]:"Network request failed: If the browser threw a CORS error, check that the redirectUri is registered in the Azure App Portal as type 'SPA'",[Ti]:"Network request failed. Please check the network trace to determine root cause.",[Ai]:"Failed to parse network response. Check network trace.",[ki]:"Error loading token to cache.",[Si]:"Cryptographic Key or Keypair not found in browser storage.",[bi]:"An authorization code must be provided (as the `code` property on the request) to this flow.",[Ei]:"An authorization code or nativeAccountId must be provided to this flow.",[Ri]:"Request cannot contain both spa code and native account id.",[_i]:"IndexedDB, which is required for persistent cryptographic key storage, is unavailable. This may be caused by browser privacy features which block persistent storage in third-party contexts.",[Pi]:`Unable to acquire token from native platform.  ${Ki}`,[Mi]:"Timed out while attempting to establish connection to browser extension",[Oi]:"Native extension is not installed. If you think this is a mistake call the initialize function.",[Ni]:`Connection to native platform has not been established. Please install a compatible browser extension and run initialize().  ${Ki}`,[qi]:`You must call and await the initialize function before attempting to call any other MSAL API.  ${Ki}`,[Li]:"The provided prompt is not supported by the native platform. This request should be routed to the web based flow.",[Ui]:"Invalid base64 encoded string.",[Hi]:"Invalid PoP token request. The request should not have both a popKid value and signPopToken set to true.",[xi]:"Failed to build request headers object.",[Di]:"Failed to parse response headers",[Bi]:"Failed to decrypt ear response"},Gi={pkceNotGenerated:{code:$o,desc:zi[$o]},cryptoDoesNotExist:{code:Wo,desc:zi[Wo]},emptyNavigateUriError:{code:Vo,desc:zi[Vo]},hashEmptyError:{code:Jo,desc:zi[Jo]},hashDoesNotContainStateError:{code:Yo,desc:zi[Yo]},hashDoesNotContainKnownPropertiesError:{code:Xo,desc:zi[Xo]},unableToParseStateError:{code:Zo,desc:zi[Zo]},stateInteractionTypeMismatchError:{code:ei,desc:zi[ei]},interactionInProgress:{code:ti,desc:zi[ti]},popupWindowError:{code:ri,desc:zi[ri]},emptyWindowError:{code:ni,desc:zi[ni]},userCancelledError:{code:oi,desc:zi[oi]},monitorPopupTimeoutError:{code:ii,desc:zi[ii]},monitorIframeTimeoutError:{code:ai,desc:zi[ai]},redirectInIframeError:{code:si,desc:zi[si]},blockTokenRequestsInHiddenIframeError:{code:ci,desc:zi[ci]},blockAcquireTokenInPopupsError:{code:li,desc:zi[li]},iframeClosedPrematurelyError:{code:hi,desc:zi[hi]},silentLogoutUnsupportedError:{code:di,desc:zi[di]},noAccountError:{code:ui,desc:zi[ui]},silentPromptValueError:{code:gi,desc:zi[gi]},noTokenRequestCacheError:{code:pi,desc:zi[pi]},unableToParseTokenRequestCacheError:{code:mi,desc:zi[mi]},authRequestNotSet:{code:fi,desc:zi[fi]},invalidCacheType:{code:yi,desc:zi[yi]},notInBrowserEnvironment:{code:Ci,desc:zi[Ci]},databaseNotOpen:{code:vi,desc:zi[vi]},noNetworkConnectivity:{code:wi,desc:zi[wi]},postRequestFailed:{code:Ii,desc:zi[Ii]},getRequestFailed:{code:Ti,desc:zi[Ti]},failedToParseNetworkResponse:{code:Ai,desc:zi[Ai]},unableToLoadTokenError:{code:ki,desc:zi[ki]},signingKeyNotFoundInStorage:{code:Si,desc:zi[Si]},authCodeRequired:{code:bi,desc:zi[bi]},authCodeOrNativeAccountRequired:{code:Ei,desc:zi[Ei]},spaCodeAndNativeAccountPresent:{code:Ri,desc:zi[Ri]},databaseUnavailable:{code:_i,desc:zi[_i]},unableToAcquireTokenFromNativePlatform:{code:Pi,desc:zi[Pi]},nativeHandshakeTimeout:{code:Mi,desc:zi[Mi]},nativeExtensionNotInstalled:{code:Oi,desc:zi[Oi]},nativeConnectionNotEstablished:{code:Ni,desc:zi[Ni]},uninitializedPublicClientApplication:{code:qi,desc:zi[qi]},nativePromptNotSupported:{code:Li,desc:zi[Li]},invalidBase64StringError:{code:Ui,desc:zi[Ui]},invalidPopTokenRequest:{code:Hi,desc:zi[Hi]}};class $i extends ye{constructor(e,t){super(e,zi[e],t),Object.setPrototypeOf(this,$i.prototype),this.name="BrowserAuthError"}}function Qi(e,t){return new $i(e,t)}const ji="invalid_grant",Wi=483,Vi=600,Ji="msal",Yi=30,Xi="msal.js.browser",Zi="53ee284d-920a-4b59-9d30-a60315b26836",ea="ppnbnpeolgkicgegkbkbjmhlideopiji",ta="MATS",ra="MicrosoftEntra",na="DOM API",oa="get-token-and-sign-out",ia="PlatformAuthDOMHandler",aa="PlatformAuthExtensionHandler",sa="Handshake",ca="HandshakeResponse",la="GetToken",ha="Response",da={LocalStorage:"localStorage",SessionStorage:"sessionStorage",MemoryStorage:"memoryStorage"},ua="GET",ga="POST",pa="signin",ma="signout",fa="request.origin",ya="urlHash",Ca="request.params",va="code.verifier",wa="interaction.status",Ia="request.native",Ta="msal.account.keys",Aa="msal.token.keys",ka="msal.version",Sa="wrapper.sku",ba="wrapper.version",Ea={acquireTokenRedirect:861,acquireTokenPopup:862,ssoSilent:863,acquireTokenSilent_authCode:864,handleRedirectPromise:865,acquireTokenByCode:866,acquireTokenSilent_silentFlow:61,logout:961,logoutPopup:962};var Ra;e.InteractionType=void 0,(Ra=e.InteractionType||(e.InteractionType={})).Redirect="redirect",Ra.Popup="popup",Ra.Silent="silent",Ra.None="none";const _a={Startup:"startup",Login:"login",Logout:"logout",AcquireToken:"acquireToken",SsoSilent:"ssoSilent",HandleRedirect:"handleRedirect",None:"none"},Pa={scopes:c},Ma="msal.db",Oa=`${Ma}.keys`,Na={Default:0,AccessToken:1,AccessTokenAndRefreshToken:2,RefreshToken:3,RefreshTokenAndNetwork:4,Skip:5},qa=[Na.Default,Na.Skip,Na.RefreshTokenAndNetwork],La="msal.browser.platform.auth.dom";function Ua(e){return encodeURIComponent(xa(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"))}function Ha(e){return Da(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function xa(e){return Da((new TextEncoder).encode(e))}function Da(e){const t=Array.from(e,(e=>String.fromCodePoint(e))).join("");return btoa(t)}function Ba(e){return(new TextDecoder).decode(Fa(e))}function Fa(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Qi(Ui)}const r=atob(t);return Uint8Array.from(r,(e=>e.codePointAt(0)||0))}const Ka="AES-GCM",za="HKDF",Ga="SHA-256",$a=new Uint8Array([1,0,1]),Qa="0123456789abcdef",ja=new Uint32Array(1),Wa="raw",Va="encrypt",Ja="decrypt",Ya={name:"RSASSA-PKCS1-v1_5",hash:Ga,modulusLength:2048,publicExponent:$a};async function Xa(e,t,r){t?.addQueueMeasurement(Hn.Sha256Digest,r);const n=(new TextEncoder).encode(e);return window.crypto.subtle.digest(Ga,n)}function Za(e){return window.crypto.getRandomValues(e)}function es(){return window.crypto.getRandomValues(ja),ja[0]}function ts(){const e=Date.now(),t=1024*es()+(1023&es()),r=new Uint8Array(16),n=Math.trunc(t/2**30),o=t&2**30-1,i=es();r[0]=e/2**40,r[1]=e/2**32,r[2]=e/2**24,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|n>>>8,r[7]=n,r[8]=128|o>>>24,r[9]=o>>>16,r[10]=o>>>8,r[11]=o,r[12]=i>>>24,r[13]=i>>>16,r[14]=i>>>8,r[15]=i;let a="";for(let e=0;e<r.length;e++)a+=Qa.charAt(r[e]>>>4),a+=Qa.charAt(15&r[e]),3!==e&&5!==e&&7!==e&&9!==e||(a+="-");return a}async function rs(e){return window.crypto.subtle.exportKey("jwk",e)}async function ns(){const e=await is(),t={alg:"dir",kty:"oct",k:Ha(new Uint8Array(e))};return xa(JSON.stringify(t))}async function os(e,t){const r=t.split(".");if(5!==r.length)throw Qi(Bi,"jwe_length");const n=await async function(e){const t=Ba(e),r=Fa(JSON.parse(t).k);return window.crypto.subtle.importKey(Wa,r,Ka,!1,[Ja])}(e).catch((()=>{throw Qi(Bi,"import_key")}));try{const e=(new TextEncoder).encode(r[0]),t=Fa(r[2]),o=Fa(r[3]),i=Fa(r[4]),a=8*i.byteLength,s=new Uint8Array(o.length+i.length);s.set(o),s.set(i,o.length);const c=await window.crypto.subtle.decrypt({name:Ka,iv:t,tagLength:a,additionalData:e},n,s);return(new TextDecoder).decode(c)}catch(e){throw Qi(Bi,"decrypt")}}async function is(){const e=await window.crypto.subtle.generateKey({name:Ka,length:256},!0,[Va,Ja]);return window.crypto.subtle.exportKey(Wa,e)}async function as(e){return window.crypto.subtle.importKey(Wa,e,za,!1,["deriveKey"])}async function ss(e,t,r){return window.crypto.subtle.deriveKey({name:za,salt:t,hash:Ga,info:(new TextEncoder).encode(r)},e,{name:Ka,length:256},!1,[Va,Ja])}async function cs(e,t,r){const n=(new TextEncoder).encode(t),o=window.crypto.getRandomValues(new Uint8Array(16)),i=await ss(e,o,r),a=await window.crypto.subtle.encrypt({name:Ka,iv:new Uint8Array(12)},i,n);return{data:Ha(new Uint8Array(a)),nonce:Ha(o)}}async function ls(e,t,r,n){const o=Fa(n),i=await ss(e,Fa(t),r),a=await window.crypto.subtle.decrypt({name:Ka,iv:new Uint8Array(12)},i,o);return(new TextDecoder).decode(a)}async function hs(e){const t=await Xa(e);return Ha(new Uint8Array(t))}const ds="storage_not_supported",us="stubbed_public_client_application_called",gs="in_mem_redirect_unavailable";var ps=Object.freeze({__proto__:null,inMemRedirectUnavailable:gs,storageNotSupported:ds,stubbedPublicClientApplicationCalled:us});const ms={[ds]:"Given storage configuration option was not supported.",[us]:"Stub instance of Public Client Application was called. If using msal-react, please ensure context is not used without a provider. For more visit: aka.ms/msaljs/browser-errors",[gs]:"Redirect cannot be supported. In-memory storage was selected and storeAuthStateInCookie=false, which would cause the library to be unable to handle the incoming hash. If you would like to use the redirect API, please use session/localStorage or set storeAuthStateInCookie=true."},fs={storageNotSupportedError:{code:ds,desc:ms[ds]},stubPcaInstanceCalled:{code:us,desc:ms[us]},inMemRedirectUnavailable:{code:gs,desc:ms[gs]}};class ys extends ye{constructor(e,t){super(e,t),this.name="BrowserConfigurationAuthError",Object.setPrototypeOf(this,ys.prototype)}}function Cs(e){return new ys(e,ms[e])}function vs(e){e.location.hash="","function"==typeof e.history.replaceState&&e.history.replaceState(null,"",`${e.location.origin}${e.location.pathname}${e.location.search}`)}function ws(e){const t=e.split("#");t.shift(),window.location.hash=t.length>0?t.join("#"):""}function Is(){return window.parent!==window}function Ts(){return"undefined"!=typeof window&&!!window.opener&&window.opener!==window&&"string"==typeof window.name&&0===window.name.indexOf(`${Ji}.`)}function As(){return"undefined"!=typeof window&&window.location?window.location.href.split("?")[0].split("#")[0]:""}function ks(){const e=new Mr(window.location.href).getUrlComponents();return`${e.Protocol}//${e.HostNameAndPort}/`}function Ss(){if(Mr.hashContainsKnownProperties(window.location.hash)&&Is())throw Qi(ci)}function bs(e){if(Is()&&!e)throw Qi(si)}function Es(){if(Ts())throw Qi(li)}function Rs(){if("undefined"==typeof window)throw Qi(Ci)}function _s(e){if(!e)throw Qi(qi)}function Ps(e){Rs(),Ss(),Es(),_s(e)}function Ms(e,t){if(Ps(e),bs(t.system.allowRedirectInIframe),t.cache.cacheLocation===da.MemoryStorage&&!t.cache.storeAuthStateInCookie)throw Cs(gs)}function Os(e){const t=document.createElement("link");t.rel="preconnect",t.href=new URL(e).origin,t.crossOrigin="anonymous",document.head.appendChild(t),window.setTimeout((()=>{try{document.head.removeChild(t)}catch{}}),1e4)}function Ns(){return ts()}var qs=Object.freeze({__proto__:null,blockAPICallsBeforeInitialize:_s,blockAcquireTokenInPopups:Es,blockNonBrowserEnvironment:Rs,blockRedirectInIframe:bs,blockReloadInHiddenIframes:Ss,clearHash:vs,createGuid:Ns,getCurrentUri:As,getHomepage:ks,invoke:Kn,invokeAsync:zn,isInIframe:Is,isInPopup:Ts,preconnect:Os,preflightCheck:Ps,redirectPreflightCheck:Ms,replaceHash:ws});class Ls{navigateInternal(e,t){return Ls.defaultNavigateWindow(e,t)}navigateExternal(e,t){return Ls.defaultNavigateWindow(e,t)}static defaultNavigateWindow(e,t){return t.noHistory?window.location.replace(e):window.location.assign(e),new Promise((e=>{setTimeout((()=>{e(!0)}),t.timeout)}))}}class Us{async sendGetRequestAsync(e,t){let r,n={},o=0;const i=Hs(t);try{r=await fetch(e,{method:ua,headers:i})}catch(e){throw Zn(Qi(window.navigator.onLine?Ti:wi),void 0,void 0,e)}n=xs(r.headers);try{return o=r.status,{headers:n,body:await r.json(),status:o}}catch(e){throw Zn(Qi(Ai),o,n,e)}}async sendPostRequestAsync(e,t){const r=t&&t.body||"",n=Hs(t);let o,i=0,a={};try{o=await fetch(e,{method:ga,headers:n,body:r})}catch(e){throw Zn(Qi(window.navigator.onLine?Ii:wi),void 0,void 0,e)}a=xs(o.headers);try{return i=o.status,{headers:a,body:await o.json(),status:i}}catch(e){throw Zn(Qi(Ai),i,a,e)}}}function Hs(e){try{const t=new Headers;if(!e||!e.headers)return t;const r=e.headers;return Object.entries(r).forEach((([e,r])=>{t.append(e,r)})),t}catch(e){throw Zn(Qi(xi),void 0,void 0,e)}}function xs(e){try{const t={};return e.forEach(((e,r)=>{t[r]=e})),t}catch(e){throw Qi(Di)}}const Ds=1e4;const Bs="@azure/msal-browser",Fs="4.13.1";class Ks{static loggerCallback(t,r){switch(t){case e.LogLevel.Error:return void console.error(r);case e.LogLevel.Info:return void console.info(r);case e.LogLevel.Verbose:return void console.debug(r);case e.LogLevel.Warning:return void console.warn(r);default:return void console.log(r)}}constructor(r){let n;this.browserEnvironment="undefined"!=typeof window,this.config=function({auth:r,cache:n,system:o,telemetry:i},a){const s={clientId:t.EMPTY_STRING,authority:`${t.DEFAULT_AUTHORITY}`,knownAuthorities:[],cloudDiscoveryMetadata:t.EMPTY_STRING,authorityMetadata:t.EMPTY_STRING,redirectUri:"undefined"!=typeof window?As():"",postLogoutRedirectUri:t.EMPTY_STRING,navigateToLoginRequestUrl:!0,clientCapabilities:[],protocolMode:br.AAD,OIDCOptions:{serverResponseType:R.FRAGMENT,defaultScopes:[t.OPENID_SCOPE,t.PROFILE_SCOPE,t.OFFLINE_ACCESS_SCOPE]},azureCloudOptions:{azureCloudInstance:vt.None,tenant:t.EMPTY_STRING},skipAuthorityMetadataCache:!1,supportsNestedAppAuth:!1,instanceAware:!1,encodeExtraQueryParams:!1},c={cacheLocation:da.SessionStorage,temporaryCacheLocation:da.SessionStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!(!n||n.cacheLocation!==da.LocalStorage),claimsBasedCachingEnabled:!1},l={loggerCallback:()=>{},logLevel:e.LogLevel.Info,piiLoggingEnabled:!1},h={...{...zr,loggerOptions:l,networkClient:a?new Us:Ro,navigationClient:new Ls,loadFrameTimeout:0,windowHashTimeout:o?.loadFrameTimeout||6e4,iframeHashTimeout:o?.loadFrameTimeout||Ds,navigateFrameWait:0,redirectNavigationTimeout:3e4,asyncPopups:!1,allowRedirectInIframe:!1,allowPlatformBroker:!1,nativeBrokerHandshakeTimeout:o?.nativeBrokerHandshakeTimeout||2e3,pollIntervalMilliseconds:Yi},...o,loggerOptions:o?.loggerOptions||l},d={application:{appName:t.EMPTY_STRING,appVersion:t.EMPTY_STRING},client:new Fo};r?.protocolMode!==br.OIDC&&r?.OIDCOptions&&new ft(h.loggerOptions).warning(JSON.stringify(gr(ar)));if(r?.protocolMode&&r.protocolMode===br.OIDC&&h?.allowPlatformBroker)throw gr(sr);return{auth:{...s,...r,OIDCOptions:{...s.OIDCOptions,...r?.OIDCOptions}},cache:{...c,...n},system:h,telemetry:{...d,...i}}}(r,this.browserEnvironment);try{n=window[da.SessionStorage]}catch(e){}const o=n?.getItem("msal.browser.log.level"),i=n?.getItem("msal.browser.log.pii")?.toLowerCase(),a="true"===i||"false"!==i&&void 0,s={...this.config.system.loggerOptions},c=o&&Object.keys(e.LogLevel).includes(o)?e.LogLevel[o]:void 0;c&&(s.loggerCallback=Ks.loggerCallback,s.logLevel=c),void 0!==a&&(s.piiLoggingEnabled=a),this.logger=new ft(s,Bs,Fs),this.available=!1}getConfig(){return this.config}getLogger(){return this.logger}isAvailable(){return this.available}isBrowserEnvironment(){return this.browserEnvironment}}const zs="USER_INTERACTION_REQUIRED",Gs="USER_CANCEL",$s="NO_NETWORK",Qs="TRANSIENT_ERROR",js="PERSISTENT_ERROR",Ws="DISABLED",Vs="ACCOUNT_UNAVAILABLE",Js="NESTED_APP_AUTH_UNAVAILABLE";class Ys{static async initializeNestedAppAuthBridge(){if(void 0===window)throw new Error("window is undefined");if(void 0===window.nestedAppAuthBridge)throw new Error("window.nestedAppAuthBridge is undefined");try{window.nestedAppAuthBridge.addEventListener("message",(e=>{const t="string"==typeof e?e:e.data,r=JSON.parse(t),n=Ys.bridgeRequests.find((e=>e.requestId===r.requestId));void 0!==n&&(Ys.bridgeRequests.splice(Ys.bridgeRequests.indexOf(n),1),r.success?n.resolve(r):n.reject(r.error))}));const e=await new Promise(((e,t)=>{const r=Ys.buildRequest("GetInitContext"),n={requestId:r.requestId,method:r.method,resolve:e,reject:t};Ys.bridgeRequests.push(n),window.nestedAppAuthBridge.postMessage(JSON.stringify(r))}));return Ys.validateBridgeResultOrThrow(e.initContext)}catch(e){throw window.console.log(e),e}}getTokenInteractive(e){return this.getToken("GetTokenPopup",e)}getTokenSilent(e){return this.getToken("GetToken",e)}async getToken(e,t){const r=await this.sendRequest(e,{tokenParams:t});return{token:Ys.validateBridgeResultOrThrow(r.token),account:Ys.validateBridgeResultOrThrow(r.account)}}getHostCapabilities(){return this.capabilities??null}getAccountContext(){return this.accountContext?this.accountContext:null}static buildRequest(e,t){return{messageType:"NestedAppAuthRequest",method:e,requestId:ts(),sendTime:Date.now(),clientLibrary:Xi,clientLibraryVersion:Fs,...t}}sendRequest(e,t){const r=Ys.buildRequest(e,t);return new Promise(((e,t)=>{const n={requestId:r.requestId,method:r.method,resolve:e,reject:t};Ys.bridgeRequests.push(n),window.nestedAppAuthBridge.postMessage(JSON.stringify(r))}))}static validateBridgeResultOrThrow(e){if(void 0===e){throw{status:Js}}return e}constructor(e,t,r,n){this.sdkName=e,this.sdkVersion=t,this.accountContext=r,this.capabilities=n}static async create(){const e=await Ys.initializeNestedAppAuthBridge();return new Ys(e.sdkName,e.sdkVersion,e.accountContext,e.capabilities)}}Ys.bridgeRequests=[];class Xs extends Ks{constructor(){super(...arguments),this.bridgeProxy=void 0,this.accountContext=null}getModuleName(){return Xs.MODULE_NAME}getId(){return Xs.ID}getBridgeProxy(){return this.bridgeProxy}async initialize(){try{if("undefined"!=typeof window){"function"==typeof window.__initializeNestedAppAuth&&await window.__initializeNestedAppAuth();const e=await Ys.create();this.accountContext=e.getAccountContext(),this.bridgeProxy=e,this.available=void 0!==e}}catch(e){this.logger.infoPii(`Could not initialize Nested App Auth bridge (${e})`)}return this.logger.info(`Nested App Auth Bridge available: ${this.available}`),this.available}}Xs.MODULE_NAME="",Xs.ID="NestedAppOperatingContext";class Zs extends Ks{getModuleName(){return Zs.MODULE_NAME}getId(){return Zs.ID}async initialize(){return this.available="undefined"!=typeof window,this.available}}Zs.MODULE_NAME="",Zs.ID="StandardOperatingContext";class ec{constructor(){this.dbName=Ma,this.version=1,this.tableName=Oa,this.dbOpen=!1}async open(){return new Promise(((e,t)=>{const r=window.indexedDB.open(this.dbName,this.version);r.addEventListener("upgradeneeded",(e=>{e.target.result.createObjectStore(this.tableName)})),r.addEventListener("success",(t=>{const r=t;this.db=r.target.result,this.dbOpen=!0,e()})),r.addEventListener("error",(()=>t(Qi(_i))))}))}closeConnection(){const e=this.db;e&&this.dbOpen&&(e.close(),this.dbOpen=!1)}async validateDbIsOpen(){if(!this.dbOpen)return this.open()}async getItem(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Qi(vi));const n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).get(e);n.addEventListener("success",(e=>{const r=e;this.closeConnection(),t(r.target.result)})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async setItem(e,t){return await this.validateDbIsOpen(),new Promise(((r,n)=>{if(!this.db)return n(Qi(vi));const o=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).put(t,e);o.addEventListener("success",(()=>{this.closeConnection(),r()})),o.addEventListener("error",(e=>{this.closeConnection(),n(e)}))}))}async removeItem(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Qi(vi));const n=this.db.transaction([this.tableName],"readwrite").objectStore(this.tableName).delete(e);n.addEventListener("success",(()=>{this.closeConnection(),t()})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async getKeys(){return await this.validateDbIsOpen(),new Promise(((e,t)=>{if(!this.db)return t(Qi(vi));const r=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).getAllKeys();r.addEventListener("success",(t=>{const r=t;this.closeConnection(),e(r.target.result)})),r.addEventListener("error",(e=>{this.closeConnection(),t(e)}))}))}async containsKey(e){return await this.validateDbIsOpen(),new Promise(((t,r)=>{if(!this.db)return r(Qi(vi));const n=this.db.transaction([this.tableName],"readonly").objectStore(this.tableName).count(e);n.addEventListener("success",(e=>{const r=e;this.closeConnection(),t(1===r.target.result)})),n.addEventListener("error",(e=>{this.closeConnection(),r(e)}))}))}async deleteDatabase(){return this.db&&this.dbOpen&&this.closeConnection(),new Promise(((e,t)=>{const r=window.indexedDB.deleteDatabase(Ma),n=setTimeout((()=>t(!1)),200);r.addEventListener("success",(()=>(clearTimeout(n),e(!0)))),r.addEventListener("blocked",(()=>(clearTimeout(n),e(!0)))),r.addEventListener("error",(()=>(clearTimeout(n),t(!1))))}))}}class tc{constructor(){this.cache=new Map}async initialize(){}getItem(e){return this.cache.get(e)||null}getUserData(e){return this.getItem(e)}setItem(e,t){this.cache.set(e,t)}async setUserData(e,t){this.setItem(e,t)}removeItem(e){this.cache.delete(e)}getKeys(){const e=[];return this.cache.forEach(((t,r)=>{e.push(r)})),e}containsKey(e){return this.cache.has(e)}clear(){this.cache.clear()}}class rc{constructor(e){this.inMemoryCache=new tc,this.indexedDBCache=new ec,this.logger=e}handleDatabaseAccessError(e){if(!(e instanceof $i&&e.errorCode===_i))throw e;this.logger.error("Could not access persistent storage. This may be caused by browser privacy features which block persistent storage in third-party contexts.")}async getItem(e){const t=this.inMemoryCache.getItem(e);if(!t)try{return this.logger.verbose("Queried item not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.getItem(e)}catch(e){this.handleDatabaseAccessError(e)}return t}async setItem(e,t){this.inMemoryCache.setItem(e,t);try{await this.indexedDBCache.setItem(e,t)}catch(e){this.handleDatabaseAccessError(e)}}async removeItem(e){this.inMemoryCache.removeItem(e);try{await this.indexedDBCache.removeItem(e)}catch(e){this.handleDatabaseAccessError(e)}}async getKeys(){const e=this.inMemoryCache.getKeys();if(0===e.length)try{return this.logger.verbose("In-memory cache is empty, now querying persistent storage."),await this.indexedDBCache.getKeys()}catch(e){this.handleDatabaseAccessError(e)}return e}async containsKey(e){const t=this.inMemoryCache.containsKey(e);if(!t)try{return this.logger.verbose("Key not found in in-memory cache, now querying persistent storage."),await this.indexedDBCache.containsKey(e)}catch(e){this.handleDatabaseAccessError(e)}return t}clearInMemory(){this.logger.verbose("Deleting in-memory keystore"),this.inMemoryCache.clear(),this.logger.verbose("In-memory keystore deleted")}async clearPersistent(){try{this.logger.verbose("Deleting persistent keystore");const e=await this.indexedDBCache.deleteDatabase();return e&&this.logger.verbose("Persistent keystore deleted"),e}catch(e){return this.handleDatabaseAccessError(e),!1}}}class nc{constructor(e,t,r){this.logger=e,function(e){if(!window)throw Qi(Ci);if(!window.crypto)throw Qi(Wo);if(!e&&!window.crypto.subtle)throw Qi(Wo,"crypto_subtle_undefined")}(r??!1),this.cache=new rc(this.logger),this.performanceClient=t}createNewGuid(){return ts()}base64Encode(e){return xa(e)}base64Decode(e){return Ba(e)}base64UrlEncode(e){return Ua(e)}encodeKid(e){return this.base64UrlEncode(JSON.stringify({kid:e}))}async getPublicKeyThumbprint(e){const t=this.performanceClient?.startMeasurement(Hn.CryptoOptsGetPublicKeyThumbprint,e.correlationId),r=await async function(e,t){return window.crypto.subtle.generateKey(Ya,e,t)}(nc.EXTRACTABLE,nc.POP_KEY_USAGES),n=await rs(r.publicKey),o=oc({e:n.e,kty:n.kty,n:n.n}),i=await this.hashString(o),a=await rs(r.privateKey),s=await async function(e,t,r){return window.crypto.subtle.importKey("jwk",e,Ya,t,r)}(a,!1,["sign"]);return await this.cache.setItem(i,{privateKey:s,publicKey:r.publicKey,requestMethod:e.resourceRequestMethod,requestUri:e.resourceRequestUri}),t&&t.end({success:!0}),i}async removeTokenBindingKey(e){await this.cache.removeItem(e);return!await this.cache.containsKey(e)}async clearKeystore(){this.cache.clearInMemory();try{return await this.cache.clearPersistent(),!0}catch(e){return e instanceof Error?this.logger.error(`Clearing keystore failed with error: ${e.message}`):this.logger.error("Clearing keystore failed with unknown error"),!1}}async signJwt(e,t,r,n){const o=this.performanceClient?.startMeasurement(Hn.CryptoOptsSignJwt,n),i=await this.cache.getItem(t);if(!i)throw Qi(Si);const a=await rs(i.publicKey),s=oc(a),c=Ua(JSON.stringify({kid:t})),l=Ua(Do.getShrHeaderString({...r?.header,alg:a.alg,kid:c}));e.cnf={jwk:JSON.parse(s)};const h=`${l}.${Ua(JSON.stringify(e))}`,d=(new TextEncoder).encode(h),u=await async function(e,t){return window.crypto.subtle.sign(Ya,e,t)}(i.privateKey,d),g=`${h}.${Ha(new Uint8Array(u))}`;return o&&o.end({success:!0}),g}async hashString(e){return hs(e)}}function oc(e){return JSON.stringify(e,Object.keys(e).sort())}nc.POP_KEY_USAGES=["sign","verify"],nc.EXTRACTABLE=!0;const ic=864e5,ac="Lax",sc="None";class cc{initialize(){return Promise.resolve()}getItem(e){const t=`${encodeURIComponent(e)}`,r=document.cookie.split(";");for(let e=0;e<r.length;e++){const n=r[e],[o,...i]=decodeURIComponent(n).trim().split("="),a=i.join("=");if(o===t)return a}return""}getUserData(){throw gt(st)}setItem(e,t,r,n=!0,o=ac){let i=`${encodeURIComponent(e)}=${encodeURIComponent(t)};path=/;SameSite=${o};`;if(r){const e=function(e){const t=new Date,r=new Date(t.getTime()+e*ic);return r.toUTCString()}(r);i+=`expires=${e};`}(n||o===sc)&&(i+="Secure;"),document.cookie=i}async setUserData(){return Promise.reject(gt(st))}removeItem(e){this.setItem(e,"",-1)}getKeys(){const e=document.cookie.split(";"),t=[];return e.forEach((e=>{const r=decodeURIComponent(e).trim().split("=");t.push(r[0])})),t}containsKey(e){return this.getKeys().includes(e)}}function lc(e){const t=e.getItem(Ta);return t?JSON.parse(t):[]}function hc(e,t){const r=t.getItem(`${Aa}.${e}`);if(r){const e=JSON.parse(r);if(e&&e.hasOwnProperty("idToken")&&e.hasOwnProperty("accessToken")&&e.hasOwnProperty("refreshToken"))return e}return{idToken:[],accessToken:[],refreshToken:[]}}const dc="msal.cache.encryption";class uc{constructor(e,t,r){if(!window.localStorage)throw Cs(ds);this.memoryStorage=new tc,this.initialized=!1,this.clientId=e,this.logger=t,this.performanceClient=r,this.broadcast=new BroadcastChannel("msal.broadcast.cache")}async initialize(e){const t=new cc,r=t.getItem(dc);let n={key:"",id:""};if(r)try{n=JSON.parse(r)}catch(e){}if(n.key&&n.id){const t=Kn(Fa,Hn.Base64Decode,this.logger,this.performanceClient,e)(n.key);this.encryptionCookie={id:n.id,key:await zn(as,Hn.GenerateHKDF,this.logger,this.performanceClient,e)(t)},await zn(this.importExistingCache.bind(this),Hn.ImportExistingCache,this.logger,this.performanceClient,e)(e)}else{this.clear();const r=ts(),n=await zn(is,Hn.GenerateBaseKey,this.logger,this.performanceClient,e)(),o=Kn(Ha,Hn.UrlEncodeArr,this.logger,this.performanceClient,e)(new Uint8Array(n));this.encryptionCookie={id:r,key:await zn(as,Hn.GenerateHKDF,this.logger,this.performanceClient,e)(n)};const i={id:r,key:o};t.setItem(dc,JSON.stringify(i),0,!0,sc)}this.broadcast.addEventListener("message",this.updateCache.bind(this)),this.initialized=!0}getItem(e){return window.localStorage.getItem(e)}getUserData(e){if(!this.initialized)throw Qi(qi);return this.memoryStorage.getItem(e)}setItem(e,t){window.localStorage.setItem(e,t)}async setUserData(e,t,r){if(!this.initialized||!this.encryptionCookie)throw Qi(qi);const{data:n,nonce:o}=await zn(cs,Hn.Encrypt,this.logger,this.performanceClient,r)(this.encryptionCookie.key,t,this.getContext(e)),i={id:this.encryptionCookie.id,nonce:o,data:n};this.memoryStorage.setItem(e,t),this.setItem(e,JSON.stringify(i)),this.broadcast.postMessage({key:e,value:t,context:this.getContext(e)})}removeItem(e){this.memoryStorage.containsKey(e)&&(this.memoryStorage.removeItem(e),this.broadcast.postMessage({key:e,value:null,context:this.getContext(e)})),window.localStorage.removeItem(e)}getKeys(){return Object.keys(window.localStorage)}containsKey(e){return window.localStorage.hasOwnProperty(e)}clear(){this.memoryStorage.clear();lc(this).forEach((e=>this.removeItem(e)));const e=hc(this.clientId,this);e.idToken.forEach((e=>this.removeItem(e))),e.accessToken.forEach((e=>this.removeItem(e))),e.refreshToken.forEach((e=>this.removeItem(e))),this.getKeys().forEach((e=>{(e.startsWith(t.CACHE_PREFIX)||-1!==e.indexOf(this.clientId))&&this.removeItem(e)}))}async importExistingCache(e){if(!this.encryptionCookie)return;let t=lc(this);t=await this.importArray(t,e),this.setItem(Ta,JSON.stringify(t));const r=hc(this.clientId,this);r.idToken=await this.importArray(r.idToken,e),r.accessToken=await this.importArray(r.accessToken,e),r.refreshToken=await this.importArray(r.refreshToken,e),this.setItem(`${Aa}.${this.clientId}`,JSON.stringify(r))}async getItemFromEncryptedCache(e,t){if(!this.encryptionCookie)return null;const r=this.getItem(e);if(!r)return null;let n;try{n=JSON.parse(r)}catch(e){return null}return n.id&&n.nonce&&n.data?n.id!==this.encryptionCookie.id?(this.performanceClient.incrementFields({encryptedCacheExpiredCount:1},t),null):zn(ls,Hn.Decrypt,this.logger,this.performanceClient,t)(this.encryptionCookie.key,n.nonce,this.getContext(e),n.data):(this.performanceClient.incrementFields({unencryptedCacheCount:1},t),null)}async importArray(e,t){const r=[],n=[];return e.forEach((e=>{const o=this.getItemFromEncryptedCache(e,t).then((t=>{t?(this.memoryStorage.setItem(e,t),r.push(e)):this.removeItem(e)}));n.push(o)})),await Promise.all(n),r}getContext(e){let t="";return e.includes(this.clientId)&&(t=this.clientId),t}updateCache(e){this.logger.trace("Updating internal cache from broadcast event");const t=this.performanceClient.startMeasurement(Hn.LocalStorageUpdated);t.add({isBackground:!0});const{key:r,value:n,context:o}=e.data;return r?o&&o!==this.clientId?(this.logger.trace(`Ignoring broadcast event from clientId: ${o}`),void t.end({success:!1,errorCode:"contextMismatch"})):(n?(this.memoryStorage.setItem(r,n),this.logger.verbose("Updated item in internal cache")):(this.memoryStorage.removeItem(r),this.logger.verbose("Removed item from internal cache")),void t.end({success:!0})):(this.logger.error("Broadcast event missing key"),void t.end({success:!1,errorCode:"noKey"}))}}class gc{constructor(){if(!window.sessionStorage)throw Cs(ds)}async initialize(){}getItem(e){return window.sessionStorage.getItem(e)}getUserData(e){return this.getItem(e)}setItem(e,t){window.sessionStorage.setItem(e,t)}async setUserData(e,t){this.setItem(e,t)}removeItem(e){window.sessionStorage.removeItem(e)}getKeys(){return Object.keys(window.sessionStorage)}containsKey(e){return window.sessionStorage.hasOwnProperty(e)}}const pc={INITIALIZE_START:"msal:initializeStart",INITIALIZE_END:"msal:initializeEnd",ACCOUNT_ADDED:"msal:accountAdded",ACCOUNT_REMOVED:"msal:accountRemoved",ACTIVE_ACCOUNT_CHANGED:"msal:activeAccountChanged",LOGIN_START:"msal:loginStart",LOGIN_SUCCESS:"msal:loginSuccess",LOGIN_FAILURE:"msal:loginFailure",ACQUIRE_TOKEN_START:"msal:acquireTokenStart",ACQUIRE_TOKEN_SUCCESS:"msal:acquireTokenSuccess",ACQUIRE_TOKEN_FAILURE:"msal:acquireTokenFailure",ACQUIRE_TOKEN_NETWORK_START:"msal:acquireTokenFromNetworkStart",SSO_SILENT_START:"msal:ssoSilentStart",SSO_SILENT_SUCCESS:"msal:ssoSilentSuccess",SSO_SILENT_FAILURE:"msal:ssoSilentFailure",ACQUIRE_TOKEN_BY_CODE_START:"msal:acquireTokenByCodeStart",ACQUIRE_TOKEN_BY_CODE_SUCCESS:"msal:acquireTokenByCodeSuccess",ACQUIRE_TOKEN_BY_CODE_FAILURE:"msal:acquireTokenByCodeFailure",HANDLE_REDIRECT_START:"msal:handleRedirectStart",HANDLE_REDIRECT_END:"msal:handleRedirectEnd",POPUP_OPENED:"msal:popupOpened",LOGOUT_START:"msal:logoutStart",LOGOUT_SUCCESS:"msal:logoutSuccess",LOGOUT_FAILURE:"msal:logoutFailure",LOGOUT_END:"msal:logoutEnd",RESTORE_FROM_BFCACHE:"msal:restoreFromBFCache",BROKER_CONNECTION_ESTABLISHED:"msal:brokerConnectionEstablished"};class mc extends Fr{constructor(e,t,r,n,o,i,a){super(e,r,n,a),this.cacheConfig=t,this.logger=n,this.internalStorage=new tc,this.browserStorage=fc(e,t.cacheLocation,n,o),this.temporaryCacheStorage=fc(e,t.temporaryCacheLocation,n,o),this.cookieStorage=new cc,this.performanceClient=o,this.eventHandler=i}async initialize(e){await this.browserStorage.initialize(e),this.trackVersionChanges(e)}trackVersionChanges(e){const t=this.browserStorage.getItem(ka);t&&(this.logger.info(`MSAL.js was last initialized by version: ${t}`),this.performanceClient.addFields({previousLibraryVersion:t},e)),t!==Fs&&this.browserStorage.setItem(ka,Fs)}validateAndParseJson(e){try{const t=JSON.parse(e);return t&&"object"==typeof t?t:null}catch(e){return null}}getAccount(e){this.logger.trace("BrowserCacheManager.getAccount called");const t=this.browserStorage.getUserData(e);if(!t)return this.removeAccountKeyFromMap(e),null;const r=this.validateAndParseJson(t);return r&&Er.isAccountEntity(r)?Fr.toObject(new Er,r):(this.removeAccountKeyFromMap(e),null)}async setAccount(e,t){this.logger.trace("BrowserCacheManager.setAccount called");const r=e.generateAccountKey();await zn(this.browserStorage.setUserData.bind(this.browserStorage),Hn.SetUserData,this.logger,this.performanceClient)(r,JSON.stringify(e),t);const n=this.addAccountKeyToMap(r);this.cacheConfig.cacheLocation===da.LocalStorage&&n&&this.eventHandler.emitEvent(pc.ACCOUNT_ADDED,void 0,e.getAccountInfo())}getAccountKeys(){return lc(this.browserStorage)}addAccountKeyToMap(e){this.logger.trace("BrowserCacheManager.addAccountKeyToMap called"),this.logger.tracePii(`BrowserCacheManager.addAccountKeyToMap called with key: ${e}`);const t=this.getAccountKeys();return-1===t.indexOf(e)?(t.push(e),this.browserStorage.setItem(Ta,JSON.stringify(t)),this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key added"),!0):(this.logger.verbose("BrowserCacheManager.addAccountKeyToMap account key already exists in map"),!1)}removeAccountKeyFromMap(e){this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap called"),this.logger.tracePii(`BrowserCacheManager.removeAccountKeyFromMap called with key: ${e}`);const t=this.getAccountKeys(),r=t.indexOf(e);r>-1?(t.splice(r,1),this.browserStorage.setItem(Ta,JSON.stringify(t)),this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap account key removed")):this.logger.trace("BrowserCacheManager.removeAccountKeyFromMap key not found in existing map")}async removeAccount(e){super.removeAccount(e),this.removeAccountKeyFromMap(e)}async removeAccountContext(e){await super.removeAccountContext(e),this.cacheConfig.cacheLocation===da.LocalStorage&&this.eventHandler.emitEvent(pc.ACCOUNT_REMOVED,void 0,e.getAccountInfo())}removeIdToken(e){super.removeIdToken(e),this.removeTokenKey(e,H.ID_TOKEN)}async removeAccessToken(e){super.removeAccessToken(e),this.removeTokenKey(e,H.ACCESS_TOKEN)}removeRefreshToken(e){super.removeRefreshToken(e),this.removeTokenKey(e,H.REFRESH_TOKEN)}getTokenKeys(){return hc(this.clientId,this.browserStorage)}addTokenKey(e,t){this.logger.trace("BrowserCacheManager addTokenKey called");const r=this.getTokenKeys();switch(t){case H.ID_TOKEN:-1===r.idToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - idToken added to map"),r.idToken.push(e));break;case H.ACCESS_TOKEN:-1===r.accessToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - accessToken added to map"),r.accessToken.push(e));break;case H.REFRESH_TOKEN:-1===r.refreshToken.indexOf(e)&&(this.logger.info("BrowserCacheManager: addTokenKey - refreshToken added to map"),r.refreshToken.push(e));break;default:throw this.logger.error(`BrowserCacheManager:addTokenKey - CredentialType provided invalid. CredentialType: ${t}`),gt(We)}this.browserStorage.setItem(`${Aa}.${this.clientId}`,JSON.stringify(r))}removeTokenKey(e,t){this.logger.trace("BrowserCacheManager removeTokenKey called");const r=this.getTokenKeys();switch(t){case H.ID_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove idToken with key: ${e} from map`);const n=r.idToken.indexOf(e);n>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - idToken removed from map"),r.idToken.splice(n,1)):this.logger.info("BrowserCacheManager: removeTokenKey - idToken does not exist in map. Either it was previously removed or it was never added.");break;case H.ACCESS_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove accessToken with key: ${e} from map`);const o=r.accessToken.indexOf(e);o>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - accessToken removed from map"),r.accessToken.splice(o,1)):this.logger.info("BrowserCacheManager: removeTokenKey - accessToken does not exist in map. Either it was previously removed or it was never added.");break;case H.REFRESH_TOKEN:this.logger.infoPii(`BrowserCacheManager: removeTokenKey - attempting to remove refreshToken with key: ${e} from map`);const i=r.refreshToken.indexOf(e);i>-1?(this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken removed from map"),r.refreshToken.splice(i,1)):this.logger.info("BrowserCacheManager: removeTokenKey - refreshToken does not exist in map. Either it was previously removed or it was never added.");break;default:throw this.logger.error(`BrowserCacheManager:removeTokenKey - CredentialType provided invalid. CredentialType: ${t}`),gt(We)}this.browserStorage.setItem(`${Aa}.${this.clientId}`,JSON.stringify(r))}getIdTokenCredential(e){const t=this.browserStorage.getUserData(e);if(!t)return this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.ID_TOKEN),null;const r=this.validateAndParseJson(t);return r&&((n=r)&&Mt(n)&&n.hasOwnProperty("realm")&&n.credentialType===H.ID_TOKEN)?(this.logger.trace("BrowserCacheManager.getIdTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getIdTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.ID_TOKEN),null);var n}async setIdTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setIdTokenCredential called");const r=Et(e);await zn(this.browserStorage.setUserData.bind(this.browserStorage),Hn.SetUserData,this.logger,this.performanceClient)(r,JSON.stringify(e),t),this.addTokenKey(r,H.ID_TOKEN)}getAccessTokenCredential(e){const t=this.browserStorage.getUserData(e);if(!t)return this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.ACCESS_TOKEN),null;const r=this.validateAndParseJson(t);return r&&(n=r)&&Mt(n)&&n.hasOwnProperty("realm")&&n.hasOwnProperty("target")&&(n.credentialType===H.ACCESS_TOKEN||n.credentialType===H.ACCESS_TOKEN_WITH_AUTH_SCHEME)?(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getAccessTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.ACCESS_TOKEN),null);var n}async setAccessTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setAccessTokenCredential called");const r=Et(e);await zn(this.browserStorage.setUserData.bind(this.browserStorage),Hn.SetUserData,this.logger,this.performanceClient)(r,JSON.stringify(e),t),this.addTokenKey(r,H.ACCESS_TOKEN)}getRefreshTokenCredential(e){const t=this.browserStorage.getUserData(e);if(!t)return this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.REFRESH_TOKEN),null;const r=this.validateAndParseJson(t);return r&&(n=r)&&Mt(n)&&n.credentialType===H.REFRESH_TOKEN?(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: cache hit"),r):(this.logger.trace("BrowserCacheManager.getRefreshTokenCredential: called, no cache hit"),this.removeTokenKey(e,H.REFRESH_TOKEN),null);var n}async setRefreshTokenCredential(e,t){this.logger.trace("BrowserCacheManager.setRefreshTokenCredential called");const r=Et(e);await zn(this.browserStorage.setUserData.bind(this.browserStorage),Hn.SetUserData,this.logger,this.performanceClient)(r,JSON.stringify(e),t),this.addTokenKey(r,H.REFRESH_TOKEN)}getAppMetadata(e){const t=this.browserStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&(n=e,(o=r)&&0===n.indexOf(x)&&o.hasOwnProperty("clientId")&&o.hasOwnProperty("environment"))?(this.logger.trace("BrowserCacheManager.getAppMetadata: cache hit"),r):(this.logger.trace("BrowserCacheManager.getAppMetadata: called, no cache hit"),null);var n,o}setAppMetadata(e){this.logger.trace("BrowserCacheManager.setAppMetadata called");const t=function({environment:e,clientId:t}){return[x,e,t].join(L).toLowerCase()}(e);this.browserStorage.setItem(t,JSON.stringify(e))}getServerTelemetry(e){const t=this.browserStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){const r=0===e.indexOf(Q.CACHE_KEY);let n=!0;return t&&(n=t.hasOwnProperty("failedRequests")&&t.hasOwnProperty("errors")&&t.hasOwnProperty("cacheHits")),r&&n}(e,r)?(this.logger.trace("BrowserCacheManager.getServerTelemetry: cache hit"),r):(this.logger.trace("BrowserCacheManager.getServerTelemetry: called, no cache hit"),null)}setServerTelemetry(e,t){this.logger.trace("BrowserCacheManager.setServerTelemetry called"),this.browserStorage.setItem(e,JSON.stringify(t))}getAuthorityMetadata(e){const t=this.internalStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getAuthorityMetadata: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){return!!t&&0===e.indexOf(B)&&t.hasOwnProperty("aliases")&&t.hasOwnProperty("preferred_cache")&&t.hasOwnProperty("preferred_network")&&t.hasOwnProperty("canonical_authority")&&t.hasOwnProperty("authorization_endpoint")&&t.hasOwnProperty("token_endpoint")&&t.hasOwnProperty("issuer")&&t.hasOwnProperty("aliasesFromNetwork")&&t.hasOwnProperty("endpointsFromNetwork")&&t.hasOwnProperty("expiresAt")&&t.hasOwnProperty("jwks_uri")}(e,r)?(this.logger.trace("BrowserCacheManager.getAuthorityMetadata: cache hit"),r):null}getAuthorityMetadataKeys(){return this.internalStorage.getKeys().filter((e=>this.isAuthorityMetadata(e)))}setWrapperMetadata(e,t){this.internalStorage.setItem(Sa,e),this.internalStorage.setItem(ba,t)}getWrapperMetadata(){return[this.internalStorage.getItem(Sa)||t.EMPTY_STRING,this.internalStorage.getItem(ba)||t.EMPTY_STRING]}setAuthorityMetadata(e,t){this.logger.trace("BrowserCacheManager.setAuthorityMetadata called"),this.internalStorage.setItem(e,JSON.stringify(t))}getActiveAccount(){const e=this.generateCacheKey(C),t=this.browserStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getActiveAccount: No active account filters found"),null;const r=this.validateAndParseJson(t);return r?(this.logger.trace("BrowserCacheManager.getActiveAccount: Active account filters schema found"),this.getAccountInfoFilteredBy({homeAccountId:r.homeAccountId,localAccountId:r.localAccountId,tenantId:r.tenantId})):(this.logger.trace("BrowserCacheManager.getActiveAccount: No active account found"),null)}setActiveAccount(e){const t=this.generateCacheKey(C);if(e){this.logger.verbose("setActiveAccount: Active account set");const r={homeAccountId:e.homeAccountId,localAccountId:e.localAccountId,tenantId:e.tenantId};this.browserStorage.setItem(t,JSON.stringify(r))}else this.logger.verbose("setActiveAccount: No account passed, active account not set"),this.browserStorage.removeItem(t);this.eventHandler.emitEvent(pc.ACTIVE_ACCOUNT_CHANGED)}getThrottlingCache(e){const t=this.browserStorage.getItem(e);if(!t)return this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null;const r=this.validateAndParseJson(t);return r&&function(e,t){let r=!1;e&&(r=0===e.indexOf(J));let n=!0;return t&&(n=t.hasOwnProperty("throttleTime")),r&&n}(e,r)?(this.logger.trace("BrowserCacheManager.getThrottlingCache: cache hit"),r):(this.logger.trace("BrowserCacheManager.getThrottlingCache: called, no cache hit"),null)}setThrottlingCache(e,t){this.logger.trace("BrowserCacheManager.setThrottlingCache called"),this.browserStorage.setItem(e,JSON.stringify(t))}getTemporaryCache(e,t){const r=t?this.generateCacheKey(e):e;if(this.cacheConfig.storeAuthStateInCookie){const e=this.cookieStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: storeAuthStateInCookies set to true, retrieving from cookies"),e}const n=this.temporaryCacheStorage.getItem(r);if(!n){if(this.cacheConfig.cacheLocation===da.LocalStorage){const e=this.browserStorage.getItem(r);if(e)return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item found in local storage"),e}return this.logger.trace("BrowserCacheManager.getTemporaryCache: No cache item found in local storage"),null}return this.logger.trace("BrowserCacheManager.getTemporaryCache: Temporary cache item returned"),n}setTemporaryCache(e,t,r){const n=r?this.generateCacheKey(e):e;this.temporaryCacheStorage.setItem(n,t),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.setTemporaryCache: storeAuthStateInCookie set to true, setting item cookie"),this.cookieStorage.setItem(n,t,void 0,this.cacheConfig.secureCookies))}removeItem(e){this.browserStorage.removeItem(e)}removeTemporaryItem(e){this.temporaryCacheStorage.removeItem(e),this.cacheConfig.storeAuthStateInCookie&&(this.logger.trace("BrowserCacheManager.removeItem: storeAuthStateInCookie is true, clearing item cookie"),this.cookieStorage.removeItem(e))}getKeys(){return this.browserStorage.getKeys()}async clear(){await this.removeAllAccounts(),this.removeAppMetadata(),this.temporaryCacheStorage.getKeys().forEach((e=>{-1===e.indexOf(t.CACHE_PREFIX)&&-1===e.indexOf(this.clientId)||this.removeTemporaryItem(e)})),this.browserStorage.getKeys().forEach((e=>{-1===e.indexOf(t.CACHE_PREFIX)&&-1===e.indexOf(this.clientId)||this.browserStorage.removeItem(e)})),this.internalStorage.clear()}async clearTokensAndKeysWithClaims(e,t){e.addQueueMeasurement(Hn.ClearTokensAndKeysWithClaims,t);const r=this.getTokenKeys(),n=[];r.accessToken.forEach((e=>{const t=this.getAccessTokenCredential(e);t?.requestedClaimsHash&&e.includes(t.requestedClaimsHash.toLowerCase())&&n.push(this.removeAccessToken(e))})),await Promise.all(n),n.length>0&&this.logger.warning(`${n.length} access tokens with claims in the cache keys have been removed from the cache.`)}generateCacheKey(e){return this.validateAndParseJson(e)?JSON.stringify(e):pr.startsWith(e,t.CACHE_PREFIX)?e:`${t.CACHE_PREFIX}.${this.clientId}.${e}`}resetRequestCache(){this.logger.trace("BrowserCacheManager.resetRequestCache called"),this.removeTemporaryItem(this.generateCacheKey(Ca)),this.removeTemporaryItem(this.generateCacheKey(va)),this.removeTemporaryItem(this.generateCacheKey(fa)),this.removeTemporaryItem(this.generateCacheKey(ya)),this.removeTemporaryItem(this.generateCacheKey(Ia)),this.setInteractionInProgress(!1)}cacheAuthorizeRequest(e,t){this.logger.trace("BrowserCacheManager.cacheAuthorizeRequest called");const r=xa(JSON.stringify(e));if(this.setTemporaryCache(Ca,r,!0),t){const e=xa(t);this.setTemporaryCache(va,e,!0)}}getCachedRequest(){this.logger.trace("BrowserCacheManager.getCachedRequest called");const e=this.getTemporaryCache(Ca,!0);if(!e)throw Qi(pi);const t=this.getTemporaryCache(va,!0);let r,n="";try{r=JSON.parse(Ba(e)),t&&(n=Ba(t))}catch(t){throw this.logger.errorPii(`Attempted to parse: ${e}`),this.logger.error(`Parsing cached token request threw with error: ${t}`),Qi(mi)}return[r,n]}getCachedNativeRequest(){this.logger.trace("BrowserCacheManager.getCachedNativeRequest called");const e=this.getTemporaryCache(Ia,!0);if(!e)return this.logger.trace("BrowserCacheManager.getCachedNativeRequest: No cached native request found"),null;const t=this.validateAndParseJson(e);return t||(this.logger.error("BrowserCacheManager.getCachedNativeRequest: Unable to parse native request"),null)}isInteractionInProgress(e){const t=this.getInteractionInProgress()?.clientId;return e?t===this.clientId:!!t}getInteractionInProgress(){const e=`${t.CACHE_PREFIX}.${wa}`,r=this.getTemporaryCache(e,!1);try{return r?JSON.parse(r):null}catch(t){return this.logger.error("Cannot parse interaction status. Removing temporary cache items and clearing url hash. Retrying interaction should fix the error"),this.removeTemporaryItem(e),this.resetRequestCache(),vs(window),null}}setInteractionInProgress(e,r=pa){const n=`${t.CACHE_PREFIX}.${wa}`;if(e){if(this.getInteractionInProgress())throw Qi(ti);this.setTemporaryCache(n,JSON.stringify({clientId:this.clientId,type:r}),!1)}else e||this.getInteractionInProgress()?.clientId!==this.clientId||this.removeTemporaryItem(n)}async hydrateCache(e,t){const r=Rt(e.account?.homeAccountId,e.account?.environment,e.idToken,this.clientId,e.tenantId);let n;t.claims&&(n=await this.cryptoImpl.hashString(t.claims));const o={idToken:r,accessToken:_t(e.account?.homeAccountId,e.account.environment,e.accessToken,this.clientId,e.tenantId,e.scopes.join(" "),e.expiresOn?At(e.expiresOn):0,e.extExpiresOn?At(e.extExpiresOn):0,Ba,void 0,e.tokenType,void 0,t.sshKid,t.claims,n)};return this.saveCacheRecord(o,e.correlationId)}async saveCacheRecord(e,t,r){try{await super.saveCacheRecord(e,t,r)}catch(e){if(e instanceof Br&&this.performanceClient&&t)try{const e=this.getTokenKeys();this.performanceClient.addFields({cacheRtCount:e.refreshToken.length,cacheIdCount:e.idToken.length,cacheAtCount:e.accessToken.length},t)}catch(e){}throw e}}}function fc(e,t,r,n){try{switch(t){case da.LocalStorage:return new uc(e,r,n);case da.SessionStorage:return new gc}}catch(e){r.error(e)}return new tc}const yc=(e,t,r,n)=>new mc(e,{cacheLocation:da.MemoryStorage,temporaryCacheLocation:da.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1},pt,t,r,n);function Cc(e,t,r,n){return e.verbose("getAllAccounts called"),r?t.getAllAccounts(n):[]}function vc(e,t,r){if(t.trace("getAccount called"),0===Object.keys(e).length)return t.warning("getAccount: No accountFilter provided"),null;const n=r.getAccountInfoFilteredBy(e);return n?(t.verbose("getAccount: Account matching provided filter found, returning"),n):(t.verbose("getAccount: No matching account found, returning null"),null)}function wc(e,t,r){if(t.trace("getAccountByUsername called"),!e)return t.warning("getAccountByUsername: No username provided"),null;const n=r.getAccountInfoFilteredBy({username:e});return n?(t.verbose("getAccountByUsername: Account matching username found, returning"),t.verbosePii(`getAccountByUsername: Returning signed-in accounts matching username: ${e}`),n):(t.verbose("getAccountByUsername: No matching account found, returning null"),null)}function Ic(e,t,r){if(t.trace("getAccountByHomeId called"),!e)return t.warning("getAccountByHomeId: No homeAccountId provided"),null;const n=r.getAccountInfoFilteredBy({homeAccountId:e});return n?(t.verbose("getAccountByHomeId: Account matching homeAccountId found, returning"),t.verbosePii(`getAccountByHomeId: Returning signed-in accounts matching homeAccountId: ${e}`),n):(t.verbose("getAccountByHomeId: No matching account found, returning null"),null)}function Tc(e,t,r){if(t.trace("getAccountByLocalId called"),!e)return t.warning("getAccountByLocalId: No localAccountId provided"),null;const n=r.getAccountInfoFilteredBy({localAccountId:e});return n?(t.verbose("getAccountByLocalId: Account matching localAccountId found, returning"),t.verbosePii(`getAccountByLocalId: Returning signed-in accounts matching localAccountId: ${e}`),n):(t.verbose("getAccountByLocalId: No matching account found, returning null"),null)}function Ac(e,t){t.setActiveAccount(e)}function kc(e){return e.getActiveAccount()}class Sc{constructor(e){this.eventCallbacks=new Map,this.logger=e||new ft({}),"undefined"!=typeof BroadcastChannel&&(this.broadcastChannel=new BroadcastChannel("msal.broadcast.event")),this.invokeCrossTabCallbacks=this.invokeCrossTabCallbacks.bind(this)}addEventCallback(e,t,r){if("undefined"!=typeof window){const n=r||Ns();return this.eventCallbacks.has(n)?(this.logger.error(`Event callback with id: ${n} is already registered. Please provide a unique id or remove the existing callback and try again.`),null):(this.eventCallbacks.set(n,[e,t||[]]),this.logger.verbose(`Event callback registered with id: ${n}`),n)}return null}removeEventCallback(e){this.eventCallbacks.delete(e),this.logger.verbose(`Event callback ${e} removed.`)}emitEvent(e,t,r,n){const o={eventType:e,interactionType:t||null,payload:r||null,error:n||null,timestamp:Date.now()};switch(e){case pc.ACCOUNT_ADDED:case pc.ACCOUNT_REMOVED:case pc.ACTIVE_ACCOUNT_CHANGED:this.broadcastChannel?.postMessage(o);break;default:this.invokeCallbacks(o)}}invokeCallbacks(e){this.eventCallbacks.forEach((([t,r],n)=>{(0===r.length||r.includes(e.eventType))&&(this.logger.verbose(`Emitting event to callback ${n}: ${e.eventType}`),t.apply(null,[e]))}))}invokeCrossTabCallbacks(e){const t=e.data;this.invokeCallbacks(t)}subscribeCrossTab(){this.broadcastChannel?.addEventListener("message",this.invokeCrossTabCallbacks)}unsubscribeCrossTab(){this.broadcastChannel?.removeEventListener("message",this.invokeCrossTabCallbacks)}}class bc{constructor(e,t,r,n,o,i,a,s,c){this.config=e,this.browserStorage=t,this.browserCrypto=r,this.networkClient=this.config.system.networkClient,this.eventHandler=o,this.navigationClient=i,this.platformAuthProvider=s,this.correlationId=c||ts(),this.logger=n.clone(Xi,Fs,this.correlationId),this.performanceClient=a}async clearCacheOnLogout(e){if(e){Er.accountInfoIsEqual(e,this.browserStorage.getActiveAccount(),!1)&&(this.logger.verbose("Setting active account to null"),this.browserStorage.setActiveAccount(null));try{await this.browserStorage.removeAccount(Er.generateAccountCacheKey(e)),this.logger.verbose("Cleared cache items belonging to the account provided in the logout request.")}catch(e){this.logger.error("Account provided in logout request was not found. Local cache unchanged.")}}else try{this.logger.verbose("No account provided in logout request, clearing all cache items.",this.correlationId),await this.browserStorage.clear(),await this.browserCrypto.clearKeystore()}catch(e){this.logger.error("Attempted to clear all MSAL cache items and failed. Local cache unchanged.")}}getRedirectUri(e){this.logger.verbose("getRedirectUri called");const t=e||this.config.auth.redirectUri;return Mr.getAbsoluteUrl(t,As())}initializeServerTelemetryManager(e,t){this.logger.verbose("initializeServerTelemetryManager called");const r={clientId:this.config.auth.clientId,correlationId:this.correlationId,apiId:e,forceRefresh:t||!1,wrapperSKU:this.browserStorage.getWrapperMetadata()[0],wrapperVer:this.browserStorage.getWrapperMetadata()[1]};return new No(r,this.browserStorage)}async getDiscoveredAuthority(e){const{account:t}=e,r=e.requestExtraQueryParameters&&e.requestExtraQueryParameters.hasOwnProperty("instance_aware")?e.requestExtraQueryParameters.instance_aware:void 0;this.performanceClient.addQueueMeasurement(Hn.StandardInteractionClientGetDiscoveredAuthority,this.correlationId);const n={protocolMode:this.config.auth.protocolMode,OIDCOptions:this.config.auth.OIDCOptions,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},o=e.requestAuthority||this.config.auth.authority,i=r?.length?"true"===r:this.config.auth.instanceAware,a=t&&i?this.config.auth.authority.replace(Mr.getDomainFromUrl(o),t.environment):o,s=$n.generateAuthority(a,e.requestAzureCloudOptions||this.config.auth.azureCloudOptions),c=await zn(Wn,Hn.AuthorityFactoryCreateDiscoveredInstance,this.logger,this.performanceClient,this.correlationId)(s,this.config.system.networkClient,this.browserStorage,n,this.logger,this.correlationId,this.performanceClient);if(t&&!c.isAlias(t.environment))throw gr(cr);return c}}async function Ec(e,t,r,n){r.addQueueMeasurement(Hn.InitializeBaseRequest,e.correlationId);const o=e.authority||t.auth.authority,i=[...e&&e.scopes||[]],a={...e,correlationId:e.correlationId,authority:o,scopes:i};if(a.authenticationScheme){if(a.authenticationScheme===j.SSH){if(!e.sshJwk)throw gr(rr);if(!e.sshKid)throw gr(nr)}n.verbose(`Authentication Scheme set to "${a.authenticationScheme}" as configured in Auth request`)}else a.authenticationScheme=j.BEARER,n.verbose('Authentication Scheme wasn\'t explicitly set in request, defaulting to "Bearer" request');return t.cache.claimsBasedCachingEnabled&&e.claims&&!pr.isEmptyObj(e.claims)&&(a.requestedClaimsHash=await hs(e.claims)),a}async function Rc(e,t,r,n,o){n.addQueueMeasurement(Hn.InitializeSilentRequest,e.correlationId);const i=await zn(Ec,Hn.InitializeBaseRequest,o,n,e.correlationId)(e,r,n,o);return{...e,...i,account:t,forceRefresh:e.forceRefresh||!1}}class _c extends bc{initializeLogoutRequest(e){this.logger.verbose("initializeLogoutRequest called",e?.correlationId);const t={correlationId:this.correlationId||ts(),...e};if(e)if(e.logoutHint)this.logger.verbose("logoutHint has already been set in logoutRequest");else if(e.account){const r=this.getLogoutHintFromIdTokenClaims(e.account);r&&(this.logger.verbose("Setting logoutHint to login_hint ID Token Claim value for the account provided"),t.logoutHint=r)}else this.logger.verbose("logoutHint was not set and account was not passed into logout request, logoutHint will not be set");else this.logger.verbose("logoutHint will not be set since no logout request was configured");return e&&null===e.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri passed as null, not setting post logout redirect uri",t.correlationId):e&&e.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to uri set on logout request",t.correlationId),t.postLogoutRedirectUri=Mr.getAbsoluteUrl(e.postLogoutRedirectUri,As())):null===this.config.auth.postLogoutRedirectUri?this.logger.verbose("postLogoutRedirectUri configured as null and no uri set on request, not passing post logout redirect",t.correlationId):this.config.auth.postLogoutRedirectUri?(this.logger.verbose("Setting postLogoutRedirectUri to configured uri",t.correlationId),t.postLogoutRedirectUri=Mr.getAbsoluteUrl(this.config.auth.postLogoutRedirectUri,As())):(this.logger.verbose("Setting postLogoutRedirectUri to current page",t.correlationId),t.postLogoutRedirectUri=Mr.getAbsoluteUrl(As(),As())),t}getLogoutHintFromIdTokenClaims(e){const t=e.idTokenClaims;if(t){if(t.login_hint)return t.login_hint;this.logger.verbose("The ID Token Claims tied to the provided account do not contain a login_hint claim, logoutHint will not be added to logout request")}else this.logger.verbose("The provided account does not contain ID Token Claims, logoutHint will not be added to logout request");return null}async createAuthCodeClient(e){this.performanceClient.addQueueMeasurement(Hn.StandardInteractionClientCreateAuthCodeClient,this.correlationId);const t=await zn(this.getClientConfiguration.bind(this),Hn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)(e);return new So(t,this.performanceClient)}async getClientConfiguration(e){const{serverTelemetryManager:r,requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:i,account:a}=e;this.performanceClient.addQueueMeasurement(Hn.StandardInteractionClientGetClientConfiguration,this.correlationId);const s=await zn(this.getDiscoveredAuthority.bind(this),Hn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,this.correlationId)({requestAuthority:n,requestAzureCloudOptions:o,requestExtraQueryParameters:i,account:a}),c=this.config.system.loggerOptions;return{authOptions:{clientId:this.config.auth.clientId,authority:s,clientCapabilities:this.config.auth.clientCapabilities,redirectUri:this.config.auth.redirectUri},systemOptions:{tokenRenewalOffsetSeconds:this.config.system.tokenRenewalOffsetSeconds,preventCorsPreflight:!0},loggerOptions:{loggerCallback:c.loggerCallback,piiLoggingEnabled:c.piiLoggingEnabled,logLevel:c.logLevel,correlationId:this.correlationId},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.browserCrypto,networkInterface:this.networkClient,storageInterface:this.browserStorage,serverTelemetryManager:r,libraryInfo:{sku:Xi,version:Fs,cpu:t.EMPTY_STRING,os:t.EMPTY_STRING},telemetry:this.config.telemetry}}async initializeAuthorizationRequest(e,r){this.performanceClient.addQueueMeasurement(Hn.StandardInteractionClientInitializeAuthorizationRequest,this.correlationId);const n=this.getRedirectUri(e.redirectUri),o={interactionType:r},i=yo.setRequestState(this.browserCrypto,e&&e.state||t.EMPTY_STRING,o),a={...await zn(Ec,Hn.InitializeBaseRequest,this.logger,this.performanceClient,this.correlationId)({...e,correlationId:this.correlationId},this.config,this.performanceClient,this.logger),redirectUri:n,state:i,nonce:e.nonce||ts(),responseMode:this.config.auth.OIDCOptions.serverResponseType};if(e.loginHint||e.sid)return a;const s=e.account||this.browserStorage.getActiveAccount();return s&&(this.logger.verbose("Setting validated request account",this.correlationId),this.logger.verbosePii(`Setting validated request account: ${s.homeAccountId}`,this.correlationId),a.account=s),a}}function Pc(e,t,r){const n=_r(e);if(!n)throw Rr(e)?(r.error(`A ${t} is present in the iframe but it does not contain known properties. It's likely that the ${t} has been replaced by code running on the redirectUri page.`),r.errorPii(`The ${t} detected is: ${e}`),Qi(Xo)):(r.error(`The request has returned to the redirectUri but a ${t} is not present. It's likely that the ${t} has been removed or the page has been redirected by code running on the redirectUri page.`),Qi(Jo));return n}function Mc(e,t,r){if(!e.state)throw Qi(Yo);const n=function(e,t){if(!t)return null;try{return yo.parseRequestState(e,t).libraryState.meta}catch(e){throw gt(Ee)}}(t,e.state);if(!n)throw Qi(Zo);if(n.interactionType!==r)throw Qi(ei)}class Oc{constructor(e,t,r,n,o){this.authModule=e,this.browserStorage=t,this.authCodeRequest=r,this.logger=n,this.performanceClient=o}async handleCodeResponse(e,t){let r;this.performanceClient.addQueueMeasurement(Hn.HandleCodeResponse,t.correlationId);try{r=function(e,t){if(Mo(e,t),!e.code)throw gt(et);return e}(e,t.state)}catch(e){throw e instanceof Vn&&e.subError===oi?Qi(oi):e}return zn(this.handleCodeResponseFromServer.bind(this),Hn.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)(r,t)}async handleCodeResponseFromServer(e,t,r=!0){if(this.performanceClient.addQueueMeasurement(Hn.HandleCodeResponseFromServer,t.correlationId),this.logger.trace("InteractionHandler.handleCodeResponseFromServer called"),this.authCodeRequest.code=e.code,e.cloud_instance_host_name&&await zn(this.authModule.updateAuthority.bind(this.authModule),Hn.UpdateTokenEndpointAuthority,this.logger,this.performanceClient,t.correlationId)(e.cloud_instance_host_name,t.correlationId),r&&(e.nonce=t.nonce||void 0),e.state=t.state,e.client_info)this.authCodeRequest.clientInfo=e.client_info;else{const e=this.createCcsCredentials(t);e&&(this.authCodeRequest.ccsCredential=e)}return await zn(this.authModule.acquireToken.bind(this.authModule),Hn.AuthClientAcquireToken,this.logger,this.performanceClient,t.correlationId)(this.authCodeRequest,e)}createCcsCredentials(e){return e.account?{credential:e.account.homeAccountId,type:Xr}:e.loginHint?{credential:e.loginHint,type:Zr}:null}}const Nc="user_switch",qc={[Nc]:"User attempted to switch accounts in the native broker, which is not allowed. All new accounts must sign-in through the standard web flow first, please try again."};class Lc extends ye{constructor(e,t,r){super(e,t),Object.setPrototypeOf(this,Lc.prototype),this.name="NativeAuthError",this.ext=r}}function Uc(e){return!(!e.ext||!e.ext.status||"PERSISTENT_ERROR"!==e.ext.status&&"DISABLED"!==e.ext.status)||(!(!e.ext||!e.ext.error||-**********!==e.ext.error)||"ContentError"===e.errorCode)}function Hc(e,t,r){if(r&&r.status)switch(r.status){case"ACCOUNT_UNAVAILABLE":return fo(ro);case"USER_INTERACTION_REQUIRED":return new po(e,t);case"USER_CANCEL":return Qi(oi);case"NO_NETWORK":return Qi(wi)}return new Lc(e,qc[e]||t,r)}class xc extends _c{async acquireToken(e){this.performanceClient.addQueueMeasurement(Hn.SilentCacheClientAcquireToken,e.correlationId);const t=this.initializeServerTelemetryManager(Ea.acquireTokenSilent_silentFlow),r=await zn(this.getClientConfiguration.bind(this),Hn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:t,requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,account:e.account}),n=new Eo(r,this.performanceClient);this.logger.verbose("Silent auth client created");try{const t=(await zn(n.acquireCachedToken.bind(n),Hn.SilentFlowClientAcquireCachedToken,this.logger,this.performanceClient,e.correlationId)(e))[0];return this.performanceClient.addFields({fromCache:!0},e.correlationId),t}catch(e){throw e instanceof $i&&e.errorCode===Si&&this.logger.verbose("Signing keypair for bound access token not found. Refreshing bound access token and generating a new crypto keypair."),e}}logout(e){this.logger.verbose("logoutRedirect called");const t=this.initializeLogoutRequest(e);return this.clearCacheOnLogout(t?.account)}}class Dc extends bc{constructor(e,t,r,n,o,i,a,s,c,l,h,d){super(e,t,r,n,o,i,s,c,d),this.apiId=a,this.accountId=l,this.platformAuthProvider=c,this.nativeStorageManager=h,this.silentCacheClient=new xc(e,this.nativeStorageManager,r,n,o,i,s,c,d);const u=this.platformAuthProvider.getExtensionName();this.skus=No.makeExtraSkuString({libraryName:Xi,libraryVersion:Fs,extensionName:u,extensionVersion:this.platformAuthProvider.getExtensionVersion()})}addRequestSKUs(e){e.extraParameters={...e.extraParameters,[an]:this.skus}}async acquireToken(e,t){this.performanceClient.addQueueMeasurement(Hn.NativeInteractionClientAcquireToken,e.correlationId),this.logger.trace("NativeInteractionClient - acquireToken called.");const r=this.performanceClient.startMeasurement(Hn.NativeInteractionClientAcquireToken,e.correlationId),n=Tt(),o=this.initializeServerTelemetryManager(this.apiId);try{const i=await this.initializeNativeRequest(e);try{const e=await this.acquireTokensFromCache(this.accountId,i);return r.end({success:!0,isNativeBroker:!1,fromCache:!0}),e}catch(e){if(t===Na.AccessToken)throw this.logger.info("MSAL internal Cache does not contain tokens, return error as per cache policy"),e;this.logger.info("MSAL internal Cache does not contain tokens, proceed to make a native call")}const a=await this.platformAuthProvider.sendMessage(i);return await this.handleNativeResponse(a,i,n).then((e=>(r.end({success:!0,isNativeBroker:!0,requestId:e.requestId}),o.clearNativeBrokerErrorCode(),e))).catch((e=>{throw r.end({success:!1,errorCode:e.errorCode,subErrorCode:e.subError,isNativeBroker:!0}),e}))}catch(e){throw e instanceof Lc&&o.setNativeBrokerErrorCode(e.errorCode),e}}createSilentCacheRequest(e,t){return{authority:e.authority,correlationId:this.correlationId,scopes:mr.fromString(e.scope).asArray(),account:t,forceRefresh:!1}}async acquireTokensFromCache(e,t){if(!e)throw this.logger.warning("NativeInteractionClient:acquireTokensFromCache - No nativeAccountId provided"),gt(Qe);const r=this.browserStorage.getBaseAccountInfo({nativeAccountId:e});if(!r)throw gt(Qe);try{const e=this.createSilentCacheRequest(t,r),n=await this.silentCacheClient.acquireToken(e),o={...r,idTokenClaims:n?.idTokenClaims,idToken:n?.idToken};return{...n,account:o}}catch(e){throw e}}async acquireTokenRedirect(e,t){this.logger.trace("NativeInteractionClient - acquireTokenRedirect called.");const{...r}=e;delete r.onRedirectNavigate;const n=await this.initializeNativeRequest(r);try{await this.platformAuthProvider.sendMessage(n)}catch(e){if(e instanceof Lc){if(this.initializeServerTelemetryManager(this.apiId).setNativeBrokerErrorCode(e.errorCode),Uc(e))throw e}}this.browserStorage.setTemporaryCache(Ia,JSON.stringify(n),!0);const o={apiId:Ea.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},i=this.config.auth.navigateToLoginRequestUrl?window.location.href:this.getRedirectUri(e.redirectUri);t.end({success:!0}),await this.navigationClient.navigateExternal(i,o)}async handleRedirectPromise(e,t){if(this.logger.trace("NativeInteractionClient - handleRedirectPromise called."),!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const r=this.browserStorage.getCachedNativeRequest();if(!r)return this.logger.verbose("NativeInteractionClient - handleRedirectPromise called but there is no cached request, returning null."),e&&t&&e?.addFields({errorCode:"no_cached_request"},t),null;const{prompt:n,...o}=r;n&&this.logger.verbose("NativeInteractionClient - handleRedirectPromise called and prompt was included in the original request, removing prompt from cached request to prevent second interaction with native broker window."),this.browserStorage.removeItem(this.browserStorage.generateCacheKey(Ia));const i=Tt();try{this.logger.verbose("NativeInteractionClient - handleRedirectPromise sending message to native broker.");const e=await this.platformAuthProvider.sendMessage(o),t=await this.handleNativeResponse(e,o,i);return this.initializeServerTelemetryManager(this.apiId).clearNativeBrokerErrorCode(),t}catch(e){throw e}}logout(){return this.logger.trace("NativeInteractionClient - logout called."),Promise.reject("Logout not implemented yet")}async handleNativeResponse(e,t,r){this.logger.trace("NativeInteractionClient - handleNativeResponse called.");const n=wt(e.id_token,Ba),o=this.createHomeAccountIdentifier(e,n),i=this.browserStorage.getAccountInfoFilteredBy({nativeAccountId:t.accountId})?.homeAccountId;if(t.extraParameters?.child_client_id&&e.account.id!==t.accountId)this.logger.info("handleNativeServerResponse: Double broker flow detected, ignoring accountId mismatch");else if(o!==i&&e.account.id!==t.accountId)throw Hc(Nc);const a=await this.getDiscoveredAuthority({requestAuthority:t.authority}),s=To(this.browserStorage,a,o,Ba,n,e.client_info,void 0,n.tid,void 0,e.account.id,this.logger);e.expires_in=Number(e.expires_in);const c=await this.generateAuthenticationResult(e,t,n,s,a.canonicalAuthority,r);return await this.cacheAccount(s),await this.cacheNativeTokens(e,t,o,n,e.access_token,c.tenantId,r),c}createHomeAccountIdentifier(e,r){return Er.generateHomeAccountId(e.client_info||t.EMPTY_STRING,Ir,this.logger,this.browserCrypto,r)}generateScopes(e,t){return t?mr.fromString(t):mr.fromString(e)}async generatePopAccessToken(e,t){if(t.tokenType===j.POP&&t.signPopToken){if(e.shr)return this.logger.trace("handleNativeServerResponse: SHR is enabled in native layer"),e.shr;const r=new vo(this.browserCrypto),n={resourceRequestMethod:t.resourceRequestMethod,resourceRequestUri:t.resourceRequestUri,shrClaims:t.shrClaims,shrNonce:t.shrNonce};if(!t.keyId)throw gt(nt);return r.signPopToken(e.access_token,t.keyId,n)}return e.access_token}async generateAuthenticationResult(e,r,n,o,i,a){const s=this.addTelemetryFromNativeResponse(e.properties.MATS),c=this.generateScopes(r.scope,e.scope),l=e.account.properties||{},h=l.UID||n.oid||n.sub||t.EMPTY_STRING,d=l.TenantId||n.tid||t.EMPTY_STRING,u=wr(o.getAccountInfo(),void 0,n,e.id_token);u.nativeAccountId!==e.account.id&&(u.nativeAccountId=e.account.id);const g=await this.generatePopAccessToken(e,r),p=r.tokenType===j.POP?j.POP:j.BEARER;return{authority:i,uniqueId:h,tenantId:d,scopes:c.asArray(),account:u,idToken:e.id_token,idTokenClaims:n,accessToken:g,fromCache:!!s&&this.isResponseFromCache(s),expiresOn:kt(a+e.expires_in),tokenType:p,correlationId:this.correlationId,state:e.state,fromNativeBroker:!0}}async cacheAccount(e){await this.browserStorage.setAccount(e,this.correlationId),this.browserStorage.removeAccountContext(e).catch((e=>{this.logger.error(`Error occurred while removing account context from browser storage. ${e}`)}))}cacheNativeTokens(e,r,n,o,i,a,s){const c=Rt(n,r.authority,e.id_token||"",r.clientId,o.tid||""),l=s+(r.tokenType===j.POP?t.SHR_NONCE_VALIDITY:("string"==typeof e.expires_in?parseInt(e.expires_in,10):e.expires_in)||0),h=this.generateScopes(e.scope,r.scope),d={idToken:c,accessToken:_t(n,r.authority,i,r.clientId,o.tid||a,h.printScopes(),l,0,Ba,void 0,r.tokenType,void 0,r.keyId)};return this.nativeStorageManager.saveCacheRecord(d,this.correlationId,r.storeInCache)}getExpiresInValue(e,r){return e===j.POP?t.SHR_NONCE_VALIDITY:("string"==typeof r?parseInt(r,10):r)||0}addTelemetryFromNativeResponse(e){const t=this.getMATSFromResponse(e);return t?(this.performanceClient.addFields({extensionId:this.platformAuthProvider.getExtensionId(),extensionVersion:this.platformAuthProvider.getExtensionVersion(),matsBrokerVersion:t.broker_version,matsAccountJoinOnStart:t.account_join_on_start,matsAccountJoinOnEnd:t.account_join_on_end,matsDeviceJoin:t.device_join,matsPromptBehavior:t.prompt_behavior,matsApiErrorCode:t.api_error_code,matsUiVisible:t.ui_visible,matsSilentCode:t.silent_code,matsSilentBiSubCode:t.silent_bi_sub_code,matsSilentMessage:t.silent_message,matsSilentStatus:t.silent_status,matsHttpStatus:t.http_status,matsHttpEventCount:t.http_event_count},this.correlationId),t):null}getMATSFromResponse(e){if(e)try{return JSON.parse(e)}catch(e){this.logger.error("NativeInteractionClient - Error parsing MATS telemetry, returning null instead")}return null}isResponseFromCache(e){return void 0===e.is_cached?(this.logger.verbose("NativeInteractionClient - MATS telemetry does not contain field indicating if response was served from cache. Returning false."),!1):!!e.is_cached}async initializeNativeRequest(e){this.logger.trace("NativeInteractionClient - initializeNativeRequest called");const t=await this.getCanonicalAuthority(e),{scopes:r,...n}=e,o=new mr(r||[]);o.appendScopes(c);const i={...n,accountId:this.accountId,clientId:this.config.auth.clientId,authority:t.urlString,scope:o.printScopes(),redirectUri:this.getRedirectUri(e.redirectUri),prompt:this.getPrompt(e.prompt),correlationId:this.correlationId,tokenType:e.authenticationScheme,windowTitleSubstring:document.title,extraParameters:{...e.extraQueryParameters,...e.tokenQueryParameters},extendedExpiryToken:!1,keyId:e.popKid};if(i.signPopToken&&e.popKid)throw Qi(Hi);if(this.handleExtraBrokerParams(i),i.extraParameters=i.extraParameters||{},i.extraParameters.telemetry=ta,e.authenticationScheme===j.POP){const t={resourceRequestUri:e.resourceRequestUri,resourceRequestMethod:e.resourceRequestMethod,shrClaims:e.shrClaims,shrNonce:e.shrNonce},r=new vo(this.browserCrypto);let n;if(i.keyId)n=this.browserCrypto.base64UrlEncode(JSON.stringify({kid:i.keyId})),i.signPopToken=!1;else{const o=await zn(r.generateCnf.bind(r),Hn.PopTokenGenerateCnf,this.logger,this.performanceClient,e.correlationId)(t,this.logger);n=o.reqCnfString,i.keyId=o.kid,i.signPopToken=!0}i.reqCnf=n}return this.addRequestSKUs(i),i}async getCanonicalAuthority(e){const t=e.authority||this.config.auth.authority;e.account&&await this.getDiscoveredAuthority({requestAuthority:t,requestAzureCloudOptions:e.azureCloudOptions,account:e.account});const r=new Mr(t);return r.validateAsUri(),r}getPrompt(e){switch(this.apiId){case Ea.ssoSilent:case Ea.acquireTokenSilent_silentFlow:return this.logger.trace("initializeNativeRequest: silent request sets prompt to none"),k.NONE}if(e)switch(e){case k.NONE:case k.CONSENT:case k.LOGIN:return this.logger.trace("initializeNativeRequest: prompt is compatible with native flow"),e;default:throw this.logger.trace(`initializeNativeRequest: prompt = ${e} is not compatible with native flow`),Qi(Li)}else this.logger.trace("initializeNativeRequest: prompt was not provided")}handleExtraBrokerParams(e){const t=e.extraParameters&&e.extraParameters.hasOwnProperty(sn)&&e.extraParameters.hasOwnProperty(cn)&&e.extraParameters.hasOwnProperty(en);if(!e.embeddedClientId&&!t)return;let r="";const n=e.redirectUri;e.embeddedClientId?(e.redirectUri=this.config.auth.redirectUri,r=e.embeddedClientId):e.extraParameters&&(e.redirectUri=e.extraParameters[cn],r=e.extraParameters[en]),e.extraParameters={child_client_id:r,child_redirect_uri:n},this.performanceClient?.addFields({embeddedClientId:r,embeddedRedirectUri:n},e.correlationId)}}async function Bc(e,t,r,n,o){const i=_o({...e.auth,authority:t},r,n,o);if(In(i,{sku:Xi,version:Fs,os:"",cpu:""}),e.auth.protocolMode!==br.OIDC&&Tn(i,e.telemetry.application),r.platformBroker&&(function(e){e.set("nativebroker","1")}(i),r.authenticationScheme===j.POP)){const e=new nc(n,o),t=new vo(e);let a;if(r.popKid)a=e.encodeKid(r.popKid);else{a=(await zn(t.generateCnf.bind(t),Hn.PopTokenGenerateCnf,n,o,r.correlationId)(r,n)).reqCnfString}On(i,a)}return hn(i,r.correlationId,o),i}async function Fc(e,r,n,o,i){if(!n.codeChallenge)throw gr(Xt);const a=await zn(Bc,Hn.GetStandardParams,o,i,n.correlationId)(e,r,n,o,i);return dn(a,b),function(e,t,r){if(!t||!r)throw gr(Xt);e.set("code_challenge",t),e.set("code_challenge_method",r)}(a,n.codeChallenge,t.S256_CODE_CHALLENGE_METHOD),Pn(a,n.extraQueryParameters||{}),Po(r,a,e.auth.encodeExtraQueryParams,n.extraQueryParameters)}async function Kc(e,t,r,n,o,i){if(!n.earJwk)throw Qi(Qo);const a=await Bc(t,r,n,o,i);dn(a,E),function(e,t){e.set("ear_jwk",encodeURIComponent(t)),e.set("ear_jwe_crypto","eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0")}(a,n.earJwk);const s=new Map;Pn(s,n.extraQueryParameters||{});return function(e,t,r){const n=e.createElement("form");return n.method="post",n.action=t,r.forEach(((t,r)=>{const o=e.createElement("input");o.hidden=!0,o.name=r,o.value=t,n.appendChild(o)})),e.body.appendChild(n),n}(e,Po(r,s,t.auth.encodeExtraQueryParams,n.extraQueryParameters),a)}async function zc(e,t,r,n,o,i,a,s,c,l){if(s.verbose("Account id found, calling WAM for token"),!l)throw Qi(Ni);const h=new nc(s,c),d=new Dc(n,o,h,s,a,n.system.navigationClient,r,c,l,t,i,e.correlationId),{userRequestState:u}=yo.parseRequestState(h,e.state);return zn(d.acquireToken.bind(d),Hn.NativeInteractionClientAcquireToken,s,c,e.correlationId)({...e,state:u,prompt:void 0})}async function Gc(e,t,r,n,o,i,a,s,c,l,h,d){if(Yn.removeThrottle(a,o.auth.clientId,e),t.accountId)return zn(zc,Hn.HandleResponsePlatformBroker,l,h,e.correlationId)(e,t.accountId,n,o,a,s,c,l,h,d);const u={...e,code:t.code||"",codeVerifier:r},g=new Oc(i,a,u,l,h);return await zn(g.handleCodeResponse.bind(g),Hn.HandleCodeResponse,l,h,e.correlationId)(t,e)}async function $c(e,t,r,n,o,i,a,s,c,l,h){if(Yn.removeThrottle(i,n.auth.clientId,e),Mo(t,e.state),!t.ear_jwe)throw Qi(jo);if(!e.earJwk)throw Qi(Qo);const d=JSON.parse(await zn(os,Hn.DecryptEarResponse,c,l,e.correlationId)(e.earJwk,t.ear_jwe));if(d.accountId)return zn(zc,Hn.HandleResponsePlatformBroker,c,l,e.correlationId)(e,d.accountId,r,n,i,a,s,c,l,h);const u=new Io(n.auth.clientId,i,new nc(c,l),c,null,null,l);u.validateTokenResponse(d);const g={code:"",state:e.state,nonce:e.nonce,client_info:d.client_info,cloud_graph_host_name:d.cloud_graph_host_name,cloud_instance_host_name:d.cloud_instance_host_name,cloud_instance_name:d.cloud_instance_name,msgraph_host:d.msgraph_host};return await zn(u.handleServerTokenResponse.bind(u),Hn.HandleServerTokenResponse,c,l,e.correlationId)(d,o,Tt(),e,g,void 0,void 0,void 0,void 0)}async function Qc(e,t,r){e.addQueueMeasurement(Hn.GeneratePkceCodes,r);const n=Kn(jc,Hn.GenerateCodeVerifier,t,e,r)(e,t,r);return{verifier:n,challenge:await zn(Wc,Hn.GenerateCodeChallengeFromVerifier,t,e,r)(n,e,t,r)}}function jc(e,t,r){try{const n=new Uint8Array(32);Kn(Za,Hn.GetRandomValues,t,e,r)(n);return Ha(n)}catch(e){throw Qi($o)}}async function Wc(e,t,r,n){t.addQueueMeasurement(Hn.GenerateCodeChallengeFromVerifier,n);try{const o=await zn(Xa,Hn.Sha256Digest,r,t,n)(e,t,n);return Ha(new Uint8Array(o))}catch(e){throw Qi($o)}}class Vc{constructor(e,t,r,n){this.logger=e,this.handshakeTimeoutMs=t,this.extensionId=n,this.resolvers=new Map,this.handshakeResolvers=new Map,this.messageChannel=new MessageChannel,this.windowListener=this.onWindowMessage.bind(this),this.performanceClient=r,this.handshakeEvent=r.startMeasurement(Hn.NativeMessageHandlerHandshake),this.platformAuthType=aa}async sendMessage(e){this.logger.trace(this.platformAuthType+" - sendMessage called.");const t={method:la,request:e},r={channel:Zi,extensionId:this.extensionId,responseId:ts(),body:t};this.logger.trace(this.platformAuthType+" - Sending request to browser extension"),this.logger.tracePii(this.platformAuthType+` - Sending request to browser extension: ${JSON.stringify(r)}`),this.messageChannel.port1.postMessage(r);const n=await new Promise(((e,t)=>{this.resolvers.set(r.responseId,{resolve:e,reject:t})}));return this.validatePlatformBrokerResponse(n)}static async createProvider(e,t,r){e.trace("PlatformAuthExtensionHandler - createProvider called.");try{const n=new Vc(e,t,r,ea);return await n.sendHandshakeRequest(),n}catch(n){const o=new Vc(e,t,r);return await o.sendHandshakeRequest(),o}}async sendHandshakeRequest(){this.logger.trace(this.platformAuthType+" - sendHandshakeRequest called."),window.addEventListener("message",this.windowListener,!1);const e={channel:Zi,extensionId:this.extensionId,responseId:ts(),body:{method:sa}};return this.handshakeEvent.add({extensionId:this.extensionId,extensionHandshakeTimeoutMs:this.handshakeTimeoutMs}),this.messageChannel.port1.onmessage=e=>{this.onChannelMessage(e)},window.postMessage(e,window.origin,[this.messageChannel.port2]),new Promise(((t,r)=>{this.handshakeResolvers.set(e.responseId,{resolve:t,reject:r}),this.timeoutId=window.setTimeout((()=>{window.removeEventListener("message",this.windowListener,!1),this.messageChannel.port1.close(),this.messageChannel.port2.close(),this.handshakeEvent.end({extensionHandshakeTimedOut:!0,success:!1}),r(Qi(Mi)),this.handshakeResolvers.delete(e.responseId)}),this.handshakeTimeoutMs)}))}onWindowMessage(e){if(this.logger.trace(this.platformAuthType+" - onWindowMessage called"),e.source!==window)return;const t=e.data;if(t.channel&&t.channel===Zi&&(!t.extensionId||t.extensionId===this.extensionId)&&t.body.method===sa){const e=this.handshakeResolvers.get(t.responseId);if(!e)return void this.logger.trace(this.platformAuthType+`.onWindowMessage - resolver can't be found for request ${t.responseId}`);this.logger.verbose(t.extensionId?`Extension with id: ${t.extensionId} not installed`:"No extension installed"),clearTimeout(this.timeoutId),this.messageChannel.port1.close(),this.messageChannel.port2.close(),window.removeEventListener("message",this.windowListener,!1),this.handshakeEvent.end({success:!1,extensionInstalled:!1}),e.reject(Qi(Oi))}}onChannelMessage(e){this.logger.trace(this.platformAuthType+" - onChannelMessage called.");const t=e.data,r=this.resolvers.get(t.responseId),n=this.handshakeResolvers.get(t.responseId);try{const e=t.body.method;if(e===ha){if(!r)return;const e=t.body.response;if(this.logger.trace(this.platformAuthType+" - Received response from browser extension"),this.logger.tracePii(this.platformAuthType+` - Received response from browser extension: ${JSON.stringify(e)}`),"Success"!==e.status)r.reject(Hc(e.code,e.description,e.ext));else{if(!e.result)throw Ce(ue,"Event does not contain result.");e.result.code&&e.result.description?r.reject(Hc(e.result.code,e.result.description,e.result.ext)):r.resolve(e.result)}this.resolvers.delete(t.responseId)}else if(e===ca){if(!n)return void this.logger.trace(this.platformAuthType+`.onChannelMessage - resolver can't be found for request ${t.responseId}`);clearTimeout(this.timeoutId),window.removeEventListener("message",this.windowListener,!1),this.extensionId=t.extensionId,this.extensionVersion=t.body.version,this.logger.verbose(this.platformAuthType+` - Received HandshakeResponse from extension: ${this.extensionId}`),this.handshakeEvent.end({extensionInstalled:!0,success:!0}),n.resolve(),this.handshakeResolvers.delete(t.responseId)}}catch(t){this.logger.error("Error parsing response from WAM Extension"),this.logger.errorPii(`Error parsing response from WAM Extension: ${t}`),this.logger.errorPii(`Unable to parse ${e}`),r?r.reject(t):n&&n.reject(t)}}validatePlatformBrokerResponse(e){if(e.hasOwnProperty("access_token")&&e.hasOwnProperty("id_token")&&e.hasOwnProperty("client_info")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scope")&&e.hasOwnProperty("expires_in"))return e;throw Ce(ue,"Response missing expected properties.")}getExtensionId(){return this.extensionId}getExtensionVersion(){return this.extensionVersion}getExtensionName(){return this.getExtensionId()===ea?"chrome":this.getExtensionId()?.length?"unknown":void 0}}class Jc{constructor(e,t,r){this.logger=e,this.performanceClient=t,this.correlationId=r,this.platformAuthType=ia}static async createProvider(e,t,r){if(e.trace("PlatformAuthDOMHandler: createProvider called"),window.navigator?.platformAuthentication){const n=await window.navigator.platformAuthentication.getSupportedContracts(ra);if(n?.includes(oa))return e.trace("Platform auth api available in DOM"),new Jc(e,t,r)}}getExtensionId(){return ra}getExtensionVersion(){return""}getExtensionName(){return na}async sendMessage(e){this.logger.trace(this.platformAuthType+" - Sending request to browser DOM API");try{const t=this.initializePlatformDOMRequest(e),r=await window.navigator.platformAuthentication.executeGetToken(t);return this.validatePlatformBrokerResponse(r)}catch(e){throw this.logger.error(this.platformAuthType+" - executeGetToken DOM API error"),e}}initializePlatformDOMRequest(e){this.logger.trace(this.platformAuthType+" - initializeNativeDOMRequest called");const{accountId:t,clientId:r,authority:n,scope:o,redirectUri:i,correlationId:a,state:s,storeInCache:c,embeddedClientId:l,extraParameters:h,...d}=e,u=this.getDOMExtraParams(d);return{accountId:t,brokerId:this.getExtensionId(),authority:n,clientId:r,correlationId:a||this.correlationId,extraParameters:{...h,...u},isSecurityTokenService:!1,redirectUri:i,scope:o,state:s,storeInCache:c,embeddedClientId:l}}validatePlatformBrokerResponse(e){if(e.hasOwnProperty("isSuccess")){if(e.hasOwnProperty("accessToken")&&e.hasOwnProperty("idToken")&&e.hasOwnProperty("clientInfo")&&e.hasOwnProperty("account")&&e.hasOwnProperty("scopes")&&e.hasOwnProperty("expiresIn"))return this.logger.trace(this.platformAuthType+" - platform broker returned successful and valid response"),this.convertToPlatformBrokerResponse(e);if(e.hasOwnProperty("error")){const t=e;if(!1===t.isSuccess&&t.error&&t.error.code)throw this.logger.trace(this.platformAuthType+" - platform broker returned error response"),Hc(t.error.code,t.error.description,{error:parseInt(t.error.errorCode),protocol_error:t.error.protocolError,status:t.error.status,properties:t.error.properties})}}throw Ce(ue,"Response missing expected properties.")}convertToPlatformBrokerResponse(e){this.logger.trace(this.platformAuthType+" - convertToNativeResponse called");return{access_token:e.accessToken,id_token:e.idToken,client_info:e.clientInfo,account:e.account,expires_in:e.expiresIn,scope:e.scopes,state:e.state||"",properties:e.properties||{},extendedLifetimeToken:e.extendedLifetimeToken??!1,shr:e.proofOfPossessionPayload}}getDOMExtraParams(e){return{...Object.entries(e).reduce(((e,[t,r])=>(e[t]=String(r),e)),{})}}}async function Yc(e,t,r,n){e.trace("getPlatformAuthProvider called",r);const o=function(){let e;try{return e=window[da.SessionStorage],"true"===e?.getItem(La)}catch(e){return!1}}();let i;e.trace("Has client allowed platform auth via DOM API: "+o);try{o&&(i=await Jc.createProvider(e,t,r)),i||(e.trace("Platform auth via DOM API not available, checking for extension"),i=await Vc.createProvider(e,n||2e3,t))}catch(t){e.trace("Platform auth not available",t)}return i}function Xc(e,t,r,n){if(t.trace("isBrokerAvailable called"),!e.system.allowPlatformBroker)return t.trace("isBrokerAvailable: allowPlatformBroker is not enabled, returning false"),!1;if(!r)return t.trace("isBrokerAvailable: Platform auth provider is not initialized, returning false"),!1;if(n)switch(n){case j.BEARER:case j.POP:return t.trace("isBrokerAvailable: authenticationScheme is supported, returning true"),!0;default:return t.trace("isBrokerAvailable: authenticationScheme is not supported, returning false"),!1}return!0}class Zc extends _c{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,a,c,l),this.unloadWindow=this.unloadWindow.bind(this),this.nativeStorage=s,this.eventHandler=o}acquireToken(e,t){try{const r={popupName:this.generatePopupName(e.scopes||c,e.authority||this.config.auth.authority),popupWindowAttributes:e.popupWindowAttributes||{},popupWindowParent:e.popupWindowParent??window};return this.performanceClient.addFields({isAsyncPopup:this.config.system.asyncPopups},this.correlationId),this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true, acquiring token"),this.acquireTokenPopupAsync(e,r,t)):(this.logger.verbose("asyncPopup set to false, opening popup before acquiring token"),r.popup=this.openSizedPopup("about:blank",r),this.acquireTokenPopupAsync(e,r,t))}catch(e){return Promise.reject(e)}}logout(e){try{this.logger.verbose("logoutPopup called");const t=this.initializeLogoutRequest(e),r={popupName:this.generateLogoutPopupName(t),popupWindowAttributes:e?.popupWindowAttributes||{},popupWindowParent:e?.popupWindowParent??window},n=e&&e.authority,o=e&&e.mainWindowRedirectUri;return this.config.system.asyncPopups?(this.logger.verbose("asyncPopups set to true"),this.logoutPopupAsync(t,r,n,o)):(this.logger.verbose("asyncPopup set to false, opening popup"),r.popup=this.openSizedPopup("about:blank",r),this.logoutPopupAsync(t,r,n,o))}catch(e){return Promise.reject(e)}}async acquireTokenPopupAsync(t,r,n){this.logger.verbose("acquireTokenPopupAsync called");const o=await zn(this.initializeAuthorizationRequest.bind(this),Hn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(t,e.InteractionType.Popup);r.popup&&Os(o.authority);const i=Xc(this.config,this.logger,this.platformAuthProvider,t.authenticationScheme);return o.platformBroker=i,this.config.auth.protocolMode===br.EAR?this.executeEarFlow(o,r):this.executeCodeFlow(o,r,n)}async executeCodeFlow(t,r,n){const o=t.correlationId,i=this.initializeServerTelemetryManager(Ea.acquireTokenPopup),a=n||await zn(Qc,Hn.GeneratePkceCodes,this.logger,this.performanceClient,o)(this.performanceClient,this.logger,o),s={...t,codeChallenge:a.challenge};try{const n=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,o)({serverTelemetryManager:i,requestAuthority:s.authority,requestAzureCloudOptions:s.azureCloudOptions,requestExtraQueryParameters:s.extraQueryParameters,account:s.account}),c=await zn(Fc,Hn.GetAuthCodeUrl,this.logger,this.performanceClient,o)(this.config,n.authority,s,this.logger,this.performanceClient),l=this.initiateAuthRequest(c,r);this.eventHandler.emitEvent(pc.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:l},null);const h=await this.monitorPopupForHash(l,r.popupWindowParent),d=Kn(Pc,Hn.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(h,this.config.auth.OIDCOptions.serverResponseType,this.logger);return await zn(Gc,Hn.HandleResponseCode,this.logger,this.performanceClient,o)(t,d,a.verifier,Ea.acquireTokenPopup,this.config,n,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}catch(e){throw r.popup?.close(),e instanceof ye&&(e.setCorrelationId(this.correlationId),i.cacheFailedRequest(e)),e}}async executeEarFlow(e,t){const r=e.correlationId,n=await zn(this.getDiscoveredAuthority.bind(this),Hn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,r)({requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account}),o=await zn(ns,Hn.GenerateEarKey,this.logger,this.performanceClient,r)(),i={...e,earJwk:o},a=t.popup||this.openPopup("about:blank",t);(await Kc(a.document,this.config,n,i,this.logger,this.performanceClient)).submit();const s=await zn(this.monitorPopupForHash.bind(this),Hn.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,r)(a,t.popupWindowParent),c=Kn(Pc,Hn.DeserializeResponse,this.logger,this.performanceClient,this.correlationId)(s,this.config.auth.OIDCOptions.serverResponseType,this.logger);return zn($c,Hn.HandleResponseEar,this.logger,this.performanceClient,r)(i,c,Ea.acquireTokenPopup,this.config,n,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}async logoutPopupAsync(t,r,n,o){this.logger.verbose("logoutPopupAsync called"),this.eventHandler.emitEvent(pc.LOGOUT_START,e.InteractionType.Popup,t);const i=this.initializeServerTelemetryManager(Ea.logoutPopup);try{await this.clearCacheOnLogout(t.account);const a=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:i,requestAuthority:n,account:t.account||void 0});try{a.authority.endSessionEndpoint}catch{if(t.account?.homeAccountId&&t.postLogoutRedirectUri&&a.authority.protocolMode===br.OIDC){if(this.browserStorage.removeAccount(t.account?.homeAccountId),this.eventHandler.emitEvent(pc.LOGOUT_SUCCESS,e.InteractionType.Popup,t),o){const e={apiId:Ea.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=Mr.getAbsoluteUrl(o,As());await this.navigationClient.navigateInternal(t,e)}return void r.popup?.close()}}const s=a.getLogoutUri(t);this.eventHandler.emitEvent(pc.LOGOUT_SUCCESS,e.InteractionType.Popup,t);const c=this.openPopup(s,r);if(this.eventHandler.emitEvent(pc.POPUP_OPENED,e.InteractionType.Popup,{popupWindow:c},null),await this.monitorPopupForHash(c,r.popupWindowParent).catch((()=>{})),o){const e={apiId:Ea.logoutPopup,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},t=Mr.getAbsoluteUrl(o,As());this.logger.verbose("Redirecting main window to url specified in the request"),this.logger.verbosePii(`Redirecting main window to: ${t}`),await this.navigationClient.navigateInternal(t,e)}else this.logger.verbose("No main window navigation requested")}catch(t){throw r.popup?.close(),t instanceof ye&&(t.setCorrelationId(this.correlationId),i.cacheFailedRequest(t)),this.eventHandler.emitEvent(pc.LOGOUT_FAILURE,e.InteractionType.Popup,null,t),this.eventHandler.emitEvent(pc.LOGOUT_END,e.InteractionType.Popup),t}this.eventHandler.emitEvent(pc.LOGOUT_END,e.InteractionType.Popup)}initiateAuthRequest(e,t){if(e)return this.logger.infoPii(`Navigate to: ${e}`),this.openPopup(e,t);throw this.logger.error("Navigate url is empty"),Qi(Vo)}monitorPopupForHash(e,t){return new Promise(((t,r)=>{this.logger.verbose("PopupHandler.monitorPopupForHash - polling started");const n=setInterval((()=>{if(e.closed)return this.logger.error("PopupHandler.monitorPopupForHash - window closed"),clearInterval(n),void r(Qi(oi));let o="";try{o=e.location.href}catch(e){}if(!o||"about:blank"===o)return;clearInterval(n);let i="";const a=this.config.auth.OIDCOptions.serverResponseType;e&&(i=a===R.QUERY?e.location.search:e.location.hash),this.logger.verbose("PopupHandler.monitorPopupForHash - popup window is on same origin as caller"),t(i)}),this.config.system.pollIntervalMilliseconds)})).finally((()=>{this.cleanPopup(e,t)}))}openPopup(e,t){try{let r;if(t.popup?(r=t.popup,this.logger.verbosePii(`Navigating popup window to: ${e}`),r.location.assign(e)):void 0===t.popup&&(this.logger.verbosePii(`Opening popup window to: ${e}`),r=this.openSizedPopup(e,t)),!r)throw Qi(ni);return r.focus&&r.focus(),this.currentWindow=r,t.popupWindowParent.addEventListener("beforeunload",this.unloadWindow),r}catch(e){throw this.logger.error("error opening popup "+e.message),Qi(ri)}}openSizedPopup(e,{popupName:t,popupWindowAttributes:r,popupWindowParent:n}){const o=n.screenLeft?n.screenLeft:n.screenX,i=n.screenTop?n.screenTop:n.screenY,a=n.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,s=n.innerHeight||document.documentElement.clientHeight||document.body.clientHeight;let c=r.popupSize?.width,l=r.popupSize?.height,h=r.popupPosition?.top,d=r.popupPosition?.left;return(!c||c<0||c>a)&&(this.logger.verbose("Default popup window width used. Window width not configured or invalid."),c=Wi),(!l||l<0||l>s)&&(this.logger.verbose("Default popup window height used. Window height not configured or invalid."),l=Vi),(!h||h<0||h>s)&&(this.logger.verbose("Default popup window top position used. Window top not configured or invalid."),h=Math.max(0,s/2-Vi/2+i)),(!d||d<0||d>a)&&(this.logger.verbose("Default popup window left position used. Window left not configured or invalid."),d=Math.max(0,a/2-Wi/2+o)),n.open(e,t,`width=${c}, height=${l}, top=${h}, left=${d}, scrollbars=yes`)}unloadWindow(e){this.currentWindow&&this.currentWindow.close(),e.preventDefault()}cleanPopup(e,t){e.close(),t.removeEventListener("beforeunload",this.unloadWindow)}generatePopupName(e,t){return`${Ji}.${this.config.auth.clientId}.${e.join("-")}.${t}.${this.correlationId}`}generateLogoutPopupName(e){const t=e.account&&e.account.homeAccountId;return`${Ji}.${this.config.auth.clientId}.${t}.${this.correlationId}`}}class el extends _c{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,a,c,l),this.nativeStorage=s}async acquireToken(t){const r=await zn(this.initializeAuthorizationRequest.bind(this),Hn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,this.correlationId)(t,e.InteractionType.Redirect);r.platformBroker=Xc(this.config,this.logger,this.platformAuthProvider,t.authenticationScheme);const n=t=>{t.persisted&&(this.logger.verbose("Page was restored from back/forward cache. Clearing temporary cache."),this.browserStorage.resetRequestCache(),this.eventHandler.emitEvent(pc.RESTORE_FROM_BFCACHE,e.InteractionType.Redirect))},o=this.getRedirectStartPage(t.redirectStartPage);this.logger.verbosePii(`Redirect start page: ${o}`),this.browserStorage.setTemporaryCache(fa,o,!0),window.addEventListener("pageshow",n);try{this.config.auth.protocolMode===br.EAR?await this.executeEarFlow(r):await this.executeCodeFlow(r,t.onRedirectNavigate)}catch(e){throw e instanceof ye&&e.setCorrelationId(this.correlationId),window.removeEventListener("pageshow",n),e}}async executeCodeFlow(e,t){const r=e.correlationId,n=this.initializeServerTelemetryManager(Ea.acquireTokenRedirect),o=await zn(Qc,Hn.GeneratePkceCodes,this.logger,this.performanceClient,r)(this.performanceClient,this.logger,r),i={...e,codeChallenge:o.challenge};this.browserStorage.cacheAuthorizeRequest(i,o.verifier);try{const r=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:i.authority,requestAzureCloudOptions:i.azureCloudOptions,requestExtraQueryParameters:i.extraQueryParameters,account:i.account}),o=await zn(Fc,Hn.GetAuthCodeUrl,this.logger,this.performanceClient,e.correlationId)(this.config,r.authority,i,this.logger,this.performanceClient);return await this.initiateAuthRequest(o,t)}catch(e){throw e instanceof ye&&(e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e)),e}}async executeEarFlow(e){const t=e.correlationId,r=await zn(this.getDiscoveredAuthority.bind(this),Hn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,t)({requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account}),n=await zn(ns,Hn.GenerateEarKey,this.logger,this.performanceClient,t)(),o={...e,earJwk:n};this.browserStorage.cacheAuthorizeRequest(o);(await Kc(document,this.config,r,o,this.logger,this.performanceClient)).submit()}async handleRedirectPromise(e="",r,n,o){const i=this.initializeServerTelemetryManager(Ea.handleRedirectPromise);try{const[a,s]=this.getRedirectResponse(e||"");if(!a)return this.logger.info("handleRedirectPromise did not detect a response as a result of a redirect. Cleaning temporary cache."),this.browserStorage.resetRequestCache(),"back_forward"!==function(){if("undefined"==typeof window||void 0===window.performance||"function"!=typeof window.performance.getEntriesByType)return;const e=window.performance.getEntriesByType("navigation"),t=e.length?e[0]:void 0;return t?.type}()?o.event.errorCode="no_server_response":this.logger.verbose("Back navigation event detected. Muting no_server_response error"),null;const c=this.browserStorage.getTemporaryCache(fa,!0)||t.EMPTY_STRING,l=Mr.removeHashFromUrl(c);if(l===Mr.removeHashFromUrl(window.location.href)&&this.config.auth.navigateToLoginRequestUrl){this.logger.verbose("Current page is loginRequestUrl, handling response"),c.indexOf("#")>-1&&ws(c);return await this.handleResponse(a,r,n,i)}if(!this.config.auth.navigateToLoginRequestUrl)return this.logger.verbose("NavigateToLoginRequestUrl set to false, handling response"),await this.handleResponse(a,r,n,i);if(!Is()||this.config.system.allowRedirectInIframe){this.browserStorage.setTemporaryCache(ya,s,!0);const e={apiId:Ea.handleRedirectPromise,timeout:this.config.system.redirectNavigationTimeout,noHistory:!0};let t=!0;if(c&&"null"!==c)this.logger.verbose(`Navigating to loginRequestUrl: ${c}`),t=await this.navigationClient.navigateInternal(c,e);else{const r=ks();this.browserStorage.setTemporaryCache(fa,r,!0),this.logger.warning("Unable to get valid login request url from cache, redirecting to home page"),t=await this.navigationClient.navigateInternal(r,e)}if(!t)return await this.handleResponse(a,r,n,i)}return null}catch(e){throw e instanceof ye&&(e.setCorrelationId(this.correlationId),i.cacheFailedRequest(e)),e}}getRedirectResponse(t){this.logger.verbose("getRedirectResponseHash called");let r=t;r||(r=this.config.auth.OIDCOptions.serverResponseType===R.QUERY?window.location.search:window.location.hash);let n=_r(r);if(n){try{Mc(n,this.browserCrypto,e.InteractionType.Redirect)}catch(e){return e instanceof ye&&this.logger.error(`Interaction type validation failed due to ${e.errorCode}: ${e.errorMessage}`),[null,""]}return vs(window),this.logger.verbose("Hash contains known properties, returning response hash"),[n,r]}const o=this.browserStorage.getTemporaryCache(ya,!0);return this.browserStorage.removeItem(this.browserStorage.generateCacheKey(ya)),o&&(n=_r(o),n)?(this.logger.verbose("Hash does not contain known properties, returning cached hash"),[n,o]):[null,""]}async handleResponse(e,t,r,n){if(!e.state)throw Qi(Yo);if(e.ear_jwe){const r=await zn(this.getDiscoveredAuthority.bind(this),Hn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,t.correlationId)({requestAuthority:t.authority,requestAzureCloudOptions:t.azureCloudOptions,requestExtraQueryParameters:t.extraQueryParameters,account:t.account});return zn($c,Hn.HandleResponseEar,this.logger,this.performanceClient,t.correlationId)(t,e,Ea.acquireTokenRedirect,this.config,r,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}const o=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:t.authority});return zn(Gc,Hn.HandleResponseCode,this.logger,this.performanceClient,t.correlationId)(t,e,r,Ea.acquireTokenRedirect,this.config,o,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}async initiateAuthRequest(e,t){if(this.logger.verbose("RedirectHandler.initiateAuthRequest called"),e){this.logger.infoPii(`RedirectHandler.initiateAuthRequest: Navigate to: ${e}`);const r={apiId:Ea.acquireTokenRedirect,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},n=t||this.config.auth.onRedirectNavigate;if("function"==typeof n){this.logger.verbose("RedirectHandler.initiateAuthRequest: Invoking onRedirectNavigate callback");return!1!==n(e)?(this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate did not return false, navigating"),void await this.navigationClient.navigateExternal(e,r)):void this.logger.verbose("RedirectHandler.initiateAuthRequest: onRedirectNavigate returned false, stopping navigation")}return this.logger.verbose("RedirectHandler.initiateAuthRequest: Navigating window to navigate url"),void await this.navigationClient.navigateExternal(e,r)}throw this.logger.info("RedirectHandler.initiateAuthRequest: Navigate url is empty"),Qi(Vo)}async logout(t){this.logger.verbose("logoutRedirect called");const r=this.initializeLogoutRequest(t),n=this.initializeServerTelemetryManager(Ea.logout);try{this.eventHandler.emitEvent(pc.LOGOUT_START,e.InteractionType.Redirect,t),await this.clearCacheOnLogout(r.account);const o={apiId:Ea.logout,timeout:this.config.system.redirectNavigationTimeout,noHistory:!1},i=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:n,requestAuthority:t&&t.authority,requestExtraQueryParameters:t?.extraQueryParameters,account:t&&t.account||void 0});if(i.authority.protocolMode===br.OIDC)try{i.authority.endSessionEndpoint}catch{if(r.account?.homeAccountId)return this.browserStorage.removeAccount(r.account?.homeAccountId),void this.eventHandler.emitEvent(pc.LOGOUT_SUCCESS,e.InteractionType.Redirect,r)}const a=i.getLogoutUri(r);if(this.eventHandler.emitEvent(pc.LOGOUT_SUCCESS,e.InteractionType.Redirect,r),!t||"function"!=typeof t.onRedirectNavigate)return this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0,ma),void await this.navigationClient.navigateExternal(a,o);if(!1!==t.onRedirectNavigate(a))return this.logger.verbose("Logout onRedirectNavigate did not return false, navigating"),this.browserStorage.getInteractionInProgress()||this.browserStorage.setInteractionInProgress(!0,ma),void await this.navigationClient.navigateExternal(a,o);this.browserStorage.setInteractionInProgress(!1),this.logger.verbose("Logout onRedirectNavigate returned false, stopping navigation")}catch(t){throw t instanceof ye&&(t.setCorrelationId(this.correlationId),n.cacheFailedRequest(t)),this.eventHandler.emitEvent(pc.LOGOUT_FAILURE,e.InteractionType.Redirect,null,t),this.eventHandler.emitEvent(pc.LOGOUT_END,e.InteractionType.Redirect),t}this.eventHandler.emitEvent(pc.LOGOUT_END,e.InteractionType.Redirect)}getRedirectStartPage(e){const t=e||window.location.href;return Mr.getAbsoluteUrl(t,As())}}async function tl(e,t,r,n,o){if(t.addQueueMeasurement(Hn.SilentHandlerInitiateAuthRequest,n),!e)throw r.info("Navigate url is empty"),Qi(Vo);return o?zn(ol,Hn.SilentHandlerLoadFrame,r,t,n)(e,o,t,n):Kn(il,Hn.SilentHandlerLoadFrameSync,r,t,n)(e)}async function rl(e,t,r,n,o){const i=al();if(!i.contentDocument)throw"No document associated with iframe!";return(await Kc(i.contentDocument,e,t,r,n,o)).submit(),i}async function nl(e,t,r,n,o,i,a){return n.addQueueMeasurement(Hn.SilentHandlerMonitorIframeForHash,i),new Promise(((n,i)=>{t<Ds&&o.warning(`system.loadFrameTimeout or system.iframeHashTimeout set to lower (${t}ms) than the default (10000ms). This may result in timeouts.`);const s=window.setTimeout((()=>{window.clearInterval(c),i(Qi(ai))}),t),c=window.setInterval((()=>{let t="";const r=e.contentWindow;try{t=r?r.location.href:""}catch(e){}if(!t||"about:blank"===t)return;let o="";r&&(o=a===R.QUERY?r.location.search:r.location.hash),window.clearTimeout(s),window.clearInterval(c),n(o)}),r)})).finally((()=>{Kn(sl,Hn.RemoveHiddenIframe,o,n,i)(e)}))}function ol(e,t,r,n){return r.addQueueMeasurement(Hn.SilentHandlerLoadFrame,n),new Promise(((r,n)=>{const o=al();window.setTimeout((()=>{o?(o.src=e,r(o)):n("Unable to load iframe")}),t)}))}function il(e){const t=al();return t.src=e,t}function al(){const e=document.createElement("iframe");return e.className="msalSilentIframe",e.style.visibility="hidden",e.style.position="absolute",e.style.width=e.style.height="0",e.style.border="0",e.setAttribute("sandbox","allow-scripts allow-same-origin allow-forms"),document.body.appendChild(e),e}function sl(e){document.body===e.parentNode&&document.body.removeChild(e)}class cl extends _c{constructor(e,t,r,n,o,i,a,s,c,l,h){super(e,t,r,n,o,i,s,l,h),this.apiId=a,this.nativeStorage=c}async acquireToken(t){this.performanceClient.addQueueMeasurement(Hn.SilentIframeClientAcquireToken,t.correlationId),t.loginHint||t.sid||t.account&&t.account.username||this.logger.warning("No user hint provided. The authorization server may need more information to complete this request.");const r={...t};r.prompt?r.prompt!==k.NONE&&r.prompt!==k.NO_SESSION&&(this.logger.warning(`SilentIframeClient. Replacing invalid prompt ${r.prompt} with ${k.NONE}`),r.prompt=k.NONE):r.prompt=k.NONE;const n=await zn(this.initializeAuthorizationRequest.bind(this),Hn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,t.correlationId)(r,e.InteractionType.Silent);return n.platformBroker=Xc(this.config,this.logger,this.platformAuthProvider,n.authenticationScheme),Os(n.authority),this.config.auth.protocolMode===br.EAR?this.executeEarFlow(n):this.executeCodeFlow(n)}async executeCodeFlow(e){let t;const r=this.initializeServerTelemetryManager(this.apiId);try{return t=await zn(this.createAuthCodeClient.bind(this),Hn.StandardInteractionClientCreateAuthCodeClient,this.logger,this.performanceClient,e.correlationId)({serverTelemetryManager:r,requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account}),await zn(this.silentTokenHelper.bind(this),Hn.SilentIframeClientTokenHelper,this.logger,this.performanceClient,e.correlationId)(t,e)}catch(n){if(n instanceof ye&&(n.setCorrelationId(this.correlationId),r.cacheFailedRequest(n)),!(t&&n instanceof ye&&n.errorCode===ji))throw n;return this.performanceClient.addFields({retryError:n.errorCode},this.correlationId),await zn(this.silentTokenHelper.bind(this),Hn.SilentIframeClientTokenHelper,this.logger,this.performanceClient,this.correlationId)(t,e)}}async executeEarFlow(e){const t=e.correlationId,r=await zn(this.getDiscoveredAuthority.bind(this),Hn.StandardInteractionClientGetDiscoveredAuthority,this.logger,this.performanceClient,t)({requestAuthority:e.authority,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account}),n=await zn(ns,Hn.GenerateEarKey,this.logger,this.performanceClient,t)(),o={...e,earJwk:n},i=await zn(rl,Hn.SilentHandlerInitiateAuthRequest,this.logger,this.performanceClient,t)(this.config,r,o,this.logger,this.performanceClient),a=this.config.auth.OIDCOptions.serverResponseType,s=await zn(nl,Hn.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,t)(i,this.config.system.iframeHashTimeout,this.config.system.pollIntervalMilliseconds,this.performanceClient,this.logger,t,a),c=Kn(Pc,Hn.DeserializeResponse,this.logger,this.performanceClient,t)(s,a,this.logger);return zn($c,Hn.HandleResponseEar,this.logger,this.performanceClient,t)(o,c,this.apiId,this.config,r,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}logout(){return Promise.reject(Qi(di))}async silentTokenHelper(e,t){const r=t.correlationId;this.performanceClient.addQueueMeasurement(Hn.SilentIframeClientTokenHelper,r);const n=await zn(Qc,Hn.GeneratePkceCodes,this.logger,this.performanceClient,r)(this.performanceClient,this.logger,r),o={...t,codeChallenge:n.challenge},i=await zn(Fc,Hn.GetAuthCodeUrl,this.logger,this.performanceClient,r)(this.config,e.authority,o,this.logger,this.performanceClient),a=await zn(tl,Hn.SilentHandlerInitiateAuthRequest,this.logger,this.performanceClient,r)(i,this.performanceClient,this.logger,r,this.config.system.navigateFrameWait),s=this.config.auth.OIDCOptions.serverResponseType,c=await zn(nl,Hn.SilentHandlerMonitorIframeForHash,this.logger,this.performanceClient,r)(a,this.config.system.iframeHashTimeout,this.config.system.pollIntervalMilliseconds,this.performanceClient,this.logger,r,s),l=Kn(Pc,Hn.DeserializeResponse,this.logger,this.performanceClient,r)(c,s,this.logger);return zn(Gc,Hn.HandleResponseCode,this.logger,this.performanceClient,r)(t,l,n.verifier,this.apiId,this.config,e,this.browserStorage,this.nativeStorage,this.eventHandler,this.logger,this.performanceClient,this.platformAuthProvider)}}class ll extends _c{async acquireToken(e){this.performanceClient.addQueueMeasurement(Hn.SilentRefreshClientAcquireToken,e.correlationId);const t=await zn(Ec,Hn.InitializeBaseRequest,this.logger,this.performanceClient,e.correlationId)(e,this.config,this.performanceClient,this.logger),r={...e,...t};e.redirectUri&&(r.redirectUri=this.getRedirectUri(e.redirectUri));const n=this.initializeServerTelemetryManager(Ea.acquireTokenSilent_silentFlow),o=await this.createRefreshTokenClient({serverTelemetryManager:n,authorityUrl:r.authority,azureCloudOptions:r.azureCloudOptions,account:r.account});return zn(o.acquireTokenByRefreshToken.bind(o),Hn.RefreshTokenClientAcquireTokenByRefreshToken,this.logger,this.performanceClient,e.correlationId)(r).catch((e=>{throw e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e),e}))}logout(){return Promise.reject(Qi(di))}async createRefreshTokenClient(e){const t=await zn(this.getClientConfiguration.bind(this),Hn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,this.correlationId)({serverTelemetryManager:e.serverTelemetryManager,requestAuthority:e.authorityUrl,requestAzureCloudOptions:e.azureCloudOptions,requestExtraQueryParameters:e.extraQueryParameters,account:e.account});return new bo(t,this.performanceClient)}}class hl{constructor(e,t,r,n){this.isBrowserEnvironment="undefined"!=typeof window,this.config=e,this.storage=t,this.logger=r,this.cryptoObj=n}async loadExternalTokens(e,t,r){if(!this.isBrowserEnvironment)throw Qi(Ci);const n=e.correlationId||ts(),o=t.id_token?wt(t.id_token,Ba):void 0,i={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache},a=e.authority?new $n($n.generateAuthority(e.authority,e.azureCloudOptions),this.config.system.networkClient,this.storage,i,this.logger,e.correlationId||ts()):void 0,s=await this.loadAccount(e,r.clientInfo||t.client_info||"",n,o,a),c=await this.loadIdToken(t,s.homeAccountId,s.environment,s.realm,n),l=await this.loadAccessToken(e,t,s.homeAccountId,s.environment,s.realm,r,n),h=await this.loadRefreshToken(t,s.homeAccountId,s.environment,n);return this.generateAuthenticationResult(e,{account:s,idToken:c,accessToken:l,refreshToken:h},o,a)}async loadAccount(e,t,r,n,o){if(this.logger.verbose("TokenCache - loading account"),e.account){const t=Er.createFromAccountInfo(e.account);return await this.storage.setAccount(t,r),t}if(!o||!t&&!n)throw this.logger.error("TokenCache - if an account is not provided on the request, authority and either clientInfo or idToken must be provided instead."),Qi(ki);const i=Er.generateHomeAccountId(t,o.authorityType,this.logger,this.cryptoObj,n),a=n?.tid,s=To(this.storage,o,i,Ba,n,t,o.hostnameAndPort,a,void 0,void 0,this.logger);return await this.storage.setAccount(s,r),s}async loadIdToken(e,t,r,n,o){if(!e.id_token)return this.logger.verbose("TokenCache - no id token found in response"),null;this.logger.verbose("TokenCache - loading id token");const i=Rt(t,r,e.id_token,this.config.auth.clientId,n);return await this.storage.setIdTokenCredential(i,o),i}async loadAccessToken(e,t,r,n,o,i,a){if(!t.access_token)return this.logger.verbose("TokenCache - no access token found in response"),null;if(!t.expires_in)return this.logger.error("TokenCache - no expiration set on the access token. Cannot add it to the cache."),null;if(!(t.scope||e.scopes&&e.scopes.length))return this.logger.error("TokenCache - scopes not specified in the request or response. Cannot add token to the cache."),null;this.logger.verbose("TokenCache - loading access token");const s=t.scope?mr.fromString(t.scope):new mr(e.scopes),c=i.expiresOn||t.expires_in+Tt(),l=i.extendedExpiresOn||(t.ext_expires_in||t.expires_in)+Tt(),h=_t(r,n,t.access_token,this.config.auth.clientId,o,s.printScopes(),c,l,Ba);return await this.storage.setAccessTokenCredential(h,a),h}async loadRefreshToken(e,t,r,n){if(!e.refresh_token)return this.logger.verbose("TokenCache - no refresh token found in response"),null;this.logger.verbose("TokenCache - loading refresh token");const o=Pt(t,r,e.refresh_token,this.config.auth.clientId,e.foci,void 0,e.refresh_token_expires_in);return await this.storage.setRefreshTokenCredential(o,n),o}generateAuthenticationResult(e,t,r,n){let o,i="",a=[],s=null;t?.accessToken&&(i=t.accessToken.secret,a=mr.fromString(t.accessToken.target).asArray(),s=kt(t.accessToken.expiresOn),o=kt(t.accessToken.extendedExpiresOn));const c=t.account;return{authority:n?n.canonicalAuthority:"",uniqueId:t.account.localAccountId,tenantId:t.account.realm,scopes:a,account:c.getAccountInfo(),idToken:t.idToken?.secret||"",idTokenClaims:r||{},accessToken:i,fromCache:!0,expiresOn:s,correlationId:e.correlationId||"",requestId:"",extExpiresOn:o,familyId:t.refreshToken?.familyId||"",tokenType:t?.accessToken?.tokenType||"",state:e.state||"",cloudGraphHostName:c.cloudGraphHostName||"",msGraphHost:c.msGraphHost||"",fromNativeBroker:!1}}}class dl extends So{constructor(e){super(e),this.includeRedirectUri=!1}}class ul extends _c{constructor(e,t,r,n,o,i,a,s,c,l){super(e,t,r,n,o,i,s,c,l),this.apiId=a}async acquireToken(t){if(!t.code)throw Qi(bi);const r=await zn(this.initializeAuthorizationRequest.bind(this),Hn.StandardInteractionClientInitializeAuthorizationRequest,this.logger,this.performanceClient,t.correlationId)(t,e.InteractionType.Silent),n=this.initializeServerTelemetryManager(this.apiId);try{const e={...r,code:t.code},o=await zn(this.getClientConfiguration.bind(this),Hn.StandardInteractionClientGetClientConfiguration,this.logger,this.performanceClient,t.correlationId)({serverTelemetryManager:n,requestAuthority:r.authority,requestAzureCloudOptions:r.azureCloudOptions,requestExtraQueryParameters:r.extraQueryParameters,account:r.account}),i=new dl(o);this.logger.verbose("Auth code client created");const a=new Oc(i,this.browserStorage,e,this.logger,this.performanceClient);return await zn(a.handleCodeResponseFromServer.bind(a),Hn.HandleCodeResponseFromServer,this.logger,this.performanceClient,t.correlationId)({code:t.code,msgraph_host:t.msGraphHost,cloud_graph_host_name:t.cloudGraphHostName,cloud_instance_host_name:t.cloudInstanceHostName},r,!1)}catch(e){throw e instanceof ye&&(e.setCorrelationId(this.correlationId),n.cacheFailedRequest(e)),e}}logout(){return Promise.reject(Qi(di))}}function gl(e){const t=e?.idTokenClaims;return t?.tfp||t?.acr?"B2C":t?.tid?"9188040d-6c67-4c5b-b112-36a304b66dad"===t?.tid?"MSA":"AAD":void 0}function pl(e,t){try{Ps(e)}catch(e){throw t.end({success:!1},e),e}}class ml{constructor(e){this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.initialized=!1,this.logger=this.operatingContext.getLogger(),this.networkClient=this.config.system.networkClient,this.navigationClient=this.config.system.navigationClient,this.redirectResponse=new Map,this.hybridAuthCodeResponses=new Map,this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new nc(this.logger,this.performanceClient):pt,this.eventHandler=new Sc(this.logger),this.browserStorage=this.isBrowserEnvironment?new mc(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,this.performanceClient,this.eventHandler,jn(this.config.auth)):yc(this.config.auth.clientId,this.logger,this.performanceClient,this.eventHandler);const t={cacheLocation:da.MemoryStorage,temporaryCacheLocation:da.MemoryStorage,storeAuthStateInCookie:!1,secureCookies:!1,cacheMigrationEnabled:!1,claimsBasedCachingEnabled:!1};this.nativeInternalStorage=new mc(this.config.auth.clientId,t,this.browserCrypto,this.logger,this.performanceClient,this.eventHandler),this.tokenCache=new hl(this.config,this.browserStorage,this.logger,this.browserCrypto),this.activeSilentTokenRequests=new Map,this.trackPageVisibility=this.trackPageVisibility.bind(this),this.trackPageVisibilityWithMeasurement=this.trackPageVisibilityWithMeasurement.bind(this)}static async createController(e,t){const r=new ml(e);return await r.initialize(t),r}trackPageVisibility(e){e&&(this.logger.info("Perf: Visibility change detected"),this.performanceClient.incrementFields({visibilityChangeCount:1},e))}async initialize(e,t){if(this.logger.trace("initialize called"),this.initialized)return void this.logger.info("initialize has already been called, exiting early.");if(!this.isBrowserEnvironment)return this.logger.info("in non-browser environment, exiting early."),this.initialized=!0,void this.eventHandler.emitEvent(pc.INITIALIZE_END);const r=e?.correlationId||this.getRequestCorrelationId(),n=this.config.system.allowPlatformBroker,o=this.performanceClient.startMeasurement(Hn.InitializeClientApplication,r);if(this.eventHandler.emitEvent(pc.INITIALIZE_START),!t)try{this.logMultipleInstances(o)}catch{}if(await zn(this.browserStorage.initialize.bind(this.browserStorage),Hn.InitializeCache,this.logger,this.performanceClient,r)(r),n)try{this.platformAuthProvider=await Yc(this.logger,this.performanceClient,r,this.config.system.nativeBrokerHandshakeTimeout)}catch(e){this.logger.verbose(e)}this.config.cache.claimsBasedCachingEnabled||(this.logger.verbose("Claims-based caching is disabled. Clearing the previous cache with claims"),await zn(this.browserStorage.clearTokensAndKeysWithClaims.bind(this.browserStorage),Hn.ClearTokensAndKeysWithClaims,this.logger,this.performanceClient,r)(this.performanceClient,r)),this.config.system.asyncPopups&&await this.preGeneratePkceCodes(r),this.initialized=!0,this.eventHandler.emitEvent(pc.INITIALIZE_END),o.end({allowPlatformBroker:n,success:!0})}async handleRedirectPromise(e){if(this.logger.verbose("handleRedirectPromise called"),_s(this.initialized),this.isBrowserEnvironment){const t=e||"";let r=this.redirectResponse.get(t);return void 0===r?(r=this.handleRedirectPromiseInternal(e),this.redirectResponse.set(t,r),this.logger.verbose("handleRedirectPromise has been called for the first time, storing the promise")):this.logger.verbose("handleRedirectPromise has been called previously, returning the result from the first call"),r}return this.logger.verbose("handleRedirectPromise returns null, not browser environment"),null}async handleRedirectPromiseInternal(t){if(!this.browserStorage.isInteractionInProgress(!0))return this.logger.info("handleRedirectPromise called but there is no interaction in progress, returning null."),null;const r=this.browserStorage.getInteractionInProgress()?.type;if(r===ma)return this.logger.verbose("handleRedirectPromise removing interaction_in_progress flag and returning null after sign-out"),this.browserStorage.setInteractionInProgress(!1),Promise.resolve(null);const n=this.getAllAccounts(),o=this.browserStorage.getCachedNativeRequest(),i=o&&this.platformAuthProvider&&!t;let a,s;this.eventHandler.emitEvent(pc.HANDLE_REDIRECT_START,e.InteractionType.Redirect);try{if(i&&this.platformAuthProvider){a=this.performanceClient.startMeasurement(Hn.AcquireTokenRedirect,o?.correlationId||""),this.logger.trace("handleRedirectPromise - acquiring token from native platform");const e=new Dc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Ea.handleRedirectPromise,this.performanceClient,this.platformAuthProvider,o.accountId,this.nativeInternalStorage,o.correlationId);s=zn(e.handleRedirectPromise.bind(e),Hn.HandleNativeRedirectPromiseMeasurement,this.logger,this.performanceClient,a.event.correlationId)(this.performanceClient,a.event.correlationId)}else{const[e,r]=this.browserStorage.getCachedRequest(),n=e.correlationId;a=this.performanceClient.startMeasurement(Hn.AcquireTokenRedirect,n),this.logger.trace("handleRedirectPromise - acquiring token from web flow");const o=this.createRedirectClient(n);s=zn(o.handleRedirectPromise.bind(o),Hn.HandleRedirectPromiseMeasurement,this.logger,this.performanceClient,a.event.correlationId)(t,e,r,a)}}catch(e){throw this.browserStorage.resetRequestCache(),e}return s.then((t=>{if(t){this.browserStorage.resetRequestCache();n.length<this.getAllAccounts().length?(this.eventHandler.emitEvent(pc.LOGIN_SUCCESS,e.InteractionType.Redirect,t),this.logger.verbose("handleRedirectResponse returned result, login success")):(this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Redirect,t),this.logger.verbose("handleRedirectResponse returned result, acquire token success")),a.end({success:!0,accountType:gl(t.account)})}else a.event.errorCode?a.end({success:!1}):a.discard();return this.eventHandler.emitEvent(pc.HANDLE_REDIRECT_END,e.InteractionType.Redirect),t})).catch((t=>{this.browserStorage.resetRequestCache();const r=t;throw n.length>0?this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,r):this.eventHandler.emitEvent(pc.LOGIN_FAILURE,e.InteractionType.Redirect,null,r),this.eventHandler.emitEvent(pc.HANDLE_REDIRECT_END,e.InteractionType.Redirect),a.end({success:!1},r),t}))}async acquireTokenRedirect(t){const r=this.getRequestCorrelationId(t);this.logger.verbose("acquireTokenRedirect called",r);const n=this.performanceClient.startMeasurement(Hn.AcquireTokenPreRedirect,r);n.add({accountType:gl(t.account),scenarioId:t.scenarioId});const o=t.onRedirectNavigate;if(o)t.onRedirectNavigate=e=>{const t="function"==typeof o?o(e):void 0;return!1!==t?n.end({success:!0}):n.discard(),t};else{const e=this.config.auth.onRedirectNavigate;this.config.auth.onRedirectNavigate=t=>{const r="function"==typeof e?e(t):void 0;return!1!==r?n.end({success:!0}):n.discard(),r}}const i=this.getAllAccounts().length>0;try{let o;if(Ms(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0,pa),i?this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_START,e.InteractionType.Redirect,t):this.eventHandler.emitEvent(pc.LOGIN_START,e.InteractionType.Redirect,t),this.platformAuthProvider&&this.canUsePlatformBroker(t)){o=new Dc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Ea.acquireTokenRedirect,this.performanceClient,this.platformAuthProvider,this.getNativeAccountId(t),this.nativeInternalStorage,r).acquireTokenRedirect(t,n).catch((e=>{if(e instanceof Lc&&Uc(e)){this.platformAuthProvider=void 0;return this.createRedirectClient(r).acquireToken(t)}if(e instanceof po){this.logger.verbose("acquireTokenRedirect - Resolving interaction required error thrown by native broker by falling back to web flow");return this.createRedirectClient(r).acquireToken(t)}throw e}))}else{o=this.createRedirectClient(r).acquireToken(t)}return await o}catch(t){throw this.browserStorage.resetRequestCache(),n.end({success:!1},t),i?this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Redirect,null,t):this.eventHandler.emitEvent(pc.LOGIN_FAILURE,e.InteractionType.Redirect,null,t),t}}acquireTokenPopup(t){const r=this.getRequestCorrelationId(t),n=this.performanceClient.startMeasurement(Hn.AcquireTokenPopup,r);n.add({scenarioId:t.scenarioId,accountType:gl(t.account)});try{this.logger.verbose("acquireTokenPopup called",r),pl(this.initialized,n),this.browserStorage.setInteractionInProgress(!0,pa)}catch(e){return Promise.reject(e)}const o=this.getAllAccounts();let i;o.length>0?this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_START,e.InteractionType.Popup,t):this.eventHandler.emitEvent(pc.LOGIN_START,e.InteractionType.Popup,t);const a=this.getPreGeneratedPkceCodes(r);if(this.canUsePlatformBroker(t))i=this.acquireTokenNative({...t,correlationId:r},Ea.acquireTokenPopup).then((e=>(n.end({success:!0,isNativeBroker:!0,accountType:gl(e.account)}),e))).catch((e=>{if(e instanceof Lc&&Uc(e)){this.platformAuthProvider=void 0;return this.createPopupClient(r).acquireToken(t,a)}if(e instanceof po){this.logger.verbose("acquireTokenPopup - Resolving interaction required error thrown by native broker by falling back to web flow");return this.createPopupClient(r).acquireToken(t,a)}throw e}));else{i=this.createPopupClient(r).acquireToken(t,a)}return i.then((t=>(o.length<this.getAllAccounts().length?this.eventHandler.emitEvent(pc.LOGIN_SUCCESS,e.InteractionType.Popup,t):this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,t),n.end({success:!0,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:gl(t.account)}),t))).catch((t=>(o.length>0?this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,t):this.eventHandler.emitEvent(pc.LOGIN_FAILURE,e.InteractionType.Popup,null,t),n.end({success:!1},t),Promise.reject(t)))).finally((async()=>{this.browserStorage.setInteractionInProgress(!1),this.config.system.asyncPopups&&await this.preGeneratePkceCodes(r)}))}trackPageVisibilityWithMeasurement(){const e=this.ssoSilentMeasurement||this.acquireTokenByCodeAsyncMeasurement;e&&(this.logger.info("Perf: Visibility change detected in ",e.event.name),e.increment({visibilityChangeCount:1}))}async ssoSilent(t){const r=this.getRequestCorrelationId(t),n={...t,prompt:t.prompt,correlationId:r};let o;if(this.ssoSilentMeasurement=this.performanceClient.startMeasurement(Hn.SsoSilent,r),this.ssoSilentMeasurement?.add({scenarioId:t.scenarioId,accountType:gl(t.account)}),pl(this.initialized,this.ssoSilentMeasurement),this.ssoSilentMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement),this.logger.verbose("ssoSilent called",r),this.eventHandler.emitEvent(pc.SSO_SILENT_START,e.InteractionType.Silent,n),this.canUsePlatformBroker(n))o=this.acquireTokenNative(n,Ea.ssoSilent).catch((e=>{if(e instanceof Lc&&Uc(e)){this.platformAuthProvider=void 0;return this.createSilentIframeClient(n.correlationId).acquireToken(n)}throw e}));else{o=this.createSilentIframeClient(n.correlationId).acquireToken(n)}return o.then((t=>(this.eventHandler.emitEvent(pc.SSO_SILENT_SUCCESS,e.InteractionType.Silent,t),this.ssoSilentMeasurement?.end({success:!0,isNativeBroker:t.fromNativeBroker,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:gl(t.account)}),t))).catch((t=>{throw this.eventHandler.emitEvent(pc.SSO_SILENT_FAILURE,e.InteractionType.Silent,null,t),this.ssoSilentMeasurement?.end({success:!1},t),t})).finally((()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)}))}async acquireTokenByCode(t){const r=this.getRequestCorrelationId(t);this.logger.trace("acquireTokenByCode called",r);const n=this.performanceClient.startMeasurement(Hn.AcquireTokenByCode,r);pl(this.initialized,n),this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_BY_CODE_START,e.InteractionType.Silent,t),n.add({scenarioId:t.scenarioId});try{if(t.code&&t.nativeAccountId)throw Qi(Ri);if(t.code){const o=t.code;let i=this.hybridAuthCodeResponses.get(o);return i?(this.logger.verbose("Existing acquireTokenByCode request found",r),n.discard()):(this.logger.verbose("Initiating new acquireTokenByCode request",r),i=this.acquireTokenByCodeAsync({...t,correlationId:r}).then((t=>(this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_BY_CODE_SUCCESS,e.InteractionType.Silent,t),this.hybridAuthCodeResponses.delete(o),n.end({success:!0,isNativeBroker:t.fromNativeBroker,accessTokenSize:t.accessToken.length,idTokenSize:t.idToken.length,accountType:gl(t.account)}),t))).catch((t=>{throw this.hybridAuthCodeResponses.delete(o),this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),n.end({success:!1},t),t})),this.hybridAuthCodeResponses.set(o,i)),await i}if(t.nativeAccountId){if(this.canUsePlatformBroker(t,t.nativeAccountId)){const e=await this.acquireTokenNative({...t,correlationId:r},Ea.acquireTokenByCode,t.nativeAccountId).catch((e=>{throw e instanceof Lc&&Uc(e)&&(this.platformAuthProvider=void 0),e}));return n.end({accountType:gl(e.account),success:!0}),e}throw Qi(Pi)}throw Qi(Ei)}catch(t){throw this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_BY_CODE_FAILURE,e.InteractionType.Silent,null,t),n.end({success:!1},t),t}}async acquireTokenByCodeAsync(e){this.logger.trace("acquireTokenByCodeAsync called",e.correlationId),this.acquireTokenByCodeAsyncMeasurement=this.performanceClient.startMeasurement(Hn.AcquireTokenByCodeAsync,e.correlationId),this.acquireTokenByCodeAsyncMeasurement?.increment({visibilityChangeCount:0}),document.addEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement);const t=this.createSilentAuthCodeClient(e.correlationId);return await t.acquireToken(e).then((e=>(this.acquireTokenByCodeAsyncMeasurement?.end({success:!0,fromCache:e.fromCache,isNativeBroker:e.fromNativeBroker}),e))).catch((e=>{throw this.acquireTokenByCodeAsyncMeasurement?.end({success:!1},e),e})).finally((()=>{document.removeEventListener("visibilitychange",this.trackPageVisibilityWithMeasurement)}))}async acquireTokenFromCache(e,t){switch(this.performanceClient.addQueueMeasurement(Hn.AcquireTokenFromCache,e.correlationId),t){case Na.Default:case Na.AccessToken:case Na.AccessTokenAndRefreshToken:const t=this.createSilentCacheClient(e.correlationId);return zn(t.acquireToken.bind(t),Hn.SilentCacheClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw gt(Ye)}}async acquireTokenByRefreshToken(e,t){switch(this.performanceClient.addQueueMeasurement(Hn.AcquireTokenByRefreshToken,e.correlationId),t){case Na.Default:case Na.AccessTokenAndRefreshToken:case Na.RefreshToken:case Na.RefreshTokenAndNetwork:const t=this.createSilentRefreshClient(e.correlationId);return zn(t.acquireToken.bind(t),Hn.SilentRefreshClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e);default:throw gt(Ye)}}async acquireTokenBySilentIframe(e){this.performanceClient.addQueueMeasurement(Hn.AcquireTokenBySilentIframe,e.correlationId);const t=this.createSilentIframeClient(e.correlationId);return zn(t.acquireToken.bind(t),Hn.SilentIframeClientAcquireToken,this.logger,this.performanceClient,e.correlationId)(e)}async logout(e){const t=this.getRequestCorrelationId(e);return this.logger.warning("logout API is deprecated and will be removed in msal-browser v3.0.0. Use logoutRedirect instead.",t),this.logoutRedirect({correlationId:t,...e})}async logoutRedirect(e){const t=this.getRequestCorrelationId(e);Ms(this.initialized,this.config),this.browserStorage.setInteractionInProgress(!0,ma);return this.createRedirectClient(t).logout(e)}logoutPopup(e){try{const t=this.getRequestCorrelationId(e);Ps(this.initialized),this.browserStorage.setInteractionInProgress(!0,ma);return this.createPopupClient(t).logout(e).finally((()=>{this.browserStorage.setInteractionInProgress(!1)}))}catch(e){return Promise.reject(e)}}async clearCache(e){if(!this.isBrowserEnvironment)return void this.logger.info("in non-browser environment, returning early.");const t=this.getRequestCorrelationId(e);return this.createSilentCacheClient(t).logout(e)}getAllAccounts(e){return Cc(this.logger,this.browserStorage,this.isBrowserEnvironment,e)}getAccount(e){return vc(e,this.logger,this.browserStorage)}getAccountByUsername(e){return wc(e,this.logger,this.browserStorage)}getAccountByHomeId(e){return Ic(e,this.logger,this.browserStorage)}getAccountByLocalId(e){return Tc(e,this.logger,this.browserStorage)}setActiveAccount(e){Ac(e,this.browserStorage)}getActiveAccount(){return kc(this.browserStorage)}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");const r=Er.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return await this.browserStorage.setAccount(r,e.correlationId),e.fromNativeBroker?(this.logger.verbose("Response was from native broker, storing in-memory"),this.nativeInternalStorage.hydrateCache(e,t)):this.browserStorage.hydrateCache(e,t)}async acquireTokenNative(e,t,r,n){if(this.logger.trace("acquireTokenNative called"),!this.platformAuthProvider)throw Qi(Ni);return new Dc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,t,this.performanceClient,this.platformAuthProvider,r||this.getNativeAccountId(e),this.nativeInternalStorage,e.correlationId).acquireToken(e,n)}canUsePlatformBroker(e,t){if(this.logger.trace("canUsePlatformBroker called"),!this.platformAuthProvider)return this.logger.trace("canUsePlatformBroker: platform broker unavilable, returning false"),!1;if(!Xc(this.config,this.logger,this.platformAuthProvider,e.authenticationScheme))return this.logger.trace("canUsePlatformBroker: isBrokerAvailable returned false, returning false"),!1;if(e.prompt)switch(e.prompt){case k.NONE:case k.CONSENT:case k.LOGIN:this.logger.trace("canUsePlatformBroker: prompt is compatible with platform broker flow");break;default:return this.logger.trace(`canUsePlatformBroker: prompt = ${e.prompt} is not compatible with platform broker flow, returning false`),!1}return!(!t&&!this.getNativeAccountId(e))||(this.logger.trace("canUsePlatformBroker: nativeAccountId is not available, returning false"),!1)}getNativeAccountId(e){const t=e.account||this.getAccount({loginHint:e.loginHint,sid:e.sid})||this.getActiveAccount();return t&&t.nativeAccountId||""}createPopupClient(e){return new Zc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.platformAuthProvider,e)}createRedirectClient(e){return new el(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.nativeInternalStorage,this.platformAuthProvider,e)}createSilentIframeClient(e){return new cl(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Ea.ssoSilent,this.performanceClient,this.nativeInternalStorage,this.platformAuthProvider,e)}createSilentCacheClient(e){return new xc(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.platformAuthProvider,e)}createSilentRefreshClient(e){return new ll(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,this.performanceClient,this.platformAuthProvider,e)}createSilentAuthCodeClient(e){return new ul(this.config,this.browserStorage,this.browserCrypto,this.logger,this.eventHandler,this.navigationClient,Ea.acquireTokenByCode,this.performanceClient,this.platformAuthProvider,e)}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){return Rs(),this.performanceClient.addPerformanceCallback(e)}removePerformanceCallback(e){return this.performanceClient.removePerformanceCallback(e)}enableAccountStorageEvents(){this.config.cache.cacheLocation===da.LocalStorage?this.eventHandler.subscribeCrossTab():this.logger.info("Account storage events are only available when cacheLocation is set to localStorage")}disableAccountStorageEvents(){this.config.cache.cacheLocation===da.LocalStorage?this.eventHandler.unsubscribeCrossTab():this.logger.info("Account storage events are only available when cacheLocation is set to localStorage")}getTokenCache(){return this.tokenCache}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){this.navigationClient=e}getConfiguration(){return this.config}getPerformanceClient(){return this.performanceClient}isBrowserEnv(){return this.isBrowserEnvironment}getRequestCorrelationId(e){return e?.correlationId?e.correlationId:this.isBrowserEnvironment?ts():t.EMPTY_STRING}async loginRedirect(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginRedirect called",t),this.acquireTokenRedirect({correlationId:t,...e||Pa})}loginPopup(e){const t=this.getRequestCorrelationId(e);return this.logger.verbose("loginPopup called",t),this.acquireTokenPopup({correlationId:t,...e||Pa})}async acquireTokenSilent(e){const t=this.getRequestCorrelationId(e),r=this.performanceClient.startMeasurement(Hn.AcquireTokenSilent,t);r.add({cacheLookupPolicy:e.cacheLookupPolicy,scenarioId:e.scenarioId}),pl(this.initialized,r),this.logger.verbose("acquireTokenSilent called",t);const n=e.account||this.getActiveAccount();if(!n)throw Qi(ui);return r.add({accountType:gl(n)}),this.acquireTokenSilentDeduped(e,n,t).then((n=>(r.end({success:!0,fromCache:n.fromCache,isNativeBroker:n.fromNativeBroker,accessTokenSize:n.accessToken.length,idTokenSize:n.idToken.length}),{...n,state:e.state,correlationId:t}))).catch((e=>{throw e instanceof ye&&e.setCorrelationId(t),r.end({success:!1},e),e}))}async acquireTokenSilentDeduped(e,t,r){const n=Jn(this.config.auth.clientId,{...e,authority:e.authority||this.config.auth.authority,correlationId:r},t.homeAccountId),o=JSON.stringify(n),i=this.activeSilentTokenRequests.get(o);if(void 0===i){this.logger.verbose("acquireTokenSilent called for the first time, storing active request",r),this.performanceClient.addFields({deduped:!1},r);const n=zn(this.acquireTokenSilentAsync.bind(this),Hn.AcquireTokenSilentAsync,this.logger,this.performanceClient,r)({...e,correlationId:r},t);return this.activeSilentTokenRequests.set(o,n),n.finally((()=>{this.activeSilentTokenRequests.delete(o)}))}return this.logger.verbose("acquireTokenSilent has been called previously, returning the result from the first call",r),this.performanceClient.addFields({deduped:!0},r),i}async acquireTokenSilentAsync(t,r){const n=()=>this.trackPageVisibility(t.correlationId);this.performanceClient.addQueueMeasurement(Hn.AcquireTokenSilentAsync,t.correlationId),this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_START,e.InteractionType.Silent,t),t.correlationId&&this.performanceClient.incrementFields({visibilityChangeCount:0},t.correlationId),document.addEventListener("visibilitychange",n);const o=await zn(Rc,Hn.InitializeSilentRequest,this.logger,this.performanceClient,t.correlationId)(t,r,this.config,this.performanceClient,this.logger),i=t.cacheLookupPolicy||Na.Default;return this.acquireTokenSilentNoIframe(o,i).catch((async e=>{const t=function(e,t){const r=!(e instanceof po&&e.subError!==so),n=e.errorCode===ji||e.errorCode===Ye,o=r&&n||e.errorCode===to||e.errorCode===no,i=qa.includes(t);return o&&i}(e,i);if(t){if(this.activeIframeRequest){if(i!==Na.Skip){const[t,r]=this.activeIframeRequest;this.logger.verbose(`Iframe request is already in progress, awaiting resolution for request with correlationId: ${r}`,o.correlationId);const n=this.performanceClient.startMeasurement(Hn.AwaitConcurrentIframe,o.correlationId);n.add({awaitIframeCorrelationId:r});const a=await t;if(n.end({success:a}),a)return this.logger.verbose(`Parallel iframe request with correlationId: ${r} succeeded. Retrying cache and/or RT redemption`,o.correlationId),this.acquireTokenSilentNoIframe(o,i);throw this.logger.info(`Iframe request with correlationId: ${r} failed. Interaction is required.`),e}return this.logger.warning("Another iframe request is currently in progress and CacheLookupPolicy is set to Skip. This may result in degraded performance and/or reliability for both calls. Please consider changing the CacheLookupPolicy to take advantage of request queuing and token cache.",o.correlationId),zn(this.acquireTokenBySilentIframe.bind(this),Hn.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o)}{let e;return this.activeIframeRequest=[new Promise((t=>{e=t})),o.correlationId],this.logger.verbose("Refresh token expired/invalid or CacheLookupPolicy is set to Skip, attempting acquire token by iframe.",o.correlationId),zn(this.acquireTokenBySilentIframe.bind(this),Hn.AcquireTokenBySilentIframe,this.logger,this.performanceClient,o.correlationId)(o).then((t=>(e(!0),t))).catch((t=>{throw e(!1),t})).finally((()=>{this.activeIframeRequest=void 0}))}}throw e})).then((r=>(this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,r),t.correlationId&&this.performanceClient.addFields({fromCache:r.fromCache,isNativeBroker:r.fromNativeBroker},t.correlationId),r))).catch((t=>{throw this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,t),t})).finally((()=>{document.removeEventListener("visibilitychange",n)}))}async acquireTokenSilentNoIframe(t,r){return Xc(this.config,this.logger,this.platformAuthProvider,t.authenticationScheme)&&t.account.nativeAccountId?(this.logger.verbose("acquireTokenSilent - attempting to acquire token from native platform"),this.acquireTokenNative(t,Ea.acquireTokenSilent_silentFlow,t.account.nativeAccountId,r).catch((async e=>{if(e instanceof Lc&&Uc(e))throw this.logger.verbose("acquireTokenSilent - native platform unavailable, falling back to web flow"),this.platformAuthProvider=void 0,gt(Ye);throw e}))):(this.logger.verbose("acquireTokenSilent - attempting to acquire token from web flow"),r===Na.AccessToken&&this.logger.verbose("acquireTokenSilent - cache lookup policy set to AccessToken, attempting to acquire token from local cache"),zn(this.acquireTokenFromCache.bind(this),Hn.AcquireTokenFromCache,this.logger,this.performanceClient,t.correlationId)(t,r).catch((n=>{if(r===Na.AccessToken)throw n;return this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_NETWORK_START,e.InteractionType.Silent,t),zn(this.acquireTokenByRefreshToken.bind(this),Hn.AcquireTokenByRefreshToken,this.logger,this.performanceClient,t.correlationId)(t,r)})))}async preGeneratePkceCodes(e){return this.logger.verbose("Generating new PKCE codes"),this.pkceCode=await zn(Qc,Hn.GeneratePkceCodes,this.logger,this.performanceClient,e)(this.performanceClient,this.logger,e),Promise.resolve()}getPreGeneratedPkceCodes(e){this.logger.verbose("Attempting to pick up pre-generated PKCE codes");const t=this.pkceCode?{...this.pkceCode}:void 0;return this.pkceCode=void 0,this.logger.verbose((t?"Found":"Did not find")+" pre-generated PKCE codes"),this.performanceClient.addFields({usePreGeneratedPkce:!!t},e),t}logMultipleInstances(e){const t=this.config.auth.clientId;if(!window)return;window.msal=window.msal||{},window.msal.clientIds=window.msal.clientIds||[];window.msal.clientIds.length>0&&this.logger.verbose("There is already an instance of MSAL.js in the window."),window.msal.clientIds.push(t),function(e,t,r){const n=window.msal?.clientIds||[],o=n.length,i=n.filter((t=>t===e)).length;i>1&&r.warning("There is already an instance of MSAL.js in the window with the same client id."),t.add({msalInstanceCount:o,sameClientIdInstanceCount:i})}(t,e,this.logger)}}class fl{constructor(e,t,r,n){this.clientId=e,this.clientCapabilities=t,this.crypto=r,this.logger=n}toNaaTokenRequest(e){let t;t=void 0===e.extraQueryParameters?new Map:new Map(Object.entries(e.extraQueryParameters));const r=e.correlationId||this.crypto.createNewGuid(),n=Mn(e.claims,this.clientCapabilities),o=e.scopes||c;return{platformBrokerId:e.account?.homeAccountId,clientId:this.clientId,authority:e.authority,scope:o.join(" "),correlationId:r,claims:pr.isEmptyObj(n)?void 0:n,state:e.state,authenticationScheme:e.authenticationScheme||j.BEARER,extraParameters:t}}fromNaaTokenResponse(e,t,r){if(!t.token.id_token||!t.token.access_token)throw gt(Te);const n=kt(r+(t.token.expires_in||0)),o=wt(t.token.id_token,this.crypto.base64Decode),i=this.fromNaaAccountInfo(t.account,t.token.id_token,o),a=t.token.scope||e.scope;return{authority:t.token.authority||i.environment,uniqueId:i.localAccountId,tenantId:i.tenantId,scopes:a.split(" "),account:i,idToken:t.token.id_token,idTokenClaims:o,accessToken:t.token.access_token,fromCache:!1,expiresOn:n,tokenType:e.authenticationScheme||j.BEARER,correlationId:e.correlationId,extExpiresOn:n,state:e.state}}fromNaaAccountInfo(e,t,r){const n=r||e.idTokenClaims,o=e.localAccountId||n?.oid||n?.sub||"",i=e.tenantId||n?.tid||"",a=e.homeAccountId||`${o}.${i}`,s=e.username||n?.preferred_username||"",c=e.name||n?.name,l=new Map,h=vr(a,o,i,n);l.set(i,h);return{homeAccountId:a,environment:e.environment,tenantId:i,username:s,localAccountId:o,name:c,idToken:t,idTokenClaims:n,tenantProfiles:l}}fromBridgeError(e){if(!function(e){return void 0!==e.status}(e))return new ye("unknown_error","An unknown error occurred");switch(e.status){case Gs:return new ut(it);case $s:return new ut(ot);case Vs:return new ut(Qe);case Ws:return new ut(ct);case Js:return new ut(e.code||ct,e.description);case Qs:case js:return new Vn(e.code,e.description);case zs:return new po(e.code,e.description);default:return new ye(e.code,e.description)}}toAuthenticationResultFromCache(e,t,r,n,o){if(!t||!r)throw gt(Te);const i=wt(t.secret,this.crypto.base64Decode),a=r.target||n.scopes.join(" ");return{authority:r.environment||e.environment,uniqueId:e.localAccountId,tenantId:e.tenantId,scopes:a.split(" "),account:e,idToken:t.secret,idTokenClaims:i||{},accessToken:r.secret,fromCache:!0,expiresOn:kt(r.expiresOn),extExpiresOn:kt(r.extendedExpiresOn),tokenType:n.authenticationScheme||j.BEARER,correlationId:o,state:n.state}}}const yl={code:"unsupported_method",desc:"This method is not supported in nested app environment."};class Cl extends ye{constructor(e,t){super(e,t),Object.setPrototypeOf(this,Cl.prototype),this.name="NestedAppAuthError"}static createUnsupportedError(){return new Cl(yl.code,yl.desc)}}class vl{constructor(e){this.operatingContext=e;const t=this.operatingContext.getBridgeProxy();if(void 0===t)throw new Error("unexpected: bridgeProxy is undefined");this.bridgeProxy=t,this.config=e.getConfig(),this.logger=this.operatingContext.getLogger(),this.performanceClient=this.config.telemetry.client,this.browserCrypto=e.isBrowserEnvironment()?new nc(this.logger,this.performanceClient,!0):pt,this.eventHandler=new Sc(this.logger),this.browserStorage=this.operatingContext.isBrowserEnvironment()?new mc(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,this.performanceClient,this.eventHandler,jn(this.config.auth)):yc(this.config.auth.clientId,this.logger,this.performanceClient,this.eventHandler),this.nestedAppAuthAdapter=new fl(this.config.auth.clientId,this.config.auth.clientCapabilities,this.browserCrypto,this.logger);const r=this.bridgeProxy.getAccountContext();this.currentAccountContext=r||null}static async createController(e){const t=new vl(e);return Promise.resolve(t)}async initialize(e,t){const r=e?.correlationId||ts();return await this.browserStorage.initialize(r),Promise.resolve()}ensureValidRequest(e){return e?.correlationId?e:{...e,correlationId:this.browserCrypto.createNewGuid()}}async acquireTokenInteractive(t){const r=this.ensureValidRequest(t);this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_START,e.InteractionType.Popup,r);const n=this.performanceClient.startMeasurement(Hn.AcquireTokenPopup,r.correlationId);n?.add({nestedAppAuthRequest:!0});try{const o=this.nestedAppAuthAdapter.toNaaTokenRequest(r),i=Tt(),a=await this.bridgeProxy.getTokenInteractive(o),s={...this.nestedAppAuthAdapter.fromNaaTokenResponse(o,a,i)};return await this.hydrateCache(s,t),this.currentAccountContext={homeAccountId:s.account.homeAccountId,environment:s.account.environment,tenantId:s.account.tenantId},this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Popup,s),n.add({accessTokenSize:s.accessToken.length,idTokenSize:s.idToken.length}),n.end({success:!0,requestId:s.requestId}),s}catch(t){const r=t instanceof ye?t:this.nestedAppAuthAdapter.fromBridgeError(t);throw this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Popup,null,t),n.end({success:!1},t),r}}async acquireTokenSilentInternal(t){const r=this.ensureValidRequest(t);this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_START,e.InteractionType.Silent,r);const n=await this.acquireTokenFromCache(r);if(n)return this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,n),n;const o=this.performanceClient.startMeasurement(Hn.SsoSilent,r.correlationId);o?.increment({visibilityChangeCount:0}),o?.add({nestedAppAuthRequest:!0});try{const n=this.nestedAppAuthAdapter.toNaaTokenRequest(r),i=Tt(),a=await this.bridgeProxy.getTokenSilent(n),s=this.nestedAppAuthAdapter.fromNaaTokenResponse(n,a,i);return await this.hydrateCache(s,t),this.currentAccountContext={homeAccountId:s.account.homeAccountId,environment:s.account.environment,tenantId:s.account.tenantId},this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,s),o?.add({accessTokenSize:s.accessToken.length,idTokenSize:s.idToken.length}),o?.end({success:!0,requestId:s.requestId}),s}catch(t){const r=t instanceof ye?t:this.nestedAppAuthAdapter.fromBridgeError(t);throw this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null,t),o?.end({success:!1},t),r}}async acquireTokenFromCache(t){const r=this.performanceClient.startMeasurement(Hn.AcquireTokenSilent,t.correlationId);if(r?.add({nestedAppAuthRequest:!0}),t.claims)return this.logger.verbose("Claims are present in the request, skipping cache lookup"),null;if(t.forceRefresh)return this.logger.verbose("forceRefresh is set to true, skipping cache lookup"),null;let n=null;switch(t.cacheLookupPolicy||(t.cacheLookupPolicy=Na.Default),t.cacheLookupPolicy){case Na.Default:case Na.AccessToken:case Na.AccessTokenAndRefreshToken:n=await this.acquireTokenFromCacheInternal(t);break;default:return null}return n?(this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_SUCCESS,e.InteractionType.Silent,n),r?.add({accessTokenSize:n?.accessToken.length,idTokenSize:n?.idToken.length}),r?.end({success:!0}),n):(this.logger.warning("Cached tokens are not found for the account, proceeding with silent token request."),this.eventHandler.emitEvent(pc.ACQUIRE_TOKEN_FAILURE,e.InteractionType.Silent,null),r?.end({success:!1}),null)}async acquireTokenFromCacheInternal(e){const t=this.bridgeProxy.getAccountContext()||this.currentAccountContext;let r=null;if(t&&(r=vc(t,this.logger,this.browserStorage)),!r)return this.logger.verbose("No active account found, falling back to the host"),Promise.resolve(null);this.logger.verbose("active account found, attempting to acquire token silently");const n={...e,correlationId:e.correlationId||this.browserCrypto.createNewGuid(),authority:e.authority||r.environment,scopes:e.scopes?.length?e.scopes:[...c]},o=this.browserStorage.getTokenKeys(),i=this.browserStorage.getAccessToken(r,n,o,r.tenantId,this.performanceClient,n.correlationId);if(!i)return this.logger.verbose("No cached access token found"),Promise.resolve(null);if(bt(i.cachedAt)||St(i.expiresOn,this.config.system.tokenRenewalOffsetSeconds))return this.logger.verbose("Cached access token has expired"),Promise.resolve(null);const a=this.browserStorage.getIdToken(r,o,r.tenantId,this.performanceClient,n.correlationId);return a?this.nestedAppAuthAdapter.toAuthenticationResultFromCache(r,a,i,n,n.correlationId):(this.logger.verbose("No cached id token found"),Promise.resolve(null))}async acquireTokenPopup(e){return this.acquireTokenInteractive(e)}acquireTokenRedirect(e){throw Cl.createUnsupportedError()}async acquireTokenSilent(e){return this.acquireTokenSilentInternal(e)}acquireTokenByCode(e){throw Cl.createUnsupportedError()}acquireTokenNative(e,t,r){throw Cl.createUnsupportedError()}acquireTokenByRefreshToken(e,t){throw Cl.createUnsupportedError()}addEventCallback(e,t){return this.eventHandler.addEventCallback(e,t)}removeEventCallback(e){this.eventHandler.removeEventCallback(e)}addPerformanceCallback(e){throw Cl.createUnsupportedError()}removePerformanceCallback(e){throw Cl.createUnsupportedError()}enableAccountStorageEvents(){throw Cl.createUnsupportedError()}disableAccountStorageEvents(){throw Cl.createUnsupportedError()}getAllAccounts(e){return Cc(this.logger,this.browserStorage,this.isBrowserEnv(),e)}getAccount(e){return vc(e,this.logger,this.browserStorage)}getAccountByUsername(e){return wc(e,this.logger,this.browserStorage)}getAccountByHomeId(e){return Ic(e,this.logger,this.browserStorage)}getAccountByLocalId(e){return Tc(e,this.logger,this.browserStorage)}setActiveAccount(e){return Ac(e,this.browserStorage)}getActiveAccount(){return kc(this.browserStorage)}handleRedirectPromise(e){return Promise.resolve(null)}loginPopup(e){return this.acquireTokenInteractive(e||Pa)}loginRedirect(e){throw Cl.createUnsupportedError()}logout(e){throw Cl.createUnsupportedError()}logoutRedirect(e){throw Cl.createUnsupportedError()}logoutPopup(e){throw Cl.createUnsupportedError()}ssoSilent(e){return this.acquireTokenSilentInternal(e)}getTokenCache(){throw Cl.createUnsupportedError()}getLogger(){return this.logger}setLogger(e){this.logger=e}initializeWrapperLibrary(e,t){}setNavigationClient(e){this.logger.warning("setNavigationClient is not supported in nested app auth")}getConfiguration(){return this.config}isBrowserEnv(){return this.operatingContext.isBrowserEnvironment()}getBrowserCrypto(){return this.browserCrypto}getPerformanceClient(){throw Cl.createUnsupportedError()}getRedirectResponse(){throw Cl.createUnsupportedError()}async clearCache(e){throw Cl.createUnsupportedError()}async hydrateCache(e,t){this.logger.verbose("hydrateCache called");const r=Er.createFromAccountInfo(e.account,e.cloudGraphHostName,e.msGraphHost);return await this.browserStorage.setAccount(r,e.correlationId),this.browserStorage.hydrateCache(e,t)}}async function wl(e){const t=new Zs(e),r=new Xs(e),n=[t.initialize(),r.initialize()];return await Promise.all(n),r.isAvailable()&&e.auth.supportsNestedAppAuth?vl.createController(r):t.isAvailable()?ml.createController(t):null}class Il{static async createPublicClientApplication(e){const t=await async function(e,t){const r=new Zs(e);return await r.initialize(),ml.createController(r,t)}(e);return new Il(e,t)}constructor(e,t){this.isBroker=!1,this.controller=t||new ml(new Zs(e))}async initialize(e){return this.controller.initialize(e,this.isBroker)}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}async function Tl(e){const t=new Il(e);return await t.initialize(),t}class Al{constructor(e){this.initialized=!1,this.operatingContext=e,this.isBrowserEnvironment=this.operatingContext.isBrowserEnvironment(),this.config=e.getConfig(),this.logger=e.getLogger(),this.performanceClient=this.config.telemetry.client,this.browserCrypto=this.isBrowserEnvironment?new nc(this.logger,this.performanceClient):pt,this.eventHandler=new Sc(this.logger),this.browserStorage=this.isBrowserEnvironment?new mc(this.config.auth.clientId,this.config.cache,this.browserCrypto,this.logger,this.performanceClient,this.eventHandler,void 0):yc(this.config.auth.clientId,this.logger,this.performanceClient,this.eventHandler)}getBrowserStorage(){return this.browserStorage}getAccount(e){return null}getAccountByHomeId(e){return null}getAccountByLocalId(e){return null}getAccountByUsername(e){return null}getAllAccounts(){return[]}initialize(){return this.initialized=!0,Promise.resolve()}acquireTokenPopup(e){return _s(this.initialized),Rs(),{}}acquireTokenRedirect(e){return _s(this.initialized),Rs(),Promise.resolve()}acquireTokenSilent(e){return _s(this.initialized),Rs(),{}}acquireTokenByCode(e){return _s(this.initialized),Rs(),{}}acquireTokenNative(e,t,r){return _s(this.initialized),Rs(),{}}acquireTokenByRefreshToken(e,t){return _s(this.initialized),Rs(),{}}addEventCallback(e,t){return null}removeEventCallback(e){}addPerformanceCallback(e){return _s(this.initialized),Rs(),""}removePerformanceCallback(e){return _s(this.initialized),Rs(),!0}enableAccountStorageEvents(){_s(this.initialized),Rs()}disableAccountStorageEvents(){_s(this.initialized),Rs()}handleRedirectPromise(e){return _s(this.initialized),Promise.resolve(null)}loginPopup(e){return _s(this.initialized),Rs(),{}}loginRedirect(e){return _s(this.initialized),Rs(),{}}logout(e){return _s(this.initialized),Rs(),{}}logoutRedirect(e){return _s(this.initialized),Rs(),{}}logoutPopup(e){return _s(this.initialized),Rs(),{}}ssoSilent(e){return _s(this.initialized),Rs(),{}}getTokenCache(){return _s(this.initialized),Rs(),{}}getLogger(){return this.logger}setLogger(e){_s(this.initialized),Rs()}setActiveAccount(e){_s(this.initialized),Rs()}getActiveAccount(){return _s(this.initialized),Rs(),null}initializeWrapperLibrary(e,t){this.browserStorage.setWrapperMetadata(e,t)}setNavigationClient(e){_s(this.initialized),Rs()}getConfiguration(){return this.config}isBrowserEnv(){return _s(this.initialized),Rs(),!0}getBrowserCrypto(){return _s(this.initialized),Rs(),{}}getPerformanceClient(){return _s(this.initialized),Rs(),{}}getRedirectResponse(){return _s(this.initialized),Rs(),{}}async clearCache(e){_s(this.initialized),Rs()}async hydrateCache(e,t){_s(this.initialized),Rs()}}class kl extends Ks{getId(){return kl.ID}getModuleName(){return kl.MODULE_NAME}async initialize(){return!0}}kl.MODULE_NAME="",kl.ID="UnknownOperatingContext";class Sl{static async createPublicClientApplication(e){const t=await wl(e);let r;return r=null!==t?new Sl(e,t):new Sl(e),r}constructor(e,t){if(this.configuration=e,t)this.controller=t;else{const t=new kl(e);this.controller=new Al(t)}}async initialize(){if(this.controller instanceof Al){const e=await wl(this.configuration);return null!==e&&(this.controller=e),this.controller.initialize()}return Promise.resolve()}async acquireTokenPopup(e){return this.controller.acquireTokenPopup(e)}acquireTokenRedirect(e){return this.controller.acquireTokenRedirect(e)}acquireTokenSilent(e){return this.controller.acquireTokenSilent(e)}acquireTokenByCode(e){return this.controller.acquireTokenByCode(e)}addEventCallback(e,t){return this.controller.addEventCallback(e,t)}removeEventCallback(e){return this.controller.removeEventCallback(e)}addPerformanceCallback(e){return this.controller.addPerformanceCallback(e)}removePerformanceCallback(e){return this.controller.removePerformanceCallback(e)}enableAccountStorageEvents(){this.controller.enableAccountStorageEvents()}disableAccountStorageEvents(){this.controller.disableAccountStorageEvents()}getAccount(e){return this.controller.getAccount(e)}getAccountByHomeId(e){return this.controller.getAccountByHomeId(e)}getAccountByLocalId(e){return this.controller.getAccountByLocalId(e)}getAccountByUsername(e){return this.controller.getAccountByUsername(e)}getAllAccounts(e){return this.controller.getAllAccounts(e)}handleRedirectPromise(e){return this.controller.handleRedirectPromise(e)}loginPopup(e){return this.controller.loginPopup(e)}loginRedirect(e){return this.controller.loginRedirect(e)}logout(e){return this.controller.logout(e)}logoutRedirect(e){return this.controller.logoutRedirect(e)}logoutPopup(e){return this.controller.logoutPopup(e)}ssoSilent(e){return this.controller.ssoSilent(e)}getTokenCache(){return this.controller.getTokenCache()}getLogger(){return this.controller.getLogger()}setLogger(e){this.controller.setLogger(e)}setActiveAccount(e){this.controller.setActiveAccount(e)}getActiveAccount(){return this.controller.getActiveAccount()}initializeWrapperLibrary(e,t){return this.controller.initializeWrapperLibrary(e,t)}setNavigationClient(e){this.controller.setNavigationClient(e)}getConfiguration(){return this.controller.getConfiguration()}async hydrateCache(e,t){return this.controller.hydrateCache(e,t)}clearCache(e){return this.controller.clearCache(e)}}const bl={initialize:()=>Promise.reject(Cs(us)),acquireTokenPopup:()=>Promise.reject(Cs(us)),acquireTokenRedirect:()=>Promise.reject(Cs(us)),acquireTokenSilent:()=>Promise.reject(Cs(us)),acquireTokenByCode:()=>Promise.reject(Cs(us)),getAllAccounts:()=>[],getAccount:()=>null,getAccountByHomeId:()=>null,getAccountByUsername:()=>null,getAccountByLocalId:()=>null,handleRedirectPromise:()=>Promise.reject(Cs(us)),loginPopup:()=>Promise.reject(Cs(us)),loginRedirect:()=>Promise.reject(Cs(us)),logout:()=>Promise.reject(Cs(us)),logoutRedirect:()=>Promise.reject(Cs(us)),logoutPopup:()=>Promise.reject(Cs(us)),ssoSilent:()=>Promise.reject(Cs(us)),addEventCallback:()=>null,removeEventCallback:()=>{},addPerformanceCallback:()=>"",removePerformanceCallback:()=>!1,enableAccountStorageEvents:()=>{},disableAccountStorageEvents:()=>{},getTokenCache:()=>{throw Cs(us)},getLogger:()=>{throw Cs(us)},setLogger:()=>{},setActiveAccount:()=>{},getActiveAccount:()=>null,initializeWrapperLibrary:()=>{},setNavigationClient:()=>{},getConfiguration:()=>{throw Cs(us)},hydrateCache:()=>Promise.reject(Cs(us)),clearCache:()=>Promise.reject(Cs(us))};function El(){let e;try{e=window[da.SessionStorage];const t=e?.getItem("msal.browser.performance.enabled");if(1===Number(t))return Promise.resolve().then((function(){return Ml}))}catch(e){}}function Rl(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.now}function _l(e){if(e&&Rl())return Math.round(window.performance.now()-e)}class Pl{constructor(e,t){this.correlationId=t,this.measureName=Pl.makeMeasureName(e,t),this.startMark=Pl.makeStartMark(e,t),this.endMark=Pl.makeEndMark(e,t)}static makeMeasureName(e,t){return`msal.measure.${e}.${t}`}static makeStartMark(e,t){return`msal.start.${e}.${t}`}static makeEndMark(e,t){return`msal.end.${e}.${t}`}static supportsBrowserPerformance(){return"undefined"!=typeof window&&void 0!==window.performance&&"function"==typeof window.performance.mark&&"function"==typeof window.performance.measure&&"function"==typeof window.performance.clearMarks&&"function"==typeof window.performance.clearMeasures&&"function"==typeof window.performance.getEntriesByName}static flushMeasurements(e,t){if(Pl.supportsBrowserPerformance())try{t.forEach((t=>{const r=Pl.makeMeasureName(t.name,e);window.performance.getEntriesByName(r,"measure").length>0&&(window.performance.clearMeasures(r),window.performance.clearMarks(Pl.makeStartMark(r,e)),window.performance.clearMarks(Pl.makeEndMark(r,e)))}))}catch(e){}}startMeasurement(){if(Pl.supportsBrowserPerformance())try{window.performance.mark(this.startMark)}catch(e){}}endMeasurement(){if(Pl.supportsBrowserPerformance())try{window.performance.mark(this.endMark),window.performance.measure(this.measureName,this.startMark,this.endMark)}catch(e){}}flushMeasurement(){if(Pl.supportsBrowserPerformance())try{const e=window.performance.getEntriesByName(this.measureName,"measure");if(e.length>0){const t=e[0].duration;return window.performance.clearMeasures(this.measureName),window.performance.clearMarks(this.startMark),window.performance.clearMarks(this.endMark),t}}catch(e){}return null}}var Ml=Object.freeze({__proto__:null,BrowserPerformanceMeasurement:Pl});e.AccountEntity=Er,e.ApiId=Ea,e.AuthError=ye,e.AuthErrorCodes=pe,e.AuthErrorMessage=fe,e.AuthenticationHeaderParser=class{constructor(e){this.headers=e}getShrNonce(){const e=this.headers[m];if(e){const t=this.parseChallenges(e);if(t.nextnonce)return t.nextnonce;throw gr(ir)}const t=this.headers[p];if(t){const e=this.parseChallenges(t);if(e.nonce)return e.nonce;throw gr(ir)}throw gr(or)}parseChallenges(e){const r=e.indexOf(" "),n=e.substr(r+1).split(","),o={};return n.forEach((e=>{const[r,n]=e.split("=");o[r]=unescape(n.replace(/['"]+/g,t.EMPTY_STRING))})),o}}
/*! @azure/msal-common v15.7.0 2025-06-10 */,e.AuthenticationScheme=j,e.AzureCloudInstance=vt,e.BrowserAuthError=$i,e.BrowserAuthErrorCodes=Fi,e.BrowserAuthErrorMessage=Gi,e.BrowserCacheLocation=da,e.BrowserConfigurationAuthError=ys,e.BrowserConfigurationAuthErrorCodes=ps,e.BrowserConfigurationAuthErrorMessage=fs,e.BrowserPerformanceClient=class extends Go{constructor(e,r,n){super(e.auth.clientId,e.auth.authority||`${t.DEFAULT_AUTHORITY}`,new ft(e.system?.loggerOptions||{},Bs,Fs),Bs,Fs,e.telemetry?.application||{appName:"",appVersion:""},r,n)}generateId(){return ts()}getPageVisibility(){return document.visibilityState?.toString()||null}deleteIncompleteSubMeasurements(e){El()?.then((t=>{const r=this.eventsByCorrelationId.get(e.event.correlationId),n=r&&r.eventId===e.event.eventId,o=[];n&&r?.incompleteSubMeasurements&&r.incompleteSubMeasurements.forEach((e=>{o.push({...e})})),t.BrowserPerformanceMeasurement.flushMeasurements(e.event.correlationId,o)}))}startMeasurement(e,t){const r=this.getPageVisibility(),n=super.startMeasurement(e,t),o=Rl()?window.performance.now():void 0,i=El()?.then((t=>new t.BrowserPerformanceMeasurement(e,n.event.correlationId)));return i?.then((e=>e.startMeasurement())),{...n,end:(e,t)=>{const a=n.end({...e,startPageVisibility:r,endPageVisibility:this.getPageVisibility(),durationMs:_l(o)},t);return i?.then((e=>e.endMeasurement())),this.deleteIncompleteSubMeasurements(n),a},discard:()=>{n.discard(),i?.then((e=>e.flushMeasurement())),this.deleteIncompleteSubMeasurements(n)}}}setPreQueueTime(e,t){if(!Rl())return void this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to set telemetry queue time for ${e}`);if(!t)return void this.logger.trace(`BrowserPerformanceClient: correlationId for ${e} not provided, unable to set telemetry queue time`);const r=this.preQueueTimeByCorrelationId.get(t);r&&(this.logger.trace(`BrowserPerformanceClient: Incomplete pre-queue ${r.name} found`,t),this.addQueueMeasurement(r.name,t,void 0,!0)),this.preQueueTimeByCorrelationId.set(t,{name:e,time:window.performance.now()})}addQueueMeasurement(e,t,r,n){if(!Rl())return void this.logger.trace(`BrowserPerformanceClient: window performance API not available, unable to add queue measurement for ${e}`);if(!t)return void this.logger.trace(`BrowserPerformanceClient: correlationId for ${e} not provided, unable to add queue measurement`);const o=super.getPreQueueTime(e,t);if(!o)return;const i=window.performance.now(),a=r||super.calculateQueuedTime(o,i);return super.addQueueMeasurement(e,t,a,n)}},e.BrowserPerformanceMeasurement=Pl,e.BrowserUtils=qs,e.CacheLookupPolicy=Na,e.ClientAuthError=ut,e.ClientAuthErrorCodes=lt,e.ClientAuthErrorMessage=dt,e.ClientConfigurationError=ur,e.ClientConfigurationErrorCodes=lr,e.ClientConfigurationErrorMessage=dr,e.DEFAULT_IFRAME_TIMEOUT_MS=Ds,e.EventHandler=Sc,e.EventMessageUtils=class{static getInteractionStatusFromEvent(t,r){switch(t.eventType){case pc.LOGIN_START:return _a.Login;case pc.SSO_SILENT_START:return _a.SsoSilent;case pc.ACQUIRE_TOKEN_START:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup)return _a.AcquireToken;break;case pc.HANDLE_REDIRECT_START:return _a.HandleRedirect;case pc.LOGOUT_START:return _a.Logout;case pc.SSO_SILENT_SUCCESS:case pc.SSO_SILENT_FAILURE:if(r&&r!==_a.SsoSilent)break;return _a.None;case pc.LOGOUT_END:if(r&&r!==_a.Logout)break;return _a.None;case pc.HANDLE_REDIRECT_END:if(r&&r!==_a.HandleRedirect)break;return _a.None;case pc.LOGIN_SUCCESS:case pc.LOGIN_FAILURE:case pc.ACQUIRE_TOKEN_SUCCESS:case pc.ACQUIRE_TOKEN_FAILURE:case pc.RESTORE_FROM_BFCACHE:if(t.interactionType===e.InteractionType.Redirect||t.interactionType===e.InteractionType.Popup){if(r&&r!==_a.Login&&r!==_a.AcquireToken)break;return _a.None}}return null}},e.EventType=pc,e.InteractionRequiredAuthError=po,e.InteractionRequiredAuthErrorCodes=co,e.InteractionRequiredAuthErrorMessage=go,e.InteractionStatus=_a,e.JsonWebTokenTypes=de,e.LocalStorage=uc,e.Logger=ft,e.MemoryStorage=tc,e.NavigationClient=Ls,e.OIDC_DEFAULT_SCOPES=c,e.PerformanceEvents=Hn,e.PromptValue=k,e.ProtocolMode=br,e.PublicClientApplication=Il,e.PublicClientNext=Sl,e.ServerError=Vn,e.ServerResponseType=R,e.SessionStorage=gc,e.SignedHttpRequest=class{constructor(e,t){const r=t&&t.loggerOptions||{};this.logger=new ft(r,Bs,Fs),this.cryptoOps=new nc(this.logger),this.popTokenGenerator=new vo(this.cryptoOps),this.shrParameters=e}async generatePublicKeyThumbprint(){const{kid:e}=await this.popTokenGenerator.generateKid(this.shrParameters);return e}async signRequest(e,t,r){return this.popTokenGenerator.signPayload(e,t,this.shrParameters,r)}async removeKeys(e){return this.cryptoOps.removeTokenBindingKey(e)}},e.StringUtils=pr,e.StubPerformanceClient=Fo,e.UrlString=Mr,e.WrapperSKU={React:"@azure/msal-react",Angular:"@azure/msal-angular"},e.createNestablePublicClientApplication=async function(e){const t=new Xs(e);if(await t.initialize(),t.isAvailable()){const r=new vl(t),n=new Il(e,r);return await n.initialize(),n}return Tl(e)},e.createStandardPublicClientApplication=Tl,e.isPlatformBrokerAvailable=async function(e,t,r){const n=new ft(e||{},Bs,Fs);n.trace("isPlatformBrokerAvailable called");const o=t||new Fo;return"undefined"==typeof window?(n.trace("Non-browser environment detected, returning false"),!1):!!await Yc(n,o,r||ts())},e.stubbedPublicClientApplication=bl,e.version=Fs}));
