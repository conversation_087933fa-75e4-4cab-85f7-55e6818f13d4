{"version": 3, "file": "abort.js", "sourceRoot": "", "sources": ["../../../src/common/abort.ts"], "names": [], "mappings": "AAAA,OAAO,EAAe,OAAO,EAAE,MAAM,SAAS,CAAC;AAE/C,MAAM,CAAC,MAAM,kBAAkB,GAAG,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC;AAE/D,MAAM,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;AAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;AACrB,MAAM,CAAC,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;AAEjD,MAAM,IAAI,GAAe,GAAG,EAAE,GAAE,CAAC,CAAC;AAElC;;;GAGG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CACnC,MAAoB,EACqB,EAAE;IAC3C,MAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAC;IACnC,IAAI,OAAO,GAAe,IAAI,CAAC;IAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;SAAM,CAAC;QACN,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACnC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC", "sourcesContent": ["import { IDisposable, onAbort } from './Event';\n\nexport const neverAbortedSignal = new AbortController().signal;\n\nconst cancelledSrc = new AbortController();\ncancelledSrc.abort();\nexport const abortedSignal = cancelledSrc.signal;\n\nconst noop: () => void = () => {};\n\n/**\n * Creates a new AbortController that is aborted when the parent signal aborts.\n * @private\n */\nexport const deriveAbortController = (\n  signal?: AbortSignal,\n): { ctrl: AbortController } & IDisposable => {\n  const ctrl = new AbortController();\n  let dispose: () => void = noop;\n  if (!signal) {\n    return { ctrl, dispose };\n  }\n\n  if (signal.aborted) {\n    ctrl.abort();\n  } else {\n    const abortEvt = onAbort(signal);\n    abortEvt.event(() => ctrl.abort());\n    dispose = abortEvt.dispose;\n  }\n\n  return { ctrl, dispose };\n};\n"]}