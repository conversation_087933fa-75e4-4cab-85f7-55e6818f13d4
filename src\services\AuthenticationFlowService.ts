import * as vscode from 'vscode';
import { SupportedProvider, ApiKeyManager } from './ApiKeyManager';
import { ProviderRegistryService, ProviderDetail } from './ProviderRegistryService';
import { ProviderHealthService } from './ProviderHealthService';

export interface AuthenticationGuide {
    provider: SupportedProvider;
    displayName: string;
    instructions: string[];
    apiKeyUrl: string;
    validationSteps: string[];
    troubleshooting: string[];
}

/**
 * Service for managing authentication flow and API key setup
 */
export class AuthenticationFlowService {
    private static instance: AuthenticationFlowService;
    private apiKeyManager?: ApiKeyManager;
    private registryService?: ProviderRegistryService;
    private healthService?: ProviderHealthService;

    public static getInstance(): AuthenticationFlowService {
        if (!AuthenticationFlowService.instance) {
            AuthenticationFlowService.instance = new AuthenticationFlowService();
        }
        return AuthenticationFlowService.instance;
    }

    constructor() {
        // Services will be initialized when needed
    }

    /**
     * Initialize services (called when needed)
     */
    private initializeServices(): void {
        if (!this.apiKeyManager) {
            // For now, we'll skip initialization since we don't have access to context
            // This will be properly initialized when the service is integrated
        }
    }

    /**
     * Guide user through API key setup for a provider
     */
    public async setupApiKey(provider: SupportedProvider): Promise<boolean> {
        this.initializeServices();

        if (!this.registryService) {
            vscode.window.showErrorMessage('Service not properly initialized');
            return false;
        }

        const providerDetail = this.registryService.getProvider(provider);
        if (!providerDetail) {
            vscode.window.showErrorMessage(`Provider ${provider} not found`);
            return false;
        }

        if (!providerDetail.requiresApiKey) {
            vscode.window.showInformationMessage(`Provider ${providerDetail.displayName} does not require an API key`);
            return true;
        }

        // Show setup guide
        const guide = this.getAuthenticationGuide(provider);
        const shouldProceed = await this.showSetupGuide(guide);
        
        if (!shouldProceed) {
            return false;
        }

        // Prompt for API key
        const apiKey = await this.promptForApiKey(providerDetail);
        if (!apiKey) {
            return false;
        }

        // Validate API key
        const isValid = await this.validateApiKey(providerDetail, apiKey);
        if (!isValid) {
            const retry = await vscode.window.showErrorMessage(
                'API key validation failed. Would you like to try again?',
                'Retry',
                'Cancel'
            );
            
            if (retry === 'Retry') {
                return this.setupApiKey(provider);
            }
            return false;
        }

        // Save API key
        await this.apiKeyManager?.storeApiKey(provider, apiKey);
        
        vscode.window.showInformationMessage(
            `✅ API key for ${providerDetail.displayName} has been successfully configured!`
        );
        
        return true;
    }

    /**
     * Show setup guide for a provider
     */
    private async showSetupGuide(guide: AuthenticationGuide): Promise<boolean> {
        const message = `Setting up ${guide.displayName}\n\n` +
            `To use ${guide.displayName}, you need to:\n` +
            guide.instructions.map((step, i) => `${i + 1}. ${step}`).join('\n');

        const action = await vscode.window.showInformationMessage(
            message,
            { modal: true },
            'Get API Key',
            'Continue',
            'Cancel'
        );

        if (action === 'Get API Key') {
            vscode.env.openExternal(vscode.Uri.parse(guide.apiKeyUrl));
            return this.showSetupGuide(guide);
        }

        return action === 'Continue';
    }

    /**
     * Prompt user for API key input
     */
    private async promptForApiKey(provider: ProviderDetail): Promise<string | undefined> {
        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter your ${provider.displayName} API key`,
            password: true,
            placeHolder: 'sk-...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                if (value.length < 10) {
                    return 'API key seems too short';
                }
                return null;
            }
        });

        return apiKey?.trim();
    }

    /**
     * Validate API key by making a test request
     */
    private async validateApiKey(provider: ProviderDetail, apiKey: string): Promise<boolean> {
        try {
            if (!this.healthService) {
                console.warn('Health service not available for validation');
                return true; // Assume valid if we can't check
            }

            // Show progress
            return await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Validating ${provider.displayName} API key...`,
                cancellable: false
            }, async () => {
                const healthStatus = await this.healthService!.checkProviderHealth(provider, apiKey);
                return healthStatus.isAuthenticated && healthStatus.isOnline;
            });
        } catch (error) {
            console.error('API key validation error:', error);
            return false;
        }
    }

    /**
     * Get authentication guide for a provider
     */
    private getAuthenticationGuide(provider: SupportedProvider): AuthenticationGuide {
        const guides: Partial<Record<SupportedProvider, AuthenticationGuide>> = {
            deepseek: {
                provider: 'deepseek',
                displayName: 'DeepSeek',
                instructions: [
                    'Visit the DeepSeek API platform',
                    'Sign up or log in to your account',
                    'Navigate to API Keys section',
                    'Create a new API key',
                    'Copy the API key (starts with "sk-")'
                ],
                apiKeyUrl: 'https://platform.deepseek.com/api_keys',
                validationSteps: [
                    'Test connection to DeepSeek API',
                    'Verify API key permissions',
                    'Check available models'
                ],
                troubleshooting: [
                    'Ensure API key is copied correctly',
                    'Check if API key has sufficient permissions',
                    'Verify your DeepSeek account has credits'
                ]
            },
            groq: {
                provider: 'groq',
                displayName: 'Groq',
                instructions: [
                    'Visit Groq Console',
                    'Sign up or log in to your account',
                    'Go to API Keys section',
                    'Generate a new API key',
                    'Copy the API key'
                ],
                apiKeyUrl: 'https://console.groq.com/keys',
                validationSteps: [
                    'Test connection to Groq API',
                    'Verify API key validity',
                    'Check rate limits'
                ],
                troubleshooting: [
                    'Ensure API key is active',
                    'Check rate limit status',
                    'Verify account is in good standing'
                ]
            },
            openrouter: {
                provider: 'openrouter',
                displayName: 'OpenRouter',
                instructions: [
                    'Visit OpenRouter website',
                    'Create an account or sign in',
                    'Go to Keys section in your dashboard',
                    'Create a new API key',
                    'Copy the generated key'
                ],
                apiKeyUrl: 'https://openrouter.ai/keys',
                validationSteps: [
                    'Test connection to OpenRouter API',
                    'Verify key permissions',
                    'Check available models'
                ],
                troubleshooting: [
                    'Ensure key has sufficient credits',
                    'Check if key is properly formatted',
                    'Verify account billing status'
                ]
            },
            local: {
                provider: 'local',
                displayName: 'Local',
                instructions: [
                    'Install Ollama or LM Studio',
                    'Start the local server',
                    'Ensure server is running on localhost:11434',
                    'No API key required'
                ],
                apiKeyUrl: 'https://ollama.ai/',
                validationSteps: [
                    'Check if local server is running',
                    'Test connection to localhost',
                    'Verify available models'
                ],
                troubleshooting: [
                    'Ensure Ollama/LM Studio is running',
                    'Check if port 11434 is available',
                    'Verify firewall settings'
                ]
            }
        };

        return guides[provider] || {
            provider,
            displayName: provider,
            instructions: ['Visit the provider website', 'Create an account', 'Generate an API key'],
            apiKeyUrl: '#',
            validationSteps: ['Test API connection'],
            troubleshooting: ['Check API key format', 'Verify account status']
        };
    }

    /**
     * Check if provider needs API key setup
     */
    public async needsApiKeySetup(provider: SupportedProvider): Promise<boolean> {
        if (!this.registryService || !this.apiKeyManager) {
            return true; // Assume setup needed if services not available
        }

        const providerDetail = this.registryService.getProvider(provider);
        if (!providerDetail?.requiresApiKey) {
            return false;
        }

        const hasKey = await this.apiKeyManager.hasApiKey(provider);
        if (!hasKey) {
            return true;
        }

        // Check if existing key is valid
        const apiKey = await this.apiKeyManager.getApiKey(provider);
        if (!apiKey) {
            return true;
        }

        if (!this.healthService) {
            return false; // Assume valid if we can't check
        }

        const healthStatus = await this.healthService.checkProviderHealth(providerDetail, apiKey);
        return !healthStatus.isAuthenticated;
    }

    /**
     * Show troubleshooting guide for authentication issues
     */
    public async showTroubleshootingGuide(provider: SupportedProvider): Promise<void> {
        const guide = this.getAuthenticationGuide(provider);
        
        const message = `Troubleshooting ${guide.displayName} Authentication\n\n` +
            'Common issues and solutions:\n' +
            guide.troubleshooting.map((step, i) => `${i + 1}. ${step}`).join('\n');

        const action = await vscode.window.showInformationMessage(
            message,
            { modal: true },
            'Retry Setup',
            'Get Help',
            'Close'
        );

        if (action === 'Retry Setup') {
            await this.setupApiKey(provider);
        } else if (action === 'Get Help') {
            vscode.env.openExternal(vscode.Uri.parse(guide.apiKeyUrl));
        }
    }

    /**
     * Validate all configured API keys
     */
    public async validateAllApiKeys(): Promise<Record<SupportedProvider, boolean>> {
        const results: Record<string, boolean> = {};

        if (!this.registryService || !this.apiKeyManager) {
            return results as Record<SupportedProvider, boolean>;
        }

        const providers = this.registryService.getProvidersRequiringApiKeys();

        for (const provider of providers) {
            const hasKey = await this.apiKeyManager.hasApiKey(provider.id as SupportedProvider);
            if (hasKey) {
                const apiKey = await this.apiKeyManager.getApiKey(provider.id as SupportedProvider);
                if (apiKey && this.healthService) {
                    const healthStatus = await this.healthService.checkProviderHealth(provider, apiKey);
                    results[provider.id] = healthStatus.isAuthenticated;
                } else {
                    results[provider.id] = false;
                }
            } else {
                results[provider.id] = false;
            }
        }

        return results as Record<SupportedProvider, boolean>;
    }

    /**
     * Remove API key for a provider
     */
    public async removeApiKey(provider: SupportedProvider): Promise<void> {
        if (!this.registryService || !this.apiKeyManager) {
            vscode.window.showErrorMessage('Service not properly initialized');
            return;
        }

        const providerDetail = this.registryService.getProvider(provider);
        if (!providerDetail) {
            return;
        }

        const confirm = await vscode.window.showWarningMessage(
            `Are you sure you want to remove the API key for ${providerDetail.displayName}?`,
            { modal: true },
            'Remove',
            'Cancel'
        );

        if (confirm === 'Remove') {
            await this.apiKeyManager.removeApiKey(provider);
            if (this.healthService) {
                this.healthService.clearCache(provider);
            }

            vscode.window.showInformationMessage(
                `API key for ${providerDetail.displayName} has been removed`
            );
        }
    }
}
