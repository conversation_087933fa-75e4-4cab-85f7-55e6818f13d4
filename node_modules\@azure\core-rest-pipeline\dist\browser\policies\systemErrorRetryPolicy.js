// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { systemErrorRetryPolicy as tspSystemErrorRetryPolicy, systemErrorRetryPolicyName as tspSystemErrorRetryPolicyName, } from "@typespec/ts-http-runtime/internal/policies";
/**
 * Name of the {@link systemErrorRetryPolicy}
 */
export const systemErrorRetryPolicyName = tspSystemErrorRetryPolicyName;
/**
 * A retry policy that specifically seeks to handle errors in the
 * underlying transport layer (e.g. DNS lookup failures) rather than
 * retryable error codes from the server itself.
 * @param options - Options that customize the policy.
 */
export function systemErrorRetryPolicy(options = {}) {
    return tspSystemErrorRetryPolicy(options);
}
//# sourceMappingURL=systemErrorRetryPolicy.js.map