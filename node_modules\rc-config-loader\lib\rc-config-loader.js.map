{"version": 3, "file": "rc-config-loader.js", "sourceRoot": "", "sources": ["../src/rc-config-loader.ts"], "names": [], "mappings": ";;;;;;AAAA,iBAAiB;AACjB,sBAAsB;AACtB,6CAA6C;AAC7C,gDAAwB;AACxB,4CAAoB;AACpB,8EAAoD;AACpD,kDAA0B;AAW1B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,CAAC;AAEnD,MAAM,kBAAkB,GAAuB;IAC3C,MAAM,EAAE,gBAAgB;IACxB,KAAK,EAAE,gBAAgB;IACvB,OAAO,EAAE,kBAAkB;IAC3B,OAAO,EAAE,kBAAkB;IAC3B,MAAM,EAAE,kBAAkB;CAC7B,CAAC;AAEF,MAAM,cAAc,GAC6B;IAC7C,WAAW,EAAE,KAAK;IAClB,gBAAgB,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;IAC3D,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;CACrB,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,kBAAsC,EAAE,SAAwB,EAAU,EAAE;IAC9F,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACtD,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;KACrD;IACD,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,SAAgB,MAAM,CAClB,OAAe,EACf,OAA6B,EAAE;IAE/B,6CAA6C;IAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,OAAO,IAAI,CAAC;IAC9D,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,cAAc,CAAC,gBAAgB,CAAC;IAClF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC;IAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC;IACnE,MAAM,oBAAoB,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IAE/F,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAClD,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,YAAY,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QAClF,CAAC,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;IAEzD,MAAM,WAAW,mCACV,kBAAkB,KACrB,EAAE,EAAE,cAAc,GACrB,CAAC;IACF,OAAO,UAAU,CAAI;QACjB,KAAK;QACL,WAAW;QACX,cAAc;QACd,cAAc;QACd,WAAW;QACX,oBAAoB;KACvB,CAAC,CAAC;AACP,CAAC;AA5BD,wBA4BC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CAAe,EAC9B,KAAK,EACL,WAAW,EACX,cAAc,EACd,cAAc,EACd,WAAW,EACX,oBAAoB,EAUvB;IAMG,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5C,OAAO,UAAU,CAAC,MAAM,EAAE;QACtB,MAAM,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;QAC/B,uCAAuC;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,cAAc,GAAG,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;YAChC,SAAS;SACZ;QACD,oDAAoD;QACpD,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,MAAM,GAAG,OAAO,CAAC;YACvB,MAAM,MAAM,GAAG,MAAM,CAAI,cAAc,EAAE,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YACD,OAAO;gBACH,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,cAAc;aAC3B,CAAC;SACL;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,MAAM,GAAG,MAAM,CAAI,cAAc,EAAE,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YACD,OAAO;gBACH,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,cAAc;aAC3B,CAAC;SACL;KACJ;IAED,IAAI,WAAW,EAAE;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAC/C,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC3B,MAAM,OAAO,GAAG,eAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;YAClD,IAAI,OAAO,CAAC,oBAAoB,CAAC,EAAE;gBAC/B,OAAO;oBACH,MAAM,EAAE,OAAO,CAAC,oBAAoB,CAAC;oBACrC,QAAQ,EAAE,UAAU;iBACvB,CAAC;aACL;SACJ;KACJ;IACD,IAAI,KAAK,CAAC,GAAG,EAAE,EAAE;QACb,OAAO,UAAU,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC,CAAC;KAChH;IACD,OAAO;AACX,CAAC;AAED,SAAS,SAAS,CAAC,CAAS;IACxB,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,IAAI,CAAC,KAAe,EAAE,QAAgB;IAC3C,OAAO,cAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,cAAI,CAAC,GAAG,CAAC,GAAG,cAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAgB,EAAE,QAAiB;IACzD,KAAK,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;IACrD,IAAI;QACA,MAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnD,OAAO,IAAA,6BAAiB,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KAC/C;IAAC,OAAO,KAAU,EAAE;QACjB,KAAK,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE;YACX,KAAK,CAAC,OAAO,GAAG,4BAA4B,QAAQ,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC;YAChF,MAAM,KAAK,CAAC;SACf;KACJ;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,QAAiB;IAC3D,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;IAE/C,IAAI;QACA,OAAO,eAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC1C;IAAC,OAAO,KAAU,EAAE;QACjB,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,EAAE;YACX,KAAK,CAAC,OAAO,GAAG,4BAA4B,QAAQ,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC;YAChF,MAAM,KAAK,CAAC;SACf;KACJ;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,QAAgB;IAC9B,OAAO,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,QAAiB;IAC3D,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;IAC/C,sDAAsD;IACtD,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAChC,IAAI;QACA,6CAA6C;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;KAC9C;IAAC,OAAO,KAAU,EAAE;QACjB,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,EAAE;YACX,KAAK,CAAC,OAAO,GAAG,4BAA4B,QAAQ,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC;YAChF,MAAM,KAAK,CAAC;SACf;KACJ;AACL,CAAC"}