{"version": 3, "file": "ExponentialBackoff.js", "sourceRoot": "", "sources": ["../../src/backoff/ExponentialBackoff.ts"], "names": [], "mappings": ";;;AACA,iFAA0F;AAiC1F,MAAM,cAAc,GAAoC;IACtD,SAAS,EAAE,0DAA2B;IACtC,QAAQ,EAAE,KAAK;IACf,QAAQ,EAAE,CAAC;IACX,YAAY,EAAE,GAAG;CAClB,CAAC;AAEF,MAAa,kBAAkB;IAG7B;;OAEG;IACH,YAAY,OAAgD;QAC1D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;IAC9E,CAAC;IAEM,IAAI;QACT,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;CACF;AAbD,gDAaC;AAED;;GAEG;AACH,MAAM,QAAQ,GAAG,CACf,OAAsC,EACtC,KAAS,EACT,KAAK,GAAG,CAAC,EACT,OAAO,GAAG,CAAC,CAAC,EACO,EAAE,CAAC,CAAC;IACvB,QAAQ,EAAE,KAAK;IACf,IAAI;QACF,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,OAAO,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import { IBackoff, IBackoffFactory } from './Backoff';\nimport { decorrelatedJitterGenerator, GeneratorFn } from './ExponentialBackoffGenerators';\n\n/**\n * Options passed into {@link ExponentialBackoff}.\n */\nexport interface IExponentialBackoffOptions<S> {\n  /**\n   * Delay generator function to use. This package provides several of these/\n   * Defaults to \"decorrelatedJitterGenerator\", a good default for most\n   * scenarios (see the linked Polly issue).\n   *\n   * @see https://github.com/App-vNext/Polly/issues/530\n   * @see https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/\n   */\n  generator: GeneratorFn<S>;\n\n  /**\n   * Maximum delay, in milliseconds. Defaults to 30s.\n   */\n  maxDelay: number;\n\n  /**\n   * Backoff exponent. Defaults to 2.\n   */\n  exponent: number;\n\n  /**\n   * The initial, first delay of the backoff, in milliseconds.\n   * Defaults to 128ms.\n   */\n  initialDelay: number;\n}\n\nconst defaultOptions: IExponentialBackoffOptions<any> = {\n  generator: decorrelatedJitterGenerator,\n  maxDelay: 30000,\n  exponent: 2,\n  initialDelay: 128,\n};\n\nexport class ExponentialBackoff<S> implements IBackoffFactory<unknown> {\n  private readonly options: IExponentialBackoffOptions<S>;\n\n  /**\n   * An implementation of exponential backoff.\n   */\n  constructor(options?: Partial<IExponentialBackoffOptions<S>>) {\n    this.options = options ? { ...defaultOptions, ...options } : defaultOptions;\n  }\n\n  public next() {\n    return instance(this.options).next(undefined);\n  }\n}\n\n/**\n * An implementation of exponential backoff.\n */\nconst instance = <S>(\n  options: IExponentialBackoffOptions<S>,\n  state?: S,\n  delay = 0,\n  attempt = -1,\n): IBackoff<unknown> => ({\n  duration: delay,\n  next() {\n    const [nextDelay, nextState] = options.generator(state, options);\n    return instance(options, nextState, nextDelay, attempt + 1);\n  },\n});\n"]}