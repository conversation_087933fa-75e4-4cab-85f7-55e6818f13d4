{"version": 3, "file": "OnBehalfOfClient.mjs", "sources": ["../../src/client/OnBehalfOfClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAmCH;;;AAGG;AACG,MAAO,gBAAiB,SAAQ,UAAU,CAAA;AAI5C,IAAA,WAAA,CAAY,aAAkC,EAAA;QAC1C,KAAK,CAAC,aAAa,CAAC,CAAC;KACxB;AAED;;;AAGG;IACI,MAAM,YAAY,CACrB,OAAgC,EAAA;AAEhC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;;AAGnD,QAAA,IAAI,CAAC,iBAAiB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CACtD,OAAO,CAAC,YAAY,CACvB,CAAC;AAEF,QAAA,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,mBAAmB,CAC3B,OAAO,EACP,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACL,SAAA;QAED,IAAI;AACA,YAAA,OAAO,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;AAC5D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CACjC,OAAO,EACP,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACL,SAAA;KACJ;AAED;;;;;;;AAOG;IACK,MAAM,6BAA6B,CACvC,OAAgC,EAAA;;AAGhC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,8BAA8B,CACzD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CACV,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;;YAEpB,IAAI,CAAC,sBAAsB,EAAE,eAAe,CACxC,YAAY,CAAC,sBAAsB,CACtC,CAAC;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,gGAAgG,CACnG,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,oBAAoB,CAC5C,CAAC;AACL,SAAA;AAAM,aAAA,IACH,SAAS,CAAC,cAAc,CACpB,iBAAiB,CAAC,SAAS,EAC3B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CACtD,EACH;;YAEE,IAAI,CAAC,sBAAsB,EAAE,eAAe,CACxC,YAAY,CAAC,2BAA2B,CAC3C,CAAC;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,CAAuG,oGAAA,EAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CAAA,SAAA,CAAW,CACxK,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,oBAAoB,CAC5C,CAAC;AACL,SAAA;;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CACjD,iBAAiB,CAAC,aAAa,CAClC,CAAC;AACF,QAAA,IAAI,aAAsC,CAAC;QAC3C,IAAI,aAAa,GAAyB,IAAI,CAAC;AAC/C,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,aAAa,GAAG,SAAS,CAAC,kBAAkB,CACxC,aAAa,CAAC,MAAM,EACpB,aAAa,CAAC,YAAY,CAC7B,CAAC;YACF,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,IAAI,aAAa,CAAC,GAAG,CAAC;AAC9D,YAAA,MAAM,WAAW,GAAgB;gBAC7B,aAAa,EAAE,aAAa,CAAC,aAAa;gBAC1C,WAAW,EAAE,aAAa,CAAC,WAAW;gBACtC,QAAQ,EAAE,aAAa,CAAC,KAAK;gBAC7B,QAAQ,EAAE,SAAS,CAAC,YAAY;AAChC,gBAAA,cAAc,EAAE,cAAc,IAAI,SAAS,CAAC,YAAY;aAC3D,CAAC;YAEF,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AACvE,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;AAC3D,SAAA;QAED,OAAO,eAAe,CAAC,4BAA4B,CAC/C,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd;AACI,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,WAAW,EAAE,iBAAiB;AAC9B,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,WAAW,EAAE,IAAI;AACpB,SAAA,EACD,IAAI,EACJ,OAAO,EACP,aAAa,CAChB,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,0BAA0B,CAC9B,eAAuB,EAAA;AAEvB,QAAA,MAAM,aAAa,GAAqB;AACpC,YAAA,aAAa,EAAE,eAAe;AAC9B,YAAA,WAAW,EACP,IAAI,CAAC,SAAS,CAAC,+BAA+B,CAAC,eAAe;YAClE,cAAc,EAAE,cAAc,CAAC,QAAQ;AACvC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAC1C,YAAA,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;SAC/B,CAAC;QAEF,MAAM,UAAU,GACZ,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;;QAGzD,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAkB,CAAC;KACxD;AAED;;;;AAIG;IACK,8BAA8B,CAClC,QAAgB,EAChB,OAAgC,EAAA;QAEhC,MAAM,UAAU,GACZ,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,CAAC;AAChE;;;AAGG;QACH,MAAM,cAAc,GAChB,UAAU;YACV,UAAU,CAAC,WAAW,EAAE;AACpB,gBAAA,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE;cACvC,cAAc,CAAC,6BAA6B;AAC9C,cAAE,cAAc,CAAC,YAAY,CAAC;AAEtC,QAAA,MAAM,iBAAiB,GAAqB;AACxC,YAAA,cAAc,EAAE,cAAc;YAC9B,QAAQ;YACR,MAAM,EAAE,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5D,YAAA,SAAS,EAAE,UAAU;YACrB,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;SAC5C,CAAC;QAEF,MAAM,YAAY,GACd,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;AAEjE,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC;QAC5C,IAAI,eAAe,GAAG,CAAC,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,eAAe,GAAG,CAAC,EAAE;AAC5B,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,sBAAsB,CAC9C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,YAAY,CAAC,CAAC,CAAsB,CAAC;KAC/C;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,OAAgC,EAChC,SAAoB,EACpB,iBAAyB,EAAA;QAEzB,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAA,MAAM,OAAO,GACT,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACrC,QAAA,MAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AAEF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,MAAM,eAAe,CAAC,yBAAyB,CACjE,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,iBAAiB,CACpB,CAAC;AAEF,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAgC,EAAA;AAEhC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,QAAA,uBAAuB,CAAC,WAAW,CAC/B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACnC,CAAC;QAEF,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAE9D,uBAAuB,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;AAEvE,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAElD,uBAAuB,CAAC,cAAc,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAA,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACF,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;AAED,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,uBAAuB,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEpE,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,kBAAkB,CAAC,YAAY,CAClC,CAAC;QAEF,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,OAAO,CAAC,YAAY,CACvB,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;AAC5C,YAAA,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;QAED,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AAElD,QAAA,IAAI,eAAe,EAAE;YACjB,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;YACF,uBAAuB,CAAC,sBAAsB,CAC1C,UAAU,EACV,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;QAED,IACI,OAAO,CAAC,MAAM;AACd,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,uBAAuB,CAAC,SAAS,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}