{"version": 3, "file": "FileSystemWorker.js", "mappings": ";;UAAA;UACA;;;;;WCDA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;ACNA,6CAA6C;AAC7C,8DAA8D;AAC9D,yFAAyF;AA8CzF,MAAM,gBAAgB;IAClB;QACI,uCAAuC;QACvC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAkC;QAC1D,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QAEzC,IAAI,CAAC;YACD,IAAI,MAAW,CAAC;YAEhB,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,MAAM;oBACP,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAsB,CAAC,CAAC;oBACzD,MAAM;gBACV,KAAK,QAAQ;oBACT,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAwB,CAAC,CAAC;oBAC1D,MAAM;gBACV,KAAK,OAAO;oBACR,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAuB,CAAC,CAAC;oBAC5D,MAAM;gBACV,KAAK,SAAS;oBACV,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAyB,CAAC,CAAC;oBAC3D,MAAM;gBACV;oBACI,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,WAAW,CAAC;gBACb,EAAE;gBACF,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,MAAM;aAClB,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,WAAW,CAAC;gBACb,EAAE;gBACF,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE;oBACL,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oBACjE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;iBAC1D;aACJ,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAAoB;QAC3C,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;QAE1D,sDAAsD;QACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC/D,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE3D,OAAO;YACH,SAAS;YACT,SAAS;YACT,IAAI;YACJ,aAAa,EAAE,MAAM,CAAC,MAAM;YAC5B,aAAa,EAAE,MAAM,CAAC,MAAM;SAC/B,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAsB;QAC5C,iEAAiE;QACjE,qEAAqE;QACrE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAqB;QAC9C,wEAAwE;QACxE,qEAAqE;QACrE,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;IACxF,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAuB;QAC7C,mEAAmE;QACnE,qEAAqE;QACrE,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACnF,CAAC;IAEO,kBAAkB,CAAC,MAAgB,EAAE,MAAgB,EAAE,QAAgB;QAC3E,yCAAyC;QACzC,sEAAsE;QACtE,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,CAAC,CAAC;QAEjC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACjB,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5C,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpE,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7B,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;YACR,CAAC;iBAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9E,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7B,CAAC,EAAE,CAAC;YACR,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7B,CAAC,EAAE,CAAC;YACR,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,cAAc,CAAC,IAAY;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClD,SAAS,EAAE,CAAC;YAChB,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,SAAS,EAAE,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;IACpC,CAAC;IAIO,iBAAiB,CAAC,OAAe,EAAE,KAAe;QACtD,6BAA6B;QAC7B,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QAEvE,OAAO;YACH,oBAAoB;YACpB,mBAAmB;YACnB,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM;YAClE,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;SAC1G,CAAC;IACN,CAAC;IAEO,mBAAmB,CAAC,OAAe,EAAE,QAAgB;QACzD,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,wCAAwC;QACxC,MAAM,WAAW,GAAG,yCAAyC,CAAC;QAC9D,MAAM,YAAY,GAAG,8BAA8B,CAAC;QACpD,MAAM,WAAW,GAAG,mEAAmE,CAAC;QAExF,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,+CAA+C;QAC/C,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAErE,OAAO;YACH,OAAO;YACP,QAAQ;YACR,OAAO;YACP,iBAAiB,EAAE,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;YACnD,aAAa;SAChB,CAAC;IACN,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,KAAe;QACnD,OAAO;YACH,QAAQ,EAAE,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,oCAAoC;YACxF,SAAS,EAAE,KAAK,CAAC,MAAM;YACvB,cAAc,EAAE,OAAO,CAAC,MAAM;YAC9B,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM;YACtC,iBAAiB,EAAE,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;YAChD,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7D,CAAC;IACN,CAAC;IAEO,6BAA6B,CAAC,OAAe;QACjD,+CAA+C;QAC/C,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC5F,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,kBAAkB;QAEtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,OAAO,KAAK,EAAE,GAAG,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,OAAO,EAAE,CAAC;gBACV,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,4BAA4B,CAAC,OAAe;QAChD,8CAA8C;QAC9C,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAE5B,8BAA8B;YAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,YAAY,EAAE,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxB,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;YAED,wCAAwC;YACxC,IAAI,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5C,UAAU,IAAI,CAAC,GAAG,YAAY,CAAC;YACnC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,WAAW,CAAC,QAAwB;QACxC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC;IACL,CAAC;CACJ;AAED,oBAAoB;AACpB,IAAI,gBAAgB,EAAE,CAAC", "sources": ["webpack://v1b3-sama/webpack/bootstrap", "webpack://v1b3-sama/webpack/runtime/make namespace object", "webpack://v1b3-sama/./src/workers/FileSystemWorker.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Web Worker for heavy FileSystem operations\n// Runs in background thread to keep main extension responsive\n// Note: This worker runs in a web worker context and doesn't have access to Node.js APIs\n\n// Type declarations for worker environment\ndeclare var self: any;\ninterface MessageEvent<T = any> {\n    data: T;\n}\n\nexport interface WorkerMessage {\n    id: string;\n    type: 'diff' | 'search' | 'index' | 'analyze';\n    payload: any;\n}\n\nexport interface WorkerResponse {\n    id: string;\n    type: 'success' | 'error' | 'progress';\n    payload: any;\n}\n\nexport interface DiffRequest {\n    original: string;\n    modified: string;\n    filename?: string;\n}\n\nexport interface SearchRequest {\n    searchPath: string;\n    pattern: string;\n    content?: string;\n    recursive: boolean;\n    includeHidden: boolean;\n    maxResults: number;\n}\n\nexport interface IndexRequest {\n    rootPath: string;\n    excludePatterns: string[];\n    includePatterns: string[];\n}\n\nexport interface AnalyzeRequest {\n    filePath: string;\n    analysisType: 'complexity' | 'dependencies' | 'metrics';\n}\n\nclass FileSystemWorker {\n    constructor() {\n        // Listen for messages from main thread\n        if (typeof self !== 'undefined') {\n            self.onmessage = this.handleMessage.bind(this);\n        }\n    }\n\n    private async handleMessage(event: MessageEvent<WorkerMessage>) {\n        const { id, type, payload } = event.data;\n\n        try {\n            let result: any;\n\n            switch (type) {\n                case 'diff':\n                    result = await this.generateDiff(payload as DiffRequest);\n                    break;\n                case 'search':\n                    result = await this.searchFiles(payload as SearchRequest);\n                    break;\n                case 'index':\n                    result = await this.indexDirectory(payload as IndexRequest);\n                    break;\n                case 'analyze':\n                    result = await this.analyzeFile(payload as AnalyzeRequest);\n                    break;\n                default:\n                    throw new Error(`Unknown operation type: ${type}`);\n            }\n\n            this.postMessage({\n                id,\n                type: 'success',\n                payload: result\n            });\n\n        } catch (error) {\n            this.postMessage({\n                id,\n                type: 'error',\n                payload: {\n                    message: error instanceof Error ? error.message : 'Unknown error',\n                    stack: error instanceof Error ? error.stack : undefined\n                }\n            });\n        }\n    }\n\n    private async generateDiff(request: DiffRequest): Promise<any> {\n        const { original, modified, filename = 'file' } = request;\n\n        // Use a more efficient diff algorithm for large files\n        const lines1 = original.split('\\n');\n        const lines2 = modified.split('\\n');\n\n        const diff = this.computeUnifiedDiff(lines1, lines2, filename);\n        const { additions, deletions } = this.countDiffLines(diff);\n\n        return {\n            additions,\n            deletions,\n            diff,\n            originalLines: lines1.length,\n            modifiedLines: lines2.length\n        };\n    }\n\n    private async searchFiles(request: SearchRequest): Promise<string[]> {\n        // File search operations are not supported in web worker context\n        // This would need to be handled by the main thread with VS Code APIs\n        throw new Error('File search operations must be handled by the main thread');\n    }\n\n    private async indexDirectory(request: IndexRequest): Promise<any> {\n        // Directory indexing operations are not supported in web worker context\n        // This would need to be handled by the main thread with VS Code APIs\n        throw new Error('Directory indexing operations must be handled by the main thread');\n    }\n\n    private async analyzeFile(request: AnalyzeRequest): Promise<any> {\n        // File analysis operations are not supported in web worker context\n        // This would need to be handled by the main thread with VS Code APIs\n        throw new Error('File analysis operations must be handled by the main thread');\n    }\n\n    private computeUnifiedDiff(lines1: string[], lines2: string[], filename: string): string {\n        // Simplified unified diff implementation\n        // In production, you might want to use a more sophisticated algorithm\n        const result: string[] = [];\n        result.push(`--- a/${filename}`);\n        result.push(`+++ b/${filename}`);\n\n        let i = 0, j = 0;\n        while (i < lines1.length || j < lines2.length) {\n            if (i < lines1.length && j < lines2.length && lines1[i] === lines2[j]) {\n                result.push(` ${lines1[i]}`);\n                i++;\n                j++;\n            } else if (i < lines1.length && (j >= lines2.length || lines1[i] !== lines2[j])) {\n                result.push(`-${lines1[i]}`);\n                i++;\n            } else {\n                result.push(`+${lines2[j]}`);\n                j++;\n            }\n        }\n\n        return result.join('\\n');\n    }\n\n    private countDiffLines(diff: string): { additions: number; deletions: number } {\n        const lines = diff.split('\\n');\n        let additions = 0;\n        let deletions = 0;\n\n        for (const line of lines) {\n            if (line.startsWith('+') && !line.startsWith('+++')) {\n                additions++;\n            } else if (line.startsWith('-') && !line.startsWith('---')) {\n                deletions++;\n            }\n        }\n\n        return { additions, deletions };\n    }\n\n\n\n    private analyzeComplexity(content: string, lines: string[]): any {\n        // Simple complexity analysis\n        const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);\n        const cognitiveComplexity = this.calculateCognitiveComplexity(content);\n\n        return {\n            cyclomaticComplexity,\n            cognitiveComplexity,\n            linesOfCode: lines.length,\n            nonEmptyLines: lines.filter(line => line.trim().length > 0).length,\n            commentLines: lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*')).length\n        };\n    }\n\n    private analyzeDependencies(content: string, filePath: string): any {\n        const imports: string[] = [];\n        const requires: string[] = [];\n        const exports: string[] = [];\n\n        // Extract imports/requires (simplified)\n        const importRegex = /import\\s+.*?\\s+from\\s+['\"]([^'\"]+)['\"]/g;\n        const requireRegex = /require\\(['\"]([^'\"]+)['\"]\\)/g;\n        const exportRegex = /export\\s+(?:default\\s+)?(?:class|function|const|let|var)\\s+(\\w+)/g;\n\n        let match;\n        while ((match = importRegex.exec(content)) !== null) {\n            imports.push(match[1]);\n        }\n\n        while ((match = requireRegex.exec(content)) !== null) {\n            requires.push(match[1]);\n        }\n\n        while ((match = exportRegex.exec(content)) !== null) {\n            exports.push(match[1]);\n        }\n\n        // Get file extension without using path module\n        const lastDot = filePath.lastIndexOf('.');\n        const fileExtension = lastDot > 0 ? filePath.substring(lastDot) : '';\n\n        return {\n            imports,\n            requires,\n            exports,\n            totalDependencies: imports.length + requires.length,\n            fileExtension\n        };\n    }\n\n    private analyzeMetrics(content: string, lines: string[]): any {\n        return {\n            fileSize: new TextEncoder().encode(content).length, // Use TextEncoder instead of Buffer\n            lineCount: lines.length,\n            characterCount: content.length,\n            wordCount: content.split(/\\s+/).length,\n            averageLineLength: content.length / lines.length,\n            maxLineLength: Math.max(...lines.map(line => line.length))\n        };\n    }\n\n    private calculateCyclomaticComplexity(content: string): number {\n        // Simplified cyclomatic complexity calculation\n        const keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?'];\n        let complexity = 1; // Base complexity\n\n        for (const keyword of keywords) {\n            const regex = new RegExp(`\\\\b${keyword}\\\\b`, 'g');\n            const matches = content.match(regex);\n            if (matches) {\n                complexity += matches.length;\n            }\n        }\n\n        return complexity;\n    }\n\n    private calculateCognitiveComplexity(content: string): number {\n        // Simplified cognitive complexity calculation\n        let complexity = 0;\n        let nestingLevel = 0;\n\n        const lines = content.split('\\n');\n        for (const line of lines) {\n            const trimmed = line.trim();\n            \n            // Increase nesting for blocks\n            if (trimmed.includes('{')) {\n                nestingLevel++;\n            }\n            if (trimmed.includes('}')) {\n                nestingLevel = Math.max(0, nestingLevel - 1);\n            }\n\n            // Add complexity for control structures\n            if (/\\b(if|while|for|switch)\\b/.test(trimmed)) {\n                complexity += 1 + nestingLevel;\n            }\n        }\n\n        return complexity;\n    }\n\n    private postMessage(response: WorkerResponse) {\n        if (typeof self !== 'undefined') {\n            self.postMessage(response);\n        }\n    }\n}\n\n// Initialize worker\nnew FileSystemWorker();\n"], "names": [], "sourceRoot": ""}