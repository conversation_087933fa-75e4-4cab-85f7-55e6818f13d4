{"version": 3, "file": "visualStudioCodeCredentialPlugin.js", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredentialPlugin.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A function that searches for credentials in the Visual Studio Code credential store.\n *\n * @returns an array of credentials (username and password)\n * @internal\n *\n * @deprecated This credential is deprecated because the VS Code Azure Account extension on which this credential\n * relies has been deprecated. Users should use other dev-time credentials, such as {@link AzureCliCredential},\n * {@link AzureDeveloperCliCredential}, or {@link AzurePowerShellCredential} for their\n * local development needs. See Azure Account extension deprecation notice [here](https://github.com/microsoft/vscode-azure-account/issues/964).\n */\nexport type VSCodeCredentialFinder = () => Promise<Array<{ account: string; password: string }>>;\n"]}