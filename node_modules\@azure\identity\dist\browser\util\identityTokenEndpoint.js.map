{"version": 3, "file": "identityTokenEndpoint.js", "sourceRoot": "", "sources": ["../../../src/util/identityTokenEndpoint.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,UAAU,8BAA8B,CAAC,QAAgB;IAC7D,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;QACxB,OAAO,cAAc,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,OAAO,mBAAmB,CAAC;IAC7B,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport function getIdentityTokenEndpointSuffix(tenantId: string): string {\n  if (tenantId === \"adfs\") {\n    return \"oauth2/token\";\n  } else {\n    return \"oauth2/v2.0/token\";\n  }\n}\n"]}