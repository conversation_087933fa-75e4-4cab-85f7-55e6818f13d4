{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "WebWorker"], "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noEmit": false, "declaration": false, "sourceMap": true, "outDir": "./dist/workers"}, "include": ["src/workers/**/*"], "exclude": ["node_modules", "out", "dist", "src/test"]}