{"version": 3, "file": "stylish.js", "sourceRoot": "", "sources": ["../../../src/formatters/stylish.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,YAAY,CAAC;AAIb,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,4BAA4B;AAC5B,OAAO,KAAK,MAAM,YAAY,CAAC;AAC/B,OAAO,aAAa,MAAM,cAAc,CAAC;AACzC,OAAO,SAAS,MAAM,YAAY,CAAC;AACnC,gFAAgF;AAChF,UAAU;AACV,gFAAgF;AAEhF;;;;;GAKG;AACH,SAAS,SAAS,CAAC,IAAY,EAAE,KAAa;IAC1C,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;AAC3C,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,OAAyB,EAAE,OAAyB;IACnE,gBAAgB;IAChB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAEpE,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,YAAY,GAAG,QAA4B,CAAC;IAChD,MAAM,UAAU,GAAG,OAAO,CAAC;IAE3B,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;QAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEjC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;QACzB,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAElD,MAAM,IAAI,GAAG,KAAK,CACd,QAAQ,CAAC,GAAG,CAAC,UAAU,OAAO;YAC1B,IAAI,WAAW,CAAC;YAChB,UAAU;YACV,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;YACnB,CAAC;YACD,IAAK,OAAe,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACnD,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/C,YAAY,GAAG,KAAK,CAAC;gBACrB,MAAM,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACJ,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACpD,QAAQ,EAAE,CAAC;YACf,CAAC;YAED,OAAO;gBACH,EAAE;gBACF,OAAO,CAAC,IAAI,IAAI,CAAC;gBACjB,OAAO,CAAC,MAAM,IAAI,CAAC;gBACnB,WAAW;gBACX,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;gBAClC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;aACnC,CAAC;QACN,CAAC,CAAC,EACF;YACI,KAAK,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;YACrB,YAAY,CAAC,GAAW;gBACpB,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CACjB,IAAI,EACJ,KAAK,CAAC,GAAG,CAAC,UAAU,IAAY;oBAC5B,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC,CAAC,CACL,CAAC;YACN,CAAC;SACJ,CACJ;aACI,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,UAAU,EAAU;YACrB,OAAO,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;gBAClD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAC9B;YACI,SAAS;YACT,KAAK;YACL,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC;YAC5B,IAAI;YACJ,MAAM;YACN,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;YAC3B,IAAI;YACJ,QAAQ;YACR,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC;YAC/B,KAAK;SACR,CAAC,IAAI,CAAC,EAAE,CAAC,CACb,CAAC;IACN,CAAC;IAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,YAAY,YAAY,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;QACvG,MAAM,IAAI,iBAAiB,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC;IAC5E,CAAC;IAED,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,WAAW,CAAC;AACvB,CAAC;AAED,eAAe,SAAS,CAAC"}