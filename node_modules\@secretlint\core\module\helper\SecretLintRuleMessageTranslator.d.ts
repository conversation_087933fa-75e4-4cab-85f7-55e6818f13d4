import { SecretLintRuleLocaleTag, SecretLintRuleLocalizeMessages, SecretLintRuleLocalizeMessageProps, SecretLintRuleMessageTranslateResult } from "@secretlint/types";
export declare const createTranslator: <T extends SecretLintRuleLocalizeMessages>(messages: T, options: {
    defaultLocale: SecretLintRuleLocaleTag;
}) => <MessageId extends keyof T, Props extends SecretLintRuleLocalizeMessageProps = T[MessageId]>(messageId: MessageId, props?: Props | undefined) => SecretLintRuleMessageTranslateResult<Props>;
//# sourceMappingURL=SecretLintRuleMessageTranslator.d.ts.map