import { SecretLintConfigDescriptor, SecretLintRuleLocaleTag } from "@secretlint/types";
export type SecretLintEngineOptionsBase = {
    /**
     * If cwd is not defined, cwd(current working dir) is current working dir.
     */
    cwd?: string;
    /**
     * If color is false, disable ANSI-color of the output.
     * Default: true
     */
    color?: boolean;
    /**
     * Specify formatter name for output
     */
    formatter: string;
    /**
     * If terminalLink is true, some formatter will output that includes clickable click
     * Support Terminal: https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda
     * Default: false
     */
    terminalLink?: boolean;
    /**
     * locale for rule message
     */
    locale?: SecretLintRuleLocaleTag;
    /**
     * If maskSecrets is true, mask secret values with "***".
     * If you want to hide actual secret values, set true
     * https://github.com/secretlint/secretlint/issues/176
     * Default: false
     */
    maskSecrets?: boolean;
};
export type SecretLintEngineOptionsConfigFilePath = SecretLintEngineOptionsBase & {
    /**
     * If configFilePath is not defined, search config file from cwd(current working dir)
     */
    configFilePath?: string;
};
export type SecretLintEngineOptionsConfigFileJSON = SecretLintEngineOptionsBase & {
    /**
     * configFileJSON is a json object
     * that is used instead of configFilePath.
     */
    configFileJSON: SecretLintConfigDescriptor;
};
export type SecretLintEngineOptions = SecretLintEngineOptionsConfigFilePath | SecretLintEngineOptionsConfigFileJSON;
/**
 * Create SecretLint Engine and return the instance.
 * The engine load config file(.secretlintrc) automatically
 * @param options
 */
export declare const createEngine: (options: SecretLintEngineOptions) => Promise<{
    /**
     * Lint a content and return the formatted results
     * @param content
     * @param filePath
     */
    executeOnContent: ({ content, filePath }: {
        content: string;
        filePath: string;
    }) => Promise<{
        ok: boolean;
        output: string;
    }>;
    /**
     * Lint files and return the formatted results
     * @param filePathList
     */
    executeOnFiles: ({ filePathList }: {
        filePathList: string[];
    }) => Promise<{
        ok: boolean;
        output: string;
    }>;
}>;
//# sourceMappingURL=index.d.ts.map