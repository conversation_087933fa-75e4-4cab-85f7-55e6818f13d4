import * as vscode from 'vscode';
import { Api<PERSON>eyManager, SupportedProvider } from './ApiKeyManager';
import { SettingsService } from './SettingsService';
import { ProviderHealthService } from './ProviderHealthService';
import { ProviderRegistryService } from './ProviderRegistryService';

/**
 * Model information for a provider
 */
export interface ModelInfo {
    id: string;
    name: string;
    description?: string;
    maxTokens?: number;
    supportsStreaming?: boolean;
    costPer1kTokens?: number;
}

/**
 * Provider status information
 */
export interface ProviderStatus {
    provider: SupportedProvider;
    isAvailable: boolean;
    hasApiKey: boolean;
    isOnline?: boolean;
    lastChecked?: Date;
    error?: string;
    responseTime?: number;
    apiKeyValid?: boolean;
}

/**
 * Dynamic Provider Service for V1b3-Sama Extension
 * Handles real-time provider switching, model discovery, and provider status monitoring
 */
export class DynamicProviderService {
    private _disposables: vscode.Disposable[] = [];
    private _modelCache = new Map<SupportedProvider, ModelInfo[]>();
    private _providerStatusCache = new Map<SupportedProvider, ProviderStatus>();
    private _changeEmitter = new vscode.EventEmitter<{ provider: SupportedProvider; models: ModelInfo[] }>();
    private _healthService: ProviderHealthService;
    private _registryService: ProviderRegistryService;

    /**
     * Event fired when models are updated for a provider
     */
    public readonly onDidUpdateModels = this._changeEmitter.event;

    constructor(
        private context: vscode.ExtensionContext,
        private apiKeyManager: ApiKeyManager,
        private settingsService: SettingsService
    ) {
        this._healthService = ProviderHealthService.getInstance();
        this._registryService = ProviderRegistryService.getInstance();
        this.initialize();
    }
    
    /**
     * Initialize the dynamic provider service
     */
    private initialize(): void {
        // Listen for API key changes
        this._disposables.push(
            this.apiKeyManager.onDidChangeApiKey(({ provider, hasKey }) => {
                this.handleApiKeyChange(provider, hasKey);
            })
        );
        
        // Listen for settings changes
        this._disposables.push(
            this.settingsService.onDidChangeSettings((event) => {
                if (event.section === 'core' && (event.key === 'provider' || event.key === 'model')) {
                    this.handleProviderChange(event.newValue);
                }
            })
        );
        
        // Initial load of provider statuses
        this.refreshAllProviderStatuses();
        
        this.context.subscriptions.push(...this._disposables);
    }
    
    /**
     * Handle API key changes
     */
    private async handleApiKeyChange(provider: SupportedProvider, hasKey: boolean): Promise<void> {
        // Update provider status
        const status = this._providerStatusCache.get(provider) || {
            provider,
            isAvailable: false,
            hasApiKey: false
        };
        
        status.hasApiKey = hasKey;
        status.lastChecked = new Date();
        
        if (hasKey) {
            // Refresh models for this provider
            await this.refreshModelsForProvider(provider);
        } else {
            // Clear models cache
            this._modelCache.delete(provider);
        }
        
        this._providerStatusCache.set(provider, status);
    }
    
    /**
     * Handle provider changes in settings
     */
    private async handleProviderChange(newProvider: SupportedProvider): Promise<void> {
        // Ensure models are loaded for the new provider
        await this.refreshModelsForProvider(newProvider);
        
        // Validate that the current model is available for the new provider
        const currentModel = this.settingsService.get('model');
        const availableModels = await this.getModelsForProvider(newProvider);
        
        if (!availableModels.some(model => model.id === currentModel)) {
            // Switch to the first available model
            const defaultModel = availableModels[0]?.id || 'default';
            await this.settingsService.updateConfiguration('model', defaultModel);
            
            vscode.window.showInformationMessage(
                `Switched to model "${defaultModel}" for provider ${newProvider}`
            );
        }
    }
    
    /**
     * Get available models for a provider
     */
    public async getModelsForProvider(provider: SupportedProvider): Promise<ModelInfo[]> {
        // Check cache first
        if (this._modelCache.has(provider)) {
            return this._modelCache.get(provider)!;
        }
        
        // Load models for the provider
        await this.refreshModelsForProvider(provider);
        return this._modelCache.get(provider) || [];
    }
    
    /**
     * Refresh models for a specific provider
     */
    public async refreshModelsForProvider(provider: SupportedProvider): Promise<void> {
        try {
            const providerConfig = this.apiKeyManager.getProviderConfig(provider);
            const hasApiKey = await this.apiKeyManager.hasApiKey(provider);
            
            if (providerConfig.requiresApiKey && !hasApiKey) {
                // Can't fetch models without API key
                this._modelCache.set(provider, this.getDefaultModelsForProvider(provider));
                return;
            }
            
            // For now, use default models. In a real implementation, you would fetch from the API
            const models = await this.fetchModelsFromProvider(provider);
            this._modelCache.set(provider, models);
            
            // Fire change event
            this._changeEmitter.fire({ provider, models });
            
        } catch (error) {
            console.error(`Failed to refresh models for ${provider}:`, error);
            // Fallback to default models
            this._modelCache.set(provider, this.getDefaultModelsForProvider(provider));
        }
    }
    
    /**
     * Fetch models from provider API (placeholder implementation)
     */
    private async fetchModelsFromProvider(provider: SupportedProvider): Promise<ModelInfo[]> {
        // This is a placeholder. In a real implementation, you would make HTTP requests to the provider's API
        // For now, return enhanced default models with additional metadata
        
        const defaultModels = this.getDefaultModelsForProvider(provider);
        
        // Add enhanced metadata based on provider
        return defaultModels.map(model => ({
            ...model,
            supportsStreaming: this.apiKeyManager.supportsStreaming(provider),
            maxTokens: this.getMaxTokensForModel(provider, model.id)
        }));
    }
    
    /**
     * Get default models for a provider
     */
    private getDefaultModelsForProvider(provider: SupportedProvider): ModelInfo[] {
        const providerConfig = this.apiKeyManager.getProviderConfig(provider);
        
        return providerConfig.defaultModels.map(modelId => ({
            id: modelId,
            name: this.getModelDisplayName(provider, modelId),
            description: this.getModelDescription(provider, modelId),
            maxTokens: this.getMaxTokensForModel(provider, modelId),
            supportsStreaming: providerConfig.supportsStreaming
        }));
    }
    
    /**
     * Get display name for a model
     */
    private getModelDisplayName(provider: SupportedProvider, modelId: string): string {
        const displayNames: Record<string, string> = {
            'deepseek-coder': 'DeepSeek Coder',
            'deepseek-chat': 'DeepSeek Chat',
            'gpt-4': 'GPT-4',
            'gpt-4-turbo': 'GPT-4 Turbo',
            'gpt-3.5-turbo': 'GPT-3.5 Turbo',
            'claude-3-opus-20240229': 'Claude 3 Opus',
            'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
            'claude-3-haiku-20240307': 'Claude 3 Haiku',
            'gemini-pro': 'Gemini Pro',
            'gemini-pro-vision': 'Gemini Pro Vision',
            'llama-3.1-70b-versatile': 'Llama 3.1 70B',
            'llama-3.1-8b-instant': 'Llama 3.1 8B',
            'mixtral-8x7b-32768': 'Mixtral 8x7B'
        };
        
        return displayNames[modelId] || modelId;
    }
    
    /**
     * Get description for a model
     */
    private getModelDescription(provider: SupportedProvider, modelId: string): string {
        const descriptions: Record<string, string> = {
            'deepseek-coder': 'Specialized for code generation and programming tasks',
            'deepseek-chat': 'General-purpose conversational AI',
            'gpt-4': 'Most capable GPT model for complex reasoning',
            'gpt-4-turbo': 'Faster version of GPT-4 with improved performance',
            'gpt-3.5-turbo': 'Fast and efficient for most tasks',
            'claude-3-opus-20240229': 'Most powerful Claude model for complex tasks',
            'claude-3-sonnet-20240229': 'Balanced performance and speed',
            'claude-3-haiku-20240307': 'Fastest Claude model for simple tasks',
            'gemini-pro': 'Google\'s advanced language model',
            'gemini-pro-vision': 'Multimodal model with vision capabilities',
            'llama-3.1-70b-versatile': 'Large, versatile open-source model',
            'llama-3.1-8b-instant': 'Fast, efficient open-source model',
            'mixtral-8x7b-32768': 'Mixture of experts model with large context'
        };
        
        return descriptions[modelId] || 'Language model for various tasks';
    }
    
    /**
     * Get maximum tokens for a model
     */
    private getMaxTokensForModel(provider: SupportedProvider, modelId: string): number {
        const tokenLimits: Record<string, number> = {
            'deepseek-coder': 4096,
            'deepseek-chat': 4096,
            'gpt-4': 8192,
            'gpt-4-turbo': 128000,
            'gpt-3.5-turbo': 4096,
            'claude-3-opus-20240229': 200000,
            'claude-3-sonnet-20240229': 200000,
            'claude-3-haiku-20240307': 200000,
            'gemini-pro': 32768,
            'gemini-pro-vision': 16384,
            'llama-3.1-70b-versatile': 8192,
            'llama-3.1-8b-instant': 8192,
            'mixtral-8x7b-32768': 32768
        };
        
        return tokenLimits[modelId] || 4096;
    }

    /**
     * Get all provider statuses
     */
    public async getAllProviderStatuses(): Promise<ProviderStatus[]> {
        const providers = this._registryService.getProviderIds() as SupportedProvider[];
        const statuses: ProviderStatus[] = [];

        for (const provider of providers) {
            const status = await this.getProviderStatus(provider);
            statuses.push(status);
        }

        return statuses;
    }

    /**
     * Get status for a specific provider
     */
    public async getProviderStatus(provider: SupportedProvider): Promise<ProviderStatus> {
        let status = this._providerStatusCache.get(provider);

        if (!status || this.shouldRefreshStatus(status)) {
            status = await this.checkProviderStatus(provider);
            this._providerStatusCache.set(provider, status);
        }

        return status;
    }

    /**
     * Check if provider status should be refreshed
     */
    private shouldRefreshStatus(status: ProviderStatus): boolean {
        if (!status.lastChecked) return true;

        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        return status.lastChecked < fiveMinutesAgo;
    }

    /**
     * Check provider status using real health checking
     */
    private async checkProviderStatus(provider: SupportedProvider): Promise<ProviderStatus> {
        const providerDetail = this._registryService.getProvider(provider);
        const hasApiKey = await this.apiKeyManager.hasApiKey(provider);

        if (!providerDetail) {
            return {
                provider,
                isAvailable: false,
                hasApiKey: false,
                lastChecked: new Date(),
                error: 'Provider not found in registry'
            };
        }

        // Get API key for health check if available
        let apiKey: string | undefined;
        if (hasApiKey) {
            apiKey = await this.apiKeyManager.getApiKey(provider);
        }

        // Perform real health check
        const healthStatus = await this._healthService.checkProviderHealth(providerDetail, apiKey);

        // Convert health status to provider status
        const status: ProviderStatus = {
            provider,
            isAvailable: healthStatus.isOnline && (!providerDetail.requiresApiKey || healthStatus.isAuthenticated),
            hasApiKey,
            isOnline: healthStatus.isOnline,
            lastChecked: healthStatus.lastChecked,
            responseTime: healthStatus.responseTime,
            apiKeyValid: healthStatus.apiKeyValid,
            error: healthStatus.error
        };

        // Additional validation for API key requirement
        if (providerDetail.requiresApiKey && !hasApiKey) {
            status.isAvailable = false;
            status.error = 'API key required';
        }

        return status;
    }

    /**
     * Refresh all provider statuses
     */
    public async refreshAllProviderStatuses(): Promise<void> {
        const providers = this._registryService.getProviderIds() as SupportedProvider[];

        const refreshPromises = providers.map(provider =>
            this.checkProviderStatus(provider).then(status =>
                this._providerStatusCache.set(provider, status)
            )
        );

        await Promise.all(refreshPromises);
    }

    /**
     * Force refresh health status for a specific provider
     */
    public async forceRefreshProviderHealth(provider: SupportedProvider): Promise<ProviderStatus> {
        // Clear health cache for this provider
        this._healthService.clearCache(provider);

        // Clear our status cache
        this._providerStatusCache.delete(provider);

        // Perform fresh health check
        const status = await this.checkProviderStatus(provider);
        this._providerStatusCache.set(provider, status);

        return status;
    }

    /**
     * Switch to a different provider
     */
    public async switchProvider(provider: SupportedProvider): Promise<void> {
        const status = await this.getProviderStatus(provider);

        if (!status.isAvailable) {
            throw new Error(`Provider ${provider} is not available: ${status.error}`);
        }

        // Update the provider setting
        await this.settingsService.updateConfiguration('provider', provider);

        // The handleProviderChange method will be called automatically via the settings listener
        vscode.window.showInformationMessage(`Switched to provider: ${this.apiKeyManager.getProviderDisplayName(provider)}`);
    }

    /**
     * Switch to a different model for the current provider
     */
    public async switchModel(modelId: string): Promise<void> {
        const currentProvider = this.settingsService.get('provider');
        const availableModels = await this.getModelsForProvider(currentProvider);

        const model = availableModels.find(m => m.id === modelId);
        if (!model) {
            throw new Error(`Model ${modelId} is not available for provider ${currentProvider}`);
        }

        await this.settingsService.updateConfiguration('model', modelId);
        vscode.window.showInformationMessage(`Switched to model: ${model.name}`);
    }

    /**
     * Get the current provider and model
     */
    public getCurrentConfiguration(): { provider: SupportedProvider; model: string } {
        return {
            provider: this.settingsService.get('provider'),
            model: this.settingsService.get('model')
        };
    }

    /**
     * Get available providers (those that are configured and available)
     */
    public async getAvailableProviders(): Promise<SupportedProvider[]> {
        const allStatuses = await this.getAllProviderStatuses();
        return allStatuses
            .filter(status => status.isAvailable)
            .map(status => status.provider);
    }

    /**
     * Clear model cache for a provider
     */
    public clearModelCache(provider?: SupportedProvider): void {
        if (provider) {
            this._modelCache.delete(provider);
        } else {
            this._modelCache.clear();
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._changeEmitter.dispose();
        this._modelCache.clear();
        this._providerStatusCache.clear();
    }
}
