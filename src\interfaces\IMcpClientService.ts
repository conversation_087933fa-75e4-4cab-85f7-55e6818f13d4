// MCP Client Service Interface
// Provides abstraction for Model Context Protocol communication

import { 
  McpServer, 
  McpServers, 
  McpTool, 
  McpResource, 
  McpResourceTemplate,
  McpServerStatus,
  ToggleMcpServerRequest,
  UpdateMcpTimeoutRequest,
  AddRemoteMcpServerRequest
} from '../shared/proto/mcp';

export interface McpConnectionConfig {
  serverName: string;
  serverUrl: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface McpContextData {
  workspaceRoot?: string;
  activeFile?: string;
  selectedText?: string;
  openFiles: string[];
  recentFiles: string[];
  projectStructure?: any;
  gitStatus?: any;
}

export interface McpToolCall {
  toolName: string;
  arguments: Record<string, any>;
  context?: McpContextData;
}

export interface McpToolResult {
  success: boolean;
  result?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export interface McpResourceContent {
  uri: string;
  content: string;
  mimeType?: string;
  metadata?: Record<string, any>;
}

export interface IMcpClientService {
  /**
   * Connect to an MCP server
   * @param config Connection configuration
   * @returns Promise that resolves when connected
   */
  connect(config: McpConnectionConfig): Promise<void>;

  /**
   * Disconnect from an MCP server
   * @param serverName Name of the server to disconnect
   * @returns Promise that resolves when disconnected
   */
  disconnect(serverName: string): Promise<void>;

  /**
   * Get the status of all MCP servers
   * @returns Promise resolving to server status information
   */
  getServers(): Promise<McpServers>;

  /**
   * Toggle an MCP server on/off
   * @param serverName Name of the server
   * @param disabled Whether to disable the server
   * @returns Promise resolving to updated server list
   */
  toggleServer(serverName: string, disabled: boolean): Promise<McpServers>;

  /**
   * Update the timeout for an MCP server
   * @param serverName Name of the server
   * @param timeout New timeout in milliseconds
   * @returns Promise resolving to updated server list
   */
  updateTimeout(serverName: string, timeout: number): Promise<McpServers>;

  /**
   * Add a remote MCP server
   * @param serverName Name of the server
   * @param serverUrl URL of the server
   * @returns Promise resolving to updated server list
   */
  addRemoteServer(serverName: string, serverUrl: string): Promise<McpServers>;

  /**
   * Download and install an MCP server
   * @param serverIdentifier Server identifier or URL
   * @returns Promise that resolves when download is complete
   */
  downloadServer(serverIdentifier: string): Promise<void>;

  /**
   * Discover available tools from connected servers
   * @param serverName Optional server name to filter by
   * @returns Promise resolving to available tools
   */
  discoverTools(serverName?: string): Promise<McpTool[]>;

  /**
   * Call a tool on an MCP server
   * @param toolCall Tool call configuration
   * @returns Promise resolving to tool execution result
   */
  callTool(toolCall: McpToolCall): Promise<McpToolResult>;

  /**
   * Get available resources from connected servers
   * @param serverName Optional server name to filter by
   * @returns Promise resolving to available resources
   */
  getResources(serverName?: string): Promise<McpResource[]>;

  /**
   * Get resource templates from connected servers
   * @param serverName Optional server name to filter by
   * @returns Promise resolving to available resource templates
   */
  getResourceTemplates(serverName?: string): Promise<McpResourceTemplate[]>;

  /**
   * Read content from an MCP resource
   * @param uri Resource URI
   * @param serverName Optional server name if known
   * @returns Promise resolving to resource content
   */
  readResource(uri: string, serverName?: string): Promise<McpResourceContent>;

  /**
   * Send current editor context to MCP servers
   * @param context Context data to send
   * @param serverNames Optional list of servers to send to
   * @returns Promise that resolves when context is sent
   */
  sendContext(context: McpContextData, serverNames?: string[]): Promise<void>;

  /**
   * Check if a server is connected
   * @param serverName Name of the server
   * @returns True if server is connected
   */
  isConnected(serverName: string): boolean;

  /**
   * Get the status of a specific server
   * @param serverName Name of the server
   * @returns Server status or undefined if not found
   */
  getServerStatus(serverName: string): McpServerStatus | undefined;

  /**
   * Register a callback for server status changes
   * @param callback Function to call when server status changes
   * @returns Unsubscribe function
   */
  onServerStatusChange(callback: (serverName: string, status: McpServerStatus) => void): () => void;

  /**
   * Register a callback for tool discovery updates
   * @param callback Function to call when tools are discovered
   * @returns Unsubscribe function
   */
  onToolsDiscovered(callback: (serverName: string, tools: McpTool[]) => void): () => void;

  /**
   * Dispose all connections and clean up resources
   */
  dispose(): void;
}
