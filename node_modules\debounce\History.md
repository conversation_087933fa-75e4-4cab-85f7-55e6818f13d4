
1.2.1 / 2021-03-09
==================

  * Add CONTRIBUTORS and MIT LICENSE file. (#28)

1.2.0 / 2018-08-14
==================

  * Added a .debounce member to debounce (#21)

1.1.0 / 2017-10-30
==================

  * Ability to force execution (#16)

1.0.2 / 2017-04-21
==================

 * Fixes #3 - Debounced function executing early? (#15)
 * Merge pull request #13 from selbekk/master
 * Remove date-now from package.json
 * Remove date-now dependency from component.json
 * Remove date-now usage

1.0.1 / 2016-07-25
==================

 * add ability to clear timer (#10)

1.0.0 / 2014-06-21
==================

 * Readme: attribute underscore.js in the License section
 * index: rewrite to use underscore.js' implementation (#2, @TooTallNate)
 * component, package: add "date-now" as a dependency
 * test: fix test
 * component, package: add "keywords" array
 * package: adjust "description"
 * package: added "repository" field (#1, @juliangruber)

0.0.3 / 2013-08-21
==================

 * immediate now defaults to `false`

0.0.2 / 2013-07-27
==================

 * consolidated with TJ's debounce

0.0.1 / 2012-11-5
==================

 * Initial release
