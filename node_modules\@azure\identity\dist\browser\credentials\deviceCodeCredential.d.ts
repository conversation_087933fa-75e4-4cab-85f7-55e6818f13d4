import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * Enables authentication to Microsoft Entra ID using a device code
 * that the user can enter into https://microsoft.com/devicelogin.
 */
export declare class DeviceCodeCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=deviceCodeCredential-browser.d.mts.map