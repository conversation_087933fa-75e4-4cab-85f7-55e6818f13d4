{"version": 3, "file": "file.js", "sourceRoot": "", "sources": ["../../../src/util/file.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,SAAS,oBAAoB,CAAC,CAAU;IACtC,OAAO,OAAO,CAAC,CAAC,IAAI,OAAQ,CAA2B,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC,CAAC;AAClF,CAAC;AAoCD,MAAM,oBAAoB,GAAG;IAC3B,WAAW,EAAE,GAAG,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,EAAE,GAAG,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,EAAE,GAAG,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IACD,IAAI,EAAE,GAAG,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;CACF,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,GAAkB,MAAM,CAAC,YAAY,CAAC,CAAC;AASvD;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,CAAU;IACtC,OAAO,OAAQ,CAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,CAAC;AAC7D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAC3B,IAAU;IAEV,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IAC5B,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,oBAAoB,CAClC,MAAgE,EAChE,IAAY,EACZ,UAAuC,EAAE;;IAEzC,OAAO,gCACF,oBAAoB,KACvB,IAAI,EAAE,MAAA,OAAO,CAAC,IAAI,mCAAI,EAAE,EACxB,YAAY,EAAE,MAAA,OAAO,CAAC,YAAY,mCAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,EAC1D,kBAAkB,EAAE,MAAA,OAAO,CAAC,kBAAkB,mCAAI,EAAE,EACpD,IAAI,EAAE,MAAA,OAAO,CAAC,IAAI,mCAAI,CAAC,CAAC,EACxB,IAAI,EACJ,MAAM,EAAE,GAAG,EAAE;YACX,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC;YACnB,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CACb,6EAA6E,CAC9E,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,CAAC;QACX,CAAC,EACD,CAAC,UAAU,CAAC,EAAE,MAAM,GACA,CAAC;AACzB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CACxB,OAAmB,EACnB,IAAY,EACZ,UAA6B,EAAE;;IAE/B,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,gCACF,oBAAoB,KACvB,IAAI,EAAE,MAAA,OAAO,CAAC,IAAI,mCAAI,EAAE,EACxB,YAAY,EAAE,MAAA,OAAO,CAAC,YAAY,mCAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,EAC1D,kBAAkB,EAAE,MAAA,OAAO,CAAC,kBAAkB,mCAAI,EAAE,EACpD,IAAI,EAAE,OAAO,CAAC,UAAU,EACxB,IAAI,EACJ,WAAW,EAAE,KAAK,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,EACvC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,EAC1C,CAAC,UAAU,CAAC,EAAE,GAAG,EAAE,CAAC,OAAO,GACP,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isNodeLike } from \"@azure/core-util\";\n\nfunction isNodeReadableStream(x: unknown): x is NodeJS.ReadableStream {\n  return Boolean(x && typeof (x as NodeJS.ReadableStream)[\"pipe\"] === \"function\");\n}\n\n/**\n * Options passed into createFile specifying metadata about the file.\n */\nexport interface CreateFileOptions {\n  /**\n   * The MIME type of the file.\n   */\n  type?: string;\n\n  /**\n   * Last modified time of the file as a UNIX timestamp.\n   * This will default to the current date.\n   */\n  lastModified?: number;\n\n  /**\n   * relative path of this file when uploading a directory.\n   */\n  webkitRelativePath?: string;\n}\n\n/**\n * Extra options for createFile when a stream is being passed in.\n */\nexport interface CreateFileFromStreamOptions extends CreateFileOptions {\n  /**\n   * Size of the file represented by the stream in bytes.\n   *\n   * This will be used by the pipeline when calculating the Content-Length header\n   * for the overall request.\n   */\n  size?: number;\n}\n\nconst unimplementedMethods = {\n  arrayBuffer: () => {\n    throw new Error(\"Not implemented\");\n  },\n  bytes: () => {\n    throw new Error(\"Not implemented\");\n  },\n  slice: () => {\n    throw new Error(\"Not implemented\");\n  },\n  text: () => {\n    throw new Error(\"Not implemented\");\n  },\n};\n\n/**\n * Private symbol used as key on objects created using createFile containing the\n * original source of the file object.\n *\n * This is used in Node to access the original Node stream without using Blob#stream, which\n * returns a web stream. This is done to avoid a couple of bugs to do with Blob#stream and\n * Readable#to/fromWeb in Node versions we support:\n * - https://github.com/nodejs/node/issues/42694 (fixed in Node 18.14)\n * - https://github.com/nodejs/node/issues/48916 (fixed in Node 20.6)\n *\n * Once these versions are no longer supported, we may be able to stop doing this.\n *\n * @internal\n */\nconst rawContent: unique symbol = Symbol(\"rawContent\");\n\n/**\n * Type signature of a blob-like object with a raw content property.\n */\nexport interface RawContent extends Blob {\n  [rawContent](): Uint8Array | NodeJS.ReadableStream | ReadableStream<Uint8Array>;\n}\n\n/**\n * Type guard to check if a given object is a blob-like object with a raw content property.\n */\nexport function hasRawContent(x: unknown): x is RawContent {\n  return typeof (x as RawContent)[rawContent] === \"function\";\n}\n\n/**\n * Extract the raw content from a given blob-like object. If the input was created using createFile\n * or createFileFromStream, the exact content passed into createFile/createFileFromStream will be used.\n * For true instances of Blob and File, returns the actual blob.\n *\n * @internal\n */\nexport function getRawContent(\n  blob: Blob,\n): Blob | NodeJS.ReadableStream | ReadableStream<Uint8Array> | Uint8Array {\n  if (hasRawContent(blob)) {\n    return blob[rawContent]();\n  } else {\n    return blob;\n  }\n}\n\n/**\n * Create an object that implements the File interface. This object is intended to be\n * passed into RequestBodyType.formData, and is not guaranteed to work as expected in\n * other situations.\n *\n * Use this function to:\n * - Create a File object for use in RequestBodyType.formData in environments where the\n *   global File object is unavailable.\n * - Create a File-like object from a readable stream without reading the stream into memory.\n *\n * @param stream - the content of the file as a callback returning a stream. When a File object made using createFile is\n *                  passed in a request's form data map, the stream will not be read into memory\n *                  and instead will be streamed when the request is made. In the event of a retry, the\n *                  stream needs to be read again, so this callback SHOULD return a fresh stream if possible.\n * @param name - the name of the file.\n * @param options - optional metadata about the file, e.g. file name, file size, MIME type.\n */\nexport function createFileFromStream(\n  stream: () => ReadableStream<Uint8Array> | NodeJS.ReadableStream,\n  name: string,\n  options: CreateFileFromStreamOptions = {},\n): File {\n  return {\n    ...unimplementedMethods,\n    type: options.type ?? \"\",\n    lastModified: options.lastModified ?? new Date().getTime(),\n    webkitRelativePath: options.webkitRelativePath ?? \"\",\n    size: options.size ?? -1,\n    name,\n    stream: () => {\n      const s = stream();\n      if (isNodeReadableStream(s)) {\n        throw new Error(\n          \"Not supported: a Node stream was provided as input to createFileFromStream.\",\n        );\n      }\n\n      return s;\n    },\n    [rawContent]: stream,\n  } as File & RawContent;\n}\n\n/**\n * Create an object that implements the File interface. This object is intended to be\n * passed into RequestBodyType.formData, and is not guaranteed to work as expected in\n * other situations.\n *\n * Use this function create a File object for use in RequestBodyType.formData in environments where the global File object is unavailable.\n *\n * @param content - the content of the file as a Uint8Array in memory.\n * @param name - the name of the file.\n * @param options - optional metadata about the file, e.g. file name, file size, MIME type.\n */\nexport function createFile(\n  content: Uint8Array,\n  name: string,\n  options: CreateFileOptions = {},\n): File {\n  if (isNodeLike) {\n    return {\n      ...unimplementedMethods,\n      type: options.type ?? \"\",\n      lastModified: options.lastModified ?? new Date().getTime(),\n      webkitRelativePath: options.webkitRelativePath ?? \"\",\n      size: content.byteLength,\n      name,\n      arrayBuffer: async () => content.buffer,\n      stream: () => new Blob([content]).stream(),\n      [rawContent]: () => content,\n    } as File & RawContent;\n  } else {\n    return new File([content], name, options);\n  }\n}\n"]}