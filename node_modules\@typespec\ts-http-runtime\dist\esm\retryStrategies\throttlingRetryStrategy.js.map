{"version": 3, "file": "throttlingRetryStrategy.js", "sourceRoot": "", "sources": ["../../../src/retryStrategies/throttlingRetryStrategy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAG9D;;;GAGG;AACH,MAAM,gBAAgB,GAAG,aAAa,CAAC;AACvC;;;;;;GAMG;AACH,MAAM,oBAAoB,GAAa,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;AAEnG;;;;;;;;GAQG;AACH,SAAS,iBAAiB,CAAC,QAA2B;IACpD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAAE,OAAO,SAAS,CAAC;IAC1E,IAAI,CAAC;QACH,kEAAkE;QAClE,KAAK,MAAM,MAAM,IAAI,oBAAoB,EAAE,CAAC;YAC1C,MAAM,eAAe,GAAG,wBAAwB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnE,IAAI,eAAe,KAAK,CAAC,IAAI,eAAe,EAAE,CAAC;gBAC7C,mCAAmC;gBACnC,oEAAoE;gBACpE,MAAM,iBAAiB,GAAG,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,OAAO,eAAe,GAAG,iBAAiB,CAAC,CAAC,mBAAmB;YACjE,CAAC;QACH,CAAC;QAED,2HAA2H;QAC3H,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,iFAAiF;QACjF,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/D,CAAC;IAAC,WAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,yBAAyB,CAAC,QAA2B;IACnE,OAAO,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,KAAK,CAAC,EAAE,QAAQ,EAAE;YAChB,MAAM,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YAChC,CAAC;YACD,OAAO;gBACL,cAAc;aACf,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport { parseHeaderValueAsNumber } from \"../util/helpers.js\";\nimport type { RetryStrategy } from \"./retryStrategy.js\";\n\n/**\n * The header that comes back from services representing\n * the amount of time (minimum) to wait to retry (in seconds or timestamp after which we can retry).\n */\nconst RetryAfterHeader = \"Retry-After\";\n/**\n * The headers that come back from services representing\n * the amount of time (minimum) to wait to retry.\n *\n * \"retry-after-ms\", \"x-ms-retry-after-ms\" : milliseconds\n * \"Retry-After\" : seconds or timestamp\n */\nconst AllRetryAfterHeaders: string[] = [\"retry-after-ms\", \"x-ms-retry-after-ms\", RetryAfterHeader];\n\n/**\n * A response is a throttling retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n *\n * Returns the `retryAfterInMs` value if the response is a throttling retry response.\n * If not throttling retry response, returns `undefined`.\n *\n * @internal\n */\nfunction getRetryAfterInMs(response?: PipelineResponse): number | undefined {\n  if (!(response && [429, 503].includes(response.status))) return undefined;\n  try {\n    // Headers: \"retry-after-ms\", \"x-ms-retry-after-ms\", \"Retry-After\"\n    for (const header of AllRetryAfterHeaders) {\n      const retryAfterValue = parseHeaderValueAsNumber(response, header);\n      if (retryAfterValue === 0 || retryAfterValue) {\n        // \"Retry-After\" header ==> seconds\n        // \"retry-after-ms\", \"x-ms-retry-after-ms\" headers ==> milli-seconds\n        const multiplyingFactor = header === RetryAfterHeader ? 1000 : 1;\n        return retryAfterValue * multiplyingFactor; // in milli-seconds\n      }\n    }\n\n    // RetryAfterHeader (\"Retry-After\") has a special case where it might be formatted as a date instead of a number of seconds\n    const retryAfterHeader = response.headers.get(RetryAfterHeader);\n    if (!retryAfterHeader) return;\n\n    const date = Date.parse(retryAfterHeader);\n    const diff = date - Date.now();\n    // negative diff would mean a date in the past, so retry asap with 0 milliseconds\n    return Number.isFinite(diff) ? Math.max(0, diff) : undefined;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * A response is a retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n */\nexport function isThrottlingRetryResponse(response?: PipelineResponse): boolean {\n  return Number.isFinite(getRetryAfterInMs(response));\n}\n\nexport function throttlingRetryStrategy(): RetryStrategy {\n  return {\n    name: \"throttlingRetryStrategy\",\n    retry({ response }) {\n      const retryAfterInMs = getRetryAfterInMs(response);\n      if (!Number.isFinite(retryAfterInMs)) {\n        return { skipStrategy: true };\n      }\n      return {\n        retryAfterInMs,\n      };\n    },\n  };\n}\n"]}