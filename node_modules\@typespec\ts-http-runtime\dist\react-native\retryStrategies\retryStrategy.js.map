{"version": 3, "file": "retryStrategy.js", "sourceRoot": "", "sources": ["../../../src/retryStrategies/retryStrategy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TypeSpecRuntimeLogger } from \"../logger/logger.js\";\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport type { RestError } from \"../restError.js\";\n\n/**\n * Information provided to the retry strategy about the current progress of the retry policy.\n */\nexport interface RetryInformation {\n  /**\n   * A {@link PipelineResponse}, if the last retry attempt succeeded.\n   */\n  response?: PipelineResponse;\n  /**\n   * A {@link RestError}, if the last retry attempt failed.\n   */\n  responseError?: RestError;\n  /**\n   * Total number of retries so far.\n   */\n  retryCount: number;\n}\n\n/**\n * Properties that can modify the behavior of the retry policy.\n */\nexport interface RetryModifiers {\n  /**\n   * If true, allows skipping the current strategy from running on the retry policy.\n   */\n  skipStrategy?: boolean;\n  /**\n   * Indicates to retry against this URL.\n   */\n  redirectTo?: string;\n  /**\n   * Controls whether to retry in a given number of milliseconds.\n   * If provided, a new retry will be attempted.\n   */\n  retryAfterInMs?: number;\n  /**\n   * Indicates to throw this error instead of retrying.\n   */\n  errorToThrow?: RestError;\n}\n\n/**\n * A retry strategy is intended to define whether to retry or not, and how to retry.\n */\nexport interface RetryStrategy {\n  /**\n   * Name of the retry strategy. Used for logging.\n   */\n  name: string;\n  /**\n   * Logger. If it's not provided, a default logger for all retry strategies is used.\n   */\n  logger?: TypeSpecRuntimeLogger;\n  /**\n   * Function that determines how to proceed with the subsequent requests.\n   * @param state - Retry state\n   */\n  retry(state: RetryInformation): RetryModifiers;\n}\n"]}