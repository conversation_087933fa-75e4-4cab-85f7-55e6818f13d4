/**
 * Memory Service - Refact-inspired memory management for V1b3-Sama
 * Provides persistent AI knowledge storage and retrieval
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { 
    <PERSON>moRecord, 
    MemoryQuery, 
    MemorySearchResult, 
    MemoryOperationResult,
    MemoryStats,
    MemoryConfiguration,
    MemoryContext,
    MemoryEvent,
    MemoryEventType
} from '../interfaces/MemoryTypes';

export class MemoryService {
    private memories: Map<string, MemoRecord> = new Map();
    private memoryIndex: Map<string, Set<string>> = new Map(); // tag -> memory IDs
    private searchIndex: Map<string, Set<string>> = new Map(); // word -> memory IDs
    private configuration: MemoryConfiguration;
    private context: vscode.ExtensionContext;
    private storageFile: string;
    private eventListeners: Map<MemoryEventType, ((event: MemoryEvent) => void)[]> = new Map();

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.storageFile = path.join(context.globalStorageUri.fsPath, 'memories.json');
        this.configuration = this.getDefaultConfiguration();
        this.initializeEventListeners();
    }

    /**
     * Initialize the memory service
     */
    public async initialize(): Promise<void> {
        try {
            // Ensure storage directory exists
            const storageDir = path.dirname(this.storageFile);
            if (!fs.existsSync(storageDir)) {
                fs.mkdirSync(storageDir, { recursive: true });
            }

            // Load existing memories
            await this.loadMemories();

            // Rebuild search index
            this.rebuildSearchIndex();

            console.log(`MemoryService initialized with ${this.memories.size} memories`);
        } catch (error) {
            console.error('Failed to initialize MemoryService:', error);
            throw error;
        }
    }

    /**
     * Add a new memory record
     */
    public async addMemory(
        type: string, 
        memory: string, 
        context?: MemoryContext
    ): Promise<MemoryOperationResult> {
        try {
            const record: MemoRecord = {
                iknow_id: this.generateId(),
                iknow_tags: [type, ...this.configuration.defaultTags],
                iknow_memory: memory,
                timestamp: Date.now(),
                project_id: context?.projectName || this.getCurrentProjectId(),
                context: JSON.stringify(context || {})
            };

            // Validate the record
            const validation = this.validateMemory(record);
            if (!validation.valid) {
                return {
                    success: false,
                    message: 'Memory validation failed',
                    error: validation.errors.join(', ')
                };
            }

            // Store the memory
            this.memories.set(record.iknow_id, record);
            this.updateIndices(record);

            // Save to disk
            await this.saveMemories();

            // Emit event
            this.emitEvent('memory_added', { record });

            // Auto-cleanup if needed
            if (this.configuration.autoCleanup && this.memories.size > this.configuration.maxMemories) {
                await this.performCleanup();
            }

            return {
                success: true,
                message: 'Memory added successfully',
                recordId: record.iknow_id
            };
        } catch (error) {
            console.error('Failed to add memory:', error);
            return {
                success: false,
                message: 'Failed to add memory',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Search memories by query
     */
    public async searchMemories(query: MemoryQuery): Promise<MemorySearchResult[]> {
        try {
            const results: MemorySearchResult[] = [];
            const searchTerms = query.query.toLowerCase().split(/\s+/);
            const minScore = query.minRelevanceScore || 0.1;

            for (const [id, record] of this.memories) {
                // Filter by project if specified
                if (query.project_id && record.project_id !== query.project_id) {
                    continue;
                }

                // Filter by tags if specified
                if (query.tags && !query.tags.some(tag => record.iknow_tags.includes(tag))) {
                    continue;
                }

                // Calculate relevance score
                const score = this.calculateRelevanceScore(record, searchTerms);
                
                if (score >= minScore) {
                    results.push({
                        record,
                        relevanceScore: score,
                        matchedTags: query.tags ? 
                            record.iknow_tags.filter(tag => query.tags!.includes(tag)) : 
                            record.iknow_tags
                    });
                }
            }

            // Sort by relevance score
            results.sort((a, b) => b.relevanceScore - a.relevanceScore);

            // Apply limit
            const limitedResults = query.limit ? results.slice(0, query.limit) : results;

            // Emit search event
            this.emitEvent('memory_searched', { query, resultCount: limitedResults.length });

            return limitedResults;
        } catch (error) {
            console.error('Failed to search memories:', error);
            return [];
        }
    }

    /**
     * Get memory statistics
     */
    public async getStats(): Promise<MemoryStats> {
        const memoriesByTag: { [tag: string]: number } = {};
        const memoriesByProject: { [project: string]: number } = {};
        let oldestTimestamp = Number.MAX_SAFE_INTEGER;
        let newestTimestamp = 0;

        for (const record of this.memories.values()) {
            // Count by tags
            for (const tag of record.iknow_tags) {
                memoriesByTag[tag] = (memoriesByTag[tag] || 0) + 1;
            }

            // Count by project
            if (record.project_id) {
                memoriesByProject[record.project_id] = (memoriesByProject[record.project_id] || 0) + 1;
            }

            // Track timestamps
            if (record.timestamp) {
                oldestTimestamp = Math.min(oldestTimestamp, record.timestamp);
                newestTimestamp = Math.max(newestTimestamp, record.timestamp);
            }
        }

        return {
            totalMemories: this.memories.size,
            memoriesByTag,
            memoriesByProject,
            oldestMemory: oldestTimestamp !== Number.MAX_SAFE_INTEGER ? new Date(oldestTimestamp) : undefined,
            newestMemory: newestTimestamp > 0 ? new Date(newestTimestamp) : undefined
        };
    }

    /**
     * Update memory configuration
     */
    public updateConfiguration(config: Partial<MemoryConfiguration>): void {
        this.configuration = { ...this.configuration, ...config };
        this.context.globalState.update('memoryConfiguration', this.configuration);
    }

    /**
     * Get current configuration
     */
    public getConfiguration(): MemoryConfiguration {
        return { ...this.configuration };
    }

    /**
     * Delete a memory by ID
     */
    public async deleteMemory(id: string): Promise<MemoryOperationResult> {
        try {
            const record = this.memories.get(id);
            if (!record) {
                return {
                    success: false,
                    message: 'Memory not found',
                    error: `No memory found with ID: ${id}`
                };
            }

            this.memories.delete(id);
            this.removeFromIndices(record);
            await this.saveMemories();

            this.emitEvent('memory_deleted', { recordId: id });

            return {
                success: true,
                message: 'Memory deleted successfully'
            };
        } catch (error) {
            console.error('Failed to delete memory:', error);
            return {
                success: false,
                message: 'Failed to delete memory',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Clear all memories
     */
    public async clearAllMemories(): Promise<MemoryOperationResult> {
        try {
            const count = this.memories.size;
            this.memories.clear();
            this.memoryIndex.clear();
            this.searchIndex.clear();
            await this.saveMemories();

            this.emitEvent('memory_cleanup', { clearedCount: count });

            return {
                success: true,
                message: `Cleared ${count} memories successfully`
            };
        } catch (error) {
            console.error('Failed to clear memories:', error);
            return {
                success: false,
                message: 'Failed to clear memories',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Add event listener
     */
    public addEventListener(eventType: MemoryEventType, listener: (event: MemoryEvent) => void): void {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        this.eventListeners.get(eventType)!.push(listener);
    }

    /**
     * Remove event listener
     */
    public removeEventListener(eventType: MemoryEventType, listener: (event: MemoryEvent) => void): void {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Load memories from disk
     */
    private async loadMemories(): Promise<void> {
        try {
            if (fs.existsSync(this.storageFile)) {
                const data = fs.readFileSync(this.storageFile, 'utf8');
                const memoriesData = JSON.parse(data);

                if (memoriesData.memories) {
                    for (const record of memoriesData.memories) {
                        this.memories.set(record.iknow_id, record);
                    }
                }

                if (memoriesData.configuration) {
                    this.configuration = { ...this.configuration, ...memoriesData.configuration };
                }
            }
        } catch (error) {
            console.error('Failed to load memories:', error);
        }
    }

    /**
     * Save memories to disk
     */
    private async saveMemories(): Promise<void> {
        try {
            const data = {
                version: '1.0.0',
                timestamp: Date.now(),
                memories: Array.from(this.memories.values()),
                configuration: this.configuration
            };

            fs.writeFileSync(this.storageFile, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Failed to save memories:', error);
            throw error;
        }
    }

    /**
     * Generate unique ID for memory record
     */
    private generateId(): string {
        return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get default configuration
     */
    private getDefaultConfiguration(): MemoryConfiguration {
        const saved = this.context.globalState.get<MemoryConfiguration>('memoryConfiguration');
        return saved || {
            maxMemories: 1000,
            autoCleanup: true,
            cleanupThresholdDays: 30,
            enableProjectIsolation: true,
            defaultTags: ['general']
        };
    }

    /**
     * Get current project ID
     */
    private getCurrentProjectId(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return path.basename(workspaceFolders[0].uri.fsPath);
        }
        return 'default';
    }

    /**
     * Validate memory record
     */
    private validateMemory(record: MemoRecord): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!record.iknow_id || record.iknow_id.trim() === '') {
            errors.push('Memory ID is required');
        }

        if (!record.iknow_memory || record.iknow_memory.trim() === '') {
            errors.push('Memory content is required');
        }

        if (record.iknow_memory && record.iknow_memory.length > 10000) {
            errors.push('Memory content is too long (max 10000 characters)');
        }

        if (!record.iknow_tags || record.iknow_tags.length === 0) {
            errors.push('At least one tag is required');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Update search indices
     */
    private updateIndices(record: MemoRecord): void {
        // Update tag index
        for (const tag of record.iknow_tags) {
            if (!this.memoryIndex.has(tag)) {
                this.memoryIndex.set(tag, new Set());
            }
            this.memoryIndex.get(tag)!.add(record.iknow_id);
        }

        // Update search index
        const words = record.iknow_memory.toLowerCase().split(/\s+/);
        for (const word of words) {
            if (word.length > 2) { // Skip very short words
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, new Set());
                }
                this.searchIndex.get(word)!.add(record.iknow_id);
            }
        }
    }

    /**
     * Remove from indices
     */
    private removeFromIndices(record: MemoRecord): void {
        // Remove from tag index
        for (const tag of record.iknow_tags) {
            const tagSet = this.memoryIndex.get(tag);
            if (tagSet) {
                tagSet.delete(record.iknow_id);
                if (tagSet.size === 0) {
                    this.memoryIndex.delete(tag);
                }
            }
        }

        // Remove from search index
        const words = record.iknow_memory.toLowerCase().split(/\s+/);
        for (const word of words) {
            const wordSet = this.searchIndex.get(word);
            if (wordSet) {
                wordSet.delete(record.iknow_id);
                if (wordSet.size === 0) {
                    this.searchIndex.delete(word);
                }
            }
        }
    }

    /**
     * Rebuild search index
     */
    private rebuildSearchIndex(): void {
        this.memoryIndex.clear();
        this.searchIndex.clear();

        for (const record of this.memories.values()) {
            this.updateIndices(record);
        }
    }

    /**
     * Calculate relevance score for search
     */
    private calculateRelevanceScore(record: MemoRecord, searchTerms: string[]): number {
        let score = 0;
        const content = record.iknow_memory.toLowerCase();
        const tags = record.iknow_tags.map(tag => tag.toLowerCase());

        for (const term of searchTerms) {
            // Exact match in content
            if (content.includes(term)) {
                score += 0.5;
            }

            // Tag match
            if (tags.some(tag => tag.includes(term))) {
                score += 0.3;
            }

            // Word boundary match (higher score)
            const wordRegex = new RegExp(`\\b${term}\\b`, 'i');
            if (wordRegex.test(content)) {
                score += 0.2;
            }
        }

        // Normalize by content length (prefer shorter, more relevant content)
        const lengthFactor = Math.min(1, 1000 / record.iknow_memory.length);
        score *= lengthFactor;

        // Boost recent memories slightly
        if (record.timestamp) {
            const daysSinceCreation = (Date.now() - record.timestamp) / (1000 * 60 * 60 * 24);
            const recencyBoost = Math.max(0, 1 - daysSinceCreation / 365); // Boost fades over a year
            score += recencyBoost * 0.1;
        }

        return Math.min(1, score); // Cap at 1.0
    }

    /**
     * Perform automatic cleanup
     */
    private async performCleanup(): Promise<void> {
        const cutoffTime = Date.now() - (this.configuration.cleanupThresholdDays * 24 * 60 * 60 * 1000);
        const toDelete: string[] = [];

        for (const [id, record] of this.memories) {
            if (record.timestamp && record.timestamp < cutoffTime) {
                toDelete.push(id);
            }
        }

        // Delete oldest memories if still over limit
        if (this.memories.size > this.configuration.maxMemories) {
            const sortedMemories = Array.from(this.memories.entries())
                .sort((a, b) => (a[1].timestamp || 0) - (b[1].timestamp || 0));

            const excessCount = this.memories.size - this.configuration.maxMemories;
            for (let i = 0; i < excessCount; i++) {
                toDelete.push(sortedMemories[i][0]);
            }
        }

        // Perform deletion
        for (const id of toDelete) {
            const record = this.memories.get(id);
            if (record) {
                this.memories.delete(id);
                this.removeFromIndices(record);
            }
        }

        if (toDelete.length > 0) {
            await this.saveMemories();
            this.emitEvent('memory_cleanup', { deletedCount: toDelete.length });
        }
    }

    /**
     * Initialize event listeners
     */
    private initializeEventListeners(): void {
        // Initialize empty event listener maps
        const eventTypes: MemoryEventType[] = [
            'memory_added', 'memory_updated', 'memory_deleted', 'memory_searched',
            'memory_migrated', 'memory_exported', 'memory_imported', 'memory_cleanup'
        ];

        for (const eventType of eventTypes) {
            this.eventListeners.set(eventType, []);
        }
    }

    /**
     * Emit memory event
     */
    private emitEvent(type: MemoryEventType, data: any): void {
        const event: MemoryEvent = {
            type,
            timestamp: Date.now(),
            data,
            context: {
                workspaceRoot: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath,
                activeFile: vscode.window.activeTextEditor?.document.fileName,
                projectName: this.getCurrentProjectId()
            }
        };

        const listeners = this.eventListeners.get(type);
        if (listeners) {
            for (const listener of listeners) {
                try {
                    listener(event);
                } catch (error) {
                    console.error(`Error in memory event listener for ${type}:`, error);
                }
            }
        }
    }
}
