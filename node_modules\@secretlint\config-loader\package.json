{"name": "@secretlint/config-loader", "version": "9.3.4", "description": "Config loader for secretlint.", "keywords": ["secretlint", "node", "config", "loader"], "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/@secretlint/config-loader/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "type": "module", "exports": {".": {"import": {"types": "./module/index.d.ts", "default": "./module/index.js"}, "default": "./module/index.js"}, "./package.json": "./package.json"}, "main": "./module/index.js", "types": "./module/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "module/", "src/"], "scripts": {"build": "tsc --build", "clean": "tsc --build --clean", "create-validation": "create-validator-ts src/descriptor-types.ts --additionalProperties", "prepublishOnly": "npm run clean && npm run build", "prettier": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublish": "npm run --if-present build", "test": "mocha", "updateSnapshot": "UPDATE_SNAPSHOT=1 npm test", "watch": "tsc --build --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "dependencies": {"@secretlint/profiler": "^9.3.4", "@secretlint/resolver": "^9.3.4", "@secretlint/types": "^9.3.4", "ajv": "^8.17.1", "debug": "^4.4.1", "rc-config-loader": "^4.1.3"}, "devDependencies": {"@secretlint/secretlint-rule-internal-test-cjs": "^9.3.4", "@secretlint/secretlint-rule-internal-test-esm": "^9.3.4", "@types/mocha": "^10.0.10", "@types/node": "^20.17.51", "create-validator-ts": "^4.0.2", "mocha": "^10.8.2", "prettier": "^2.8.1", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "packageManager": "yarn@1.22.22", "engines": {"node": "^14.13.1 || >=16.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "1b0bebd91380d94ee0a756900ce9b39c8b3cfd1b"}