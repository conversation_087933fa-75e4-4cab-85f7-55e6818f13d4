{"version": 3, "file": "SecretLintRuleTranslator.d.ts", "sourceRoot": "", "sources": ["../src/SecretLintRuleTranslator.ts"], "names": [], "mappings": "AACA,MAAM,MAAM,uBAAuB,GAC7B,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,OAAO,GACP,OAAO,GACP,MAAM,CAAC;AACb,MAAM,MAAM,kCAAkC,GAAG;IAAE,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,CAAC;AAE1E,MAAM,MAAM,oCAAoC,CAAC,KAAK,SAAS,kCAAkC,IAAI,CACjG,KAAK,CAAC,EAAE,KAAK,KACZ,MAAM,CAAC;AACZ,MAAM,MAAM,kCAAkC,CAAC,KAAK,SAAS,kCAAkC,IAAI;KAC9F,CAAC,IAAI,uBAAuB,GAAG,oCAAoC,CAAC,KAAK,CAAC;CAC9E,GAAG;IAEA,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;CAC7B,CAAC;AACF;;;;;;;;;;GAUG;AACH,MAAM,MAAM,8BAA8B,GAAG;IAEzC,CAAC,KAAK,EAAE,MAAM,GAAG,kCAAkC,CAAC,GAAG,CAAC,CAAC;CAC5D,CAAC;AAEF,MAAM,MAAM,oCAAoC,CAAC,KAAK,SAAS,kCAAkC,IAAI;IACjG,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,KAAK,GAAG,SAAS,CAAC;CAC3B,CAAC;AACF,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7B,MAAM,MAAM,8BAA8B,CACtC,CAAC,SAAS,8BAA8B,EACxC,SAAS,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,EAEnC,KAAK,SAAS,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IACzF,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,oCAAoC,CAAC,KAAK,CAAC,CAAC;AAEzF,MAAM,MAAM,qCAAqC,CAAC,CAAC,SAAS,8BAA8B,IAAI,CAC1F,QAAQ,EAAE,CAAC,KACV,8BAA8B,CAAC,CAAC,CAAC,CAAC"}