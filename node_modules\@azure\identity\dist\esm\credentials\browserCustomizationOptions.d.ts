/**
 * Shared configuration options for browser customization
 */
export interface BrowserCustomizationOptions {
    /**
     * Shared configuration options for browser customization
     */
    browserCustomizationOptions?: {
        /**
         * Format for error messages for display in browser
         */
        errorMessage?: string;
        /**
         * Format for success messages for display in browser
         */
        successMessage?: string;
    };
}
//# sourceMappingURL=browserCustomizationOptions.d.ts.map