// AI Tools Service Implementation
// Provides automatic tools that AI can call for file operations and system tasks

import * as vscode from 'vscode';
import { IAIToolsService, AITool, AIToolCall, AIToolResult } from '../interfaces/IAIToolsService';
import { IFileSystemService } from '../interfaces/IFileSystemService';
import { ITerminalService } from '../interfaces/ITerminalService';
import { AutoApprovalManager } from './AutoApprovalManager';
import { ActionHistoryManager } from '../actionHistoryManager';
import { FileOperationsService } from './FileOperationsService';
import { WebSearchService } from './WebSearchService';

export class AIToolsService implements IAIToolsService {
    private tools: Map<string, AITool> = new Map();
    private fileOpsService: FileOperationsService;
    private webSearchService: WebSearchService;

    constructor(
        private fileSystemService: IFileSystemService,
        private terminalService: ITerminalService,
        private autoApprovalManager: AutoApprovalManager,
        private actionHistoryManager: ActionHistoryManager,
        context: vscode.ExtensionContext
    ) {
        this.fileOpsService = new FileOperationsService(context);
        this.webSearchService = new WebSearchService(context);
        this.registerDefaultTools();
    }

    public getAvailableTools(): AITool[] {
        return Array.from(this.tools.values());
    }

    public async executeTool(toolCall: AIToolCall): Promise<AIToolResult> {
        const tool = this.tools.get(toolCall.name);
        if (!tool) {
            return {
                success: false,
                error: `Tool '${toolCall.name}' not found`
            };
        }

        try {
            // Check permissions for the tool
            if (!this.checkToolPermissions(toolCall.name)) {
                return {
                    success: false,
                    error: `Permission denied for tool '${toolCall.name}'. Check auto-approval settings.`
                };
            }

            const result = await tool.handler(toolCall.parameters);
            
            // Log the action
            this.actionHistoryManager.logAction(
                `AI Tool: ${toolCall.name} - ${result.success ? 'Success' : 'Failed'}`
            );

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                error: `Tool execution failed: ${errorMessage}`
            };
        }
    }

    public registerTool(tool: AITool): void {
        this.tools.set(tool.name, tool);
    }

    public unregisterTool(toolName: string): void {
        this.tools.delete(toolName);
    }

    public hasToolAvailable(toolName: string): boolean {
        return this.tools.has(toolName);
    }

    private checkToolPermissions(toolName: string): boolean {
        // Map tool names to approval categories
        const toolPermissions: Record<string, string> = {
            'create_file': 'createFiles',
            'write_file': 'modifyFiles',
            'read_file': 'readProjectFiles',
            'delete_file': 'deleteFiles',
            'list_files': 'readProjectFiles',
            'create_directory': 'createFiles',
            'execute_command': 'executeCommands',
            'search_files': 'readProjectFiles',
            'web_search': 'useBrowser',
            'search_technical': 'useBrowser',
            'search_error': 'useBrowser'
        };

        const permission = toolPermissions[toolName];
        if (!permission) {
            return true; // Allow unknown tools by default
        }

        return this.autoApprovalManager.checkApproval(permission);
    }

    private registerDefaultTools(): void {
        // File creation tool
        this.registerTool({
            name: 'create_file',
            description: 'Create a new file with specified content',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'File path relative to workspace root'
                    },
                    content: {
                        type: 'string',
                        description: 'File content'
                    }
                },
                required: ['path', 'content']
            },
            handler: async (params) => this.handleCreateFile(params)
        });

        // File writing tool
        this.registerTool({
            name: 'write_file',
            description: 'Write content to an existing file or create if it doesn\'t exist',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'File path relative to workspace root'
                    },
                    content: {
                        type: 'string',
                        description: 'File content'
                    }
                },
                required: ['path', 'content']
            },
            handler: async (params) => this.handleWriteFile(params)
        });

        // File reading tool
        this.registerTool({
            name: 'read_file',
            description: 'Read the contents of a file',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'File path relative to workspace root'
                    }
                },
                required: ['path']
            },
            handler: async (params) => this.handleReadFile(params)
        });

        // File deletion tool
        this.registerTool({
            name: 'delete_file',
            description: 'Delete a file',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'File path relative to workspace root'
                    }
                },
                required: ['path']
            },
            handler: async (params) => this.handleDeleteFile(params)
        });

        // Directory listing tool
        this.registerTool({
            name: 'list_files',
            description: 'List files in a directory',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Directory path relative to workspace root'
                    },
                    recursive: {
                        type: 'boolean',
                        description: 'Whether to list files recursively'
                    }
                },
                required: ['path']
            },
            handler: async (params) => this.handleListFiles(params)
        });

        // Directory creation tool
        this.registerTool({
            name: 'create_directory',
            description: 'Create a new directory',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Directory path relative to workspace root'
                    }
                },
                required: ['path']
            },
            handler: async (params) => this.handleCreateDirectory(params)
        });

        // Command execution tool
        this.registerTool({
            name: 'execute_command',
            description: 'Execute a shell command',
            parameters: {
                type: 'object',
                properties: {
                    command: {
                        type: 'string',
                        description: 'Command to execute'
                    },
                    workingDirectory: {
                        type: 'string',
                        description: 'Working directory for the command'
                    }
                },
                required: ['command']
            },
            handler: async (params) => this.handleExecuteCommand(params)
        });

        // File search tool
        this.registerTool({
            name: 'search_files',
            description: 'Search for files by pattern and content',
            parameters: {
                type: 'object',
                properties: {
                    pattern: {
                        type: 'string',
                        description: 'File pattern to search for'
                    },
                    content: {
                        type: 'string',
                        description: 'Content to search for within files'
                    },
                    path: {
                        type: 'string',
                        description: 'Directory to search in'
                    }
                },
                required: ['pattern']
            },
            handler: async (params) => this.handleSearchFiles(params)
        });

        // File rename tool
        this.registerTool({
            name: 'rename_file',
            description: 'Rename or move a file',
            parameters: {
                type: 'object',
                properties: {
                    oldPath: {
                        type: 'string',
                        description: 'Current file path relative to workspace root'
                    },
                    newPath: {
                        type: 'string',
                        description: 'New file path relative to workspace root'
                    }
                },
                required: ['oldPath', 'newPath']
            },
            handler: async (params) => this.handleRenameFile(params)
        });

        // Get directory structure tool
        this.registerTool({
            name: 'get_directory_structure',
            description: 'Get the directory structure of a path',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'Directory path relative to workspace root'
                    },
                    maxDepth: {
                        type: 'number',
                        description: 'Maximum depth to traverse'
                    }
                },
                required: ['path']
            },
            handler: async (params) => this.handleGetDirectoryStructure(params)
        });

        // File diff tool
        this.registerTool({
            name: 'get_file_diff',
            description: 'Get diff between current file content and new content',
            parameters: {
                type: 'object',
                properties: {
                    path: {
                        type: 'string',
                        description: 'File path relative to workspace root'
                    },
                    newContent: {
                        type: 'string',
                        description: 'New content to compare against'
                    }
                },
                required: ['path', 'newContent']
            },
            handler: async (params) => this.handleGetFileDiff(params)
        });

        // Web search tool
        this.registerTool({
            name: 'web_search',
            description: 'Search the web for current information',
            parameters: {
                type: 'object',
                properties: {
                    query: {
                        type: 'string',
                        description: 'Search query'
                    },
                    maxResults: {
                        type: 'number',
                        description: 'Maximum number of results to return'
                    }
                },
                required: ['query']
            },
            handler: async (params) => this.handleWebSearch(params)
        });

        // Technical search tool
        this.registerTool({
            name: 'search_technical',
            description: 'Search for technical/programming information',
            parameters: {
                type: 'object',
                properties: {
                    query: {
                        type: 'string',
                        description: 'Technical search query'
                    },
                    includeStackOverflow: {
                        type: 'boolean',
                        description: 'Include Stack Overflow results'
                    },
                    includeGitHub: {
                        type: 'boolean',
                        description: 'Include GitHub results'
                    }
                },
                required: ['query']
            },
            handler: async (params) => this.handleTechnicalSearch(params)
        });

        // Error search tool
        this.registerTool({
            name: 'search_error',
            description: 'Search for solutions to error messages',
            parameters: {
                type: 'object',
                properties: {
                    errorMessage: {
                        type: 'string',
                        description: 'Error message to search for'
                    },
                    technology: {
                        type: 'string',
                        description: 'Technology/language context'
                    }
                },
                required: ['errorMessage']
            },
            handler: async (params) => this.handleErrorSearch(params)
        });
    }

    private async handleCreateFile(params: any): Promise<AIToolResult> {
        try {
            const { path, content } = params;
            const result = await this.fileOpsService.createFile(path, content);

            if (result.success) {
                return {
                    success: true,
                    result: `File created: ${path}`,
                    metadata: {
                        filesCreated: [path]
                    }
                };
            } else {
                return {
                    success: false,
                    error: result.error || 'Failed to create file'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleWriteFile(params: any): Promise<AIToolResult> {
        try {
            const { path, content } = params;
            const exists = await this.fileOpsService.fileExists(path);

            let result;
            if (exists) {
                result = await this.fileOpsService.modifyFile(path, content);
            } else {
                result = await this.fileOpsService.createFile(path, content);
            }

            if (result.success) {
                return {
                    success: true,
                    result: `File ${exists ? 'updated' : 'created'}: ${path}`,
                    metadata: exists ? { filesModified: [path] } : { filesCreated: [path] }
                };
            } else {
                return {
                    success: false,
                    error: result.error || 'Failed to write file'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleReadFile(params: any): Promise<AIToolResult> {
        try {
            const { path } = params;
            const content = await this.fileOpsService.readFile(path);

            return {
                success: true,
                result: content
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleDeleteFile(params: any): Promise<AIToolResult> {
        try {
            const { path } = params;
            const result = await this.fileOpsService.deleteFile(path);

            if (result.success) {
                return {
                    success: true,
                    result: `File deleted: ${path}`,
                    metadata: {
                        filesDeleted: [path]
                    }
                };
            } else {
                return {
                    success: false,
                    error: result.error || 'Failed to delete file'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleListFiles(params: any): Promise<AIToolResult> {
        try {
            const { path, recursive = false } = params;
            const files = await this.fileSystemService.listFiles(path, recursive);
            
            return {
                success: true,
                result: files
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleCreateDirectory(params: any): Promise<AIToolResult> {
        try {
            const { path } = params;
            await this.fileSystemService.createDirectory(path);
            
            return {
                success: true,
                result: `Directory created: ${path}`
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleExecuteCommand(params: any): Promise<AIToolResult> {
        try {
            const { command, workingDirectory } = params;
            const result = await this.terminalService.executeCommand(command, {
                cwd: workingDirectory
            });

            return {
                success: result.exitCode === 0,
                result: result.stdout || result.stderr,
                metadata: {
                    commandsExecuted: [command],
                    exitCode: result.exitCode,
                    duration: result.duration
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleSearchFiles(params: any): Promise<AIToolResult> {
        try {
            const { pattern, content, path = '.' } = params;
            const files = await this.fileOpsService.searchFiles(pattern, path);

            return {
                success: true,
                result: files
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleRenameFile(params: any): Promise<AIToolResult> {
        try {
            const { oldPath, newPath } = params;
            const result = await this.fileOpsService.renameFile(oldPath, newPath);

            if (result.success) {
                return {
                    success: true,
                    result: `File renamed: ${oldPath} → ${newPath}`,
                    metadata: {
                        filesModified: [newPath],
                        filesDeleted: [oldPath]
                    }
                };
            } else {
                return {
                    success: false,
                    error: result.error || 'Failed to rename file'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleGetDirectoryStructure(params: any): Promise<AIToolResult> {
        try {
            const { path = '', maxDepth = 3 } = params;
            const structure = await this.fileOpsService.getDirectoryStructure(path, maxDepth);

            return {
                success: true,
                result: structure
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleGetFileDiff(params: any): Promise<AIToolResult> {
        try {
            const { path, newContent } = params;
            const diff = await this.fileOpsService.getFileDiff(path, newContent);

            return {
                success: true,
                result: diff
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleWebSearch(params: any): Promise<AIToolResult> {
        try {
            if (!this.webSearchService.isWebSearchEnabled()) {
                return {
                    success: false,
                    error: 'Web search is disabled in settings'
                };
            }

            const { query, maxResults = 5 } = params;
            const searchResponse = await this.webSearchService.search(query, { maxResults });

            return {
                success: true,
                result: this.webSearchService.formatSearchResults(searchResponse),
                metadata: {
                    searchQuery: query,
                    resultsCount: searchResponse.results.length,
                    searchTime: searchResponse.searchTime
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleTechnicalSearch(params: any): Promise<AIToolResult> {
        try {
            if (!this.webSearchService.isWebSearchEnabled()) {
                return {
                    success: false,
                    error: 'Web search is disabled in settings'
                };
            }

            const { query, includeStackOverflow = true, includeGitHub = true } = params;
            const searchResponse = await this.webSearchService.searchTechnical(query, {
                includeStackOverflow,
                includeGitHub,
                includeDocs: true
            });

            return {
                success: true,
                result: this.webSearchService.formatSearchResults(searchResponse),
                metadata: {
                    searchQuery: query,
                    resultsCount: searchResponse.results.length,
                    searchTime: searchResponse.searchTime,
                    searchType: 'technical'
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async handleErrorSearch(params: any): Promise<AIToolResult> {
        try {
            if (!this.webSearchService.isWebSearchEnabled()) {
                return {
                    success: false,
                    error: 'Web search is disabled in settings'
                };
            }

            const { errorMessage, technology } = params;
            const searchResponse = await this.webSearchService.searchError(errorMessage, technology);

            return {
                success: true,
                result: this.webSearchService.formatSearchResults(searchResponse),
                metadata: {
                    errorMessage,
                    technology,
                    resultsCount: searchResponse.results.length,
                    searchTime: searchResponse.searchTime,
                    searchType: 'error'
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}
