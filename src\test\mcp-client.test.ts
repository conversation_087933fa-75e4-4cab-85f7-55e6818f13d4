// Test file for MCP Client Service
// Tests the real gRPC implementation

import * as assert from 'assert';
import * as sinon from 'sinon';
import { McpClientService } from '../services/McpClientService';
import { McpServerStatus } from '../shared/proto/mcp';
import { McpConnectionConfig } from '../interfaces/IMcpClientService';

describe('McpClientService - gRPC Implementation', () => {
    let mcpClientService: McpClientService;
    let sandbox: sinon.SinonSandbox;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        mcpClientService = new McpClientService();
    });

    afterEach(() => {
        sandbox.restore();
        mcpClientService.dispose();
    });

    describe('Proto Definition Loading', () => {
        it('should load proto definitions successfully', () => {
            // The service should be created without throwing errors
            assert.ok(mcpClientService);
        });

        it('should have packageDefinition loaded', () => {
            // Access private property for testing
            const packageDefinition = (mcpClientService as any).packageDefinition;
            assert.ok(packageDefinition, 'Package definition should be loaded');
        });
    });

    describe('Connection Management', () => {
        it('should create proper credentials for HTTPS URLs', () => {
            const createCredentials = (mcpClientService as any).createCredentials.bind(mcpClientService);
            
            // Test HTTPS URL
            const httpsCredentials = createCredentials('https://example.com:443');
            assert.ok(httpsCredentials, 'Should create SSL credentials for HTTPS');
            
            // Test HTTP URL
            const httpCredentials = createCredentials('http://localhost:50051');
            assert.ok(httpCredentials, 'Should create insecure credentials for HTTP');
        });

        it('should generate unique request IDs', () => {
            const generateRequestId = (mcpClientService as any).generateRequestId.bind(mcpClientService);
            
            const id1 = generateRequestId();
            const id2 = generateRequestId();
            
            assert.ok(id1.startsWith('req_'), 'Request ID should have proper prefix');
            assert.notStrictEqual(id1, id2, 'Request IDs should be unique');
        });
    });

    describe('Error Handling', () => {
        it('should normalize gRPC errors properly', () => {
            const normalizeGrpcError = (mcpClientService as any).normalizeGrpcError.bind(mcpClientService);
            
            // Mock gRPC status codes
            const grpc = require('@grpc/grpc-js');
            
            const unavailableError = { code: grpc.status.UNAVAILABLE, message: 'Service unavailable' };
            const normalizedError = normalizeGrpcError(unavailableError);
            
            assert.ok(normalizedError instanceof Error);
            assert.ok(normalizedError.message.includes('unavailable'));
        });
    });

    describe('Server Management', () => {
        it('should track server status correctly', () => {
            const serverName = 'test-server';
            
            // Test status update
            const updateServerStatus = (mcpClientService as any).updateServerStatus.bind(mcpClientService);
            updateServerStatus(serverName, McpServerStatus.CONNECTING);
            
            const status = mcpClientService.getServerStatus(serverName);
            assert.strictEqual(status, McpServerStatus.CONNECTING);
        });

        it('should handle server status callbacks', (done) => {
            const serverName = 'test-server';
            
            // Register callback
            const unsubscribe = mcpClientService.onServerStatusChange((name, status) => {
                assert.strictEqual(name, serverName);
                assert.strictEqual(status, McpServerStatus.CONNECTED);
                unsubscribe();
                done();
            });
            
            // Trigger status change
            const updateServerStatus = (mcpClientService as any).updateServerStatus.bind(mcpClientService);
            updateServerStatus(serverName, McpServerStatus.CONNECTED);
        });
    });

    describe('Tool Discovery', () => {
        it('should handle tool discovery callbacks', (done) => {
            const serverName = 'test-server';
            const mockTools = [
                { name: 'test-tool', description: 'Test tool' }
            ];
            
            // Register callback
            const unsubscribe = mcpClientService.onToolsDiscovered((name, tools) => {
                assert.strictEqual(name, serverName);
                assert.strictEqual(tools.length, 1);
                assert.strictEqual(tools[0].name, 'test-tool');
                unsubscribe();
                done();
            });
            
            // Simulate tool discovery
            const server = {
                name: serverName,
                config: '{}',
                status: McpServerStatus.CONNECTED,
                tools: mockTools,
                resources: [],
                resourceTemplates: []
            };
            
            (mcpClientService as any).servers.set(serverName, server);
            
            // Trigger tools callback
            for (const callback of (mcpClientService as any).toolsCallbacks) {
                callback(serverName, mockTools);
            }
        });
    });

    describe('Connection Lifecycle', () => {
        it('should handle connection failure gracefully', async () => {
            const config: McpConnectionConfig = {
                serverName: 'test-server',
                serverUrl: 'grpc://invalid-server:50051',
                timeout: 1000
            };

            try {
                await mcpClientService.connect(config);
                assert.fail('Should have thrown an error for invalid server');
            } catch (error) {
                assert.ok(error instanceof Error);
                assert.ok(error.message.includes('MCP connection failed'));
            }
        });

        it('should clean up connections on dispose', () => {
            const serverName = 'test-server';
            
            // Add a mock connection
            (mcpClientService as any).connections.set(serverName, { close: sinon.stub() });
            
            // Add a mock server
            (mcpClientService as any).servers.set(serverName, {
                name: serverName,
                status: McpServerStatus.CONNECTED
            });
            
            // Dispose should clean up
            mcpClientService.dispose();
            
            assert.strictEqual((mcpClientService as any).connections.size, 0);
            assert.strictEqual((mcpClientService as any).servers.size, 0);
        });
    });
});
