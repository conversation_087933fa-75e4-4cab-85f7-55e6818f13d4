{"version": 3, "file": "tokenProvider.js", "sourceRoot": "", "sources": ["../../src/tokenProvider.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,+BAA+B,EAC/B,mBAAmB,EACnB,qBAAqB,GACtB,MAAM,2BAA2B,CAAC;AAiBnC;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,sBAAsB,CACpC,UAA2B,EAC3B,MAAyB,EACzB,OAAuC;IAEvC,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;IACtD,MAAM,QAAQ,GAAG,mBAAmB,EAAE,CAAC;IACvC,QAAQ,CAAC,SAAS,CAAC,+BAA+B,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC5E,KAAK,UAAU,iBAAiB;;QAC9B,sDAAsD;QACtD,sDAAsD;QACtD,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,WAAW,CACpC;YACE,WAAW,EAAE,CAAC,OAAO,EAAE,EAAE,CACvB,OAAO,CAAC,OAAO,CAAC;gBACd,OAAO;gBACP,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC;SACL,EACD,qBAAqB,CAAC;YACpB,GAAG,EAAE,qBAAqB;YAC1B,WAAW;YACX,cAAc;SACf,CAAC,CACH,CAAC;QACF,MAAM,WAAW,GAAG,MAAA,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredential, TracingContext } from \"@azure/core-auth\";\nimport {\n  bearerTokenAuthenticationPolicy,\n  createEmptyPipeline,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\n\n/**\n * The options to configure the token provider.\n */\nexport interface GetBearerTokenProviderOptions {\n  /** The abort signal to abort requests to get tokens */\n  abortSignal?: AbortSignal;\n  /** The tracing options for the requests to get tokens */\n  tracingOptions?: {\n    /**\n     * Tracing Context for the current request to get a token.\n     */\n    tracingContext?: TracingContext;\n  };\n}\n\n/**\n * Returns a callback that provides a bearer token.\n * For example, the bearer token can be used to authenticate a request as follows:\n * ```ts snippet:token_provider_example\n * import { DefaultAzureCredential, getBearerTokenProvider } from \"@azure/identity\";\n * import { createPipelineRequest } from \"@azure/core-rest-pipeline\";\n *\n * const credential = new DefaultAzureCredential();\n * const scope = \"https://cognitiveservices.azure.com/.default\";\n * const getAccessToken = getBearerTokenProvider(credential, scope);\n * const token = await getAccessToken();\n *\n * // usage\n * const request = createPipelineRequest({ url: \"https://example.com\" });\n * request.headers.set(\"Authorization\", `Bearer ${token}`);\n * ```\n *\n * @param credential - The credential used to authenticate the request.\n * @param scopes - The scopes required for the bearer token.\n * @param options - Options to configure the token provider.\n * @returns a callback that provides a bearer token.\n */\nexport function getBearerTokenProvider(\n  credential: TokenCredential,\n  scopes: string | string[],\n  options?: GetBearerTokenProviderOptions,\n): () => Promise<string> {\n  const { abortSignal, tracingOptions } = options || {};\n  const pipeline = createEmptyPipeline();\n  pipeline.addPolicy(bearerTokenAuthenticationPolicy({ credential, scopes }));\n  async function getRefreshedToken(): Promise<string> {\n    // Create a pipeline with just the bearer token policy\n    // and run a dummy request through it to get the token\n    const res = await pipeline.sendRequest(\n      {\n        sendRequest: (request) =>\n          Promise.resolve({\n            request,\n            status: 200,\n            headers: request.headers,\n          }),\n      },\n      createPipelineRequest({\n        url: \"https://example.com\",\n        abortSignal,\n        tracingOptions,\n      }),\n    );\n    const accessToken = res.headers.get(\"authorization\")?.split(\" \")[1];\n    if (!accessToken) {\n      throw new Error(\"Failed to get access token\");\n    }\n    return accessToken;\n  }\n  return getRefreshedToken;\n}\n"]}