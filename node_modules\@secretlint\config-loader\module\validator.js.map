{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../src/validator.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAEzD,OAAO,EACH,mCAAmC,EACnC,uCAAuC,EACvC,6CAA6C,GAChD,MAAM,iCAAiC,CAAC;AAEzC,SAAS,SAAS,CAAC,SAAc,EAAE,OAAe;IAC9C,IAAI,SAAS,EAAE;QACX,OAAO;KACV;IACD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAED,MAAM,QAAQ,GAAG,CACb,KAA4E,EACrC,EAAE;IACzC,OAAO,OAAO,IAAI,KAAK,CAAC,CAAC,8BAA8B;AAC3D,CAAC,CAAC;AACF;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACpC,gBAA4C,EACiB,EAAE;IAC/D,MAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;QACxC,OAAO;YACH,EAAE,EAAE,KAAK;YACT,KAAK,EAAE,IAAI,KAAK,CAAC;;;;;;;;;CAS5B,CAAC;SACO,CAAC;KACL;IACD,sBAAsB;IACtB,IAAI;QACA,mCAAmC,CAAC,gBAAgB,CAAC,CAAC;KACzD;IAAC,OAAO,KAAK,EAAE;QACZ,6CAA6C;QAC7C,MAAM,YAAY,GACd,KAAK,YAAY,KAAK;YAClB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,8BAA8B,EAAE,cAAc,CAAC;YACvE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;KACxC;IACD,KAAK,MAAM,YAAY,IAAI,gBAAgB,CAAC,KAAK,EAAE;QAC/C,qBAAqB;QACrB,gFAAgF;QAChF,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxB,MAAM,UAAU,GAAG,YAA8C,CAAC;YAClE,IAAI;gBACA,6CAA6C,CAAC,UAAU,CAAC,CAAC;aAC7D;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,YAAY,GACd,KAAK,YAAY,KAAK;oBAClB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,wCAAwC,EAAE,YAAY,CAAC,EAAE,CAAC;oBAClF,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;aACxC;YACD,IAAI,iBAAiB,IAAI,YAAY,EAAE;gBACnC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,kDAAkD,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;aAC/F;YACD,IAAI,UAAU,IAAI,YAAY,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,2CAA2C,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;aACvF;SACJ;aAAM;YACH,MAAM,IAAI,GAAG,YAAwC,CAAC;YACtD,IAAI;gBACA,uCAAuC,CAAC,IAAI,CAAC,CAAC;aACjD;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,YAAY,GACd,KAAK,YAAY,KAAK;oBAClB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,EAAE,YAAY,CAAC,EAAE,CAAC;oBAC5E,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;aACxC;SACJ;KACJ;IACD,OAAO;QACH,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QACvB,KAAK,EAAE,IAAI,gBAAgB,CAAC,MAAM,EAAE,gCAAgC,CAAC;KACxE,CAAC;AACN,CAAC,CAAC;AAMF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,CAAC,EACzC,MAAM,GAEU,EAA8C,EAAE;IAChE,MAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,uCAAuC;IACvC,aAAa;IACb,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,KAAK,EAAE;QACrC,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxB,kBAAkB;YAClB,MAAM,MAAM,GAAG,YAA8C,CAAC;YAC9D,MAAM,WAAW,GAAG,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;YACxC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,qCAAqC,CAAC,CAAC;YACnF,SAAS,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,oCAAoC,CAAC,CAAC;YACtF,SAAS,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE,sCAAsC,CAAC,CAAC;YAC5F,yDAAyD;YACzD,MAAM,sBAAsB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7E,WAAW,EAAE,OAAO,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBACvC,MAAM,SAAS,GAAG,sBAAsB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;gBACxE,IAAI,CAAC,SAAS,EAAE;oBACZ,MAAM,CAAC,IAAI,CACP,IAAI,KAAK,CAAC,uBAAuB,iBAAiB,CAAC,EAAE,mBAAmB,MAAM,CAAC,EAAE;;iDAExD,MAAM,CAAC,EAAE,wBAAwB,iBAAiB,CAAC,EAAE;;EAEpG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;CAClC,CAAC,CACmB,CAAC;iBACL;YACL,CAAC,CAAC,CAAC;SACN;aAAM;YACH,gBAAgB;YAChB,MAAM,IAAI,GAAG,YAAwC,CAAC;YACtD,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,kCAAkC,CAAC,CAAC;YAClF,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE,oCAAoC,CAAC,CAAC;YACxF,+BAA+B;YAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;gBACrC,MAAM,UAAU,GAAa,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;oBAC5C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;wBACtC,MAAM,CAAC,IAAI,CACP,IAAI,KAAK,CAAC,kCAAkC,cAAc,gBAAgB,IAAI,CAAC,EAAE;;+CAE9D,IAAI,CAAC,EAAE,+BAA+B,cAAc;;EAEjG,IAAI,CAAC,SAAS,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;CAC1D,CAAC,CACuB,CAAC;qBACL;gBACL,CAAC,CAAC,CAAC;aACN;SACJ;KACJ;IACD,OAAO;QACH,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QACvB,KAAK,EAAE,IAAI,gBAAgB,CAAC,MAAM,EAAE,gCAAgC,CAAC;KACxE,CAAC;AACN,CAAC,CAAC"}