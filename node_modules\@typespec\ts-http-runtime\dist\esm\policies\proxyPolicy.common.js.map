{"version": 3, "file": "proxyPolicy.common.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC;AAC7C,MAAM,YAAY,GAAG,qDAAqD,CAAC;AAE3E,MAAM,UAAU,uBAAuB;IACrC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW;IACzB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB;IACpC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport const proxyPolicyName = \"proxyPolicy\";\nconst errorMessage = \"proxyPolicy is not supported in browser environment\";\n\nexport function getDefaultProxySettings(): never {\n  throw new Error(errorMessage);\n}\n\n/**\n * proxyPolicy is not supported in the browser and attempting\n * to use it will raise an error.\n */\nexport function proxyPolicy(): never {\n  throw new Error(errorMessage);\n}\n\n/**\n * A function to reset the cached agents.\n * proxyPolicy is not supported in the browser and attempting\n * to use it will raise an error.\n * @internal\n */\nexport function resetCachedProxyAgents(): never {\n  throw new Error(errorMessage);\n}\n"]}