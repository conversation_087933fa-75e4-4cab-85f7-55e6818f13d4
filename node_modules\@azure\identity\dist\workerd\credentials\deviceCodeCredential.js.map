{"version": 3, "file": "deviceCodeCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/deviceCodeCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,yBAAyB,EACzB,mCAAmC,EACnC,eAAe,GAChB,MAAM,0BAA0B,CAAC;AAOlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAE1D,MAAM,MAAM,GAAG,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;AAExD;;;GAGG;AACH,MAAM,UAAU,+BAA+B,CAAC,cAA8B;IAC5E,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAO/B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,YAAY,OAAqC;;QAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,MAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,uBAAuB,CAAC;QAC9D,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,mCAAI,+BAA+B,CAAC;QACzF,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,kCAChD,OAAO,KACV,MAAM,EACN,sBAAsB,EAAE,OAAO,IAAI,EAAE,IACrC,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,CAAC;IAChF,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,kCAC3E,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,eAAe,EACvC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,kCAC1E,UAAU,KACb,8BAA8B,EAAE,KAAK,IACrC,CAAC;YACH,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAC5C,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n  resolveTenantId,\n} from \"../util/tenantIdUtils.js\";\nimport type {\n  DeviceCodeCredentialOptions,\n  DeviceCodeInfo,\n  DeviceCodePromptCallback,\n} from \"./deviceCodeCredentialOptions.js\";\nimport type { AuthenticationRecord } from \"../msal/types.js\";\nimport { credentialLogger } from \"../util/logging.js\";\nimport { ensureScopes } from \"../util/scopeUtils.js\";\nimport { tracingClient } from \"../util/tracing.js\";\nimport type { MsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport { createMsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport { DeveloperSignOnClientId } from \"../constants.js\";\n\nconst logger = credentialLogger(\"DeviceCodeCredential\");\n\n/**\n * Method that logs the user code from the DeviceCodeCredential.\n * @param deviceCodeInfo - The device code.\n */\nexport function defaultDeviceCodePromptCallback(deviceCodeInfo: DeviceCodeInfo): void {\n  console.log(deviceCodeInfo.message);\n}\n\n/**\n * Enables authentication to Microsoft Entra ID using a device code\n * that the user can enter into https://microsoft.com/devicelogin.\n */\nexport class DeviceCodeCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private disableAutomaticAuthentication?: boolean;\n  private msalClient: MsalClient;\n  private userPromptCallback: DeviceCodePromptCallback;\n\n  /**\n   * Creates an instance of DeviceCodeCredential with the details needed\n   * to initiate the device code authorization flow with Microsoft Entra ID.\n   *\n   * A message will be logged, giving users a code that they can use to authenticate once they go to https://microsoft.com/devicelogin\n   *\n   * Developers can configure how this message is shown by passing a custom `userPromptCallback`:\n   *\n   * ```ts snippet:device_code_credential_example\n   * import { DeviceCodeCredential } from \"@azure/identity\";\n   *\n   * const credential = new DeviceCodeCredential({\n   *   tenantId: process.env.AZURE_TENANT_ID,\n   *   clientId: process.env.AZURE_CLIENT_ID,\n   *   userPromptCallback: (info) => {\n   *     console.log(\"CUSTOMIZED PROMPT CALLBACK\", info.message);\n   *   },\n   * });\n   * ```\n   *\n   * @param options - Options for configuring the client which makes the authentication requests.\n   */\n  constructor(options?: DeviceCodeCredentialOptions) {\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n    const clientId = options?.clientId ?? DeveloperSignOnClientId;\n    const tenantId = resolveTenantId(logger, options?.tenantId, clientId);\n    this.userPromptCallback = options?.userPromptCallback ?? defaultDeviceCodePromptCallback;\n    this.msalClient = createMsalClient(clientId, tenantId, {\n      ...options,\n      logger,\n      tokenCredentialOptions: options || {},\n    });\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger,\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalClient.getTokenByDeviceCode(arrayScopes, this.userPromptCallback, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      },\n    );\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will always generate a challenge for the user.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {},\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        await this.msalClient.getTokenByDeviceCode(arrayScopes, this.userPromptCallback, {\n          ...newOptions,\n          disableAutomaticAuthentication: false, // this method should always allow user interaction\n        });\n        return this.msalClient.getActiveAccount();\n      },\n    );\n  }\n}\n"]}