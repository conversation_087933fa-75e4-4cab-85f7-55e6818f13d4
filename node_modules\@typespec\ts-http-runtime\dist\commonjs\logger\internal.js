"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLoggerContext = void 0;
var logger_js_1 = require("./logger.js");
Object.defineProperty(exports, "createLoggerContext", { enumerable: true, get: function () { return logger_js_1.createLoggerContext; } });
//# sourceMappingURL=internal.js.map