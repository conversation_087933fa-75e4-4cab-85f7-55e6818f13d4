// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { RestError as TspRestError, isRestError as tspIsRestError, } from "@typespec/ts-http-runtime";
/**
 * A custom error type for failed pipeline requests.
 */
// eslint-disable-next-line @typescript-eslint/no-redeclare
export const RestError = TspRestError;
/**
 * Typeguard for RestError
 * @param e - Something caught by a catch clause.
 */
export function isRestError(e) {
    return tspIsRestError(e);
}
//# sourceMappingURL=restError.js.map