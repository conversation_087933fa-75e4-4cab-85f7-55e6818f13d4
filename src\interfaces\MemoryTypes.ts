/**
 * Memory Types - Refact-inspired memory management interfaces
 * Based on <PERSON><PERSON><PERSON>'s memory system for persistent AI knowledge
 */

export interface MemoRecord {
    iknow_id: string;
    iknow_tags: string[];
    iknow_memory: string;
    timestamp?: number;
    project_id?: string;
    context?: string;
}

export interface MemorySearchResult {
    record: MemoRecord;
    relevanceScore: number;
    matchedTags: string[];
}

export interface MemoryQuery {
    query: string;
    tags?: string[];
    project_id?: string;
    limit?: number;
    minRelevanceScore?: number;
}

export interface MemoryStats {
    totalMemories: number;
    memoriesByTag: { [tag: string]: number };
    memoriesByProject: { [project: string]: number };
    oldestMemory?: Date;
    newestMemory?: Date;
}

export interface MemoryMigrationResult {
    migratedCount: number;
    skippedCount: number;
    errorCount: number;
    errors: string[];
}

export interface MemoryConfiguration {
    maxMemories: number;
    autoCleanup: boolean;
    cleanupThresholdDays: number;
    enableProjectIsolation: boolean;
    defaultTags: string[];
}

export interface LegacyMemoryRecord {
    id: string;
    content: string;
    tags: string[];
    created_at: string;
    updated_at: string;
    metadata?: any;
}

export interface MemoryContext {
    workspaceRoot?: string;
    activeFile?: string;
    projectName?: string;
    userContext?: string;
}

export interface MemoryOperationResult {
    success: boolean;
    message: string;
    recordId?: string;
    error?: string;
}

export interface MemoryExportData {
    version: string;
    exportDate: string;
    memories: MemoRecord[];
    stats: MemoryStats;
    configuration: MemoryConfiguration;
}

export interface MemoryImportResult {
    importedCount: number;
    skippedCount: number;
    duplicateCount: number;
    errorCount: number;
    errors: string[];
}

/**
 * Memory event types for the event system
 */
export type MemoryEventType = 
    | 'memory_added'
    | 'memory_updated'
    | 'memory_deleted'
    | 'memory_searched'
    | 'memory_migrated'
    | 'memory_exported'
    | 'memory_imported'
    | 'memory_cleanup';

export interface MemoryEvent {
    type: MemoryEventType;
    timestamp: number;
    data: any;
    context?: MemoryContext;
}

/**
 * Memory storage backend interface
 */
export interface MemoryStorageBackend {
    initialize(): Promise<void>;
    store(record: MemoRecord): Promise<MemoryOperationResult>;
    retrieve(id: string): Promise<MemoRecord | null>;
    search(query: MemoryQuery): Promise<MemorySearchResult[]>;
    update(id: string, updates: Partial<MemoRecord>): Promise<MemoryOperationResult>;
    delete(id: string): Promise<MemoryOperationResult>;
    list(options?: { limit?: number; offset?: number; tags?: string[] }): Promise<MemoRecord[]>;
    getStats(): Promise<MemoryStats>;
    cleanup(olderThanDays: number): Promise<number>;
    export(): Promise<MemoryExportData>;
    import(data: MemoryExportData): Promise<MemoryImportResult>;
    close(): Promise<void>;
}

/**
 * Memory indexing for fast search
 */
export interface MemoryIndex {
    addToIndex(record: MemoRecord): void;
    removeFromIndex(id: string): void;
    updateIndex(id: string, record: MemoRecord): void;
    search(query: string, options?: { tags?: string[]; limit?: number }): MemorySearchResult[];
    rebuildIndex(records: MemoRecord[]): void;
    getIndexStats(): { totalRecords: number; indexSize: number };
}

/**
 * Memory validation rules
 */
export interface MemoryValidationRule {
    name: string;
    validate(record: MemoRecord): { valid: boolean; error?: string };
}

export interface MemoryValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Memory analytics for insights
 */
export interface MemoryAnalytics {
    getMostUsedTags(limit?: number): { tag: string; count: number }[];
    getMemoryTrends(days?: number): { date: string; count: number }[];
    getProjectActivity(projectId?: string): { project: string; memoryCount: number; lastActivity: Date }[];
    getSearchPatterns(): { query: string; frequency: number }[];
    getMemoryQuality(): { averageLength: number; tagCoverage: number; duplicateRate: number };
}

/**
 * Memory synchronization for multi-device support
 */
export interface MemorySyncConfig {
    enabled: boolean;
    syncInterval: number;
    conflictResolution: 'local' | 'remote' | 'merge' | 'ask';
    syncEndpoint?: string;
    apiKey?: string;
}

export interface MemorySyncResult {
    success: boolean;
    syncedCount: number;
    conflictCount: number;
    errorCount: number;
    lastSyncTime: Date;
}
