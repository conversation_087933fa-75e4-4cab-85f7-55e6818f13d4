/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/** @internal */
export type AuthorityMetadataEntity = {
    aliases: Array<string>;
    preferred_cache: string;
    preferred_network: string;
    canonical_authority: string;
    authorization_endpoint: string;
    token_endpoint: string;
    end_session_endpoint?: string;
    issuer: string;
    aliasesFromNetwork: boolean;
    endpointsFromNetwork: boolean;
    expiresAt: number;
    jwks_uri: string;
};
