/*! @azure/msal-browser v4.13.1 2025-06-10 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const pkceNotCreated = "pkce_not_created";
const earJwkEmpty = "ear_jwk_empty";
const earJweEmpty = "ear_jwe_empty";
const cryptoNonExistent = "crypto_nonexistent";
const emptyNavigateUri = "empty_navigate_uri";
const hashEmptyError = "hash_empty_error";
const noStateInHash = "no_state_in_hash";
const hashDoesNotContainKnownProperties = "hash_does_not_contain_known_properties";
const unableToParseState = "unable_to_parse_state";
const stateInteractionTypeMismatch = "state_interaction_type_mismatch";
const interactionInProgress = "interaction_in_progress";
const popupWindowError = "popup_window_error";
const emptyWindowError = "empty_window_error";
const userCancelled = "user_cancelled";
const monitorPopupTimeout = "monitor_popup_timeout";
const monitorWindowTimeout = "monitor_window_timeout";
const redirectInIframe = "redirect_in_iframe";
const blockIframeReload = "block_iframe_reload";
const blockNestedPopups = "block_nested_popups";
const iframeClosedPrematurely = "iframe_closed_prematurely";
const silentLogoutUnsupported = "silent_logout_unsupported";
const noAccountError = "no_account_error";
const silentPromptValueError = "silent_prompt_value_error";
const noTokenRequestCacheError = "no_token_request_cache_error";
const unableToParseTokenRequestCacheError = "unable_to_parse_token_request_cache_error";
const authRequestNotSetError = "auth_request_not_set_error";
const invalidCacheType = "invalid_cache_type";
const nonBrowserEnvironment = "non_browser_environment";
const databaseNotOpen = "database_not_open";
const noNetworkConnectivity = "no_network_connectivity";
const postRequestFailed = "post_request_failed";
const getRequestFailed = "get_request_failed";
const failedToParseResponse = "failed_to_parse_response";
const unableToLoadToken = "unable_to_load_token";
const cryptoKeyNotFound = "crypto_key_not_found";
const authCodeRequired = "auth_code_required";
const authCodeOrNativeAccountIdRequired = "auth_code_or_nativeAccountId_required";
const spaCodeAndNativeAccountIdPresent = "spa_code_and_nativeAccountId_present";
const databaseUnavailable = "database_unavailable";
const unableToAcquireTokenFromNativePlatform = "unable_to_acquire_token_from_native_platform";
const nativeHandshakeTimeout = "native_handshake_timeout";
const nativeExtensionNotInstalled = "native_extension_not_installed";
const nativeConnectionNotEstablished = "native_connection_not_established";
const uninitializedPublicClientApplication = "uninitialized_public_client_application";
const nativePromptNotSupported = "native_prompt_not_supported";
const invalidBase64String = "invalid_base64_string";
const invalidPopTokenRequest = "invalid_pop_token_request";
const failedToBuildHeaders = "failed_to_build_headers";
const failedToParseHeaders = "failed_to_parse_headers";
const failedToDecryptEarResponse = "failed_to_decrypt_ear_response";

export { authCodeOrNativeAccountIdRequired, authCodeRequired, authRequestNotSetError, blockIframeReload, blockNestedPopups, cryptoKeyNotFound, cryptoNonExistent, databaseNotOpen, databaseUnavailable, earJweEmpty, earJwkEmpty, emptyNavigateUri, emptyWindowError, failedToBuildHeaders, failedToDecryptEarResponse, failedToParseHeaders, failedToParseResponse, getRequestFailed, hashDoesNotContainKnownProperties, hashEmptyError, iframeClosedPrematurely, interactionInProgress, invalidBase64String, invalidCacheType, invalidPopTokenRequest, monitorPopupTimeout, monitorWindowTimeout, nativeConnectionNotEstablished, nativeExtensionNotInstalled, nativeHandshakeTimeout, nativePromptNotSupported, noAccountError, noNetworkConnectivity, noStateInHash, noTokenRequestCacheError, nonBrowserEnvironment, pkceNotCreated, popupWindowError, postRequestFailed, redirectInIframe, silentLogoutUnsupported, silentPromptValueError, spaCodeAndNativeAccountIdPresent, stateInteractionTypeMismatch, unableToAcquireTokenFromNativePlatform, unableToLoadToken, unableToParseState, unableToParseTokenRequestCacheError, uninitializedPublicClientApplication, userCancelled };
//# sourceMappingURL=BrowserAuthErrorCodes.mjs.map
