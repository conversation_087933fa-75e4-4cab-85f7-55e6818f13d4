{"version": 3, "file": "getClient.js", "sourceRoot": "", "sources": ["../../../src/client/getClient.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAwBlC,8BA4GC;AAhID,yDAA2D;AAU3D,qDAA+C;AAC/C,mDAAkD;AAClD,qEAAyD;AAEzD;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,QAAgB,EAAE,gBAA+B,EAAE;;IAC3E,MAAM,QAAQ,GAAG,MAAA,aAAa,CAAC,QAAQ,mCAAI,IAAA,wCAAqB,EAAC,aAAa,CAAC,CAAC;IAChF,IAAI,MAAA,aAAa,CAAC,kBAAkB,0CAAE,MAAM,EAAE,CAAC;QAC7C,KAAK,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;YACpE,2DAA2D;YAC3D,6CAA6C;YAC7C,MAAM,UAAU,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAChE,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;gBACzB,UAAU;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,MAAM,EAAE,uBAAuB,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;IAC9D,MAAM,WAAW,GAAG,MAAA,aAAa,CAAC,QAAQ,mCAAI,QAAQ,CAAC;IACvD,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,GAAG,IAAgB,EAAqC,EAAE;QACtF,MAAM,MAAM,GAAG,CAAC,cAAiC,EAAU,EAAE,CAC3D,IAAA,+BAAe,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,kBAAI,uBAAuB,IAAK,cAAc,EAAG,CAAC;QAE3F,OAAO;YACL,GAAG,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAChE,OAAO,cAAc,CACnB,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACjE,OAAO,cAAc,CACnB,MAAM,EACN,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,GAAG,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAChE,OAAO,cAAc,CACnB,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAClE,OAAO,cAAc,CACnB,OAAO,EACP,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,MAAM,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACnE,OAAO,cAAc,CACnB,QAAQ,EACR,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACjE,OAAO,cAAc,CACnB,MAAM,EACN,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBACpE,OAAO,cAAc,CACnB,SAAS,EACT,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,EAAE,EAAoB,EAAE;gBAClE,OAAO,cAAc,CACnB,OAAO,EACP,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,MAAM;QACZ,aAAa,EAAE,MAAM;QACrB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,MAAmB,EACnB,GAAW,EACX,QAAkB,EAClB,OAA0B,EAC1B,uBAAiC,EACjC,UAAuB;;IAEvB,uBAAuB,GAAG,MAAA,OAAO,CAAC,uBAAuB,mCAAI,uBAAuB,CAAC;IACrF,OAAO;QACL,IAAI,EAAE,UAAU,WAAW,EAAE,UAAU;YACrC,OAAO,IAAA,4BAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,kCACH,OAAO,KAAE,uBAAuB,KACrC,UAAU,CACX,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAClC,CAAC;QACD,KAAK,CAAC,eAAe;YACnB,IAAI,gCAAU,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CACb,sPAAsP,CACvP,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAA,4BAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,kCACH,OAAO,KAAE,uBAAuB,EAAE,gBAAgB,EAAE,IAAI,KAC7D,UAAU,CAC2B,CAAC;YAC1C,CAAC;QACH,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,IAAI,gCAAU,EAAE,CAAC;gBACf,OAAO,IAAA,4BAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,kCACH,OAAO,KAAE,uBAAuB,EAAE,gBAAgB,EAAE,IAAI,KAC7D,UAAU,CACwB,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,uHAAuH,CACxH,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, HttpMethods } from \"../interfaces.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createDefaultPipeline } from \"./clientHelpers.js\";\nimport type {\n  Client,\n  ClientOptions,\n  HttpBrowserStreamResponse,\n  HttpNodeStreamResponse,\n  RequestParameters,\n  ResourceMethods,\n  StreamableMethod,\n} from \"./common.js\";\nimport { sendRequest } from \"./sendRequest.js\";\nimport { buildRequestUrl } from \"./urlHelpers.js\";\nimport { isNodeLike } from \"../util/checkEnvironment.js\";\n\n/**\n * Creates a client with a default pipeline\n * @param endpoint - Base endpoint for the client\n * @param credentials - Credentials to authenticate the requests\n * @param options - Client options\n */\nexport function getClient(endpoint: string, clientOptions: ClientOptions = {}): Client {\n  const pipeline = clientOptions.pipeline ?? createDefaultPipeline(clientOptions);\n  if (clientOptions.additionalPolicies?.length) {\n    for (const { policy, position } of clientOptions.additionalPolicies) {\n      // Sign happens after Retry and is commonly needed to occur\n      // before policies that intercept post-retry.\n      const afterPhase = position === \"perRetry\" ? \"Sign\" : undefined;\n      pipeline.addPolicy(policy, {\n        afterPhase,\n      });\n    }\n  }\n\n  const { allowInsecureConnection, httpClient } = clientOptions;\n  const endpointUrl = clientOptions.endpoint ?? endpoint;\n  const client = (path: string, ...args: Array<any>): ResourceMethods<StreamableMethod> => {\n    const getUrl = (requestOptions: RequestParameters): string =>\n      buildRequestUrl(endpointUrl, path, args, { allowInsecureConnection, ...requestOptions });\n\n    return {\n      get: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"GET\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      post: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"POST\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      put: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"PUT\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      patch: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"PATCH\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      delete: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"DELETE\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      head: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"HEAD\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      options: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"OPTIONS\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      trace: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"TRACE\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n    };\n  };\n\n  return {\n    path: client,\n    pathUnchecked: client,\n    pipeline,\n  };\n}\n\nfunction buildOperation(\n  method: HttpMethods,\n  url: string,\n  pipeline: Pipeline,\n  options: RequestParameters,\n  allowInsecureConnection?: boolean,\n  httpClient?: HttpClient,\n): StreamableMethod {\n  allowInsecureConnection = options.allowInsecureConnection ?? allowInsecureConnection;\n  return {\n    then: function (onFulfilled, onrejected) {\n      return sendRequest(\n        method,\n        url,\n        pipeline,\n        { ...options, allowInsecureConnection },\n        httpClient,\n      ).then(onFulfilled, onrejected);\n    },\n    async asBrowserStream() {\n      if (isNodeLike) {\n        throw new Error(\n          \"`asBrowserStream` is supported only in the browser environment. Use `asNodeStream` instead to obtain the response body stream. If you require a Web stream of the response in Node, consider using `Readable.toWeb` on the result of `asNodeStream`.\",\n        );\n      } else {\n        return sendRequest(\n          method,\n          url,\n          pipeline,\n          { ...options, allowInsecureConnection, responseAsStream: true },\n          httpClient,\n        ) as Promise<HttpBrowserStreamResponse>;\n      }\n    },\n    async asNodeStream() {\n      if (isNodeLike) {\n        return sendRequest(\n          method,\n          url,\n          pipeline,\n          { ...options, allowInsecureConnection, responseAsStream: true },\n          httpClient,\n        ) as Promise<HttpNodeStreamResponse>;\n      } else {\n        throw new Error(\n          \"`isNodeStream` is not supported in the browser environment. Use `asBrowserStream` to obtain the response body stream.\",\n        );\n      }\n    },\n  };\n}\n"]}