{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AACtC,OAAO,EAAE,aAAa,EAAqB,MAAM,YAAY,CAAC;AAC9D,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,MAAM,MAAM,OAAO,CAAC;AAC3B,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAE7C,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAEnC,MAAM,WAAW,GAAG;;;;;;;;;;yFAUqE,gBAAgB,EAAE;KACpG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;KACxB,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoChB,CAAC;AACF,MAAM,kBAAkB,GAAG,QAAiB,CAAC;AAC7C,MAAM,mBAAmB,GAAG,SAAkB,CAAC;AAC/C,MAAM,OAAO,GAAG;IACZ,IAAI,EAAE;QACF,IAAI,EAAE,mBAAmB;KAC5B;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,SAAS;KACrB;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,kBAAkB;KAC3B;IACD,UAAU,EAAE;QACR,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,iBAAiB,EAAE;QACf,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,WAAW,EAAE;QACT,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,YAAY,EAAE;QACV,IAAI,EAAE,kBAAkB;KAC3B;IACD,gBAAgB,EAAE;QACd,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,mBAAmB;KAC/B;IACD,aAAa,EAAE;QACX,IAAI,EAAE,kBAAkB;KAC3B;IACD,OAAO,EAAE;QACL,IAAI,EAAE,mBAAmB;KAC5B;IACD,gBAAgB,EAAE;QACd,IAAI,EAAE,kBAAkB;KAC3B;IACD,MAAM,EAAE;QACJ,IAAI,EAAE,kBAAkB;KAC3B;IACD;;OAEG;IACH,KAAK,EAAE;QACH,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,IAAI;KAChB;IACD;;;;OAIG;IACH,YAAY,EAAE;QACV,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,IAAI;KAChB;IACD,eAAe;IACf,GAAG,EAAE;QACD,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE;KACzB;IACD,KAAK,EAAE;QACH,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,IAAI,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE;IACnD,OAAO,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE;IACtD,WAAW,EAAE;QACT,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,YAAY,EAAE;QACV,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,OAAO,EAAE;QACL,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;IACD,SAAS,EAAE;QACP,IAAI,EAAE,kBAAkB;KAC3B;IACD,QAAQ,EAAE;QACN,IAAI,EAAE,kBAAkB;KAC3B;IACD,IAAI,EAAE;QACF,IAAI,EAAE,kBAAkB;KAC3B;IACD,OAAO,EAAE;QACL,IAAI,EAAE,kBAAkB;KAC3B;IACD,EAAE,EAAE;QACA,IAAI,EAAE,kBAAkB;KAC3B;IACD,cAAc,EAAE;QACZ,IAAI,EAAE,kBAAkB;KAC3B;IACD,IAAI,EAAE;QACF,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,KAAK;KACjB;CACJ,CAAC;AACF,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/E,MAAM,CAAC,MAAM,GAAG,GAAG;IACf,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,MAAM;CAChB,CAAC;AAEF,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE;IACxB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,KAAK,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,EAA8B,EAAE;IAClG,4CAA4C;IAC5C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,YAAY,GAAG,MAAM,QAAQ,EAAE,CAAC;QACtC,OAAO;YACH,YAAY;YACZ,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,cAAc,EAAE,KAAK,CAAC,MAAM;YAC5B,cAAc,EAAE,KAAK,CAAC,gBAAgB;YACtC,GAAG,EAAE,KAAK,CAAC,GAAG;SACjB,CAAC;KACL;SAAM;QACH,OAAO;YACH,kBAAkB,EAAE,KAAK;YACzB,cAAc,EAAE,KAAK,CAAC,MAAM;YAC5B,cAAc,EAAE,KAAK,CAAC,gBAAgB;YACtC,GAAG,EAAE,KAAK,CAAC,GAAG;SACjB,CAAC;KACL;AACL,CAAC,CAAC;AACF,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,EACpB,KAAK,GAAG,GAAG,CAAC,KAAK,EACjB,KAAK,GAAG,GAAG,CAAC,KAAK,EAC2D,EAAE;IAC9E,IAAI,KAAK,CAAC,IAAI,EAAE;QACZ,OAAO;YACH,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,IAAI;SACf,CAAC;KACL;IACD,IAAI,KAAK,CAAC,OAAO,EAAE;QACf,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO;YACH,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,WAAW,EAAE,OAAO,IAAI,EAAE;YAClC,MAAM,EAAE,IAAI;SACf,CAAC;KACL;IACD,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,uBAAuB;KAChC,CAAC,CAAC;IACH,IAAI,KAAK,CAAC,KAAK,EAAE;QACb,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;QACpC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,cAAc,CAAC,CAAC;KAC3C;IACD,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;IACtB,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1B,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1B,IAAI,KAAK,CAAC,IAAI,EAAE;QACZ,OAAO,gBAAgB,CAAC;YACpB,GAAG;SACN,CAAC,CAAC;KACN;IACD,MAAM,UAAU,GAAG,MAAM,cAAc,CAAC;QACpC,KAAK;QACL,KAAK;KACR,CAAC,CAAC;IACH,KAAK,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IACnC,OAAO,aAAa,CAAC;QACjB,UAAU;QACV,aAAa,EAAE,KAAK,CAAC,gBAAgB;YACjC,CAAC,CAAC;gBACI,8BAA8B;gBAC9B,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC;gBAClD,GAAG,EAAE,GAAG;gBACR,SAAS,EAAE,KAAK,CAAC,MAAM;gBACvB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,WAAW,EAAE,KAAK,CAAC,WAAW;aACjC;YACH,CAAC,CAAC;gBACI,cAAc,EAAE,KAAK,CAAC,YAAY;gBAClC,GAAG,EAAE,GAAG;gBACR,SAAS,EAAE,KAAK,CAAC,MAAM;gBACvB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,WAAW,EAAE,KAAK,CAAC,WAAW;aACjC;KACV,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;QAClB,kBAAkB,CAAC,IAAI,CAAC;YACpB,IAAI,EAAE,qBAAqB;SAC9B,CAAC,CAAC;QACH,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;YACtB,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;gBACzB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aAClD;iBAAM;gBACH,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvB,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;oBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;oBACxE,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,YAAY,IAAI,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;aACN;SACJ;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC"}