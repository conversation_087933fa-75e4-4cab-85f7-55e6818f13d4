// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export { agentPolicy, agentPolicyName } from "./agentPolicy.js";
export { decompressResponsePolicy, decompressResponsePolicyName, } from "./decompressResponsePolicy.js";
export { defaultRetryPolicy, defaultRetryPolicyName, } from "./defaultRetryPolicy.js";
export { exponentialRetryPolicy, exponentialRetryPolicyName, } from "./exponentialRetryPolicy.js";
export { retryPolicy } from "./retryPolicy.js";
export { systemErrorRetryPolicy, systemErrorRetryPolicyName } from "./systemErrorRetryPolicy.js";
export { throttlingRetryPolicy, throttlingRetryPolicyName } from "./throttlingRetryPolicy.js";
export { formDataPolicy, formDataPolicyName } from "./formDataPolicy.js";
export { logPolicy, logPolicyName } from "./logPolicy.js";
export { multipartPolicy, multipartPolicyName } from "./multipartPolicy.js";
export { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from "./proxyPolicy.js";
export { redirectPolicy, redirectPolicyName } from "./redirectPolicy.js";
export { tlsPolicy, tlsPolicyName } from "./tlsPolicy.js";
export { userAgentPolicy, userAgentPolicyName } from "./userAgentPolicy.js";
//# sourceMappingURL=internal.js.map