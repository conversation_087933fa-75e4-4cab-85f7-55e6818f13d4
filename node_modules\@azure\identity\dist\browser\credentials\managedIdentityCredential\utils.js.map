{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/utils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,kBAAkB,GAAG,WAAW,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GACpC,6QAA6Q,CAAC;AAEhR;;;;;;;;GAQG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAyB;IAC3D,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QACtC,KAAK,GAAG,MAAM,CAAC;IACjB,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAChE,CAAC;AAeD;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CAAC,IAA6B;IACpE,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrB,OAAO,QAAQ,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC7C,CAAC;IAED,MAAM,IAAI,KAAK,CACb,2DAA2D,IAAI,CAAC,UAAU,kBAAkB,IAAI,CAAC,UAAU,GAAG,CAC/G,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,IAA6B;IACjE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrB,OAAO,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,qDAAqD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IAC3F,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nconst DefaultScopeSuffix = \"/.default\";\n\n/**\n * Error message for Service Fabric Managed Identity environment.\n */\nexport const serviceFabricErrorMessage =\n  \"Specifying a `clientId` or `resourceId` is not supported by the Service Fabric managed identity environment. The managed identity configuration is determined by the Service Fabric cluster resource configuration. See https://aka.ms/servicefabricmi for more information\";\n\n/**\n * Most MSIs send requests to the IMDS endpoint, or a similar endpoint.\n * These are GET requests that require sending a `resource` parameter on the query.\n * This resource can be derived from the scopes received through the getToken call, as long as only one scope is received.\n * Multiple scopes assume that the resulting token will have access to multiple resources, which won't be the case.\n *\n * For that reason, when we encounter multiple scopes, we return undefined.\n * It's up to the individual MSI implementations to throw the errors (which helps us provide less generic errors).\n */\nexport function mapScopesToResource(scopes: string | string[]): string | undefined {\n  let scope = \"\";\n  if (Array.isArray(scopes)) {\n    if (scopes.length !== 1) {\n      return;\n    }\n\n    scope = scopes[0];\n  } else if (typeof scopes === \"string\") {\n    scope = scopes;\n  }\n\n  if (!scope.endsWith(DefaultScopeSuffix)) {\n    return scope;\n  }\n\n  return scope.substr(0, scope.lastIndexOf(DefaultScopeSuffix));\n}\n\n/**\n * Internal type roughly matching the raw responses of the authentication endpoints.\n *\n * @internal\n */\nexport interface TokenResponseParsedBody {\n  access_token?: string;\n  refresh_token?: string;\n  expires_in: number;\n  expires_on?: number | string;\n  refresh_on?: number | string;\n}\n\n/**\n * Given a token response, return the expiration timestamp as the number of milliseconds from the Unix epoch.\n * @param body - A parsed response body from the authentication endpoint.\n */\nexport function parseExpirationTimestamp(body: TokenResponseParsedBody): number {\n  if (typeof body.expires_on === \"number\") {\n    return body.expires_on * 1000;\n  }\n\n  if (typeof body.expires_on === \"string\") {\n    const asNumber = +body.expires_on;\n    if (!isNaN(asNumber)) {\n      return asNumber * 1000;\n    }\n\n    const asDate = Date.parse(body.expires_on);\n    if (!isNaN(asDate)) {\n      return asDate;\n    }\n  }\n\n  if (typeof body.expires_in === \"number\") {\n    return Date.now() + body.expires_in * 1000;\n  }\n\n  throw new Error(\n    `Failed to parse token expiration from body. expires_in=\"${body.expires_in}\", expires_on=\"${body.expires_on}\"`,\n  );\n}\n\n/**\n * Given a token response, return the expiration timestamp as the number of milliseconds from the Unix epoch.\n * @param body - A parsed response body from the authentication endpoint.\n */\nexport function parseRefreshTimestamp(body: TokenResponseParsedBody): number | undefined {\n  if (body.refresh_on) {\n    if (typeof body.refresh_on === \"number\") {\n      return body.refresh_on * 1000;\n    }\n\n    if (typeof body.refresh_on === \"string\") {\n      const asNumber = +body.refresh_on;\n      if (!isNaN(asNumber)) {\n        return asNumber * 1000;\n      }\n\n      const asDate = Date.parse(body.refresh_on);\n      if (!isNaN(asDate)) {\n        return asDate;\n      }\n    }\n    throw new Error(`Failed to parse refresh_on from body. refresh_on=\"${body.refresh_on}\"`);\n  } else {\n    return undefined;\n  }\n}\n"]}