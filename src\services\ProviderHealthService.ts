import * as vscode from 'vscode';
import axios, { AxiosResponse } from 'axios';
import { ProviderDetail } from './ProviderRegistryService';
import { ApiKeyManager } from './ApiKeyManager';

export interface ProviderHealthStatus {
    provider: string;
    isOnline: boolean;
    isAuthenticated: boolean;
    responseTime?: number;
    lastChecked: Date;
    error?: string;
    apiKeyValid?: boolean;
    serviceVersion?: string;
}

export interface HealthCheckResult {
    success: boolean;
    responseTime: number;
    error?: string;
    metadata?: Record<string, any>;
}

/**
 * Service for checking provider health and API key validity
 */
export class ProviderHealthService {
    private static instance: ProviderHealthService;
    private healthCache = new Map<string, ProviderHealthStatus>();
    private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    private readonly REQUEST_TIMEOUT = 10000; // 10 seconds

    public static getInstance(): ProviderHealthService {
        if (!ProviderHealthService.instance) {
            ProviderHealthService.instance = new ProviderHealthService();
        }
        return ProviderHealthService.instance;
    }

    /**
     * Check health status for a specific provider
     */
    public async checkProviderHealth(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<ProviderHealthStatus> {
        const cacheKey = `${provider.id}_${apiKey ? 'auth' : 'noauth'}`;
        const cached = this.healthCache.get(cacheKey);
        
        // Return cached result if still valid
        if (cached && this.isCacheValid(cached)) {
            return cached;
        }

        const startTime = Date.now();
        const status: ProviderHealthStatus = {
            provider: provider.id,
            isOnline: false,
            isAuthenticated: false,
            lastChecked: new Date()
        };

        try {
            // Perform health check based on provider type
            const healthResult = await this.performHealthCheck(provider, apiKey);
            
            status.isOnline = healthResult.success;
            status.responseTime = healthResult.responseTime;
            status.isAuthenticated = apiKey ? healthResult.success : true;
            status.apiKeyValid = apiKey ? healthResult.success : undefined;
            
            if (healthResult.metadata) {
                status.serviceVersion = healthResult.metadata.version;
            }
            
            if (!healthResult.success) {
                status.error = healthResult.error;
            }

        } catch (error) {
            status.error = error instanceof Error ? error.message : 'Unknown error';
            status.responseTime = Date.now() - startTime;
        }

        // Cache the result
        this.healthCache.set(cacheKey, status);
        return status;
    }

    /**
     * Perform actual health check based on provider configuration
     */
    private async performHealthCheck(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<HealthCheckResult> {
        const startTime = Date.now();
        
        try {
            switch (provider.id) {
                case 'deepseek':
                    return await this.checkDeepSeekHealth(provider, apiKey);
                case 'groq':
                    return await this.checkGroqHealth(provider, apiKey);
                case 'openrouter':
                    return await this.checkOpenRouterHealth(provider, apiKey);
                case 'local':
                    return await this.checkLocalHealth(provider);
                default:
                    return await this.checkGenericHealth(provider, apiKey);
            }
        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : 'Health check failed'
            };
        }
    }

    /**
     * Check DeepSeek API health
     */
    private async checkDeepSeekHealth(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const url = `${provider.apiBaseUrl}${provider.healthCheckUrl || '/models'}`;
        
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };
        
        if (apiKey) {
            headers['Authorization'] = `Bearer ${apiKey}`;
        }

        try {
            const response: AxiosResponse = await axios.get(url, {
                headers,
                timeout: this.REQUEST_TIMEOUT,
                validateStatus: (status) => status < 500 // Accept 4xx as valid response
            });

            const responseTime = Date.now() - startTime;
            
            // DeepSeek returns 401 for invalid API key, 200 for valid
            if (response.status === 401) {
                return {
                    success: false,
                    responseTime,
                    error: 'Invalid API key'
                };
            }
            
            if (response.status === 200) {
                return {
                    success: true,
                    responseTime,
                    metadata: {
                        modelsCount: response.data?.data?.length || 0
                    }
                };
            }

            return {
                success: false,
                responseTime,
                error: `HTTP ${response.status}: ${response.statusText}`
            };

        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: axios.isAxiosError(error) 
                    ? `Network error: ${error.message}` 
                    : 'Unknown error'
            };
        }
    }

    /**
     * Check Groq API health
     */
    private async checkGroqHealth(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const url = `${provider.apiBaseUrl}${provider.healthCheckUrl || '/models'}`;
        
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };
        
        if (apiKey) {
            headers['Authorization'] = `Bearer ${apiKey}`;
        }

        try {
            const response: AxiosResponse = await axios.get(url, {
                headers,
                timeout: this.REQUEST_TIMEOUT,
                validateStatus: (status) => status < 500
            });

            const responseTime = Date.now() - startTime;
            
            if (response.status === 401) {
                return {
                    success: false,
                    responseTime,
                    error: 'Invalid API key'
                };
            }
            
            if (response.status === 200) {
                return {
                    success: true,
                    responseTime,
                    metadata: {
                        modelsCount: response.data?.data?.length || 0
                    }
                };
            }

            return {
                success: false,
                responseTime,
                error: `HTTP ${response.status}: ${response.statusText}`
            };

        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: axios.isAxiosError(error) 
                    ? `Network error: ${error.message}` 
                    : 'Unknown error'
            };
        }
    }

    /**
     * Check OpenRouter API health
     */
    private async checkOpenRouterHealth(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const url = `${provider.apiBaseUrl}${provider.healthCheckUrl || '/models'}`;
        
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/v1b3-sama/extension',
            'X-Title': 'V1b3-Sama VS Code Extension'
        };
        
        if (apiKey) {
            headers['Authorization'] = `Bearer ${apiKey}`;
        }

        try {
            const response: AxiosResponse = await axios.get(url, {
                headers,
                timeout: this.REQUEST_TIMEOUT,
                validateStatus: (status) => status < 500
            });

            const responseTime = Date.now() - startTime;
            
            if (response.status === 401) {
                return {
                    success: false,
                    responseTime,
                    error: 'Invalid API key'
                };
            }
            
            if (response.status === 200) {
                return {
                    success: true,
                    responseTime,
                    metadata: {
                        modelsCount: response.data?.data?.length || 0
                    }
                };
            }

            return {
                success: false,
                responseTime,
                error: `HTTP ${response.status}: ${response.statusText}`
            };

        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: axios.isAxiosError(error) 
                    ? `Network error: ${error.message}` 
                    : 'Unknown error'
            };
        }
    }

    /**
     * Check local server health (Ollama/LM Studio)
     */
    private async checkLocalHealth(provider: ProviderDetail): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const url = `${provider.apiBaseUrl}${provider.healthCheckUrl || '/api/version'}`;
        
        try {
            const response: AxiosResponse = await axios.get(url, {
                timeout: this.REQUEST_TIMEOUT,
                validateStatus: (status) => status < 500
            });

            const responseTime = Date.now() - startTime;
            
            if (response.status === 200) {
                return {
                    success: true,
                    responseTime,
                    metadata: {
                        version: response.data?.version || 'unknown'
                    }
                };
            }

            return {
                success: false,
                responseTime,
                error: `HTTP ${response.status}: ${response.statusText}`
            };

        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: axios.isAxiosError(error) 
                    ? `Local server not responding: ${error.message}` 
                    : 'Local server unavailable'
            };
        }
    }

    /**
     * Generic health check for unknown providers
     */
    private async checkGenericHealth(
        provider: ProviderDetail, 
        apiKey?: string
    ): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const url = `${provider.apiBaseUrl}${provider.healthCheckUrl || '/'}`;
        
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };
        
        if (apiKey && provider.authType === 'bearerToken') {
            headers['Authorization'] = `Bearer ${apiKey}`;
        } else if (apiKey && provider.authType === 'apiKey') {
            headers['X-API-Key'] = apiKey;
        }

        try {
            const response: AxiosResponse = await axios.get(url, {
                headers,
                timeout: this.REQUEST_TIMEOUT,
                validateStatus: (status) => status < 500
            });

            const responseTime = Date.now() - startTime;
            
            if (response.status >= 200 && response.status < 300) {
                return {
                    success: true,
                    responseTime
                };
            }

            return {
                success: false,
                responseTime,
                error: `HTTP ${response.status}: ${response.statusText}`
            };

        } catch (error) {
            return {
                success: false,
                responseTime: Date.now() - startTime,
                error: axios.isAxiosError(error) 
                    ? `Network error: ${error.message}` 
                    : 'Unknown error'
            };
        }
    }

    /**
     * Check if cached health status is still valid
     */
    private isCacheValid(status: ProviderHealthStatus): boolean {
        const age = Date.now() - status.lastChecked.getTime();
        return age < this.CACHE_DURATION;
    }

    /**
     * Clear health cache for a specific provider or all providers
     */
    public clearCache(providerId?: string): void {
        if (providerId) {
            // Clear all cache entries for this provider
            for (const key of this.healthCache.keys()) {
                if (key.startsWith(providerId)) {
                    this.healthCache.delete(key);
                }
            }
        } else {
            this.healthCache.clear();
        }
    }

    /**
     * Get cached health status without performing new check
     */
    public getCachedHealth(providerId: string, hasApiKey: boolean = false): ProviderHealthStatus | undefined {
        const cacheKey = `${providerId}_${hasApiKey ? 'auth' : 'noauth'}`;
        return this.healthCache.get(cacheKey);
    }

    /**
     * Get all cached health statuses
     */
    public getAllCachedHealth(): ProviderHealthStatus[] {
        return Array.from(this.healthCache.values());
    }
}
