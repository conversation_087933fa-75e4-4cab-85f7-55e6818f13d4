import { ManagedIdentityIdType } from "../utils/Constants.js";
import type { ManagedIdentityIdParams } from "./Configuration.js";
export declare class ManagedIdentityId {
    private _id;
    get id(): string;
    private set id(value);
    private _idType;
    get idType(): ManagedIdentityIdType;
    private set idType(value);
    constructor(managedIdentityIdParams?: ManagedIdentityIdParams);
}
//# sourceMappingURL=ManagedIdentityId.d.ts.map