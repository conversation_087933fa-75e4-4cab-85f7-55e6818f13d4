import * as vscode from 'vscode';
import * as diff from 'diff';
import { WorkerPoolService } from './WorkerPoolService';

export interface UnifiedDiffResult {
    additions: number;
    deletions: number;
    diff: string;
    originalLines?: number;
    modifiedLines?: number;
    changes?: DiffChange[];
    summary?: string;
    filename?: string;
    originalContent?: string;
    newContent?: string;
}

export interface DiffChange {
    type: 'added' | 'removed' | 'unchanged';
    value: string;
    lineNumber?: number;
}

/**
 * Unified Diff Service - Consolidates all diff generation functionality
 * Replaces duplicate implementations in:
 * - FileSystemService.generateDiff() and generateDiffAsync()
 * - FileSystemWorker.generateDiff() and computeUnifiedDiff()
 * - WorkerPoolService.generateDiff()
 * - DiffPreviewService.generateDiff()
 */
export class UnifiedDiffService {
    private outputChannel: vscode.OutputChannel;
    private workerPool?: WorkerPoolService;
    private static readonly LARGE_FILE_THRESHOLD = 100000; // 100KB

    constructor(workerPool?: WorkerPoolService) {
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama Unified Diff');
        this.workerPool = workerPool;
    }

    /**
     * Main diff generation method - automatically chooses best implementation
     * Replaces: FileSystemService.generateDiff(), WorkerPoolService.generateDiff()
     */
    public async generateDiff(
        originalContent: string, 
        newContent: string, 
        filename: string = 'file',
        options?: {
            includeStructuredChanges?: boolean;
            includePreviewHtml?: boolean;
            useWorkerForLargeFiles?: boolean;
        }
    ): Promise<UnifiedDiffResult> {
        const opts = {
            includeStructuredChanges: false,
            includePreviewHtml: false,
            useWorkerForLargeFiles: true,
            ...options
        };

        // For large files, use worker pool for better performance
        const isLargeFile = originalContent.length > UnifiedDiffService.LARGE_FILE_THRESHOLD || 
                           newContent.length > UnifiedDiffService.LARGE_FILE_THRESHOLD;

        if (isLargeFile && this.workerPool && opts.useWorkerForLargeFiles) {
            try {
                return await this.generateDiffAsync(originalContent, newContent, filename, opts);
            } catch (error) {
                console.warn('Worker diff generation failed, falling back to sync:', error);
                return this.generateDiffSync(originalContent, newContent, filename, opts);
            }
        }

        return this.generateDiffSync(originalContent, newContent, filename, opts);
    }

    /**
     * Synchronous diff generation for small files
     * Replaces: FileSystemService.createUnifiedDiff() and countDiffLines()
     */
    public generateDiffSync(
        originalContent: string, 
        newContent: string, 
        filename: string = 'file',
        options?: {
            includeStructuredChanges?: boolean;
            includePreviewHtml?: boolean;
        }
    ): UnifiedDiffResult {
        try {
            const opts = {
                includeStructuredChanges: false,
                includePreviewHtml: false,
                ...options
            };

            // Generate unified diff using the diff library
            const unifiedDiff = this.createUnifiedDiff(originalContent, newContent, filename);
            const { additions, deletions } = this.countDiffLines(unifiedDiff);

            const result: UnifiedDiffResult = {
                additions,
                deletions,
                diff: unifiedDiff,
                originalLines: originalContent.split('\n').length,
                modifiedLines: newContent.split('\n').length
            };

            // Add structured changes if requested (for preview functionality)
            if (opts.includeStructuredChanges) {
                const changes = diff.diffLines(originalContent, newContent);
                result.changes = this.convertToStructuredChanges(changes);
                result.filename = filename;
                result.originalContent = originalContent;
                result.newContent = newContent;
                result.summary = `+${additions} -${deletions}`;
            }

            this.outputChannel.appendLine(`Generated diff for ${filename}: +${additions} -${deletions}`);
            return result;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`❌ Failed to generate diff for ${filename}: ${errorMsg}`);
            
            // Return fallback result
            return {
                additions: 1,
                deletions: 1,
                diff: `--- a/${filename}\n+++ b/${filename}\n@@ -1,1 +1,1 @@\n-${originalContent}\n+${newContent}`,
                originalLines: originalContent.split('\n').length,
                modifiedLines: newContent.split('\n').length
            };
        }
    }

    /**
     * Asynchronous diff generation using worker pool
     * Replaces: FileSystemService.generateDiffAsync()
     */
    public async generateDiffAsync(
        originalContent: string, 
        newContent: string, 
        filename: string = 'file',
        options?: {
            includeStructuredChanges?: boolean;
            includePreviewHtml?: boolean;
        }
    ): Promise<UnifiedDiffResult> {
        if (!this.workerPool) {
            // Fallback to synchronous method
            return this.generateDiffSync(originalContent, newContent, filename, options);
        }

        try {
            const result = await this.workerPool.generateDiff(originalContent, newContent, filename);
            
            // Convert worker result to unified format
            const unifiedResult: UnifiedDiffResult = {
                additions: result.additions,
                deletions: result.deletions,
                diff: result.diff,
                originalLines: result.originalLines,
                modifiedLines: result.modifiedLines
            };

            // Add structured changes if requested
            if (options?.includeStructuredChanges) {
                const changes = diff.diffLines(originalContent, newContent);
                unifiedResult.changes = this.convertToStructuredChanges(changes);
                unifiedResult.filename = filename;
                unifiedResult.originalContent = originalContent;
                unifiedResult.newContent = newContent;
                unifiedResult.summary = `+${result.additions} -${result.deletions}`;
            }

            return unifiedResult;
        } catch (error) {
            console.error('Worker diff generation failed:', error);
            throw error;
        }
    }

    /**
     * Create unified diff format
     * Replaces: FileSystemService.createUnifiedDiff() and FileSystemWorker.computeUnifiedDiff()
     */
    private createUnifiedDiff(originalContent: string, newContent: string, filename: string): string {
        const lines1 = originalContent.split('\n');
        const lines2 = newContent.split('\n');
        
        const diff: string[] = [];
        diff.push(`--- a/${filename}`);
        diff.push(`+++ b/${filename}`);
        
        // Simple unified diff implementation
        let i = 0, j = 0;
        const hunkLines: string[] = [];
        let hunkStart = -1;
        
        while (i < lines1.length || j < lines2.length) {
            if (i < lines1.length && j < lines2.length && lines1[i] === lines2[j]) {
                // Lines match
                if (hunkStart !== -1) {
                    hunkLines.push(` ${lines1[i]}`);
                }
                i++;
                j++;
            } else {
                // Lines differ - start a hunk if not already started
                if (hunkStart === -1) {
                    hunkStart = Math.max(0, i - 3); // Include 3 lines of context
                    // Add context lines
                    for (let k = hunkStart; k < i; k++) {
                        if (k < lines1.length) {
                            hunkLines.push(` ${lines1[k]}`);
                        }
                    }
                }
                
                if (i < lines1.length && (j >= lines2.length || lines1[i] !== lines2[j])) {
                    hunkLines.push(`-${lines1[i]}`);
                    i++;
                } else if (j < lines2.length) {
                    hunkLines.push(`+${lines2[j]}`);
                    j++;
                }
            }
        }
        
        // Add final hunk if exists
        if (hunkStart !== -1) {
            const removedCount = hunkLines.filter(l => l.startsWith('-')).length;
            const addedCount = hunkLines.filter(l => l.startsWith('+')).length;
            diff.push(`@@ -${hunkStart + 1},${removedCount} +${hunkStart + 1},${addedCount} @@`);
            diff.push(...hunkLines);
        }
        
        return diff.join('\n');
    }

    /**
     * Count additions and deletions in diff
     * Replaces: FileSystemService.countDiffLines() and FileSystemWorker.countDiffLines()
     */
    private countDiffLines(diffText: string): { additions: number; deletions: number } {
        const lines = diffText.split('\n');
        let additions = 0;
        let deletions = 0;
        
        for (const line of lines) {
            if (line.startsWith('+') && !line.startsWith('+++')) {
                additions++;
            } else if (line.startsWith('-') && !line.startsWith('---')) {
                deletions++;
            }
        }
        
        return { additions, deletions };
    }

    /**
     * Convert diff.js changes to structured format
     * Replaces: DiffPreviewService structured change logic
     */
    private convertToStructuredChanges(changes: diff.Change[]): DiffChange[] {
        const structuredChanges: DiffChange[] = [];
        let currentLineNumber = 1;

        for (const change of changes) {
            const lines = change.value.split('\n');
            // Remove the last empty line if it exists
            if (lines[lines.length - 1] === '') {
                lines.pop();
            }

            for (const line of lines) {
                if (change.added) {
                    structuredChanges.push({
                        type: 'added',
                        value: line,
                        lineNumber: currentLineNumber
                    });
                    currentLineNumber++;
                } else if (change.removed) {
                    structuredChanges.push({
                        type: 'removed',
                        value: line,
                        lineNumber: currentLineNumber
                    });
                    // Don't increment line number for removed lines
                } else {
                    structuredChanges.push({
                        type: 'unchanged',
                        value: line,
                        lineNumber: currentLineNumber
                    });
                    currentLineNumber++;
                }
            }
        }

        return structuredChanges;
    }

    /**
     * Read current file content for diff comparison
     * Replaces: DiffPreviewService.readCurrentFileContent()
     */
    public async readCurrentFileContent(fileName: string): Promise<string | null> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                return null;
            }

            const filePath = vscode.Uri.joinPath(workspaceFolders[0].uri, fileName);
            const fileContent = await vscode.workspace.fs.readFile(filePath);
            return Buffer.from(fileContent).toString('utf8');
        } catch (error) {
            // File doesn't exist or can't be read
            return null;
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.outputChannel.dispose();
    }
}
