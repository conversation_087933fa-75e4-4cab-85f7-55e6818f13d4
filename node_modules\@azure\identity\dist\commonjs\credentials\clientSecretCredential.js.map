{"version": 3, "file": "clientSecretCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientSecretCredential.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAIlC,mEAAmE;AACnE,+DAGkC;AAGlC,4CAA0D;AAC1D,mDAAsD;AACtD,yDAAqD;AACrD,mDAAmD;AAEnD,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,wBAAwB,CAAC,CAAC;AAE1D;;;;;;;GAOG;AACH,MAAa,sBAAsB;IAMjC;;;;;;;;;OASG;IACH,YACE,QAAgB,EAChB,QAAgB,EAChB,YAAoB,EACpB,UAAyC,EAAE;QAE3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,sCAA0B,CAClC,gKAAgK,CACjK,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,sCAA0B,CAClC,gKAAgK,CACjK,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,sCAA0B,CAClC,oKAAoK,CACrK,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,IAAA,sDAAmC,EACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAA,gCAAgB,EAAC,QAAQ,EAAE,QAAQ,kCAChD,OAAO,KACV,MAAM,EACN,sBAAsB,EAAE,OAAO,IAC/B,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,0BAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,IAAA,4CAAyB,EAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,IAAA,4BAAY,EAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAC5F,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA9ED,wDA8EC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport type { MsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport { createMsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils.js\";\n\nimport type { ClientSecretCredentialOptions } from \"./clientSecretCredentialOptions.js\";\nimport { CredentialUnavailableError } from \"../errors.js\";\nimport { credentialLogger } from \"../util/logging.js\";\nimport { ensureScopes } from \"../util/scopeUtils.js\";\nimport { tracingClient } from \"../util/tracing.js\";\n\nconst logger = credentialLogger(\"ClientSecretCredential\");\n\n/**\n * Enables authentication to Microsoft Entra ID using a client secret\n * that was generated for an App Registration. More information on how\n * to configure a client secret can be found here:\n *\n * https://learn.microsoft.com/entra/identity-platform/quickstart-configure-app-access-web-apis#add-credentials-to-your-web-application\n *\n */\nexport class ClientSecretCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalClient: MsalClient;\n  private clientSecret: string;\n\n  /**\n   * Creates an instance of the ClientSecretCredential with the details\n   * needed to authenticate against Microsoft Entra ID with a client\n   * secret.\n   *\n   * @param tenantId - The Microsoft Entra tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    clientSecret: string,\n    options: ClientSecretCredentialOptions = {},\n  ) {\n    if (!tenantId) {\n      throw new CredentialUnavailableError(\n        \"ClientSecretCredential: tenantId is a required parameter. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.\",\n      );\n    }\n\n    if (!clientId) {\n      throw new CredentialUnavailableError(\n        \"ClientSecretCredential: clientId is a required parameter. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.\",\n      );\n    }\n\n    if (!clientSecret) {\n      throw new CredentialUnavailableError(\n        \"ClientSecretCredential: clientSecret is a required parameter. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.\",\n      );\n    }\n\n    this.clientSecret = clientSecret;\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n\n    this.msalClient = createMsalClient(clientId, tenantId, {\n      ...options,\n      logger,\n      tokenCredentialOptions: options,\n    });\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger,\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalClient.getTokenByClientSecret(arrayScopes, this.clientSecret, newOptions);\n      },\n    );\n  }\n}\n"]}