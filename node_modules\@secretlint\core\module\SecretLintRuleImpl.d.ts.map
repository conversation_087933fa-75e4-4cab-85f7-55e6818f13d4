{"version": 3, "file": "SecretLintRuleImpl.d.ts", "sourceRoot": "", "sources": ["../src/SecretLintRuleImpl.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,qBAAqB,EAErB,wBAAwB,EAExB,oBAAoB,EACvB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,YAAY,EAAE,MAAM,iCAAiC,CAAC;AAE/D,MAAM,MAAM,qBAAqB,GAAG;IAChC,OAAO,EAAE,qBAAqB,CAAC;IAC/B,cAAc,EAAE,wBAAwB,CAAC;CAC5C,CAAC;AAEF,qBAAa,cAAc;IACvB,OAAO,CAAC,gBAAgB,CAA8B;IACtD,OAAO,CAAC,WAAW,CAAwB;IAC3C,OAAO,CAAC,cAAc,CAA2B;gBAErC,EAAE,cAAc,EAAE,OAAO,EAAE,EAAE,qBAAqB;IAQ9D,eAAe,IAAI,YAAY,EAAE;IAajC,iBAAiB,CAAC,UAAU,EAAE,oBAAoB;IAW5C,IAAI,CAAC,MAAM,EAAE,oBAAoB;CAO1C"}