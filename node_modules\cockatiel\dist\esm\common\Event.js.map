{"version": 3, "file": "Event.js", "sourceRoot": "", "sources": ["../../../src/common/Event.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AASlE,MAAM,CAAC,MAAM,cAAc,GAAgB,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;AAOxE,MAAM,KAAW,KAAK,CA8DrB;AA9DD,WAAiB,KAAK;IACpB;;OAEG;IACU,UAAI,GAAG,CAAI,KAAe,EAAE,QAA2B,EAAe,EAAE;QACnF,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,UAA8B,CAAC;QAEnC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;YACzB,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEhB,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,IAAI,CAAC,CAAC,kDAAkD;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,EAAE,CAAC;YAChB,UAAU,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,cAAc,CAAC,CAAC,mCAAmC;QAC5D,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;IAEF;;;OAGG;IACU,eAAS,GAAG,CAAI,KAAe,EAAE,MAAoB,EAAc,EAAE;QAChF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,OAAO,CAAI,OAAO,CAAC,EAAE,CAAC,MAAA,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACjC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEzB,SAAS,CAAC,IAAI,CACZ,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE;gBAClB,MAAM,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC;YACnC,CAAC,CAAC,CACH,CAAC;YAEF,SAAS,CAAC,IAAI,CACZ,MAAA,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBACjB,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE,CAAC;gBAC1B,CAAC,CAAC,OAAO,EAAE,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,EA9DgB,KAAK,KAAL,KAAK,QA8DrB;AAED,8DAA8D;AAC9D,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,MAAmB,EAAwC,EAAE;IACnF,MAAM,GAAG,GAAG,IAAI,YAAY,EAAQ,CAAC;IACrC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC;IACvD,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,EAAE,CAAE,MAAc,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAEtE,kEAAkE;IAClE,MAAM,CAAC,GAAG,GAAG,EAAE;QACb,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAED,MAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAE7C,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;AAC7C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,YAAY;IAAzB;QAGE;;WAEG;QACa,gBAAW,GAAa,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAiEtF,CAAC;IA/DC;;OAEG;IACH,IAAW,IAAI;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;YAChD,OAAO,CAAC,CAAC;QACX,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,KAAQ;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,QAAQ;QACV,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAES,gBAAgB,CAAC,QAA2B;QACpD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC5B,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;YAChD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC1D,CAAC;IAEO,cAAc,CAAC,QAA2B;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAChC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC7B,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,sBAA0B,SAAQ,YAAe;IAA9D;;QAcE;;WAEG;QACa,gBAAW,GAAa,QAAQ,CAAC,EAAE;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YAED,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC;IASJ,CAAC;IA1BC;;OAEG;IACH,IAAW,UAAU;QACnB,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAcD;;OAEG;IACI,IAAI,CAAC,KAAQ;QAClB,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,YAAgB,SAAQ,YAAe;IAA7C;;QAOE;;WAEG;QACa,gBAAW,GAAa,QAAQ,CAAC,EAAE;YACjD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC/B,OAAO,cAAc,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;QACH,CAAC,CAAC;IAUJ,CAAC;IARC;;OAEG;IACI,IAAI,CAAC,KAAQ;QAClB,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;CACF", "sourcesContent": ["import { TaskCancelledError } from '../errors/TaskCancelledError';\n\n/**\n * Type that can be disposed.\n */\nexport interface IDisposable {\n  dispose(): void;\n}\n\nexport const noopDisposable: IDisposable = { dispose: () => undefined };\n\n/**\n * Function that subscribes the method to receive data.\n */\nexport type Event<T> = (listener: (data: T) => void) => IDisposable;\n\nexport namespace Event {\n  /**\n   * Adds a handler that handles one event on the emitter.\n   */\n  export const once = <T>(event: Event<T>, listener: (data: T) => void): IDisposable => {\n    let syncDispose = false;\n    let disposable: IDisposable | void;\n\n    disposable = event(value => {\n      listener(value);\n\n      if (disposable) {\n        disposable.dispose();\n      } else {\n        syncDispose = true; // callback can fire before disposable is returned\n      }\n    });\n\n    if (syncDispose) {\n      disposable.dispose();\n      return noopDisposable; // no reason to keep the ref around\n    }\n\n    return disposable;\n  };\n\n  /**\n   * Returns a promise that resolves when the event fires, or when cancellation\n   * is requested, whichever happens first.\n   */\n  export const toPromise = <T>(event: Event<T>, signal?: AbortSignal): Promise<T> => {\n    if (!signal) {\n      return new Promise<T>(resolve => once(event, resolve));\n    }\n\n    if (signal.aborted) {\n      return Promise.reject(new TaskCancelledError());\n    }\n\n    const toDispose: IDisposable[] = [];\n\n    return new Promise<T>((resolve, reject) => {\n      const abortEvt = onAbort(signal);\n      toDispose.push(abortEvt);\n\n      toDispose.push(\n        abortEvt.event(() => {\n          reject(new TaskCancelledError());\n        }),\n      );\n\n      toDispose.push(\n        once(event, data => {\n          resolve(data);\n        }),\n      );\n    }).finally(() => {\n      for (const d of toDispose) {\n        d.dispose();\n      }\n    });\n  };\n}\n\n/** Creates an Event that fires when the signal is aborted. */\nexport const onAbort = (signal: AbortSignal): { event: Event<void> } & IDisposable => {\n  const evt = new OneShotEvent<void>();\n  if (signal.aborted) {\n    evt.emit();\n    return { event: evt.addListener, dispose: () => {} };\n  }\n\n  const dispose = () => (signal as any).removeEventListener('abort', l);\n\n  // @types/node is currently missing the event types on AbortSignal\n  const l = () => {\n    evt.emit();\n    dispose();\n  };\n\n  (signal as any).addEventListener('abort', l);\n\n  return { event: evt.addListener, dispose };\n};\n\n/**\n * Base event emitter. Calls listeners when data is emitted.\n */\nexport class EventEmitter<T> {\n  protected listeners?: Array<(data: T) => void> | ((data: T) => void);\n\n  /**\n   * Event<T> function.\n   */\n  public readonly addListener: Event<T> = listener => this.addListenerInner(listener);\n\n  /**\n   * Gets the number of event listeners.\n   */\n  public get size() {\n    if (!this.listeners) {\n      return 0;\n    } else if (typeof this.listeners === 'function') {\n      return 1;\n    } else {\n      return this.listeners.length;\n    }\n  }\n\n  /**\n   * Emits event data.\n   */\n  public emit(value: T) {\n    if (!this.listeners) {\n      // no-op\n    } else if (typeof this.listeners === 'function') {\n      this.listeners(value);\n    } else {\n      for (const listener of this.listeners) {\n        listener(value);\n      }\n    }\n  }\n\n  protected addListenerInner(listener: (data: T) => void): IDisposable {\n    if (!this.listeners) {\n      this.listeners = listener;\n    } else if (typeof this.listeners === 'function') {\n      this.listeners = [this.listeners, listener];\n    } else {\n      this.listeners.push(listener);\n    }\n\n    return { dispose: () => this.removeListener(listener) };\n  }\n\n  private removeListener(listener: (data: T) => void) {\n    if (!this.listeners) {\n      return;\n    }\n\n    if (typeof this.listeners === 'function') {\n      if (this.listeners === listener) {\n        this.listeners = undefined;\n      }\n      return;\n    }\n\n    const index = this.listeners.indexOf(listener);\n    if (index === -1) {\n      return;\n    }\n\n    if (this.listeners.length === 2) {\n      this.listeners = index === 0 ? this.listeners[1] : this.listeners[0];\n    } else {\n      this.listeners = this.listeners.slice(0, index).concat(this.listeners.slice(index + 1));\n    }\n  }\n}\n\n/**\n * An event emitter that memorizes and instantly re-emits its last value\n * to attached listeners.\n */\nexport class MemorizingEventEmitter<T> extends EventEmitter<T> {\n  /**\n   * Last emitted value, wrapped in an object so that we can correctly detect\n   * emission of 'undefined' values.\n   */\n  private lastValue?: { value: T };\n\n  /**\n   * Gets whether this emitter has yet emitted any event.\n   */\n  public get hasEmitted() {\n    return !!this.lastValue;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public readonly addListener: Event<T> = listener => {\n    const disposable = this.addListenerInner(listener);\n    if (this.lastValue) {\n      listener(this.lastValue.value);\n    }\n\n    return disposable;\n  };\n\n  /**\n   * @inheritdoc\n   */\n  public emit(value: T) {\n    this.lastValue = { value };\n    super.emit(value);\n  }\n}\n\n/**\n * An event emitter that fires a value once and removes all\n * listeners automatically after doing so.\n */\nclass OneShotEvent<T> extends EventEmitter<T> {\n  /**\n   * Last emitted value, wrapped in an object so that we can correctly detect\n   * emission of 'undefined' values.\n   */\n  private lastValue?: { value: T };\n\n  /**\n   * @inheritdoc\n   */\n  public readonly addListener: Event<T> = listener => {\n    if (this.lastValue) {\n      listener(this.lastValue.value);\n      return noopDisposable;\n    } else {\n      return this.addListenerInner(listener);\n    }\n  };\n\n  /**\n   * @inheritdoc\n   */\n  public emit(value: T) {\n    this.lastValue = { value };\n    super.emit(value);\n    this.listeners = undefined;\n  }\n}\n"]}