import * as vscode from 'vscode';
import { ProviderRegistryService } from './ProviderRegistryService';
import { ProviderHealthService } from './ProviderHealthService';
import { AuthenticationFlowService } from './AuthenticationFlowService';
import { ModelDiscoveryService } from './ModelDiscoveryService';
import { ProviderStatusMonitoringService } from './ProviderStatusMonitoringService';
import { ProviderStatusIndicatorService } from './ProviderStatusIndicatorService';
import { DynamicProviderService } from './DynamicProviderService';
import { ApiKeyManager, SupportedProvider } from './ApiKeyManager';
import { SettingsService } from './SettingsService';

/**
 * Integration service that coordinates all dynamic provider management components
 */
export class DynamicProviderIntegrationService {
    private static instance: DynamicProviderIntegrationService;
    private _disposables: vscode.Disposable[] = [];
    private _initialized = false;

    // Service instances
    private _registryService: ProviderRegistryService;
    private _healthService: ProviderHealthService;
    private _authService: AuthenticationFlowService;
    private _modelDiscoveryService?: ModelDiscoveryService;
    private _monitoringService: ProviderStatusMonitoringService;
    private _statusIndicatorService: ProviderStatusIndicatorService;
    private _dynamicProviderService?: DynamicProviderService;
    private _apiKeyManager?: ApiKeyManager;
    private _settingsService?: SettingsService;

    public static getInstance(): DynamicProviderIntegrationService {
        if (!DynamicProviderIntegrationService.instance) {
            DynamicProviderIntegrationService.instance = new DynamicProviderIntegrationService();
        }
        return DynamicProviderIntegrationService.instance;
    }

    constructor() {
        // Initialize core services
        this._registryService = ProviderRegistryService.getInstance();
        this._healthService = ProviderHealthService.getInstance();
        this._authService = AuthenticationFlowService.getInstance();
        // Will be initialized during setup
        this._monitoringService = ProviderStatusMonitoringService.getInstance();
        this._statusIndicatorService = ProviderStatusIndicatorService.getInstance();
    }

    /**
     * Initialize all dynamic provider management services
     */
    public async initialize(
        context: vscode.ExtensionContext,
        apiKeyManager: ApiKeyManager,
        settingsService: SettingsService
    ): Promise<void> {
        if (this._initialized) {
            console.warn('DynamicProviderIntegrationService already initialized');
            return;
        }

        console.log('Initializing Dynamic Provider Management System...');

        // Store service references
        this._apiKeyManager = apiKeyManager;
        this._settingsService = settingsService;

        try {
            // 1. Initialize provider registry with dynamic configuration
            await this._registryService.initialize(context);
            console.log('✓ Provider Registry initialized');

            // 2. Initialize model discovery service
            this._modelDiscoveryService = new ModelDiscoveryService();
            console.log('✓ Model Discovery Service initialized');

            // 3. Initialize dynamic provider service
            this._dynamicProviderService = new DynamicProviderService(
                context,
                apiKeyManager,
                settingsService
            );
            console.log('✓ Dynamic Provider Service initialized');

            // 4. Initialize monitoring service
            await this._monitoringService.initialize(context);
            console.log('✓ Provider Status Monitoring initialized');

            // 5. Initialize status indicator
            await this._statusIndicatorService.initialize(context);
            console.log('✓ Provider Status Indicator initialized');

            // 6. Register commands
            this.registerCommands(context);
            console.log('✓ Commands registered');

            // 7. Set up event listeners
            this.setupEventListeners();
            console.log('✓ Event listeners configured');

            // 8. Perform initial health checks
            await this.performInitialHealthChecks();
            console.log('✓ Initial health checks completed');

            this._initialized = true;
            console.log('🚀 Dynamic Provider Management System fully initialized');

        } catch (error) {
            console.error('Failed to initialize Dynamic Provider Management System:', error);
            vscode.window.showErrorMessage(
                'Failed to initialize provider management system. Some features may not work correctly.'
            );
            throw error;
        }
    }

    /**
     * Register VS Code commands
     */
    private registerCommands(context: vscode.ExtensionContext): void {
        const commands = [
            // Provider management commands
            vscode.commands.registerCommand('v1b3-sama.refreshProviders', async () => {
                await this.refreshAllProviders();
                vscode.window.showInformationMessage('Provider information refreshed');
            }),

            vscode.commands.registerCommand('v1b3-sama.setupApiKey', async () => {
                const provider = await this.selectProvider('Select provider to set up API key for:');
                if (provider) {
                    await this._authService.setupApiKey(provider);
                }
            }),

            vscode.commands.registerCommand('v1b3-sama.testProviderHealth', async () => {
                const provider = await this.selectProvider('Select provider to test:');
                if (provider) {
                    await this.testProviderHealth(provider);
                }
            }),

            vscode.commands.registerCommand('v1b3-sama.switchProvider', async () => {
                await this.switchProvider();
            }),

            vscode.commands.registerCommand('v1b3-sama.showProviderDashboard', async () => {
                await this.showProviderDashboard();
            }),

            // Model discovery commands
            vscode.commands.registerCommand('v1b3-sama.refreshModels', async () => {
                await this.refreshModels();
                vscode.window.showInformationMessage('Model information refreshed');
            }),

            vscode.commands.registerCommand('v1b3-sama.searchModels', async () => {
                await this.searchModels();
            }),

            // Monitoring commands
            vscode.commands.registerCommand('v1b3-sama.toggleMonitoring', async () => {
                await this.toggleMonitoring();
            }),

            vscode.commands.registerCommand('v1b3-sama.showMonitoringStats', async () => {
                await this.showMonitoringStats();
            })
        ];

        this._disposables.push(...commands);
        context.subscriptions.push(...this._disposables);
    }

    /**
     * Set up event listeners between services
     */
    private setupEventListeners(): void {
        if (!this._apiKeyManager || !this._settingsService) return;

        // Listen for API key changes and refresh health status
        if (this._apiKeyManager) {
            this._disposables.push(
                this._apiKeyManager.onDidChangeApiKey(async ({ provider }) => {
                    console.log(`API key changed for ${provider}, refreshing health status`);
                    try {
                        await this._monitoringService.forceHealthCheck(provider);
                    } catch (error) {
                        console.error(`Failed to refresh health for ${provider}:`, error);
                    }
                })
            );
        }

        // Listen for provider status changes and show notifications
        this._disposables.push(
            this._monitoringService.onDidChangeProviderStatus(status => {
                this._statusIndicatorService.showHealthNotification(status);
            })
        );

        // Listen for settings changes
        if (this._settingsService) {
            this._disposables.push(
                this._settingsService.onDidChangeSettings(event => {
                    if (event.section === 'monitoring') {
                        console.log('Monitoring settings changed, reloading configuration');
                    }
                })
            );
        }
    }

    /**
     * Perform initial health checks for all providers
     */
    private async performInitialHealthChecks(): Promise<void> {
        try {
            const providers = this._registryService.getProvidersRequiringApiKeys();
            
            for (const provider of providers) {
                const needsSetup = await this._authService.needsApiKeySetup(provider.id as SupportedProvider);
                if (needsSetup) {
                    console.log(`Provider ${provider.id} needs API key setup`);
                } else {
                    console.log(`Provider ${provider.id} is configured`);
                }
            }
        } catch (error) {
            console.error('Failed to perform initial health checks:', error);
        }
    }

    /**
     * Refresh all provider information
     */
    public async refreshAllProviders(): Promise<void> {
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Refreshing provider information...',
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 20, message: 'Reloading provider configuration' });
                await this._registryService.reloadConfiguration();

                progress.report({ increment: 40, message: 'Clearing model cache' });
                if (this._modelDiscoveryService) {
                    this._modelDiscoveryService.clearCache();
                }

                progress.report({ increment: 60, message: 'Refreshing health status' });
                this._healthService.clearCache();

                progress.report({ increment: 80, message: 'Updating status indicators' });
                await this._statusIndicatorService.forceRefresh();

                progress.report({ increment: 100, message: 'Complete' });
            });
        } catch (error) {
            console.error('Failed to refresh providers:', error);
            vscode.window.showErrorMessage('Failed to refresh provider information');
        }
    }

    /**
     * Test health for a specific provider
     */
    private async testProviderHealth(provider: SupportedProvider): Promise<void> {
        try {
            const status = await this._monitoringService.forceHealthCheck(provider);
            
            const statusText = status.status.toUpperCase();
            const responseTime = status.responseTime ? ` (${status.responseTime}ms)` : '';
            
            if (status.status === 'online') {
                vscode.window.showInformationMessage(
                    `✅ ${provider} is ${statusText}${responseTime}`
                );
            } else {
                vscode.window.showWarningMessage(
                    `⚠️ ${provider} is ${statusText}${responseTime}`
                );
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to test ${provider}: ${error}`);
        }
    }

    /**
     * Switch to a different provider
     */
    private async switchProvider(): Promise<void> {
        if (!this._settingsService || !this._dynamicProviderService) return;

        try {
            const availableProviders = await this._dynamicProviderService.getAvailableProviders();
            const currentProvider = this._settingsService.get('provider');

            const items = availableProviders.map(provider => ({
                label: provider,
                description: provider === currentProvider ? '(Current)' : '',
                picked: provider === currentProvider
            }));

            const selected = await vscode.window.showQuickPick(items, {
                title: 'Select LLM Provider',
                placeHolder: 'Choose a provider to switch to'
            });

            if (selected && selected.label !== currentProvider) {
                await this._dynamicProviderService.switchProvider(selected.label as SupportedProvider);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to switch provider: ${error}`);
        }
    }

    /**
     * Show provider dashboard
     */
    private async showProviderDashboard(): Promise<void> {
        // This would open a webview with comprehensive provider information
        // For now, we'll show the status overview
        await vscode.commands.executeCommand('v1b3-sama.showProviderStatus');
    }

    /**
     * Refresh models for all providers
     */
    private async refreshModels(): Promise<void> {
        if (!this._apiKeyManager) return;

        try {
            const apiKeys: Record<string, string> = {};
            const providers = this._registryService.getProvidersRequiringApiKeys();
            
            for (const provider of providers) {
                const apiKey = await this._apiKeyManager.getApiKey(provider.id as SupportedProvider);
                if (apiKey) {
                    apiKeys[provider.id] = apiKey;
                }
            }

            if (this._modelDiscoveryService) {
                await this._modelDiscoveryService.getAllModels(apiKeys);
            }
        } catch (error) {
            console.error('Failed to refresh models:', error);
        }
    }

    /**
     * Search models across all providers
     */
    private async searchModels(): Promise<void> {
        if (!this._apiKeyManager) return;

        const query = await vscode.window.showInputBox({
            prompt: 'Search for models',
            placeHolder: 'Enter model name or description...'
        });

        if (!query) return;

        try {
            const apiKeys: Record<string, string> = {};
            const providers = this._registryService.getProvidersRequiringApiKeys();
            
            for (const provider of providers) {
                const apiKey = await this._apiKeyManager.getApiKey(provider.id as SupportedProvider);
                if (apiKey) {
                    apiKeys[provider.id] = apiKey;
                }
            }

            if (!this._modelDiscoveryService) {
                vscode.window.showErrorMessage('Model discovery service not available');
                return;
            }

            const results = await this._modelDiscoveryService.searchModels(query, apiKeys);
            
            if (results.length === 0) {
                vscode.window.showInformationMessage('No models found matching your search');
                return;
            }

            const items = results.map(model => ({
                label: model.name,
                description: `${model.provider} | ${model.id}`,
                detail: model.description
            }));

            await vscode.window.showQuickPick(items, {
                title: `Found ${results.length} models`,
                placeHolder: 'Select a model to view details'
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Search failed: ${error}`);
        }
    }

    /**
     * Toggle monitoring on/off
     */
    private async toggleMonitoring(): Promise<void> {
        const config = this._monitoringService.getConfiguration();
        const newState = !config.enabled;
        
        await this._monitoringService.updateConfiguration({ enabled: newState });
        
        vscode.window.showInformationMessage(
            `Provider monitoring ${newState ? 'enabled' : 'disabled'}`
        );
    }

    /**
     * Show monitoring statistics
     */
    private async showMonitoringStats(): Promise<void> {
        const stats = this._monitoringService.getMonitoringStats();
        
        const message = [
            `Providers: ${stats.onlineProviders}/${stats.totalProviders} online`,
            `Overall uptime: ${stats.overallUptime.toFixed(1)}%`,
            `Average response time: ${stats.averageResponseTime.toFixed(0)}ms`,
            `Total health checks: ${stats.totalChecks}`
        ].join('\n');

        vscode.window.showInformationMessage(message, { modal: true });
    }

    /**
     * Helper method to select a provider
     */
    private async selectProvider(prompt: string): Promise<SupportedProvider | undefined> {
        const providers = this._registryService.getProviderIds();
        const selected = await vscode.window.showQuickPick(providers, {
            title: prompt,
            placeHolder: 'Choose a provider'
        });
        return selected as SupportedProvider;
    }

    /**
     * Get all service instances (for external access)
     */
    public getServices() {
        return {
            registry: this._registryService,
            health: this._healthService,
            auth: this._authService,
            modelDiscovery: this._modelDiscoveryService,
            monitoring: this._monitoringService,
            statusIndicator: this._statusIndicatorService,
            dynamicProvider: this._dynamicProviderService
        };
    }

    /**
     * Check if the service is initialized
     */
    public isInitialized(): boolean {
        return this._initialized;
    }

    /**
     * Dispose of all resources
     */
    public dispose(): void {
        console.log('Disposing Dynamic Provider Management System...');
        
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];

        this._registryService.dispose();
        this._monitoringService.dispose();
        this._statusIndicatorService.dispose();
        this._dynamicProviderService?.dispose();

        this._initialized = false;
        console.log('✓ Dynamic Provider Management System disposed');
    }
}
