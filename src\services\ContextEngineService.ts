/**
 * Context Engine Service - Advanced code context understanding and retrieval
 * Inspired by Augment's real-time indexing and Refact's AST-based tools
 */

import * as vscode from 'vscode';
import * as ts from 'typescript';
import * as path from 'path';
import {
    IContextEngineService,
    AstSymbol,
    CodeRelationship,
    RankedContext,
    ContextQuery,
    ContextSearchResult,
    IndexingProgress,
    ContextEngineStats,
    LearningData,
    ContextRankingFactors,
    ExecutionPlanContext,
    ExecutionPlanQuery
} from '../interfaces/IContextEngineService';
import { ExecutionPlan, ExecutionStep } from '../interfaces/ILLMService';

export class ContextEngineService implements IContextEngineService {
    private fileAstCache = new Map<string, AstSymbol[]>();
    private relationshipGraph = new Map<string, CodeRelationship[]>();
    private searchIndex = new Map<string, Set<string>>();
    private symbolIndex = new Map<string, AstSymbol[]>();
    private executionPlanCache = new Map<string, ExecutionPlanContext>();
    private executionPlanFileIndex = new Map<string, Set<string>>(); // file -> plan IDs
    private fileWatcher?: vscode.FileSystemWatcher;
    private indexingProgress: IndexingProgress;
    private learningData: LearningData;
    private disposables: vscode.Disposable[] = [];

    // Event emitters
    private _onIndexingProgress = new vscode.EventEmitter<IndexingProgress>();
    private _onIndexingComplete = new vscode.EventEmitter<ContextEngineStats>();
    private _onError = new vscode.EventEmitter<Error>();

    public readonly onIndexingProgress = this._onIndexingProgress.event;
    public readonly onIndexingComplete = this._onIndexingComplete.event;
    public readonly onError = this._onError.event;

    constructor(private context: vscode.ExtensionContext) {
        this.indexingProgress = {
            totalFiles: 0,
            processedFiles: 0,
            currentFile: '',
            stage: 'scanning',
            errors: []
        };

        this.learningData = {
            acceptedSuggestions: new Map(),
            rejectedSuggestions: new Map(),
            frequentlyAccessedFiles: new Map(),
            userPatterns: new Map(),
            contextPreferences: new Map()
        };

        this.setupFileWatchers();
        this.loadLearningData();
        this.loadExecutionPlans();
    }

    public async initialize(): Promise<void> {
        console.log('Context Engine: Initializing...');
        
        try {
            await this.scanWorkspace();
            console.log('Context Engine: Initialization complete');
        } catch (error) {
            console.error('Context Engine: Initialization failed', error);
            this._onError.fire(error as Error);
        }
    }

    public async getRankedContext(query: ContextQuery): Promise<ContextSearchResult> {
        const startTime = Date.now();

        try {
            // Record user interaction for learning
            this.recordQueryInteraction(query.query);

            // Analyze query intent
            const queryAnalysis = this.analyzeQuery(query.query);

            // Get candidate results
            const candidates = await this.searchCandidates(query);

            // Rank results
            const rankedResults = await this.rankResults(candidates, query);

            // Apply limits
            const maxResults = query.maxResults || 10;
            const results = rankedResults.slice(0, maxResults);

            const searchTime = Date.now() - startTime;

            // Log context retrieval for debugging
            console.log(`Context Engine: Retrieved ${results.length} results for query "${query.query}" in ${searchTime}ms`);

            return {
                results,
                totalMatches: candidates.length,
                searchTime,
                queryAnalysis
            };
        } catch (error) {
            console.error('Context Engine: Search failed', error);
            this._onError.fire(error as Error);
            throw error;
        }
    }

    /**
     * Get enhanced context for AI prompts - automatically called by LLM services
     * Now includes file operation guidance for Augment-style behavior
     */
    public async getEnhancedContext(userPrompt: string): Promise<string> {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            const contextQuery: ContextQuery = {
                query: userPrompt,
                activeFile: activeEditor?.document.uri.fsPath,
                selectedText: activeEditor?.selection && !activeEditor.selection.isEmpty
                    ? activeEditor.document.getText(activeEditor.selection)
                    : undefined,
                maxResults: 5,
                includeSymbols: true,
                includeRelationships: true
            };

            const searchResult = await this.getRankedContext(contextQuery);

            // Analyze file operation requirements
            const fileOpAnalysis = await this.analyzeFileOperationRequirements(userPrompt, activeEditor);

            // Build enhanced context string with file operation guidance
            let contextString = '\n\n## 🎯 WORKSPACE CONTEXT & FILE OPERATION GUIDANCE\n\n';

            // Add file operation requirements first (most important)
            contextString += fileOpAnalysis.guidance;

            if (searchResult.results.length > 0) {
                contextString += '\n## 📁 Relevant Code Context\n\n';

                for (const result of searchResult.results) {
                    contextString += `### ${result.filePath}\n`;
                    contextString += `Score: ${result.score.toFixed(2)}\n`;

                    if (result.symbols.length > 0) {
                        contextString += `Symbols: ${result.symbols.map(s => `${s.name}(${s.type})`).join(', ')}\n`;
                    }

                    if (result.snippet) {
                        contextString += `\`\`\`\n${result.snippet}\n\`\`\`\n`;
                    }

                    contextString += '\n';
                }
            }

            // Add execution plan context
            const executionPlanContext = await this.getExecutionPlanContext(userPrompt, activeEditor ? [activeEditor.document.uri.fsPath] : undefined);
            if (executionPlanContext) {
                contextString += executionPlanContext;
            }

            return contextString;
        } catch (error) {
            console.error('Context Engine: Failed to get enhanced context', error);
            return '';
        }
    }

    /**
     * Analyze user prompt to determine file operation requirements (Augment-style)
     */
    public async analyzeFileOperationRequirements(userPrompt: string, activeEditor?: vscode.TextEditor): Promise<{
        requiresFileOps: boolean;
        operationType: 'create' | 'modify' | 'delete' | 'complex';
        suggestedPaths: string[];
        guidance: string;
    }> {
        const prompt = userPrompt.toLowerCase();
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';

        // Analyze prompt for file operation indicators
        const createIndicators = ['create', 'add', 'make', 'build', 'generate', 'new', 'setup', 'initialize'];
        const modifyIndicators = ['fix', 'update', 'change', 'modify', 'edit', 'improve', 'refactor'];
        const deleteIndicators = ['delete', 'remove', 'clean'];
        const complexIndicators = ['app', 'project', 'system', 'complete', 'full', 'entire'];

        let operationType: 'create' | 'modify' | 'delete' | 'complex' = 'create';
        let requiresFileOps = true; // Default to true for coding agent behavior

        // Determine operation type
        if (complexIndicators.some(indicator => prompt.includes(indicator))) {
            operationType = 'complex';
        } else if (deleteIndicators.some(indicator => prompt.includes(indicator))) {
            operationType = 'delete';
        } else if (modifyIndicators.some(indicator => prompt.includes(indicator))) {
            operationType = 'modify';
        } else if (createIndicators.some(indicator => prompt.includes(indicator))) {
            operationType = 'create';
        }

        // Get workspace structure for intelligent path suggestions
        const suggestedPaths = await this.suggestFilePaths(userPrompt, operationType, activeEditor);

        // Build guidance string
        let guidance = `### 🚀 FILE OPERATION REQUIREMENTS\n`;
        guidance += `**Operation Type**: ${operationType.toUpperCase()}\n`;
        guidance += `**Workspace Root**: ${workspaceRoot}\n`;

        if (suggestedPaths.length > 0) {
            guidance += `**Suggested Locations**:\n`;
            suggestedPaths.forEach(path => {
                guidance += `  - ${path}\n`;
            });
        }

        // Add specific guidance based on operation type
        switch (operationType) {
            case 'create':
                guidance += `\n**GUIDANCE**: Create new files using <file_operation> tags. Follow existing project structure and naming conventions.\n`;
                break;
            case 'modify':
                guidance += `\n**GUIDANCE**: Modify existing files using <file_operation type="modify">. Preserve existing functionality while adding requested changes.\n`;
                break;
            case 'delete':
                guidance += `\n**GUIDANCE**: Use <file_operation type="delete"> for file removal. Consider dependencies and imports.\n`;
                break;
            case 'complex':
                guidance += `\n**GUIDANCE**: Use <execution_plan> for multi-file operations. Create comprehensive project structure with proper dependencies.\n`;
                break;
        }

        guidance += `\n**CRITICAL**: NEVER write code in chat. ALL code must be in file_operation tags for workspace integration.\n\n`;

        return {
            requiresFileOps,
            operationType,
            suggestedPaths,
            guidance
        };
    }

    /**
     * Suggest intelligent file paths based on prompt and workspace structure
     */
    private async suggestFilePaths(userPrompt: string, operationType: string, activeEditor?: vscode.TextEditor): Promise<string[]> {
        const suggestions: string[] = [];
        const prompt = userPrompt.toLowerCase();

        // Get existing directory structure
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) return suggestions;

        try {
            // Language-specific directory suggestions
            this.addLanguageSpecificSuggestions(prompt, suggestions);

            // Generic patterns that work across languages
            if (prompt.includes('util') || prompt.includes('helper')) {
                suggestions.push('src/utils/', 'src/helpers/', 'utils/', 'helpers/');
            }

            if (prompt.includes('test') || prompt.includes('spec')) {
                suggestions.push('tests/', 'test/', 'src/test/', '__tests__/', 'spec/');
            }

            if (prompt.includes('config') || prompt.includes('setting')) {
                suggestions.push('config/', 'src/config/', 'settings/');
            }

            if (prompt.includes('model') || prompt.includes('entity')) {
                suggestions.push('src/models/', 'src/entities/', 'models/', 'entities/');
            }

            if (prompt.includes('service') || prompt.includes('api')) {
                suggestions.push('src/services/', 'src/api/', 'services/', 'api/');
            }

            if (prompt.includes('controller') || prompt.includes('handler')) {
                suggestions.push('src/controllers/', 'src/handlers/', 'controllers/', 'handlers/');
            }

            // If active editor exists, suggest related paths
            if (activeEditor) {
                const currentPath = activeEditor.document.uri.fsPath;
                const currentDir = path.dirname(currentPath);
                const relativePath = path.relative(workspaceRoot, currentDir);
                suggestions.push(relativePath + '/');
            }

            // Remove duplicates and sort
            return [...new Set(suggestions)].sort();
        } catch (error) {
            console.error('Context Engine: Failed to suggest file paths', error);
            return ['src/'];
        }
    }

    /**
     * Add language-specific directory suggestions based on prompt analysis
     */
    private addLanguageSpecificSuggestions(prompt: string, suggestions: string[]): void {
        // JavaScript/TypeScript/React
        if (prompt.includes('react') || prompt.includes('component') || prompt.includes('jsx')) {
            suggestions.push('src/components/', 'components/');
            if (prompt.includes('hook')) suggestions.push('src/hooks/', 'hooks/');
        }

        // Python
        if (prompt.includes('python') || prompt.includes('.py')) {
            suggestions.push('src/', 'lib/', 'modules/');
            if (prompt.includes('test')) suggestions.push('tests/', 'test/');
        }

        // Java
        if (prompt.includes('java') || prompt.includes('spring') || prompt.includes('maven')) {
            suggestions.push('src/main/java/', 'src/test/java/');
            if (prompt.includes('controller')) suggestions.push('src/main/java/com/example/controller/');
            if (prompt.includes('service')) suggestions.push('src/main/java/com/example/service/');
            if (prompt.includes('model') || prompt.includes('entity')) suggestions.push('src/main/java/com/example/model/');
        }

        // C/C++
        if (prompt.includes('c++') || prompt.includes('cpp') || prompt.includes('c ')) {
            suggestions.push('src/', 'include/', 'lib/');
            if (prompt.includes('header')) suggestions.push('include/', 'headers/');
        }

        // C#
        if (prompt.includes('c#') || prompt.includes('csharp') || prompt.includes('.net')) {
            suggestions.push('src/', 'Models/', 'Controllers/', 'Services/');
        }

        // Go
        if (prompt.includes('go ') || prompt.includes('golang')) {
            suggestions.push('cmd/', 'internal/', 'pkg/');
        }

        // Rust
        if (prompt.includes('rust') || prompt.includes('cargo')) {
            suggestions.push('src/', 'src/bin/', 'tests/');
        }

        // PHP
        if (prompt.includes('php') || prompt.includes('laravel') || prompt.includes('symfony')) {
            suggestions.push('src/', 'app/', 'lib/');
            if (prompt.includes('controller')) suggestions.push('app/Controllers/', 'src/Controller/');
            if (prompt.includes('model')) suggestions.push('app/Models/', 'src/Model/');
        }

        // Ruby
        if (prompt.includes('ruby') || prompt.includes('rails')) {
            suggestions.push('app/', 'lib/', 'config/');
            if (prompt.includes('controller')) suggestions.push('app/controllers/');
            if (prompt.includes('model')) suggestions.push('app/models/');
        }

        // Swift
        if (prompt.includes('swift') || prompt.includes('ios')) {
            suggestions.push('Sources/', 'Tests/');
        }

        // Kotlin
        if (prompt.includes('kotlin') || prompt.includes('android')) {
            suggestions.push('src/main/kotlin/', 'src/test/kotlin/');
        }

        // Database/SQL
        if (prompt.includes('sql') || prompt.includes('database') || prompt.includes('migration')) {
            suggestions.push('migrations/', 'sql/', 'database/', 'db/');
        }

        // Configuration files
        if (prompt.includes('docker')) {
            suggestions.push('./', 'docker/', 'deployment/');
        }

        if (prompt.includes('yaml') || prompt.includes('yml')) {
            suggestions.push('config/', '.github/', 'deployment/');
        }
    }

    public async findSymbolDefinition(symbolName: string, filePath?: string): Promise<AstSymbol | undefined> {
        const symbols = this.symbolIndex.get(symbolName) || [];
        
        if (filePath) {
            // Prefer symbols from the same file or related files
            const relatedFiles = await this.getRelatedFiles(filePath, 2);
            const relatedSymbols = symbols.filter(s => 
                s.filePath === filePath || relatedFiles.includes(s.filePath)
            );
            if (relatedSymbols.length > 0) {
                return relatedSymbols[0];
            }
        }
        
        return symbols[0];
    }

    public async findSymbolReferences(symbolName: string, filePath?: string): Promise<AstSymbol[]> {
        const allSymbols = this.symbolIndex.get(symbolName) || [];
        
        if (filePath) {
            // Include references from related files
            const relatedFiles = await this.getRelatedFiles(filePath, 3);
            return allSymbols.filter(s => 
                s.filePath !== filePath && relatedFiles.includes(s.filePath)
            );
        }
        
        return allSymbols;
    }

    public async findSimilarCode(code: string, language?: string): Promise<RankedContext[]> {
        // This is a simplified implementation
        // In a full implementation, you would use AST similarity algorithms
        const words = code.toLowerCase().split(/\W+/).filter(w => w.length > 2);
        const candidates: RankedContext[] = [];
        
        for (const [filePath, symbols] of this.fileAstCache) {
            if (language && !filePath.endsWith(`.${language}`)) continue;
            
            let score = 0;
            const matchingSymbols = symbols.filter(symbol => 
                words.some(word => symbol.name.toLowerCase().includes(word))
            );
            
            if (matchingSymbols.length > 0) {
                score = matchingSymbols.length / symbols.length;
                candidates.push({
                    filePath,
                    content: '', // Would load actual content
                    symbols: matchingSymbols,
                    score,
                    rankingFactors: {
                        semanticSimilarity: score,
                        recency: 0,
                        activeFileBoost: 0,
                        userInteraction: 0,
                        graphProximity: 0,
                        frequency: 0
                    }
                });
            }
        }
        
        return candidates.sort((a, b) => b.score - a.score);
    }

    public async getCodeRelationships(filePath: string): Promise<CodeRelationship[]> {
        return this.relationshipGraph.get(filePath) || [];
    }

    public async getRelatedFiles(filePath: string, maxDepth: number = 2): Promise<string[]> {
        const visited = new Set<string>();
        const queue: Array<{file: string, depth: number}> = [{file: filePath, depth: 0}];
        const related: string[] = [];
        
        while (queue.length > 0) {
            const {file, depth} = queue.shift()!;
            
            if (visited.has(file) || depth > maxDepth) continue;
            visited.add(file);
            
            if (depth > 0) related.push(file);
            
            const relationships = this.relationshipGraph.get(file) || [];
            for (const rel of relationships) {
                if (!visited.has(rel.targetFile)) {
                    queue.push({file: rel.targetFile, depth: depth + 1});
                }
            }
        }
        
        return related;
    }

    public async updateFileIndex(filePath: string): Promise<void> {
        try {
            await this.processFile(vscode.Uri.file(filePath));
        } catch (error) {
            console.error(`Context Engine: Failed to update index for ${filePath}`, error);
            this._onError.fire(error as Error);
        }
    }

    public async removeFileFromIndex(filePath: string): Promise<void> {
        this.fileAstCache.delete(filePath);
        this.relationshipGraph.delete(filePath);
        
        // Remove from symbol index
        for (const [symbol, symbols] of this.symbolIndex) {
            const filtered = symbols.filter(s => s.filePath !== filePath);
            if (filtered.length === 0) {
                this.symbolIndex.delete(symbol);
            } else {
                this.symbolIndex.set(symbol, filtered);
            }
        }
        
        // Remove from search index
        for (const [word, files] of this.searchIndex) {
            files.delete(filePath);
            if (files.size === 0) {
                this.searchIndex.delete(word);
            }
        }
        
        console.log(`Context Engine: Removed ${filePath} from index`);
    }

    public async reindexWorkspace(): Promise<void> {
        this.clearAllCaches();
        await this.scanWorkspace();
    }

    public getIndexingProgress(): IndexingProgress {
        return { ...this.indexingProgress };
    }

    public async getStats(): Promise<ContextEngineStats> {
        let totalSymbols = 0;
        for (const symbols of this.fileAstCache.values()) {
            totalSymbols += symbols.length;
        }
        
        let totalRelationships = 0;
        for (const relationships of this.relationshipGraph.values()) {
            totalRelationships += relationships.length;
        }
        
        return {
            totalFiles: this.fileAstCache.size,
            totalSymbols,
            totalRelationships,
            indexSize: this.calculateIndexSize(),
            lastIndexed: Date.now(),
            supportedLanguages: ['typescript', 'javascript', 'python', 'java', 'csharp'],
            memoryUsage: process.memoryUsage().heapUsed
        };
    }

    public async recordFeedback(query: string, result: RankedContext, accepted: boolean): Promise<void> {
        const key = `${query}:${result.filePath}`;
        
        if (accepted) {
            const current = this.learningData.acceptedSuggestions.get(key) || 0;
            this.learningData.acceptedSuggestions.set(key, current + 1);
        } else {
            const current = this.learningData.rejectedSuggestions.get(key) || 0;
            this.learningData.rejectedSuggestions.set(key, current + 1);
        }
        
        await this.saveLearningData();
    }

    public async getLearningData(): Promise<LearningData> {
        return { ...this.learningData };
    }

    public async clearCache(): Promise<void> {
        this.clearAllCaches();
        await this.saveLearningData();
    }

    public dispose(): void {
        this.fileWatcher?.dispose();
        this._onIndexingProgress.dispose();
        this._onIndexingComplete.dispose();
        this._onError.dispose();
        
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        
        this.clearAllCaches();
    }

    // Private helper methods
    private setupFileWatchers(): void {
        // Watch for TypeScript/JavaScript files
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*.{ts,js,tsx,jsx,py,java,cs}');

        this.disposables.push(
            this.fileWatcher.onDidChange(uri => this.updateFileIndex(uri.fsPath)),
            this.fileWatcher.onDidCreate(uri => this.updateFileIndex(uri.fsPath)),
            this.fileWatcher.onDidDelete(uri => this.removeFileFromIndex(uri.fsPath))
        );

        // Watch for active editor changes
        this.disposables.push(
            vscode.window.onDidChangeActiveTextEditor(editor => {
                if (editor) {
                    this.recordFileAccess(editor.document.uri.fsPath);
                }
            })
        );
    }

    private async scanWorkspace(): Promise<void> {
        this.indexingProgress.stage = 'scanning';
        this._onIndexingProgress.fire(this.indexingProgress);

        const files = await vscode.workspace.findFiles(
            '**/*.{ts,js,tsx,jsx,py,java,cs}',
            '**/node_modules/**'
        );

        this.indexingProgress.totalFiles = files.length;
        this.indexingProgress.stage = 'parsing';
        this._onIndexingProgress.fire(this.indexingProgress);

        for (const file of files) {
            try {
                this.indexingProgress.currentFile = file.fsPath;
                await this.processFile(file);
                this.indexingProgress.processedFiles++;
                this._onIndexingProgress.fire(this.indexingProgress);
            } catch (error) {
                console.error(`Context Engine: Error processing ${file.fsPath}`, error);
                this.indexingProgress.errors.push(`${file.fsPath}: ${error}`);
            }
        }

        this.indexingProgress.stage = 'relationships';
        this._onIndexingProgress.fire(this.indexingProgress);

        await this.buildRelationshipGraph();

        this.indexingProgress.stage = 'complete';
        this._onIndexingProgress.fire(this.indexingProgress);

        const stats = await this.getStats();
        this._onIndexingComplete.fire(stats);

        console.log(`Context Engine: Workspace scan complete. Processed ${files.length} files.`);

        // Show completion notification
        vscode.window.showInformationMessage(
            `Context Engine: Indexed ${stats.totalFiles} files with ${stats.totalSymbols} symbols. Enhanced code context is now available!`
        );
    }

    private analyzeQuery(query: string): any {
        const lowerQuery = query.toLowerCase();

        // Extract potential symbols
        const symbolPattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
        const extractedSymbols = query.match(symbolPattern) || [];

        // Determine intent
        let intent: 'definition' | 'usage' | 'similar' | 'related' | 'general' = 'general';
        let confidence = 0.5;

        if (lowerQuery.includes('define') || lowerQuery.includes('definition') || lowerQuery.includes('what is')) {
            intent = 'definition';
            confidence = 0.8;
        } else if (lowerQuery.includes('usage') || lowerQuery.includes('use') || lowerQuery.includes('example')) {
            intent = 'usage';
            confidence = 0.8;
        } else if (lowerQuery.includes('similar') || lowerQuery.includes('like')) {
            intent = 'similar';
            confidence = 0.7;
        } else if (lowerQuery.includes('related') || lowerQuery.includes('connect')) {
            intent = 'related';
            confidence = 0.7;
        }

        return {
            intent,
            extractedSymbols,
            confidence
        };
    }

    private async searchCandidates(query: ContextQuery): Promise<RankedContext[]> {
        const candidates: RankedContext[] = [];
        const queryWords = query.query.toLowerCase().split(/\W+/).filter(w => w.length > 2);

        // Search by symbols
        for (const word of queryWords) {
            const symbols = this.symbolIndex.get(word) || [];
            for (const symbol of symbols) {
                const existing = candidates.find(c => c.filePath === symbol.filePath);
                if (existing) {
                    existing.symbols.push(symbol);
                } else {
                    candidates.push({
                        filePath: symbol.filePath,
                        content: '',
                        symbols: [symbol],
                        score: 0,
                        rankingFactors: {
                            semanticSimilarity: 0,
                            recency: 0,
                            activeFileBoost: 0,
                            userInteraction: 0,
                            graphProximity: 0,
                            frequency: 0
                        }
                    });
                }
            }
        }

        // Search by content
        for (const word of queryWords) {
            const files = this.searchIndex.get(word) || new Set();
            for (const filePath of files) {
                const existing = candidates.find(c => c.filePath === filePath);
                if (!existing) {
                    const symbols = this.fileAstCache.get(filePath) || [];
                    candidates.push({
                        filePath,
                        content: '',
                        symbols,
                        score: 0,
                        rankingFactors: {
                            semanticSimilarity: 0,
                            recency: 0,
                            activeFileBoost: 0,
                            userInteraction: 0,
                            graphProximity: 0,
                            frequency: 0
                        }
                    });
                }
            }
        }

        return candidates;
    }

    private async rankResults(candidates: RankedContext[], query: ContextQuery): Promise<RankedContext[]> {
        const activeFile = query.activeFile || vscode.window.activeTextEditor?.document.uri.fsPath;
        const queryWords = query.query.toLowerCase().split(/\W+/).filter(w => w.length > 2);

        for (const candidate of candidates) {
            const factors = candidate.rankingFactors;

            // Semantic similarity
            factors.semanticSimilarity = this.calculateSemanticSimilarity(candidate, queryWords);

            // Recency
            factors.recency = this.calculateRecency(candidate.filePath);

            // Active file boost
            factors.activeFileBoost = activeFile === candidate.filePath ? 0.5 : 0;

            // User interaction
            factors.userInteraction = this.calculateUserInteraction(candidate.filePath);

            // Graph proximity
            if (activeFile) {
                factors.graphProximity = await this.calculateGraphProximity(activeFile, candidate.filePath);
            }

            // Frequency
            factors.frequency = this.calculateFrequency(candidate.filePath);

            // Calculate total score
            candidate.score =
                factors.semanticSimilarity * 0.3 +
                factors.recency * 0.1 +
                factors.activeFileBoost * 0.2 +
                factors.userInteraction * 0.15 +
                factors.graphProximity * 0.15 +
                factors.frequency * 0.1;
        }

        return candidates.sort((a, b) => b.score - a.score);
    }

    private async processFile(uri: vscode.Uri): Promise<void> {
        try {
            const document = await vscode.workspace.openTextDocument(uri);
            const text = document.getText();
            const filePath = uri.fsPath;

            if (filePath.endsWith('.ts') || filePath.endsWith('.tsx') || filePath.endsWith('.js') || filePath.endsWith('.jsx')) {
                await this.processTypeScriptFile(filePath, text);
            } else if (filePath.endsWith('.py')) {
                await this.processPythonFile(filePath, text);
            }
            // Add more language processors as needed

        } catch (error) {
            console.error(`Context Engine: Error processing ${uri.fsPath}`, error);
            throw error;
        }
    }

    private async processTypeScriptFile(filePath: string, text: string): Promise<void> {
        const sourceFile = ts.createSourceFile(
            filePath,
            text,
            ts.ScriptTarget.Latest,
            true
        );

        const symbols: AstSymbol[] = [];
        const relationships: CodeRelationship[] = [];

        const visitNode = (node: ts.Node) => {
            // Extract symbols
            if (ts.isFunctionDeclaration(node) || ts.isClassDeclaration(node) ||
                ts.isVariableDeclaration(node) || ts.isInterfaceDeclaration(node) ||
                ts.isMethodDeclaration(node) || ts.isPropertyDeclaration(node)) {

                const nameNode = (node as any).name;
                if (nameNode && nameNode.text) {
                    const pos = sourceFile.getLineAndCharacterOfPosition(node.getStart(sourceFile));
                    symbols.push({
                        name: nameNode.text,
                        kind: ts.SyntaxKind[node.kind],
                        filePath,
                        start: node.getStart(sourceFile),
                        end: node.getEnd(),
                        line: pos.line + 1,
                        column: pos.character + 1,
                        type: this.getNodeType(node),
                        scope: this.getNodeScope(node),
                        signature: this.getNodeSignature(node, sourceFile)
                    });
                }
            }

            // Extract import/export relationships
            if (ts.isImportDeclaration(node)) {
                const moduleSpecifier = (node.moduleSpecifier as ts.StringLiteral).text;
                relationships.push({
                    sourceFile: filePath,
                    targetFile: this.resolveModulePath(moduleSpecifier, filePath),
                    relationType: 'import',
                    line: sourceFile.getLineAndCharacterOfPosition(node.getStart(sourceFile)).line + 1
                });
            }

            ts.forEachChild(node, visitNode);
        };

        visitNode(sourceFile);

        // Store in caches
        this.fileAstCache.set(filePath, symbols);
        this.relationshipGraph.set(filePath, relationships);

        // Update indices
        this.updateSymbolIndex(symbols);
        this.updateSearchIndex(filePath, text);
    }

    private async processPythonFile(filePath: string, text: string): Promise<void> {
        // Simplified Python processing - in a full implementation,
        // you would use a Python AST parser
        const symbols: AstSymbol[] = [];
        const lines = text.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // Simple regex patterns for Python symbols
            const functionMatch = line.match(/^\s*def\s+(\w+)/);
            const classMatch = line.match(/^\s*class\s+(\w+)/);

            if (functionMatch) {
                symbols.push({
                    name: functionMatch[1],
                    kind: 'function',
                    filePath,
                    start: 0,
                    end: 0,
                    line: i + 1,
                    column: line.indexOf('def') + 1,
                    type: 'function'
                });
            }

            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    kind: 'class',
                    filePath,
                    start: 0,
                    end: 0,
                    line: i + 1,
                    column: line.indexOf('class') + 1,
                    type: 'class'
                });
            }
        }

        this.fileAstCache.set(filePath, symbols);
        this.updateSymbolIndex(symbols);
        this.updateSearchIndex(filePath, text);
    }

    private updateSymbolIndex(symbols: AstSymbol[]): void {
        for (const symbol of symbols) {
            const existing = this.symbolIndex.get(symbol.name) || [];
            existing.push(symbol);
            this.symbolIndex.set(symbol.name, existing);
        }
    }

    private updateSearchIndex(filePath: string, text: string): void {
        const words = text.toLowerCase().split(/\W+/).filter(w => w.length > 2);
        for (const word of words) {
            if (!this.searchIndex.has(word)) {
                this.searchIndex.set(word, new Set());
            }
            this.searchIndex.get(word)!.add(filePath);
        }
    }

    private async buildRelationshipGraph(): Promise<void> {
        // Build bidirectional relationships
        for (const [sourceFile, relationships] of this.relationshipGraph) {
            for (const rel of relationships) {
                if (rel.targetFile && rel.targetFile !== sourceFile) {
                    const targetRels = this.relationshipGraph.get(rel.targetFile) || [];
                    const reverseRel: CodeRelationship = {
                        sourceFile: rel.targetFile,
                        targetFile: sourceFile,
                        relationType: rel.relationType === 'import' ? 'export' : rel.relationType,
                        symbolName: rel.symbolName
                    };
                    targetRels.push(reverseRel);
                    this.relationshipGraph.set(rel.targetFile, targetRels);
                }
            }
        }
    }

    private clearAllCaches(): void {
        this.fileAstCache.clear();
        this.relationshipGraph.clear();
        this.searchIndex.clear();
        this.symbolIndex.clear();
        this.executionPlanCache.clear();
        this.executionPlanFileIndex.clear();
    }

    private calculateIndexSize(): number {
        return JSON.stringify({
            fileAstCache: Array.from(this.fileAstCache.entries()),
            relationshipGraph: Array.from(this.relationshipGraph.entries()),
            searchIndex: Array.from(this.searchIndex.entries()),
            symbolIndex: Array.from(this.symbolIndex.entries())
        }).length;
    }

    private async loadLearningData(): Promise<void> {
        try {
            const data = this.context.globalState.get('contextEngine.learningData') as any;
            if (data) {
                this.learningData = {
                    acceptedSuggestions: new Map(data.acceptedSuggestions || []),
                    rejectedSuggestions: new Map(data.rejectedSuggestions || []),
                    frequentlyAccessedFiles: new Map(data.frequentlyAccessedFiles || []),
                    userPatterns: new Map(data.userPatterns || []),
                    contextPreferences: new Map(data.contextPreferences || [])
                };
            }
        } catch (error) {
            console.error('Context Engine: Failed to load learning data', error);
        }
    }

    private async saveLearningData(): Promise<void> {
        try {
            const data = {
                acceptedSuggestions: Array.from(this.learningData.acceptedSuggestions.entries()),
                rejectedSuggestions: Array.from(this.learningData.rejectedSuggestions.entries()),
                frequentlyAccessedFiles: Array.from(this.learningData.frequentlyAccessedFiles.entries()),
                userPatterns: Array.from(this.learningData.userPatterns.entries()),
                contextPreferences: Array.from(this.learningData.contextPreferences.entries())
            };
            await this.context.globalState.update('contextEngine.learningData', data);
        } catch (error) {
            console.error('Context Engine: Failed to save learning data', error);
        }
    }

    private recordFileAccess(filePath: string): void {
        const current = this.learningData.frequentlyAccessedFiles.get(filePath) || 0;
        this.learningData.frequentlyAccessedFiles.set(filePath, current + 1);
    }

    private recordQueryInteraction(query: string): void {
        const current = this.learningData.userPatterns.get('queries') || [];
        current.push({
            query,
            timestamp: Date.now(),
            activeFile: vscode.window.activeTextEditor?.document.uri.fsPath
        });

        // Keep only last 100 queries
        if (current.length > 100) {
            current.splice(0, current.length - 100);
        }

        this.learningData.userPatterns.set('queries', current);
    }

    private calculateSemanticSimilarity(candidate: RankedContext, queryWords: string[]): number {
        let matches = 0;
        const totalWords = queryWords.length;

        for (const word of queryWords) {
            // Check symbol names
            const symbolMatch = candidate.symbols.some(s =>
                s.name.toLowerCase().includes(word)
            );
            if (symbolMatch) matches++;
        }

        return totalWords > 0 ? matches / totalWords : 0;
    }

    private calculateRecency(filePath: string): number {
        // Simple recency calculation - in a full implementation,
        // you would track actual file modification times
        try {
            const stats = require('fs').statSync(filePath);
            const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
            return Math.max(0, 1 - (ageInDays / 30)); // Decay over 30 days
        } catch {
            return 0;
        }
    }

    private calculateUserInteraction(filePath: string): number {
        const accessCount = this.learningData.frequentlyAccessedFiles.get(filePath) || 0;
        const maxAccess = Math.max(...Array.from(this.learningData.frequentlyAccessedFiles.values()));
        return maxAccess > 0 ? accessCount / maxAccess : 0;
    }

    private async calculateGraphProximity(activeFile: string, candidateFile: string): Promise<number> {
        if (activeFile === candidateFile) return 1;

        const relatedFiles = await this.getRelatedFiles(activeFile, 3);
        const index = relatedFiles.indexOf(candidateFile);

        if (index === -1) return 0;
        return 1 - (index / relatedFiles.length);
    }

    private calculateFrequency(filePath: string): number {
        const accessCount = this.learningData.frequentlyAccessedFiles.get(filePath) || 0;
        return Math.min(1, accessCount / 100); // Normalize to max 100 accesses
    }

    private getNodeType(node: ts.Node): string {
        if (ts.isFunctionDeclaration(node)) return 'function';
        if (ts.isClassDeclaration(node)) return 'class';
        if (ts.isInterfaceDeclaration(node)) return 'interface';
        if (ts.isVariableDeclaration(node)) return 'variable';
        if (ts.isMethodDeclaration(node)) return 'method';
        if (ts.isPropertyDeclaration(node)) return 'property';
        return 'unknown';
    }

    private getNodeScope(node: ts.Node): string {
        // Simplified scope detection
        let parent = node.parent;
        while (parent) {
            if (ts.isClassDeclaration(parent)) return 'class';
            if (ts.isFunctionDeclaration(parent)) return 'function';
            if (ts.isModuleDeclaration(parent)) return 'module';
            parent = parent.parent;
        }
        return 'global';
    }

    private getNodeSignature(node: ts.Node, sourceFile: ts.SourceFile): string {
        // Get the text representation of the node
        return node.getText(sourceFile).split('\n')[0].trim();
    }

    private resolveModulePath(moduleSpecifier: string, currentFile: string): string {
        // Simplified module resolution
        if (moduleSpecifier.startsWith('.')) {
            const currentDir = path.dirname(currentFile);
            return path.resolve(currentDir, moduleSpecifier);
        }
        return moduleSpecifier; // External module
    }

    // Execution Plan Methods
    public async addExecutionPlan(plan: ExecutionPlan, createdBy: string): Promise<void> {
        try {
            const relatedFiles = this.extractFilesFromPlan(plan);
            const impact = await this.analyzeExecutionPlanImpact(plan);

            const planContext: ExecutionPlanContext = {
                plan,
                createdAt: new Date(),
                createdBy,
                status: 'pending',
                executedSteps: [],
                failedSteps: [],
                relatedFiles,
                dependencies: impact.dependencies,
                estimatedImpact: {
                    filesAffected: impact.affectedFiles.length,
                    linesChanged: impact.estimatedChanges,
                    complexity: impact.complexity
                }
            };

            this.executionPlanCache.set(plan.id, planContext);

            // Index by related files
            for (const filePath of relatedFiles) {
                if (!this.executionPlanFileIndex.has(filePath)) {
                    this.executionPlanFileIndex.set(filePath, new Set());
                }
                this.executionPlanFileIndex.get(filePath)!.add(plan.id);
            }

            // Save to persistent storage
            await this.saveExecutionPlans();

            console.log(`Context Engine: Added execution plan ${plan.id} with ${relatedFiles.length} related files`);
        } catch (error) {
            console.error('Context Engine: Failed to add execution plan', error);
            this._onError.fire(error as Error);
        }
    }

    public async updateExecutionPlan(planId: string, updates: Partial<ExecutionPlanContext>): Promise<void> {
        try {
            const existing = this.executionPlanCache.get(planId);
            if (!existing) {
                throw new Error(`Execution plan ${planId} not found`);
            }

            const updated = { ...existing, ...updates };
            this.executionPlanCache.set(planId, updated);

            // Save to persistent storage
            await this.saveExecutionPlans();

            console.log(`Context Engine: Updated execution plan ${planId}`);
        } catch (error) {
            console.error('Context Engine: Failed to update execution plan', error);
            this._onError.fire(error as Error);
        }
    }

    public async getExecutionPlans(query: ExecutionPlanQuery): Promise<ExecutionPlanContext[]> {
        const plans = Array.from(this.executionPlanCache.values());
        let filtered = plans;

        // Filter by status
        if (query.includeCompleted === false) {
            filtered = filtered.filter(p => p.status !== 'completed');
        }
        if (query.includeActive === false) {
            filtered = filtered.filter(p => p.status !== 'executing');
        }
        if (query.includeFailed === false) {
            filtered = filtered.filter(p => p.status !== 'failed');
        }

        // Filter by time range
        if (query.timeRange) {
            filtered = filtered.filter(p =>
                p.createdAt >= query.timeRange!.start &&
                p.createdAt <= query.timeRange!.end
            );
        }

        // Filter by related files
        if (query.relatedToFiles && query.relatedToFiles.length > 0) {
            filtered = filtered.filter(p =>
                query.relatedToFiles!.some(file => p.relatedFiles.includes(file))
            );
        }

        // Text search in plan description and steps
        if (query.query) {
            const searchTerms = query.query.toLowerCase().split(/\s+/);
            filtered = filtered.filter(p => {
                const searchText = [
                    p.plan.description,
                    p.createdBy,
                    ...p.plan.steps.map(s => s.description)
                ].join(' ').toLowerCase();

                return searchTerms.some(term => searchText.includes(term));
            });
        }

        // Sort by creation date (newest first)
        filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

        // Apply limit
        if (query.maxResults) {
            filtered = filtered.slice(0, query.maxResults);
        }

        return filtered;
    }

    public async getExecutionPlan(planId: string): Promise<ExecutionPlanContext | undefined> {
        return this.executionPlanCache.get(planId);
    }

    public async getExecutionPlansForFiles(filePaths: string[]): Promise<ExecutionPlanContext[]> {
        const planIds = new Set<string>();

        for (const filePath of filePaths) {
            const plans = this.executionPlanFileIndex.get(filePath);
            if (plans) {
                plans.forEach(id => planIds.add(id));
            }
        }

        const results: ExecutionPlanContext[] = [];
        for (const planId of planIds) {
            const plan = this.executionPlanCache.get(planId);
            if (plan) {
                results.push(plan);
            }
        }

        return results.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }

    public async analyzeExecutionPlanImpact(plan: ExecutionPlan): Promise<{
        affectedFiles: string[];
        potentialConflicts: string[];
        dependencies: string[];
        complexity: 'low' | 'medium' | 'high';
        estimatedChanges: number;
    }> {
        const affectedFiles = this.extractFilesFromPlan(plan);
        const potentialConflicts: string[] = [];
        const dependencies: string[] = [];
        let estimatedChanges = 0;

        // Analyze each step
        for (const step of plan.steps) {
            if (step.path) {
                // Check for existing files that might conflict
                if (this.fileAstCache.has(step.path)) {
                    const symbols = this.fileAstCache.get(step.path) || [];
                    if (symbols.length > 0 && step.type === 'file_create') {
                        potentialConflicts.push(step.path);
                    }
                }

                // Get dependencies from relationships
                const relationships = this.relationshipGraph.get(step.path) || [];
                for (const rel of relationships) {
                    if (!dependencies.includes(rel.targetFile)) {
                        dependencies.push(rel.targetFile);
                    }
                }

                // Estimate changes
                if (step.content) {
                    estimatedChanges += step.content.split('\n').length;
                } else {
                    estimatedChanges += 10; // Default estimate
                }
            }
        }

        // Determine complexity
        let complexity: 'low' | 'medium' | 'high' = 'low';
        if (affectedFiles.length > 10 || estimatedChanges > 500 || potentialConflicts.length > 0) {
            complexity = 'high';
        } else if (affectedFiles.length > 3 || estimatedChanges > 100) {
            complexity = 'medium';
        }

        return {
            affectedFiles,
            potentialConflicts,
            dependencies,
            complexity,
            estimatedChanges
        };
    }

    public async getExecutionPlanContext(userPrompt: string, targetFiles?: string[]): Promise<string> {
        try {
            let contextString = '\n\n## Execution Plan Context\n\n';

            // Get recent execution plans
            const recentPlans = await this.getExecutionPlans({
                query: '',
                maxResults: 5,
                timeRange: {
                    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                    end: new Date()
                }
            });

            if (recentPlans.length > 0) {
                contextString += '### Recent Execution Plans\n';
                for (const planCtx of recentPlans) {
                    contextString += `- **${planCtx.plan.description}** (${planCtx.status})\n`;
                    contextString += `  - Files: ${planCtx.relatedFiles.length}\n`;
                    contextString += `  - Complexity: ${planCtx.estimatedImpact.complexity}\n`;
                }
                contextString += '\n';
            }

            // Get plans related to target files
            if (targetFiles && targetFiles.length > 0) {
                const relatedPlans = await this.getExecutionPlansForFiles(targetFiles);
                if (relatedPlans.length > 0) {
                    contextString += '### Related Execution Plans\n';
                    for (const planCtx of relatedPlans.slice(0, 3)) {
                        contextString += `- **${planCtx.plan.description}**\n`;
                        contextString += `  - Status: ${planCtx.status}\n`;
                        contextString += `  - Steps: ${planCtx.plan.steps.length}\n`;
                    }
                    contextString += '\n';
                }
            }

            // Add execution patterns from learning data
            const patterns = this.learningData.userPatterns.get('executionPlans') || [];
            if (patterns.length > 0) {
                contextString += '### Common Execution Patterns\n';
                const recentPatterns = patterns.slice(-3);
                for (const pattern of recentPatterns) {
                    contextString += `- ${pattern.type}: ${pattern.description}\n`;
                }
                contextString += '\n';
            }

            return contextString;
        } catch (error) {
            console.error('Context Engine: Failed to get execution plan context', error);
            return '';
        }
    }

    // Helper methods for execution plans
    private extractFilesFromPlan(plan: ExecutionPlan): string[] {
        const files = new Set<string>();

        for (const step of plan.steps) {
            if (step.path) {
                files.add(step.path);
            }
        }

        return Array.from(files);
    }

    private async saveExecutionPlans(): Promise<void> {
        try {
            const data = {
                plans: Array.from(this.executionPlanCache.entries()),
                fileIndex: Array.from(this.executionPlanFileIndex.entries()).map(([file, planIds]) => [
                    file,
                    Array.from(planIds)
                ])
            };
            await this.context.globalState.update('contextEngine.executionPlans', JSON.stringify(data));
        } catch (error) {
            console.error('Context Engine: Failed to save execution plans', error);
        }
    }

    private async loadExecutionPlans(): Promise<void> {
        try {
            const data = this.context.globalState.get('contextEngine.executionPlans') as string;
            if (data) {
                const parsed = JSON.parse(data);

                // Restore execution plans
                if (parsed.plans) {
                    for (const [planId, planContext] of parsed.plans) {
                        // Convert date strings back to Date objects
                        planContext.createdAt = new Date(planContext.createdAt);
                        if (planContext.pausedAt) {
                            planContext.pausedAt = new Date(planContext.pausedAt);
                        }
                        if (planContext.startedAt) {
                            planContext.startedAt = new Date(planContext.startedAt);
                        }
                        if (planContext.completedAt) {
                            planContext.completedAt = new Date(planContext.completedAt);
                        }

                        this.executionPlanCache.set(planId, planContext);
                    }
                }

                // Restore file index
                if (parsed.fileIndex) {
                    for (const [file, planIds] of parsed.fileIndex) {
                        this.executionPlanFileIndex.set(file, new Set(planIds));
                    }
                }
            }
        } catch (error) {
            console.error('Context Engine: Failed to load execution plans', error);
        }
    }

    /**
     * Process user request through context engine
     */
    async processRequest(content: string): Promise<{
        filesIndexed?: number;
        relatedFiles?: number;
        complexity?: string;
        confidence?: number;
    }> {
        try {
            // Get enhanced context for the request
            const enhancedContext = await this.getEnhancedContext(content);

            // Analyze the request complexity
            const complexity = this.analyzeRequestComplexity(content);

            // Get related files count
            const relatedFiles = await this.findRelatedFilesForRequest(content);

            // Calculate confidence based on available context
            const confidence = this.calculateContextConfidence(enhancedContext, relatedFiles.length);

            return {
                filesIndexed: this.fileAstCache.size,
                relatedFiles: relatedFiles.length,
                complexity,
                confidence
            };
        } catch (error) {
            console.error('Context Engine: Failed to process request', error);
            return {
                filesIndexed: 0,
                relatedFiles: 0,
                complexity: 'Low',
                confidence: 50
            };
        }
    }

    private analyzeRequestComplexity(content: string): string {
        const complexityIndicators = [
            'create', 'build', 'implement', 'refactor', 'migrate',
            'multiple files', 'database', 'api', 'authentication',
            'testing', 'deployment', 'configuration'
        ];

        const matches = complexityIndicators.filter(indicator =>
            content.toLowerCase().includes(indicator)
        ).length;

        if (matches >= 3) return 'High';
        if (matches >= 1) return 'Medium';
        return 'Low';
    }

    private async findRelatedFilesForRequest(content: string): Promise<string[]> {
        try {
            // Extract potential file names and symbols from the request
            const fileExtensions = ['.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp', '.cs'];
            const relatedFiles = new Set<string>();

            // Look for file references in the request
            for (const [filePath] of this.fileAstCache) {
                const fileName = filePath.split('/').pop() || '';
                if (content.toLowerCase().includes(fileName.toLowerCase())) {
                    relatedFiles.add(filePath);
                }
            }

            // If no direct file references, use semantic search
            if (relatedFiles.size === 0) {
                const searchResult = await this.getRankedContext({
                    query: content,
                    maxResults: 5,
                    includeSymbols: true
                });

                searchResult.results.forEach(result => {
                    relatedFiles.add(result.filePath);
                });
            }

            return Array.from(relatedFiles);
        } catch (error) {
            console.error('Context Engine: Failed to find related files', error);
            return [];
        }
    }

    private calculateContextConfidence(enhancedContext: string, relatedFilesCount: number): number {
        let confidence = 50; // Base confidence

        // Boost confidence based on context length
        if (enhancedContext.length > 1000) confidence += 20;
        else if (enhancedContext.length > 500) confidence += 10;

        // Boost confidence based on related files
        if (relatedFilesCount > 5) confidence += 20;
        else if (relatedFilesCount > 2) confidence += 10;
        else if (relatedFilesCount > 0) confidence += 5;

        // Boost confidence based on symbol index size
        if (this.symbolIndex.size > 100) confidence += 10;
        else if (this.symbolIndex.size > 50) confidence += 5;

        return Math.min(95, Math.max(30, confidence));
    }

}
