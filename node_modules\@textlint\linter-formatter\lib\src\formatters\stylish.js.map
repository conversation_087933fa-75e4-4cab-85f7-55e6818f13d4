{"version": 3, "file": "stylish.js", "sourceRoot": "", "sources": ["../../../src/formatters/stylish.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,YAAY,CAAC;;;;;AAIb,kDAA0B;AAC1B,4BAA4B;AAC5B,4DAA+B;AAC/B,gEAAyC;AACzC,4DAAmC;AACnC,gFAAgF;AAChF,UAAU;AACV,gFAAgF;AAEhF;;;;;GAKG;AACH,SAAS,SAAS,CAAC,IAAY,EAAE,KAAa;IAC1C,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;AAC3C,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,OAAyB,EAAE,OAAyB;IACnE,gBAAgB;IAChB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAEpE,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,YAAY,GAAG,QAA4B,CAAC;IAChD,MAAM,UAAU,GAAG,OAAO,CAAC;IAE3B,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;QAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEjC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;QACzB,MAAM,IAAI,GAAG,eAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAElD,MAAM,IAAI,GAAG,IAAA,oBAAK,EACd,QAAQ,CAAC,GAAG,CAAC,UAAU,OAAO;YAC1B,IAAI,WAAW,CAAC;YAChB,UAAU;YACV,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,eAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;YACnB,CAAC;YACD,IAAK,OAAe,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACnD,WAAW,GAAG,WAAW,GAAG,eAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/C,YAAY,GAAG,KAAK,CAAC;gBACrB,MAAM,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACJ,WAAW,GAAG,WAAW,GAAG,eAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACpD,QAAQ,EAAE,CAAC;YACf,CAAC;YAED,OAAO;gBACH,EAAE;gBACF,OAAO,CAAC,IAAI,IAAI,CAAC;gBACjB,OAAO,CAAC,MAAM,IAAI,CAAC;gBACnB,WAAW;gBACX,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;gBAClC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;aACnC,CAAC;QACN,CAAC,CAAC,EACF;YACI,KAAK,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;YACrB,YAAY,CAAC,GAAW;gBACpB,MAAM,KAAK,GAAG,IAAA,oBAAS,EAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CACjB,IAAI,EACJ,KAAK,CAAC,GAAG,CAAC,UAAU,IAAY;oBAC5B,OAAO,IAAA,sBAAa,EAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC,CAAC,CACL,CAAC;YACN,CAAC;SACJ,CACJ;aACI,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,UAAU,EAAU;YACrB,OAAO,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;gBAClD,OAAO,eAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,eAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAC9B;YACI,SAAS;YACT,KAAK;YACL,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC;YAC5B,IAAI;YACJ,MAAM;YACN,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;YAC3B,IAAI;YACJ,QAAQ;YACR,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC;YAC/B,KAAK;SACR,CAAC,IAAI,CAAC,EAAE,CAAC,CACb,CAAC;IACN,CAAC;IAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACnB,MAAM,IAAI,eAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,YAAY,YAAY,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;QACvG,MAAM,IAAI,iBAAiB,eAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC;IAC5E,CAAC;IAED,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,IAAA,oBAAS,EAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,WAAW,CAAC;AACvB,CAAC;AAED,kBAAe,SAAS,CAAC"}