/**
 * Cross-Session Context Service
 * Maintains context across VS Code sessions and provides relevant past context
 */

import * as vscode from 'vscode';
import { 
    ICrossSessionContextService, 
    SessionContext, 
    PastContext,
    IEnhancedConversationPersistence,
    IUserPreferenceService,
    IProjectMemoryService,
    EnhancedConversationData,
    ThreadedMessage
} from '../interfaces/IEnhancedMemorySystem';

export class CrossSessionContextService implements ICrossSessionContextService {
    private static readonly SESSION_CONTEXT_KEY = 'v1b3sama.sessionContext';
    private context: vscode.ExtensionContext;
    private currentSessionContext: SessionContext;

    constructor(
        context: vscode.ExtensionContext,
        private conversationPersistence: IEnhancedConversationPersistence,
        private userPreferenceService: IUserPreferenceService,
        private projectMemoryService: IProjectMemoryService
    ) {
        this.context = context;
        this.currentSessionContext = this.initializeSessionContext();
        this.loadSessionContext();
        this.setupSessionTracking();
    }

    public async getSessionContext(projectId?: string): Promise<SessionContext> {
        const targetProjectId = projectId || this.getCurrentProjectId();
        
        // Update current session context
        this.currentSessionContext.currentProject = targetProjectId;
        this.currentSessionContext.lastActivity = Date.now();
        
        // Get recent conversations for this project
        const recentConversations = await this.conversationPersistence.getAllConversations(targetProjectId);
        this.currentSessionContext.recentConversations = recentConversations
            .slice(0, 10)
            .map((c: any) => c.id);
        
        // Get user preferences
        this.currentSessionContext.preferences = await this.userPreferenceService.getPreferences();
        
        await this.saveSessionContext();
        return { ...this.currentSessionContext };
    }

    public async updateSessionContext(context: Partial<SessionContext>): Promise<void> {
        this.currentSessionContext = {
            ...this.currentSessionContext,
            ...context,
            lastActivity: Date.now()
        };
        
        await this.saveSessionContext();
    }

    public async restoreSessionState(checkpointId: string): Promise<void> {
        try {
            const checkpoint = await this.conversationPersistence.loadCheckpoint(checkpointId);
            if (!checkpoint) {
                throw new Error(`Checkpoint ${checkpointId} not found`);
            }

            // Restore workspace state if available
            if (checkpoint.workspaceState) {
                // Apply workspace state restoration logic here
                console.log('Restoring workspace state from checkpoint:', checkpoint.name);
            }

            // Update session context
            await this.updateSessionContext({
                // userNotes: checkpoint.userNotes || '', // Removed as not in interface
                lastActivity: Date.now()
            });

            vscode.window.showInformationMessage(`Restored session state from checkpoint: ${checkpoint.name}`);
        } catch (error) {
            console.error('Failed to restore session state:', error);
            vscode.window.showErrorMessage(`Failed to restore session state: ${error}`);
        }
    }

    public async getRelevantPastContext(currentPrompt: string, projectId?: string): Promise<PastContext> {
        const targetProjectId = projectId || this.getCurrentProjectId();
        
        try {
            // Get relevant conversations
            const relevantConversations = await this.findRelevantConversations(currentPrompt, targetProjectId);
            
            // Get applicable code patterns
            const applicablePatterns = await this.findApplicablePatterns(currentPrompt, targetProjectId);
            
            // Get project insights
            const projectInsights = await this.projectMemoryService.findRelevantMemories(targetProjectId, currentPrompt);
            
            // Get relevant user preferences
            const userPreferences = await this.userPreferenceService.getPreferencesByRelevance(currentPrompt);
            
            return {
                relevantConversations,
                applicablePatterns,
                projectInsights,
                userPreferences
            };
        } catch (error) {
            console.error('Failed to get relevant past context:', error);
            return {
                relevantConversations: [],
                applicablePatterns: [],
                projectInsights: [],
                userPreferences: []
            };
        }
    }

    private async findRelevantConversations(
        prompt: string, 
        projectId: string
    ): Promise<Array<{
        conversation: EnhancedConversationData;
        relevanceScore: number;
        relevantMessages: ThreadedMessage[];
    }>> {
        const conversations = await this.conversationPersistence.getAllConversations(projectId);
        const promptLower = prompt.toLowerCase();
        const results: Array<{
            conversation: EnhancedConversationData;
            relevanceScore: number;
            relevantMessages: ThreadedMessage[];
        }> = [];

        for (const conversation of conversations) {
            let relevanceScore = 0;
            const relevantMessages: ThreadedMessage[] = [];

            // Check title relevance
            if (conversation.title.toLowerCase().includes(promptLower)) {
                relevanceScore += 0.3;
            }

            // Check message relevance
            for (const message of conversation.messages) {
                if (message.content.toLowerCase().includes(promptLower)) {
                    relevanceScore += 0.1;
                    relevantMessages.push(message);
                }
            }

            // Boost recent conversations
            const daysSinceUpdate = (Date.now() - new Date(conversation.updatedAt).getTime()) / (1000 * 60 * 60 * 24);
            if (daysSinceUpdate < 7) {
                relevanceScore += 0.2;
            }

            // Boost conversations with similar provider/model
            const currentProvider = this.getCurrentProvider();
            if (conversation.provider === currentProvider) {
                relevanceScore += 0.1;
            }

            if (relevanceScore > 0.2) {
                results.push({
                    conversation,
                    relevanceScore,
                    relevantMessages: relevantMessages.slice(0, 5) // Limit relevant messages
                });
            }
        }

        return results
            .sort((a, b) => b.relevanceScore - a.relevanceScore)
            .slice(0, 5); // Return top 5 relevant conversations
    }

    private async findApplicablePatterns(prompt: string, projectId: string): Promise<any[]> {
        // This would integrate with the Context Engine's code patterns
        // For now, return empty array as patterns are handled by Context Engine
        return [];
    }

    private initializeSessionContext(): SessionContext {
        return {
            currentProject: this.getCurrentProjectId(),
            activeFiles: [],
            recentConversations: [],
            userGoals: [],
            workspaceState: {},
            preferences: [],
            lastActivity: Date.now()
        };
    }

    private async loadSessionContext(): Promise<void> {
        try {
            const stored = this.context.workspaceState.get<SessionContext>(
                CrossSessionContextService.SESSION_CONTEXT_KEY
            );
            
            if (stored) {
                this.currentSessionContext = {
                    ...this.currentSessionContext,
                    ...stored,
                    lastActivity: Date.now() // Update activity time
                };
            }
        } catch (error) {
            console.error('Failed to load session context:', error);
        }
    }

    private async saveSessionContext(): Promise<void> {
        try {
            await this.context.workspaceState.update(
                CrossSessionContextService.SESSION_CONTEXT_KEY,
                this.currentSessionContext
            );
        } catch (error) {
            console.error('Failed to save session context:', error);
        }
    }

    private setupSessionTracking(): void {
        // Track active file changes
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                const filePath = editor.document.uri.fsPath;
                if (!this.currentSessionContext.activeFiles.includes(filePath)) {
                    this.currentSessionContext.activeFiles.unshift(filePath);
                    // Keep only last 10 active files
                    this.currentSessionContext.activeFiles = this.currentSessionContext.activeFiles.slice(0, 10);
                }
                this.currentSessionContext.lastActivity = Date.now();
                this.saveSessionContext();
            }
        });

        // Track workspace changes
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.currentSessionContext.currentProject = this.getCurrentProjectId();
            this.currentSessionContext.lastActivity = Date.now();
            this.saveSessionContext();
        });
    }

    private getCurrentProjectId(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return require('path').basename(workspaceFolders[0].uri.fsPath);
        }
        return 'default';
    }

    private getCurrentProvider(): string {
        // This would get the current provider from settings or state
        // For now, return a default
        return 'deepseek';
    }

    /**
     * Get session context as formatted string for LLM prompts
     */
    public async getSessionContextAsString(projectId?: string): Promise<string> {
        const sessionContext = await this.getSessionContext(projectId);
        const pastContext = await this.getRelevantPastContext('', projectId);
        
        let contextString = '\n\n## Session Context\n\n';
        
        if (sessionContext.activeFiles.length > 0) {
            contextString += `**Recently Active Files:**\n`;
            for (const file of sessionContext.activeFiles.slice(0, 5)) {
                contextString += `- ${file}\n`;
            }
            contextString += '\n';
        }
        
        if (pastContext.relevantConversations.length > 0) {
            contextString += `**Recent Related Conversations:**\n`;
            for (const conv of pastContext.relevantConversations.slice(0, 3)) {
                contextString += `- ${conv.conversation.title} (relevance: ${(conv.relevanceScore * 100).toFixed(0)}%)\n`;
            }
            contextString += '\n';
        }
        
        return contextString;
    }

    /**
     * Clear session context (for privacy/reset)
     */
    public async clearSessionContext(): Promise<void> {
        this.currentSessionContext = this.initializeSessionContext();
        await this.context.workspaceState.update(CrossSessionContextService.SESSION_CONTEXT_KEY, undefined);
    }
}
