/*! @azure/msal-common v15.7.0 2025-05-30 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const clientInfoDecodingError = "client_info_decoding_error";
const clientInfoEmptyError = "client_info_empty_error";
const tokenParsingError = "token_parsing_error";
const nullOrEmptyToken = "null_or_empty_token";
const endpointResolutionError = "endpoints_resolution_error";
const networkError = "network_error";
const openIdConfigError = "openid_config_error";
const hashNotDeserialized = "hash_not_deserialized";
const invalidState = "invalid_state";
const stateMismatch = "state_mismatch";
const stateNotFound = "state_not_found";
const nonceMismatch = "nonce_mismatch";
const authTimeNotFound = "auth_time_not_found";
const maxAgeTranspired = "max_age_transpired";
const multipleMatchingTokens = "multiple_matching_tokens";
const multipleMatchingAccounts = "multiple_matching_accounts";
const multipleMatchingAppMetadata = "multiple_matching_appMetadata";
const requestCannotBeMade = "request_cannot_be_made";
const cannotRemoveEmptyScope = "cannot_remove_empty_scope";
const cannotAppendScopeSet = "cannot_append_scopeset";
const emptyInputScopeSet = "empty_input_scopeset";
const deviceCodePollingCancelled = "device_code_polling_cancelled";
const deviceCodeExpired = "device_code_expired";
const deviceCodeUnknownError = "device_code_unknown_error";
const noAccountInSilentRequest = "no_account_in_silent_request";
const invalidCacheRecord = "invalid_cache_record";
const invalidCacheEnvironment = "invalid_cache_environment";
const noAccountFound = "no_account_found";
const noCryptoObject = "no_crypto_object";
const unexpectedCredentialType = "unexpected_credential_type";
const invalidAssertion = "invalid_assertion";
const invalidClientCredential = "invalid_client_credential";
const tokenRefreshRequired = "token_refresh_required";
const userTimeoutReached = "user_timeout_reached";
const tokenClaimsCnfRequiredForSignedJwt = "token_claims_cnf_required_for_signedjwt";
const authorizationCodeMissingFromServerResponse = "authorization_code_missing_from_server_response";
const bindingKeyNotRemoved = "binding_key_not_removed";
const endSessionEndpointNotSupported = "end_session_endpoint_not_supported";
const keyIdMissing = "key_id_missing";
const noNetworkConnectivity = "no_network_connectivity";
const userCanceled = "user_canceled";
const missingTenantIdError = "missing_tenant_id_error";
const methodNotImplemented = "method_not_implemented";
const nestedAppAuthBridgeDisabled = "nested_app_auth_bridge_disabled";

export { authTimeNotFound, authorizationCodeMissingFromServerResponse, bindingKeyNotRemoved, cannotAppendScopeSet, cannotRemoveEmptyScope, clientInfoDecodingError, clientInfoEmptyError, deviceCodeExpired, deviceCodePollingCancelled, deviceCodeUnknownError, emptyInputScopeSet, endSessionEndpointNotSupported, endpointResolutionError, hashNotDeserialized, invalidAssertion, invalidCacheEnvironment, invalidCacheRecord, invalidClientCredential, invalidState, keyIdMissing, maxAgeTranspired, methodNotImplemented, missingTenantIdError, multipleMatchingAccounts, multipleMatchingAppMetadata, multipleMatchingTokens, nestedAppAuthBridgeDisabled, networkError, noAccountFound, noAccountInSilentRequest, noCryptoObject, noNetworkConnectivity, nonceMismatch, nullOrEmptyToken, openIdConfigError, requestCannotBeMade, stateMismatch, stateNotFound, tokenClaimsCnfRequiredForSignedJwt, tokenParsingError, tokenRefreshRequired, unexpectedCredentialType, userCanceled, userTimeoutReached };
//# sourceMappingURL=ClientAuthErrorCodes.mjs.map
