{"version": 3, "file": "decompressResponsePolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/decompressResponsePolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAkBlC,4DAEC;AAhBD,0EAGqD;AAErD;;GAEG;AACU,QAAA,4BAA4B,GAAG,uCAA+B,CAAC;AAE5E;;;GAGG;AACH,SAAgB,wBAAwB;IACtC,OAAO,IAAA,mCAA2B,GAAE,CAAC;AACvC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  decompressResponsePolicyName as tspDecompressResponsePolicyName,\n  decompressResponsePolicy as tspDecompressResponsePolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the decompressResponsePolicy.\n */\nexport const decompressResponsePolicyName = tspDecompressResponsePolicyName;\n\n/**\n * A policy to enable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport function decompressResponsePolicy(): PipelinePolicy {\n  return tspDecompressResponsePolicy();\n}\n"]}