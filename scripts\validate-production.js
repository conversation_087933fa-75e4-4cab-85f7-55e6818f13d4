#!/usr/bin/env node

/**
 * Production Build Validation Script
 * Validates production builds and measures performance impact
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏭 V1b3-Sama Production Build Validation');
console.log('='.repeat(50));

// Configuration
const config = {
    distPath: path.join(__dirname, '..', 'dist'),
    packagePath: path.join(__dirname, '..', 'package.json'),
    vsixPath: path.join(__dirname, '..', 'v1b3-sama.vsix'),
    reportPath: path.join(__dirname, '..', 'PRODUCTION_VALIDATION.md'),
    thresholds: {
        maxBundleSize: 3 * 1024 * 1024,    // 3MB
        maxActivationTime: 2000,            // 2 seconds
        maxMemoryUsage: 100 * 1024 * 1024,  // 100MB
        minCompressionRatio: 0.3            // 30% compression
    }
};

// Validation results
const validationResults = {
    timestamp: new Date().toISOString(),
    buildInfo: {},
    bundleAnalysis: {},
    performanceTests: {},
    securityChecks: {},
    compatibilityTests: {},
    recommendations: [],
    overallStatus: 'unknown'
};

// Build production package
async function buildProduction() {
    console.log('🔨 Building production package...');
    
    try {
        // Clean previous builds
        if (fs.existsSync(config.distPath)) {
            execSync(`rm -rf ${config.distPath}`, { stdio: 'pipe' });
        }
        
        // Build production
        console.log('  📦 Running production build...');
        execSync('npm run package', { stdio: 'pipe' });
        
        // Package extension
        console.log('  📦 Creating VSIX package...');
        execSync('npx vsce package --out v1b3-sama.vsix', { stdio: 'pipe' });
        
        console.log('✅ Production build complete');
        return true;
        
    } catch (error) {
        console.error('❌ Production build failed:', error.message);
        return false;
    }
}

// Analyze bundle
function analyzeBundleSize() {
    console.log('\n📏 Analyzing bundle size...');
    
    if (!fs.existsSync(config.distPath)) {
        console.log('❌ Dist folder not found');
        return null;
    }
    
    const analysis = {
        totalSize: 0,
        files: [],
        compressionRatio: 0,
        largestFiles: []
    };
    
    // Recursively analyze files
    function analyzeDirectory(dir, relativePath = '') {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const fullPath = path.join(dir, file);
            const relativeFilePath = path.join(relativePath, file);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
                analyzeDirectory(fullPath, relativeFilePath);
            } else {
                const fileInfo = {
                    path: relativeFilePath,
                    size: stats.size,
                    sizeKB: Math.round(stats.size / 1024)
                };
                
                analysis.files.push(fileInfo);
                analysis.totalSize += stats.size;
            }
        });
    }
    
    analyzeDirectory(config.distPath);
    
    // Sort by size
    analysis.files.sort((a, b) => b.size - a.size);
    analysis.largestFiles = analysis.files.slice(0, 5);
    
    // Check VSIX size for compression ratio
    if (fs.existsSync(config.vsixPath)) {
        const vsixSize = fs.statSync(config.vsixPath).size;
        analysis.compressionRatio = 1 - (vsixSize / analysis.totalSize);
    }
    
    console.log(`  📊 Total bundle size: ${Math.round(analysis.totalSize / 1024 / 1024 * 100) / 100}MB`);
    console.log(`  🗜️  Compression ratio: ${Math.round(analysis.compressionRatio * 100)}%`);
    
    return analysis;
}

// Test extension activation performance
async function testActivationPerformance() {
    console.log('\n⚡ Testing activation performance...');
    
    // Simulate activation test
    const measurements = [];
    
    for (let i = 0; i < 5; i++) {
        const startTime = performance.now();
        
        // Simulate extension activation
        await new Promise(resolve => {
            setTimeout(() => {
                // Simulate loading time based on bundle size
                const bundleSize = validationResults.bundleAnalysis.totalSize || 1024 * 1024;
                const loadTime = Math.min(2000, bundleSize / 1000); // Rough estimate
                setTimeout(resolve, loadTime + Math.random() * 200);
            }, 100);
        });
        
        const duration = performance.now() - startTime;
        measurements.push(duration);
        console.log(`  Run ${i + 1}: ${Math.round(duration)}ms`);
    }
    
    const avgActivationTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    
    return {
        measurements,
        average: Math.round(avgActivationTime),
        min: Math.round(Math.min(...measurements)),
        max: Math.round(Math.max(...measurements))
    };
}

// Test memory usage
async function testMemoryUsage() {
    console.log('\n💾 Testing memory usage...');
    
    // Simulate memory usage based on bundle analysis
    const bundleSize = validationResults.bundleAnalysis.totalSize || 1024 * 1024;
    const estimatedMemory = Math.min(config.thresholds.maxMemoryUsage, bundleSize * 1.5);
    
    console.log(`  📊 Estimated memory usage: ${Math.round(estimatedMemory / 1024 / 1024)}MB`);
    
    return {
        estimated: estimatedMemory,
        baseline: 50 * 1024 * 1024, // 50MB baseline
        threshold: config.thresholds.maxMemoryUsage
    };
}

// Security checks
function performSecurityChecks() {
    console.log('\n🔒 Performing security checks...');
    
    const checks = {
        packageJson: false,
        dependencies: false,
        bundleIntegrity: false,
        permissions: false
    };
    
    try {
        // Check package.json
        const packageJson = JSON.parse(fs.readFileSync(config.packagePath, 'utf8'));
        checks.packageJson = packageJson.name && packageJson.version && packageJson.publisher;
        
        // Check for known vulnerable dependencies (simplified)
        const dependencies = Object.keys(packageJson.dependencies || {});
        checks.dependencies = !dependencies.some(dep => 
            ['lodash', 'moment', 'request'].includes(dep) // Example vulnerable packages
        );
        
        // Check bundle integrity
        checks.bundleIntegrity = fs.existsSync(config.distPath) && 
                                fs.readdirSync(config.distPath).length > 0;
        
        // Check permissions in package.json
        const activationEvents = packageJson.activationEvents || [];
        checks.permissions = !activationEvents.includes('*'); // Avoid wildcard activation
        
        console.log(`  📋 Package.json: ${checks.packageJson ? '✅' : '❌'}`);
        console.log(`  🔍 Dependencies: ${checks.dependencies ? '✅' : '❌'}`);
        console.log(`  🛡️  Bundle integrity: ${checks.bundleIntegrity ? '✅' : '❌'}`);
        console.log(`  🔐 Permissions: ${checks.permissions ? '✅' : '❌'}`);
        
    } catch (error) {
        console.error('❌ Security check failed:', error.message);
    }
    
    return checks;
}

// Compatibility tests
function testCompatibility() {
    console.log('\n🔄 Testing compatibility...');
    
    const compatibility = {
        vscodeVersion: false,
        nodeVersion: false,
        platform: false,
        dependencies: false
    };
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(config.packagePath, 'utf8'));
        
        // Check VS Code version compatibility
        const engines = packageJson.engines || {};
        compatibility.vscodeVersion = engines.vscode && engines.vscode.includes('^1.');
        
        // Check Node version
        const nodeVersion = process.version;
        compatibility.nodeVersion = nodeVersion.startsWith('v16.') || 
                                   nodeVersion.startsWith('v18.') || 
                                   nodeVersion.startsWith('v20.');
        
        // Check platform
        compatibility.platform = ['win32', 'darwin', 'linux'].includes(process.platform);
        
        // Check critical dependencies
        const deps = packageJson.dependencies || {};
        compatibility.dependencies = deps.vscode || deps['@types/vscode'];
        
        console.log(`  📱 VS Code version: ${compatibility.vscodeVersion ? '✅' : '❌'}`);
        console.log(`  🟢 Node version: ${compatibility.nodeVersion ? '✅' : '❌'}`);
        console.log(`  💻 Platform: ${compatibility.platform ? '✅' : '❌'}`);
        console.log(`  📦 Dependencies: ${compatibility.dependencies ? '✅' : '❌'}`);
        
    } catch (error) {
        console.error('❌ Compatibility check failed:', error.message);
    }
    
    return compatibility;
}

// Generate recommendations
function generateRecommendations() {
    const recommendations = [];
    
    // Bundle size recommendations
    if (validationResults.bundleAnalysis.totalSize > config.thresholds.maxBundleSize) {
        recommendations.push({
            category: 'Bundle Size',
            severity: 'high',
            issue: 'Bundle size exceeds threshold',
            recommendation: 'Optimize dependencies and implement code splitting'
        });
    }
    
    // Performance recommendations
    if (validationResults.performanceTests.activation?.average > config.thresholds.maxActivationTime) {
        recommendations.push({
            category: 'Performance',
            severity: 'medium',
            issue: 'Slow activation time',
            recommendation: 'Optimize initialization sequence and lazy load services'
        });
    }
    
    // Compression recommendations
    if (validationResults.bundleAnalysis.compressionRatio < config.thresholds.minCompressionRatio) {
        recommendations.push({
            category: 'Compression',
            severity: 'low',
            issue: 'Poor compression ratio',
            recommendation: 'Review file types and consider additional compression'
        });
    }
    
    // Security recommendations
    const securityIssues = Object.values(validationResults.securityChecks).filter(check => !check).length;
    if (securityIssues > 0) {
        recommendations.push({
            category: 'Security',
            severity: 'high',
            issue: `${securityIssues} security checks failed`,
            recommendation: 'Review and fix security issues before release'
        });
    }
    
    return recommendations;
}

// Calculate overall status
function calculateOverallStatus() {
    const criticalIssues = validationResults.recommendations.filter(r => r.severity === 'high').length;
    const mediumIssues = validationResults.recommendations.filter(r => r.severity === 'medium').length;
    
    if (criticalIssues > 0) {
        return 'failed';
    } else if (mediumIssues > 2) {
        return 'warning';
    } else {
        return 'passed';
    }
}

// Generate validation report
function generateValidationReport() {
    const statusEmoji = {
        passed: '✅',
        warning: '⚠️',
        failed: '❌'
    };
    
    return `# Production Build Validation Report

**Status:** ${statusEmoji[validationResults.overallStatus]} ${validationResults.overallStatus.toUpperCase()}
**Generated:** ${validationResults.timestamp}

## Build Information

- **Bundle Size:** ${Math.round(validationResults.bundleAnalysis.totalSize / 1024 / 1024 * 100) / 100}MB
- **Compression Ratio:** ${Math.round(validationResults.bundleAnalysis.compressionRatio * 100)}%
- **Files:** ${validationResults.bundleAnalysis.files?.length || 0}

## Performance Tests

- **Activation Time:** ${validationResults.performanceTests.activation?.average || 0}ms (avg)
- **Memory Usage:** ${Math.round((validationResults.performanceTests.memory?.estimated || 0) / 1024 / 1024)}MB (estimated)

## Security Checks

${Object.entries(validationResults.securityChecks).map(([check, passed]) => 
    `- **${check}:** ${passed ? '✅ Passed' : '❌ Failed'}`
).join('\n')}

## Compatibility Tests

${Object.entries(validationResults.compatibilityTests).map(([test, passed]) => 
    `- **${test}:** ${passed ? '✅ Compatible' : '❌ Issues detected'}`
).join('\n')}

## Recommendations

${validationResults.recommendations.length > 0 ? 
    validationResults.recommendations.map(rec => `
### ${rec.category} (${rec.severity.toUpperCase()})
- **Issue:** ${rec.issue}
- **Recommendation:** ${rec.recommendation}
`).join('\n') : 
    '✅ No issues detected. Build is ready for production.'}

## Next Steps

${validationResults.overallStatus === 'passed' ? 
    '1. Deploy to marketplace\n2. Monitor performance metrics\n3. Collect user feedback' :
    '1. Address critical issues\n2. Re-run validation\n3. Review performance optimizations'}
`;
}

// Main validation function
async function runProductionValidation() {
    try {
        // Step 1: Build production package
        const buildSuccess = await buildProduction();
        if (!buildSuccess) {
            process.exit(1);
        }
        
        // Step 2: Analyze bundle
        validationResults.bundleAnalysis = analyzeBundleSize();
        
        // Step 3: Test performance
        validationResults.performanceTests = {
            activation: await testActivationPerformance(),
            memory: await testMemoryUsage()
        };
        
        // Step 4: Security checks
        validationResults.securityChecks = performSecurityChecks();
        
        // Step 5: Compatibility tests
        validationResults.compatibilityTests = testCompatibility();
        
        // Step 6: Generate recommendations
        validationResults.recommendations = generateRecommendations();
        
        // Step 7: Calculate overall status
        validationResults.overallStatus = calculateOverallStatus();
        
        // Step 8: Generate report
        const report = generateValidationReport();
        fs.writeFileSync(config.reportPath, report);
        
        console.log('\n🎉 Production validation complete!');
        console.log(`📋 Report saved to: ${config.reportPath}`);
        
        // Display summary
        console.log('\n📊 Validation Summary:');
        console.log('='.repeat(30));
        console.log(`Status: ${validationResults.overallStatus.toUpperCase()}`);
        console.log(`Bundle Size: ${Math.round(validationResults.bundleAnalysis.totalSize / 1024 / 1024 * 100) / 100}MB`);
        console.log(`Activation Time: ${validationResults.performanceTests.activation?.average || 0}ms`);
        console.log(`Recommendations: ${validationResults.recommendations.length}`);
        
        if (validationResults.overallStatus === 'failed') {
            console.log('\n❌ Critical issues detected. Fix before release.');
            process.exit(1);
        } else if (validationResults.overallStatus === 'warning') {
            console.log('\n⚠️  Issues detected. Review recommendations.');
        } else {
            console.log('\n✅ Build ready for production!');
        }
        
    } catch (error) {
        console.error('❌ Validation failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    runProductionValidation();
}

module.exports = {
    runProductionValidation,
    analyzeBundleSize,
    testActivationPerformance,
    performSecurityChecks
};
