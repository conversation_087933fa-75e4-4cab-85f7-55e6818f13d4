{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAEA,OAAO,EACH,gBAAgB,IAAI,wBAAwB,EAC5C,aAAa,IAAI,uBAAuB,GAC3C,MAAM,4BAA4B,CAAC;AAEpC,OAAO,YAAY,MAAM,eAAe,CAAC;AAEzC,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,MAAM,MAAM,OAAO,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACjE,OAAO,aAAa,MAAM,sBAAsB,CAAC;AACjD,OAAO,mBAAmB,MAAM,6BAA6B,CAAC;AAC9D,OAAO,cAAc,MAAM,uBAAuB,CAAC;AAEnD,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAC9C,MAAM,gBAAgB,GAAG,CAAC,UAAkB,EAAE,EAAE;IAC5C,OAAO,UAAU,CAAC,UAAU,EAAE;QAC1B,gBAAgB,EAAE,MAAM,CAAC,IAAI;QAC7B,YAAY,EAAE,WAAW;KAC5B,CAAC,CAAC;AACP,CAAC,CAAC;AAqBF;;GAEG;AACH,MAAM,uCAAuC,GAAG,CAC5C,oBAA0C,EAC1C,EAAE,kBAAkB,EAAmC,EACzC,EAAE;IAChB,OAAO;QACH,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;QACvC,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACpD,MAAM,aAAa,GACf,OAAO,CAAC,QAAQ,KAAK,MAAM;gBACvB,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS;oBAChC,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO;wBAC9B,CAAC,CAAC,CAAC;wBACH,CAAC,CAAC,CAAC,CAAC;YAEZ,qDAAqD;YACrD,MAAM,SAAS,GACX,kBAAkB,IAAI,OAAO,CAAC,OAAO;gBACjC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE;oBAC7C,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;wBACrB,OAAO,IAAI,CAAC;oBAChB,CAAC;iBACJ,CAAC;gBACJ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;YAC5B,OAAO;gBACH,qBAAqB;gBACrB,0BAA0B;gBAC1B,qBAAqB;gBACrB,YAAY;gBACZ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;gBAC7F;;mBAEG;gBACH,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBACvB;;mBAEG;gBACH,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;gBAC5B;;mBAEG;gBACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;gBAChC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,IAAI,SAAS,KAAK,OAAO,CAAC,OAAO,EAAE;gBAC5C,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,uBAAuB;gBACvB,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,MAAM;aACf,CAAC;QACN,CAAC,CAAC;KACL,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,eAA0C;IAC1E,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;IACpD,MAAM,qBAAqB,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IAClF;;OAEG;IACH,MAAM,kBAAkB,GAAG,qBAAqB,IAAI,eAAe,CAAC,YAAY,IAAI,KAAK,CAAC;IAC1F,KAAK,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;IAEzC,IAAI;QACA,MAAM,SAAS,GAAG,MAAM,yBAAyB,CAAC,eAAe,CAAC,CAAC;QACnE,OAAO;YACH,MAAM,EAAE,CAAC,OAA+B,EAAE,EAAE;gBACxC,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;SACJ,CAAC;KACL;IAAC,MAAM;QACJ,MAAM,SAAS,GAAG,MAAM,uBAAuB,CAAC,eAAe,CAAC,CAAC;QACjE,OAAO;YACH,MAAM,EAAE,CAAC,OAA+B,EAAE,EAAE;gBACxC,OAAO,SAAS,CAAC,MAAM,CACnB,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACnB,uCAAuC,CAAC,MAAM,EAAE;oBAC5C,kBAAkB;iBACrB,CAAC,CACL,CACJ,CAAC;YACN,CAAC;SACJ,CAAC;KACL;AACL,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAAC,eAAgC;IAC5E,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;IACpD,KAAK,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;IACzC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC3C,QAAQ,aAAa,EAAE;YACnB,KAAK,MAAM;gBACP,OAAO;oBACH,MAAM,EAAE,SAAS,yBAAyB,CAAC,OAA+B;wBACtE,OAAO,aAAa,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;oBACnD,CAAC;iBACJ,CAAC;YACN,KAAK,aAAa;gBACd,OAAO;oBACH,MAAM,EAAE,SAAS,yBAAyB,CAAC,OAA+B;wBACtE,OAAO,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;oBACzD,CAAC;iBACJ,CAAC;YACN,KAAK,OAAO;gBACR,OAAO;oBACH,MAAM,EAAE,SAAS,yBAAyB,CAAC,OAA+B;wBACtE,OAAO,cAAc,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;oBACpD,CAAC;iBACJ,CAAC;SACT;KACJ;IACD,IAAI,SAAoB,CAAC;IACzB,IAAI,aAAa,CAAC;IAClB,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QAC9B,aAAa,GAAG,aAAa,CAAC;KACjC;SAAM,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC,EAAE;QAClE,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;KAC9D;SAAM;QACH,MAAM,OAAO,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,gBAAgB,CAAC,wBAAwB,aAAa,EAAE,CAAC,CAAC;QAC7G,IAAI,OAAO,EAAE;YACT,aAAa,GAAG,OAAO,CAAC;SAC3B;KACJ;IACD,IAAI,CAAC,aAAa,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4BAA4B,aAAa,EAAE,CAAC,CAAC;KAChE;IACD,IAAI;QACA,kCAAkC;QAClC,SAAS,GACL,CACI,MAAM,aAAa,CAAC,aAAa,EAAE;YAC/B,gBAAgB,EAAE,MAAM,CAAC,IAAI;YAC7B,YAAY,EAAE,WAAW;SAC5B,CAAC,CACL,CAAC,OAGL,CAAC,OAAO,CAAC;KACb;IAAC,OAAO,EAAE,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,4BAA4B,aAAa;EAC/D,EAAE,EAAE,CAAC,CAAC;KACH;IACD,OAAO;QACH,MAAM,EAAE,SAAS,yBAAyB,CAAC,OAA+B;YACtE,OAAO,SAAS,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAC/C,CAAC;KACJ,CAAC;AACN,CAAC;AAMD,MAAM,UAAU,gBAAgB;IAC5B,MAAM,kBAAkB,GAAG,wBAAwB,EAAE,CAAC;IACtD,MAAM,oBAAoB,GAAG,0BAA0B,EAAE,CAAC;IAC1D,MAAM,wBAAwB,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACzF,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAChD,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CACpE,CAAC;IACF,OAAO,CAAC,GAAG,kBAAkB,EAAE,GAAG,oBAAoB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAM,UAAU,0BAA0B;IACtC,OAAO;QACH;YACI,IAAI,EAAE,MAAM;SACf;QACD;YACI,IAAI,EAAE,aAAa;SACtB;QACD;YACI,IAAI,EAAE,OAAO;SAChB;KACJ,CAAC;AACN,CAAC"}