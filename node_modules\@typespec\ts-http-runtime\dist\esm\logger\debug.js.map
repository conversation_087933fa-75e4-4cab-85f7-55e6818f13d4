{"version": 3, "file": "debug.js", "sourceRoot": "", "sources": ["../../../src/logger/debug.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAgE/B,MAAM,gBAAgB,GACpB,CAAC,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;AAEpF,IAAI,aAAiC,CAAC;AACtC,IAAI,iBAAiB,GAAa,EAAE,CAAC;AACrC,IAAI,iBAAiB,GAAa,EAAE,CAAC;AACrC,MAAM,SAAS,GAAe,EAAE,CAAC;AAEjC,IAAI,gBAAgB,EAAE,CAAC;IACrB,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,QAAQ,GAAU,MAAM,CAAC,MAAM,CACnC,CAAC,SAAiB,EAAY,EAAE;IAC9B,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC,EACD;IACE,MAAM;IACN,OAAO;IACP,OAAO;IACP,GAAG;CACJ,CACF,CAAC;AAEF,SAAS,MAAM,CAAC,UAAkB;IAChC,aAAa,GAAG,UAAU,CAAC;IAC3B,iBAAiB,GAAG,EAAE,CAAC;IACvB,iBAAiB,GAAG,EAAE,CAAC;IACvB,MAAM,QAAQ,GAAG,KAAK,CAAC;IACvB,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IAC5F,KAAK,MAAM,EAAE,IAAI,aAAa,EAAE,CAAC;QAC/B,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,iBAAiB,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,iBAAiB,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AAED,SAAS,OAAO,CAAC,SAAiB;IAChC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;QACjD,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,OAAO;IACd,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,CAAC;IACnC,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,SAAiB;IACvC,MAAM,WAAW,GAAa,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;QACjD,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC;QAC3B,OAAO;QACP,GAAG,EAAE,QAAQ,CAAC,GAAG;QACjB,SAAS;QACT,MAAM;KACP,CAAC,CAAC;IAEH,SAAS,KAAK,CAAC,GAAG,IAAW;QAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACtC,CAAC;QACD,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAE5B,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,OAAO;IACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAiB,SAAiB;IAC/C,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,SAAS,EAAE,CAAC,CAAC;IACrE,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IAC3B,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,eAAe,QAAQ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { log } from \"./log.js\";\n\n/**\n * A simple mechanism for enabling logging.\n * Intended to mimic the publicly available `debug` package.\n */\nexport interface Debug {\n  /**\n   * Creates a new logger with the given namespace.\n   */\n  (namespace: string): Debugger;\n  /**\n   * The default log method (defaults to console)\n   */\n  log: (...args: any[]) => void;\n  /**\n   * Enables a particular set of namespaces.\n   * To enable multiple separate them with commas, e.g. \"info,debug\".\n   * Supports wildcards, e.g. \"typeSpecRuntime:*\"\n   * Supports skip syntax, e.g. \"typeSpecRuntime:*,-typeSpecRuntime:storage:*\" will enable\n   * everything under typeSpecRuntime except for things under typeSpecRuntime:storage.\n   */\n  enable: (namespaces: string) => void;\n  /**\n   * Checks if a particular namespace is enabled.\n   */\n  enabled: (namespace: string) => boolean;\n  /**\n   * Disables all logging, returns what was previously enabled.\n   */\n  disable: () => string;\n}\n\n/**\n * A log function that can be dynamically enabled and redirected.\n */\nexport interface Debugger {\n  /**\n   * Logs the given arguments to the `log` method.\n   */\n  (...args: any[]): void;\n  /**\n   * True if this logger is active and logging.\n   */\n  enabled: boolean;\n  /**\n   * Used to cleanup/remove this logger.\n   */\n  destroy: () => boolean;\n  /**\n   * The current log method. Can be overridden to redirect output.\n   */\n  log: (...args: any[]) => void;\n  /**\n   * The namespace of this logger.\n   */\n  namespace: string;\n  /**\n   * Extends this logger with a child namespace.\n   * Namespaces are separated with a ':' character.\n   */\n  extend: (namespace: string) => Debugger;\n}\n\nconst debugEnvVariable =\n  (typeof process !== \"undefined\" && process.env && process.env.DEBUG) || undefined;\n\nlet enabledString: string | undefined;\nlet enabledNamespaces: RegExp[] = [];\nlet skippedNamespaces: RegExp[] = [];\nconst debuggers: Debugger[] = [];\n\nif (debugEnvVariable) {\n  enable(debugEnvVariable);\n}\n\nconst debugObj: Debug = Object.assign(\n  (namespace: string): Debugger => {\n    return createDebugger(namespace);\n  },\n  {\n    enable,\n    enabled,\n    disable,\n    log,\n  },\n);\n\nfunction enable(namespaces: string): void {\n  enabledString = namespaces;\n  enabledNamespaces = [];\n  skippedNamespaces = [];\n  const wildcard = /\\*/g;\n  const namespaceList = namespaces.split(\",\").map((ns) => ns.trim().replace(wildcard, \".*?\"));\n  for (const ns of namespaceList) {\n    if (ns.startsWith(\"-\")) {\n      skippedNamespaces.push(new RegExp(`^${ns.substr(1)}$`));\n    } else {\n      enabledNamespaces.push(new RegExp(`^${ns}$`));\n    }\n  }\n  for (const instance of debuggers) {\n    instance.enabled = enabled(instance.namespace);\n  }\n}\n\nfunction enabled(namespace: string): boolean {\n  if (namespace.endsWith(\"*\")) {\n    return true;\n  }\n\n  for (const skipped of skippedNamespaces) {\n    if (skipped.test(namespace)) {\n      return false;\n    }\n  }\n  for (const enabledNamespace of enabledNamespaces) {\n    if (enabledNamespace.test(namespace)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction disable(): string {\n  const result = enabledString || \"\";\n  enable(\"\");\n  return result;\n}\n\nfunction createDebugger(namespace: string): Debugger {\n  const newDebugger: Debugger = Object.assign(debug, {\n    enabled: enabled(namespace),\n    destroy,\n    log: debugObj.log,\n    namespace,\n    extend,\n  });\n\n  function debug(...args: any[]): void {\n    if (!newDebugger.enabled) {\n      return;\n    }\n    if (args.length > 0) {\n      args[0] = `${namespace} ${args[0]}`;\n    }\n    newDebugger.log(...args);\n  }\n\n  debuggers.push(newDebugger);\n\n  return newDebugger;\n}\n\nfunction destroy(this: Debugger): boolean {\n  const index = debuggers.indexOf(this);\n  if (index >= 0) {\n    debuggers.splice(index, 1);\n    return true;\n  }\n  return false;\n}\n\nfunction extend(this: Debugger, namespace: string): Debugger {\n  const newDebugger = createDebugger(`${this.namespace}:${namespace}`);\n  newDebugger.log = this.log;\n  return newDebugger;\n}\n\nexport default debugObj;\n"]}