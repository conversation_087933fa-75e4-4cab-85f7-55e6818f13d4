{"version": 3, "file": "BrowserPerformanceClient.mjs", "sources": ["../../src/telemetry/BrowserPerformanceClient.ts"], "sourcesContent": [null], "names": ["BrowserCrypto.createNewGuid"], "mappings": ";;;;;;;AAAA;;;AAGG;AAqBH;;AAEG;AACH,SAAS,wBAAwB,GAAA;AAC7B,IAAA,IAAI,cAAmC,CAAC;IACxC,IAAI;AACA,QAAA,cAAc,GAAG,MAAM,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC7D,MAAM,WAAW,GAAG,cAAc,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACtE,QAAA,IAAI,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC3B,YAAA,OAAO,OAAO,qCAAoC,CAAC,CAAC;AACvD,SAAA;;AAEJ,KAAA;IAAC,OAAO,CAAC,EAAE,GAAE;AAEd,IAAA,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;AAEG;AACH,SAAS,6BAA6B,GAAA;AAClC,IAAA,QACI,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;QACzC,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,UAAU,EAC9C;AACN,CAAC;AAED;;;;AAIG;AACH,SAAS,iBAAiB,CACtB,SAA0C,EAAA;AAE1C,IAAA,IAAI,CAAC,SAAS,IAAI,CAAC,6BAA6B,EAAE,EAAE;AAChD,QAAA,OAAO,SAAS,CAAC;AACpB,KAAA;AAED,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;AAC5D,CAAC;AAEK,MAAO,wBACT,SAAQ,iBAAiB,CAAA;AAGzB,IAAA,WAAA,CACI,aAA4B,EAC5B,SAAuB,EACvB,aAAmC,EAAA;QAEnC,KAAK,CACD,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC3B,aAAa,CAAC,IAAI,CAAC,SAAS,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAE,CAAA,EAChE,IAAI,MAAM,CACN,aAAa,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,EACzC,IAAI,EACJ,OAAO,CACV,EACD,IAAI,EACJ,OAAO,EACP,aAAa,CAAC,SAAS,EAAE,WAAW,IAAI;AACpC,YAAA,OAAO,EAAE,EAAE;AACX,YAAA,UAAU,EAAE,EAAE;AACjB,SAAA,EACD,SAAS,EACT,aAAa,CAChB,CAAC;KACL;IAED,UAAU,GAAA;AACN,QAAA,OAAOA,aAA2B,EAAE,CAAC;KACxC;IAEO,iBAAiB,GAAA;QACrB,OAAO,QAAQ,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC;KACvD;AAEO,IAAA,+BAA+B,CACnC,eAA2C,EAAA;QAE3C,KAAK,wBAAwB,EAAE,EAAE,IAAI,CAAC,CAAC,MAAM,KAAI;AAC7C,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAC5C,eAAe,CAAC,KAAK,CAAC,aAAa,CACtC,CAAC;YACF,MAAM,WAAW,GACb,SAAS;gBACT,SAAS,CAAC,OAAO,KAAK,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC;YACxD,MAAM,sBAAsB,GAAqB,EAAE,CAAC;AACpD,YAAA,IAAI,WAAW,IAAI,SAAS,EAAE,yBAAyB,EAAE;gBACrD,SAAS,CAAC,yBAAyB,CAAC,OAAO,CACvC,CAAC,cAA8B,KAAI;oBAC/B,sBAAsB,CAAC,IAAI,CAAC,EAAE,GAAG,cAAc,EAAE,CAAC,CAAC;AACvD,iBAAC,CACJ,CAAC;AACL,aAAA;;AAED,YAAA,MAAM,CAAC,6BAA6B,CAAC,iBAAiB,CAClD,eAAe,CAAC,KAAK,CAAC,aAAa,EACnC,sBAAsB,CACzB,CAAC;AACN,SAAC,CAAC,CAAC;KACN;AAED;;;;;;;AAOG;IACH,gBAAgB,CACZ,WAAmB,EACnB,aAAsB,EAAA;;AAGtB,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACrD,MAAM,eAAe,GAAG,KAAK,CAAC,gBAAgB,CAC1C,WAAW,EACX,aAAa,CAChB,CAAC;QACF,MAAM,SAAS,GAAuB,6BAA6B,EAAE;AACjE,cAAE,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;cACxB,SAAS,CAAC;QAEhB,MAAM,kBAAkB,GAAG,wBAAwB,EAAE,EAAE,IAAI,CACvD,CAAC,MAAM,KAAI;AACP,YAAA,OAAO,IAAI,MAAM,CAAC,6BAA6B,CAC3C,WAAW,EACX,eAAe,CAAC,KAAK,CAAC,aAAa,CACtC,CAAC;AACN,SAAC,CACJ,CAAC;AACF,QAAA,KAAK,kBAAkB,EAAE,IAAI,CAAC,CAAC,WAAW,KACtC,WAAW,CAAC,gBAAgB,EAAE,CACjC,CAAC;QAEF,OAAO;AACH,YAAA,GAAG,eAAe;AAClB,YAAA,GAAG,EAAE,CACD,KAAiC,EACjC,KAAe,KACU;AACzB,gBAAA,MAAM,GAAG,GAAG,eAAe,CAAC,GAAG,CAC3B;AACI,oBAAA,GAAG,KAAK;oBACR,mBAAmB;AACnB,oBAAA,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;AAC3C,oBAAA,UAAU,EAAE,iBAAiB,CAAC,SAAS,CAAC;iBAC3C,EACD,KAAK,CACR,CAAC;AACF,gBAAA,KAAK,kBAAkB,EAAE,IAAI,CAAC,CAAC,WAAW,KACtC,WAAW,CAAC,cAAc,EAAE,CAC/B,CAAC;AACF,gBAAA,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,CAAC;AAEtD,gBAAA,OAAO,GAAG,CAAC;aACd;YACD,OAAO,EAAE,MAAK;gBACV,eAAe,CAAC,OAAO,EAAE,CAAC;AAC1B,gBAAA,KAAK,kBAAkB,EAAE,IAAI,CAAC,CAAC,WAAW,KACtC,WAAW,CAAC,gBAAgB,EAAE,CACjC,CAAC;AACF,gBAAA,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,CAAC;aACzD;SACJ,CAAC;KACL;AAED;;;;;AAKG;IACH,eAAe,CACX,SAA4B,EAC5B,aAAsB,EAAA;QAEtB,IAAI,CAAC,6BAA6B,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA0G,uGAAA,EAAA,SAAS,CAAE,CAAA,CACxH,CAAC;YACF,OAAO;AACV,SAAA;QAED,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA+C,4CAAA,EAAA,SAAS,CAAmD,iDAAA,CAAA,CAC9G,CAAC;YACF,OAAO;AACV,SAAA;QAED,MAAM,aAAa,GACf,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACxD;;;AAGG;AACH,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAA,+CAAA,EAAkD,aAAa,CAAC,IAAI,CAAA,MAAA,CAAQ,EAC5E,aAAa,CAChB,CAAC;AACF,YAAA,IAAI,CAAC,mBAAmB,CACpB,aAAa,CAAC,IAAI,EAClB,aAAa,EACb,SAAS,EACT,IAAI,CACP,CAAC;AACL,SAAA;AACD,QAAA,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,aAAa,EAAE;AAChD,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;AACjC,SAAA,CAAC,CAAC;KACN;AAED;;;;;;;;AAQG;AACH,IAAA,mBAAmB,CACf,SAAiB,EACjB,aAAsB,EACtB,SAAkB,EAClB,iBAA2B,EAAA;QAE3B,IAAI,CAAC,6BAA6B,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAuG,oGAAA,EAAA,SAAS,CAAE,CAAA,CACrH,CAAC;YACF,OAAO;AACV,SAAA;QAED,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA+C,4CAAA,EAAA,SAAS,CAAgD,8CAAA,CAAA,CAC3G,CAAC;YACF,OAAO;AACV,SAAA;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,EAAE;YACf,OAAO;AACV,SAAA;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAC7C,QAAA,MAAM,YAAY,GACd,SAAS,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAEtE,QAAA,OAAO,KAAK,CAAC,mBAAmB,CAC5B,SAAS,EACT,aAAa,EACb,YAAY,EACZ,iBAAiB,CACpB,CAAC;KACL;AACJ;;;;"}