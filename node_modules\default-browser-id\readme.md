# default-browser-id

> Get the [bundle identifier](https://developer.apple.com/library/Mac/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/plist/info/CFBundleIdentifier) of the default browser *(macOS)*\
> Example: `com.apple.Safari`

## Install

```sh
npm install default-browser-id
```

## Usage

```js
import defaultBrowserId from 'default-browser-id';

console.log(await defaultBrowserId());
//=> 'com.apple.Safari'
```
