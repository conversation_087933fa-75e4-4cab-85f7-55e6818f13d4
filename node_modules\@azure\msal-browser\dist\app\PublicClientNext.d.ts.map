{"version": 3, "file": "PublicClientNext.d.ts", "sourceRoot": "", "sources": ["../../src/app/PublicClientNext.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AACvE,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EACH,2BAA2B,EAC3B,WAAW,EACX,aAAa,EACb,MAAM,EACT,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAElE,OAAO,EACH,oBAAoB,EACpB,aAAa,EAChB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAG3E,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD;;;;;;;;;;GAUG;AACH,qBAAa,gBAAiB,YAAW,wBAAwB;IAK7D,SAAS,CAAC,UAAU,EAAG,WAAW,CAAC;IACnC,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;WAEnB,6BAA6B,CAC7C,aAAa,EAAE,aAAa,GAC7B,OAAO,CAAC,wBAAwB,CAAC;IAapC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,OAAO;IAeP;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAajC;;;;;;OAMG;IACG,iBAAiB,CACnB,OAAO,EAAE,YAAY,GACtB,OAAO,CAAC,oBAAoB,CAAC;IAIhC;;;;;;;;OAQG;IACH,oBAAoB,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7D;;;;;OAKG;IACH,kBAAkB,CACd,aAAa,EAAE,aAAa,GAC7B,OAAO,CAAC,oBAAoB,CAAC;IAIhC;;;;;;;;;OASG;IACH,kBAAkB,CACd,OAAO,EAAE,wBAAwB,GAClC,OAAO,CAAC,oBAAoB,CAAC;IAIhC;;;OAGG;IACH,gBAAgB,CACZ,QAAQ,EAAE,qBAAqB,EAC/B,UAAU,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,GAC9B,MAAM,GAAG,IAAI;IAIhB;;;OAGG;IACH,mBAAmB,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAI7C;;;;;OAKG;IACH,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM;IAIrE;;;;;OAKG;IACH,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAItD;;OAEG;IACH,0BAA0B,IAAI,IAAI;IAIlC;;OAEG;IACH,2BAA2B,IAAI,IAAI;IAInC;;;;OAIG;IACH,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI;IAI5D;;;;;;;OAOG;IACH,kBAAkB,CAAC,aAAa,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAI7D;;;;;;;OAOG;IACH,mBAAmB,CAAC,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAIxD;;;;;;;;OAQG;IACH,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAI1D;;;;OAIG;IACH,cAAc,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,WAAW,EAAE;IAI5D;;;;;;OAMG;IACH,qBAAqB,CACjB,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,GAC1B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAIvC;;;;;;OAMG;IACH,UAAU,CACN,OAAO,CAAC,EAAE,YAAY,GAAG,SAAS,GACnC,OAAO,CAAC,oBAAoB,CAAC;IAIhC;;;;;;;;OAQG;IACH,aAAa,CAAC,OAAO,CAAC,EAAE,eAAe,GAAG,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAInE;;;;OAIG;IACH,MAAM,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIxD;;;;OAIG;IACH,cAAc,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhE;;;OAGG;IACH,WAAW,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7D;;;;;;;;;;;;;;OAcG;IACH,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAInE;;OAEG;IACH,aAAa,IAAI,WAAW;IAI5B;;OAEG;IACH,SAAS,IAAI,MAAM;IAInB;;;OAGG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAI/B;;;OAGG;IACH,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI;IAInD;;OAEG;IACH,gBAAgB,IAAI,WAAW,GAAG,IAAI;IAItC;;;;OAIG;IACH,wBAAwB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAIhE;;;OAGG;IACH,mBAAmB,CAAC,gBAAgB,EAAE,iBAAiB,GAAG,IAAI;IAI9D;;;OAGG;IACH,gBAAgB,IAAI,oBAAoB;IAIxC;;;;;OAKG;IACG,YAAY,CACd,MAAM,EAAE,oBAAoB,EAC5B,OAAO,EACD,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,YAAY,GACnB,OAAO,CAAC,IAAI,CAAC;IAIhB;;;OAGG;IACH,UAAU,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;CAG/D"}