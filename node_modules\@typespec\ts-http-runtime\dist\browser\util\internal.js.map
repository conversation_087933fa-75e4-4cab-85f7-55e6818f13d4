{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/util/internal.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,yBAAyB,EAAE,MAAM,aAAa,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAsB,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AACnE,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,aAAa,EACb,MAAM,EACN,aAAa,EACb,WAAW,GACZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAqB,MAAM,oBAAoB,CAAC;AAC/F,OAAO,EAAE,SAAS,EAAyB,MAAM,gBAAgB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { calculateRetryDelay } from \"./delay.js\";\nexport { getRandomIntegerInclusive } from \"./random.js\";\nexport { isObject, type UnknownObject } from \"./object.js\";\nexport { isError } from \"./error.js\";\nexport { computeSha256Hash, computeSha256Hmac } from \"./sha256.js\";\nexport { randomUUID } from \"./uuidUtils.js\";\nexport {\n  isBrowser,\n  isBun,\n  isNodeLike,\n  isNodeRuntime,\n  isDeno,\n  isReactNative,\n  isWebWorker,\n} from \"./checkEnvironment.js\";\nexport { stringToUint8Array, uint8ArrayToString, type EncodingType } from \"./bytesEncoding.js\";\nexport { Sanitizer, type SanitizerOptions } from \"./sanitizer.js\";\n"]}