{"version": 3, "file": "concat.js", "sourceRoot": "", "sources": ["../../../src/util/concat.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAElC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAEzC,SAAgB,mBAAmB;;QAGjC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC;YACH,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,cAAM,MAAM,CAAC,IAAI,EAAE,CAAA,CAAC;gBAC5C,IAAI,IAAI,EAAE,CAAC;oBACT,6BAAO;gBACT,CAAC;gBAED,oBAAM,KAAK,CAAA,CAAC;YACd,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;CAAA;AAED,SAAS,iBAAiB,CAAI,SAAc;IAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;QACrC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QACtB,SAAS,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,MAA0D;IAE1D,IAAI,MAAM,YAAY,cAAc,EAAE,CAAC;QACrC,iBAAiB,CAAa,MAAM,CAAC,CAAC;QACtC,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;SAAM,CAAC;QACN,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAED,SAAS,QAAQ,CACf,MAA8E;IAE9E,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5C,CAAC;SAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,OAAO,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AASD;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,MAAM,CAC1B,OAAgD;IAEhD,OAAO;QACL,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEtF,OAAO,QAAQ,CAAC,IAAI,CAClB,CAAC;;;gBACC,KAAK,MAAM,MAAM,IAAI,OAAkC,EAAE,CAAC;;wBACxD,KAA0B,eAAA,0BAAA,cAAA,MAAM,CAAA,CAAA,YAAA,qFAAE,CAAC;4BAAT,sBAAM;4BAAN,WAAM;4BAArB,MAAM,KAAK,KAAA,CAAA;4BACpB,oBAAM,KAAK,CAAA,CAAC;wBACd,CAAC;;;;;;;;;gBACH,CAAC;YACH,CAAC;SAAA,CAAC,EAAE,CACL,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { Readable } from \"stream\";\nimport type { ReadableStream as AsyncIterableReadableStream } from \"stream/web\";\nimport { isBlob } from \"./typeGuards.js\";\n\nasync function* streamAsyncIterator(\n  this: ReadableStream<Uint8Array>,\n): AsyncIterableIterator<Uint8Array> {\n  const reader = this.getReader();\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nfunction makeAsyncIterable<T>(webStream: any): asserts webStream is AsyncIterableReadableStream<T> {\n  if (!webStream[Symbol.asyncIterator]) {\n    webStream[Symbol.asyncIterator] = streamAsyncIterator.bind(webStream);\n  }\n\n  if (!webStream.values) {\n    webStream.values = streamAsyncIterator.bind(webStream);\n  }\n}\n\nfunction ensureNodeStream(\n  stream: ReadableStream<Uint8Array> | NodeJS.ReadableStream,\n): NodeJS.ReadableStream {\n  if (stream instanceof ReadableStream) {\n    makeAsyncIterable<Uint8Array>(stream);\n    return Readable.fromWeb(stream);\n  } else {\n    return stream;\n  }\n}\n\nfunction toStream(\n  source: ReadableStream<Uint8Array> | NodeJS.ReadableStream | Uint8Array | Blob,\n): NodeJS.ReadableStream {\n  if (source instanceof Uint8Array) {\n    return Readable.from(Buffer.from(source));\n  } else if (isBlob(source)) {\n    return ensureNodeStream(source.stream());\n  } else {\n    return ensureNodeStream(source);\n  }\n}\n\n/**\n * Accepted binary data types for concat\n *\n * @internal\n */\nexport type ConcatSource = ReadableStream<Uint8Array> | NodeJS.ReadableStream | Uint8Array | Blob;\n\n/**\n * Utility function that concatenates a set of binary inputs into one combined output.\n *\n * @param sources - array of sources for the concatenation\n * @returns - in Node, a (() =\\> NodeJS.ReadableStream) which, when read, produces a concatenation of all the inputs.\n *           In browser, returns a `Blob` representing all the concatenated inputs.\n *\n * @internal\n */\nexport async function concat(\n  sources: (ConcatSource | (() => ConcatSource))[],\n): Promise<(() => NodeJS.ReadableStream) | Blob> {\n  return function () {\n    const streams = sources.map((x) => (typeof x === \"function\" ? x() : x)).map(toStream);\n\n    return Readable.from(\n      (async function* () {\n        for (const stream of streams as NodeJS.ReadableStream[]) {\n          for await (const chunk of stream) {\n            yield chunk;\n          }\n        }\n      })(),\n    );\n  };\n}\n"]}