/**
 * Execution State Manager - Core state machine for plan-based interactive execution
 * Manages the agent's execution state and coordinates between UI controls and backend operations
 *
 * HARDENED STATE MACHINE IMPLEMENTATION
 * - Strict state transitions with validation
 * - Process management for running operations
 * - Robust error handling and recovery
 */

import * as vscode from 'vscode';
import { ExecutionPlan, ExecutionStep, ILLMService } from '../interfaces/ILLMService';
import { PlanExecutorService } from './PlanExecutorService';
import { ActionHistoryManager } from '../actionHistoryManager';
import { ApiProviderManager } from '../apiProviderManager';
import { UnifiedExecutionService, ExecutionContext } from './UnifiedExecutionService';

export type ExecutionState = 'idle' | 'awaitingApproval' | 'executing' | 'paused' | 'error';

// Note: ExecutionContext moved to UnifiedExecutionService for consolidation

export interface ExecutionEvent {
    type: 'stateChanged' | 'stepCompleted' | 'stepFailed' | 'planCompleted' | 'planFailed';
    state: ExecutionState;
    context: ExecutionContext;
    step?: ExecutionStep;
    error?: string;
}

// Valid state transitions matrix for hardened state machine
const VALID_TRANSITIONS: Record<ExecutionState, ExecutionState[]> = {
    'idle': ['awaitingApproval'],
    'awaitingApproval': ['executing', 'paused', 'idle'],
    'executing': ['paused', 'idle', 'error'],
    'paused': ['executing', 'idle'],
    'error': ['idle']
};

/**
 * Central state manager for plan-based execution workflow with hardened controls
 */
export class ExecutionStateManager {
    private _state: ExecutionState = 'idle';
    private _context: ExecutionContext = {
        executedSteps: [],
        failedSteps: [],
        runningProcesses: new Map()
    };
    private _eventListeners: ((event: ExecutionEvent) => void)[] = [];
    private _planExecutor: PlanExecutorService;
    private _actionHistory: ActionHistoryManager;
    private _outputChannel: vscode.OutputChannel;
    private _isTransitioning: boolean = false; // Prevent concurrent state changes
    private _llmService: ILLMService;
    private _apiProviderManager: ApiProviderManager;
    private _extensionContext: vscode.ExtensionContext;
    private _unifiedExecutionService: UnifiedExecutionService;

    constructor(
        planExecutor: PlanExecutorService,
        actionHistory: ActionHistoryManager,
        llmService: ILLMService,
        apiProviderManager: ApiProviderManager,
        context: vscode.ExtensionContext,
        unifiedExecutionService: UnifiedExecutionService
    ) {
        this._planExecutor = planExecutor;
        this._actionHistory = actionHistory;
        this._llmService = llmService;
        this._apiProviderManager = apiProviderManager;
        this._extensionContext = context;
        this._unifiedExecutionService = unifiedExecutionService;
        this._outputChannel = vscode.window.createOutputChannel('V1b3-Sama Execution');
    }

    /**
     * Get current execution state
     */
    public get state(): ExecutionState {
        return this._state;
    }

    /**
     * Get current execution context
     */
    public get context(): ExecutionContext {
        return { ...this._context };
    }

    /**
     * Check if state transition is valid
     */
    private _isValidTransition(fromState: ExecutionState, toState: ExecutionState): boolean {
        return VALID_TRANSITIONS[fromState]?.includes(toState) || false;
    }

    /**
     * Validate and perform state transition
     */
    private _validateAndTransition(toState: ExecutionState): void {
        if (this._isTransitioning) {
            throw new Error('State transition in progress, please wait');
        }

        if (!this._isValidTransition(this._state, toState)) {
            throw new Error(`Invalid transition from ${this._state} to ${toState}`);
        }

        this._isTransitioning = true;
        try {
            this._setState(toState);
        } finally {
            this._isTransitioning = false;
        }
    }

    /**
     * Set execution plan and transition to awaiting approval
     */
    public setPlan(plan: ExecutionPlan): void {
        if (this._state !== 'idle') {
            throw new Error(`Cannot set plan in state: ${this._state}. Must be in 'idle' state.`);
        }

        this._context = {
            currentPlan: plan,
            currentStep: 0,
            executedSteps: [],
            failedSteps: [],
            startedAt: undefined,
            completedAt: undefined,
            error: undefined,
            runningProcesses: new Map()
        };

        this._validateAndTransition('awaitingApproval');
        this._actionHistory.logAction(`Plan set: ${plan.description}`);
        this._outputChannel.appendLine(`📋 Execution plan ready: ${plan.description}`);
        this._outputChannel.appendLine(`📊 Steps: ${plan.steps.length}`);
    }

    /**
     * Approve and execute the current plan (Play button)
     * Transitions from awaitingApproval or paused to executing
     */
    public async executePlan(): Promise<void> {
        if (this._state !== 'awaitingApproval' && this._state !== 'paused') {
            throw new Error(`Cannot execute plan in state: ${this._state}. Must be in 'awaitingApproval' or 'paused' state.`);
        }

        if (!this._context.currentPlan) {
            throw new Error('No plan available for execution');
        }

        // Validate transition before starting execution
        this._validateAndTransition('executing');

        if (!this._context.startedAt) {
            this._context.startedAt = new Date();
        }

        this._actionHistory.logAction(`Plan execution started: ${this._context.currentPlan.description}`);
        this._outputChannel.appendLine(`▶️ Starting plan execution...`);

        try {
            // Use unified execution service for plan execution
            await this._unifiedExecutionService.executePlan(this._context.currentPlan);

            this._context.completedAt = new Date();
            this._validateAndTransition('idle');
            this._actionHistory.logAction(`Plan execution completed successfully`);
            this._outputChannel.appendLine(`✅ Plan execution completed successfully`);

            // Clear the plan after successful completion
            this._clearExecutionContext();

            this._emitEvent({
                type: 'planCompleted',
                state: this._state,
                context: this._context
            });
        } catch (error) {
            this._context.error = error instanceof Error ? error.message : String(error);
            this._validateAndTransition('error');
            this._actionHistory.logAction(`Plan execution failed: ${this._context.error}`);
            this._outputChannel.appendLine(`❌ Plan execution failed: ${this._context.error}`);

            this._emitEvent({
                type: 'planFailed',
                state: this._state,
                context: this._context,
                error: this._context.error
            });
        }
    }

    /**
     * Pause execution at current step (Pause button)
     * Transitions from awaitingApproval to paused (preserves plan)
     * Note: Cannot pause during actual execution - this pauses before execution starts
     */
    public pausePlan(): void {
        if (this._state !== 'awaitingApproval') {
            throw new Error(`Cannot pause plan in state: ${this._state}. Must be in 'awaitingApproval' state.`);
        }

        this._validateAndTransition('paused');
        this._context.pausedAt = new Date();
        this._actionHistory.logAction(`Plan execution paused (plan preserved for modification)`);
        this._outputChannel.appendLine(`⏸️ Plan execution paused - plan preserved for modification`);
    }

    /**
     * Stop execution and reset state (Stop button)
     * Transitions from any state to idle and clears execution plan
     * Terminates any running processes
     */
    public stopPlan(silent: boolean = false): void {
        if (this._state === 'idle') {
            return; // Already in idle state
        }

        // Terminate any running processes
        this._terminateRunningProcesses();

        // Note: Silent flag removed as it's not part of ExecutionContext interface

        // Force transition to idle (valid from any state)
        this._isTransitioning = true;
        try {
            this._setState('idle');
        } finally {
            this._isTransitioning = false;
        }

        // Clear execution context completely
        this._clearExecutionContext();

        if (!silent) {
            this._actionHistory.logAction(`Plan execution stopped and reset`);
            this._outputChannel.appendLine(`⏹️ Plan execution stopped and reset`);
        }
    }

    /**
     * Update current plan (for pause/modify workflow)
     * Only allowed in paused state to preserve execution context
     */
    public updatePlan(plan: ExecutionPlan): void {
        if (this._state !== 'paused') {
            throw new Error(`Cannot update plan in state: ${this._state}. Must be in 'paused' state.`);
        }

        this._context.currentPlan = plan;
        // Reset step counter for updated plan
        this._context.currentStep = 0;
        this._context.executedSteps = [];
        this._context.failedSteps = [];

        this._actionHistory.logAction(`Plan updated: ${plan.description}`);
        this._outputChannel.appendLine(`📝 Plan updated: ${plan.description}`);
    }

    /**
     * Clear execution context (used by stop and completion)
     */
    private _clearExecutionContext(): void {
        this._context = {
            executedSteps: [],
            failedSteps: [],
            runningProcesses: new Map()
        };
    }

    /**
     * Terminate all running processes
     */
    private _terminateRunningProcesses(): void {
        for (const [processId, process] of this._context.runningProcesses) {
            try {
                if (process && typeof process.kill === 'function') {
                    process.kill('SIGTERM');
                    this._outputChannel.appendLine(`🔪 Terminated process: ${processId}`);
                }
            } catch (error) {
                this._outputChannel.appendLine(`⚠️ Failed to terminate process ${processId}: ${error}`);
            }
        }
        this._context.runningProcesses.clear();
    }

    /**
     * Add event listener for state changes
     */
    public addEventListener(listener: (event: ExecutionEvent) => void): void {
        this._eventListeners.push(listener);
    }

    /**
     * Remove event listener
     */
    public removeEventListener(listener: (event: ExecutionEvent) => void): void {
        const index = this._eventListeners.indexOf(listener);
        if (index >= 0) {
            this._eventListeners.splice(index, 1);
        }
    }

    // Note: _executeSteps() method moved to UnifiedExecutionService for consolidation

    // Note: _executeStep() method moved to UnifiedExecutionService for consolidation

    /**
     * Generate file content using LLM for a given step
     */
    private async _generateFileContent(step: ExecutionStep): Promise<string> {
        try {
            // Get current provider and model from ApiProviderManager
            const currentProvider = this._apiProviderManager.getCurrentProvider();
            const currentModel = this._apiProviderManager.getCurrentModel();

            // Retrieve API key from secure storage
            const apiKey = await this._apiProviderManager.getSecureApiKey(currentProvider);

            if (!apiKey) {
                throw new Error(`API key not found for provider: ${currentProvider}. Please configure it in settings.`);
            }

            // Build prompt for content generation
            const prompt = this._buildContentGenerationPrompt(step);

            // Make real LLM API call
            const response = await this._llmService.sendMessage([{
                role: 'user',
                content: prompt
            }], currentProvider, currentModel, apiKey);

            this._outputChannel.appendLine(`✅ Generated content for ${step.path} using ${currentProvider}/${currentModel}`);
            return response.content;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ LLM generation failed for ${step.path}: ${errorMessage}`);

            // Provide user-friendly error feedback
            if (errorMessage.includes('API key not found')) {
                throw new Error(`Please configure your ${this._apiProviderManager.getCurrentProvider()} API key in the extension settings.`);
            } else if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
                throw new Error(`API rate limit exceeded. Please try again later or check your API quota.`);
            } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
                throw new Error(`Network error occurred. Please check your internet connection and try again.`);
            } else {
                throw new Error(`Failed to generate content for ${step.path}: ${errorMessage}`);
            }
        }
    }



    /**
     * Build prompt for content generation based on step context
     */
    private _buildContentGenerationPrompt(step: ExecutionStep): string {
        const fileExtension = step.path?.split('.').pop()?.toLowerCase() || '';
        const language = this._inferLanguageFromExtension(fileExtension);

        let prompt = `Generate complete, working ${language} code for the file "${step.path}".

Task: ${step.description}

Requirements:
- Provide complete, functional code
- Follow best practices for ${language}
- Include proper error handling where appropriate
- Add meaningful comments
- Ensure the code is production-ready

IMPORTANT: Return ONLY the code content, no explanations or markdown formatting.`;

        // Add context from the execution plan if available
        if (this._context.currentPlan) {
            prompt += `\n\nProject Context: ${this._context.currentPlan.description}`;

            // Add context from other steps
            const otherSteps = this._context.currentPlan.steps
                .filter(s => s.id !== step.id && s.path)
                .map(s => `- ${s.path}: ${s.description}`)
                .join('\n');

            if (otherSteps) {
                prompt += `\n\nRelated files in this project:\n${otherSteps}`;
            }
        }

        return prompt;
    }

    /**
     * Infer programming language from file extension
     */
    private _inferLanguageFromExtension(extension: string): string {
        const languageMap: { [key: string]: string } = {
            'js': 'JavaScript',
            'jsx': 'React JSX',
            'ts': 'TypeScript',
            'tsx': 'React TypeScript',
            'py': 'Python',
            'java': 'Java',
            'cpp': 'C++',
            'c': 'C',
            'h': 'C/C++ Header',
            'cs': 'C#',
            'php': 'PHP',
            'rb': 'Ruby',
            'go': 'Go',
            'rs': 'Rust',
            'html': 'HTML',
            'css': 'CSS',
            'scss': 'SCSS',
            'json': 'JSON',
            'yaml': 'YAML',
            'yml': 'YAML',
            'xml': 'XML',
            'md': 'Markdown',
            'sh': 'Shell Script',
            'bat': 'Batch Script',
            'ps1': 'PowerShell'
        };

        return languageMap[extension] || 'text';
    }

    /**
     * Set state and emit event
     */
    private _setState(newState: ExecutionState): void {
        const oldState = this._state;
        this._state = newState;
        
        this._emitEvent({
            type: 'stateChanged',
            state: newState,
            context: this._context
        });
    }

    /**
     * Emit event to all listeners
     */
    private _emitEvent(event: ExecutionEvent): void {
        for (const listener of this._eventListeners) {
            try {
                listener(event);
            } catch (error) {
                console.error('Error in execution event listener:', error);
            }
        }
    }

    /**
     * Dispose resources
     */
    public dispose(): void {
        this._eventListeners = [];
        this._outputChannel.dispose();
    }
}
