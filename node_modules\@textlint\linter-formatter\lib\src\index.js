// LICENSE : MIT
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFormatterList = exports.createFormatter = exports.loadFormatter = void 0;
const module_interop_1 = require("@textlint/module-interop");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const resolver_1 = require("@textlint/resolver");
const debug_1 = __importDefault(require("debug"));
// formatter
const checkstyle_1 = __importDefault(require("./formatters/checkstyle"));
const compact_1 = __importDefault(require("./formatters/compact"));
const jslint_xml_1 = __importDefault(require("./formatters/jslint-xml"));
const json_1 = __importDefault(require("./formatters/json"));
const junit_1 = __importDefault(require("./formatters/junit"));
const pretty_error_1 = __importDefault(require("./formatters/pretty-error"));
const stylish_1 = __importDefault(require("./formatters/stylish"));
const table_1 = __importDefault(require("./formatters/table"));
const tap_1 = __importDefault(require("./formatters/tap"));
const unix_1 = __importDefault(require("./formatters/unix"));
const builtinFormatterList = {
    checkstyle: checkstyle_1.default,
    compact: compact_1.default,
    "jslint-xml": jslint_xml_1.default,
    json: json_1.default,
    junit: junit_1.default,
    "pretty-error": pretty_error_1.default,
    stylish: stylish_1.default,
    table: table_1.default,
    tap: tap_1.default,
    unix: unix_1.default
};
const builtinFormatterNames = Object.keys(builtinFormatterList);
const debug = (0, debug_1.default)("textlint:@textlint/linter-formatter");
const isFormatterFunction = (formatter) => {
    return typeof formatter === "function";
};
async function loadFormatter(formatterConfig) {
    var _a;
    const formatterName = formatterConfig.formatterName;
    debug(`formatterName: ${formatterName}`);
    if (builtinFormatterNames.includes(formatterName)) {
        return {
            format(results) {
                return builtinFormatterList[formatterName](results, formatterConfig);
            }
        };
    }
    let formatter;
    let formatterPath;
    if (fs_1.default.existsSync(formatterName)) {
        formatterPath = formatterName;
    }
    else if (fs_1.default.existsSync(path_1.default.resolve(process.cwd(), formatterName))) {
        formatterPath = path_1.default.resolve(process.cwd(), formatterName);
    }
    else {
        const pkgPath = (0, resolver_1.tryResolve)(`textlint-formatter-${formatterName}`, {
            parentModule: "linter-formatter"
        }) ||
            (0, resolver_1.tryResolve)(formatterName, {
                parentModule: "linter-formatter"
            });
        if (pkgPath) {
            formatterPath = pkgPath;
        }
    }
    if (!formatterPath) {
        throw new Error(`Could not find formatter ${formatterName}`);
    }
    try {
        const mod = (_a = (0, module_interop_1.moduleInterop)((await (0, resolver_1.dynamicImport)(formatterPath, {
            parentModule: "linter-formatter"
        })).exports)) === null || _a === void 0 ? void 0 : _a.default;
        if (!isFormatterFunction(mod)) {
            throw new Error(`formatter should export function, but ${formatterPath} exports ${typeof mod}`);
        }
        formatter = mod;
    }
    catch (ex) {
        throw new Error(`Could not find formatter ${formatterName}
${ex}`);
    }
    return {
        format(results) {
            return formatter(results, formatterConfig);
        }
    };
}
exports.loadFormatter = loadFormatter;
/**
 * @deprecated use loadFormatter
 * @param formatterConfig
 */
function createFormatter(formatterConfig) {
    const formatterName = formatterConfig.formatterName;
    debug(`formatterName: ${formatterName}`);
    if (builtinFormatterNames.includes(formatterName)) {
        return function (results) {
            return builtinFormatterList[formatterName](results, formatterConfig);
        };
    }
    let formatter;
    let formatterPath;
    if (fs_1.default.existsSync(formatterName)) {
        formatterPath = formatterName;
    }
    else if (fs_1.default.existsSync(path_1.default.resolve(process.cwd(), formatterName))) {
        formatterPath = path_1.default.resolve(process.cwd(), formatterName);
    }
    else {
        const pkgPath = (0, resolver_1.tryResolve)(`textlint-formatter-${formatterName}`, {
            parentModule: "linter-formatter"
        }) ||
            (0, resolver_1.tryResolve)(formatterName, {
                parentModule: "linter-formatter"
            });
        if (pkgPath) {
            formatterPath = pkgPath;
        }
    }
    if (!formatterPath) {
        throw new Error(`Could not find formatter ${formatterName}`);
    }
    try {
        formatter = (0, module_interop_1.moduleInterop)(require(formatterPath));
    }
    catch (ex) {
        throw new Error(`Could not find formatter ${formatterName}
${ex}`);
    }
    return function (results) {
        return formatter(results, formatterConfig);
    };
}
exports.createFormatter = createFormatter;
function getFormatterList() {
    return builtinFormatterNames.map((name) => {
        return {
            name
        };
    });
}
exports.getFormatterList = getFormatterList;
//# sourceMappingURL=index.js.map