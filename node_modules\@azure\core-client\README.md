# Azure Core Service client library for JavaScript

This library is primarily intended to be used in code generated by [AutoRest](https://github.com/Azure/Autorest) and [`autorest.typescript`](https://github.com/Azure/autorest.typescript).

## Getting started

### Currently supported environments

- [LTS versions of Node.js](https://github.com/nodejs/release#release-schedule)
- Latest versions of Safari, Chrome, Edge, and Firefox.

See our [support policy](https://github.com/Azure/azure-sdk-for-js/blob/main/SUPPORT.md) for more details.

### Installation

This package is primarily used in generated code and not meant to be consumed directly by end users.

## Key concepts

### ServiceClient

This is the common base class for generated clients. It provides the methods `sendOperationRequest` and `sendRequest`.

`sendRequest` simply makes an HTTP request using a `Pipeline` (see `@azure/core-rest-pipeline` for details.)

`sendOperationRequest` is used by generated code to make a request using an `OperationSpec` and `OperationArguments`. An `OperationSpec` is a translation of request requirements from the OpenAPI/Swagger file that describes the service.

### createClientPipeline

This method is used to create a `Pipeline` instance that is customized with extra policies that perform serialization and deserialization. The bulk of the work is in converting requests into the right HTTP message that a service expects and then converting the HTTP response payload into the response shape described by the service's OpenAPI specification file.

### Mappers / createSerializer

`Mapper`s are used to encode and decode data into HTTP headers and bodies. They describe all request and response fields. They are referenced by `OperationSpec`s.

The method `createSerializer` creates a `Serializer` that is used to do the bulk of the work transforming data when making a request or processing a response. Given a corpus of `Mapper`s and the appropriate `OperationSpec` it can manufacture an HTTP request object from a user provided input or convert an HTTP response object into a user-friendly form.

## Next steps

You can build and run the tests locally by executing `rushx test`. Explore the [test](https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/core/core-client/test) folder to see advanced usage and behavior of the public classes.

Learn more about [AutoRest](https://github.com/Azure/autorest) and the [autorest.typescript extension](https://github.com/Azure/autorest.typescript) for generating a compatible client on top of this package.

## Troubleshooting

If you run into issues while using this library, please feel free to [file an issue](https://github.com/Azure/azure-sdk-for-js/issues/new).

## Contributing

If you'd like to contribute to this library, please read the [contributing guide](https://github.com/Azure/azure-sdk-for-js/blob/main/CONTRIBUTING.md) to learn more about how to build and test the code.
