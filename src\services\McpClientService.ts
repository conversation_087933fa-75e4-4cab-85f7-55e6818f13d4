// MCP Client Service Implementation
// Handles Model Context Protocol communication with external servers

import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import * as path from 'path';
import {
  IMcpClientService,
  McpConnectionConfig,
  McpContextData,
  McpToolCall,
  McpToolResult,
  McpResourceContent
} from '../interfaces/IMcpClientService';
import {
  McpServer,
  McpServers,
  McpTool,
  McpResource,
  McpResourceTemplate,
  McpServerStatus,
  ToggleMcpServerRequest,
  UpdateMcpTimeoutRequest,
  AddRemoteMcpServerRequest
} from '../shared/proto/mcp';
import { StringRequest } from '../shared/proto/common';
import { ISecurityService } from '../interfaces/ISecurityService';

export class McpClientService extends EventEmitter implements IMcpClientService {
  private servers: Map<string, McpServer> = new Map();
  private connections: Map<string, any> = new Map(); // gRPC connections
  private statusCallbacks: Set<(serverName: string, status: McpServerStatus) => void> = new Set();
  private toolsCallbacks: Set<(serverName: string, tools: McpTool[]) => void> = new Set();
  private securityService?: ISecurityService;
  private packageDefinition?: any;

  constructor(securityService?: ISecurityService) {
    super();
    this.securityService = securityService;
    this.loadProtoDefinition();
  }

  private loadProtoDefinition(): void {
    try {
      // Try multiple possible paths for proto files
      const possiblePaths = [
        path.join(__dirname, '../proto/mcp.proto'),     // When running from dist/
        path.join(__dirname, '../../proto/mcp.proto'),  // When running from out/
        path.join(__dirname, 'proto/mcp.proto'),        // Alternative path
      ];

      const possibleIncludeDirs = [
        path.join(__dirname, '../proto'),
        path.join(__dirname, '../../proto'),
        path.join(__dirname, 'proto'),
      ];

      let protoPath: string | undefined;
      let includeDir: string | undefined;

      // Find the correct proto file path
      for (const testPath of possiblePaths) {
        try {
          if (require('fs').existsSync(testPath)) {
            protoPath = testPath;
            break;
          }
        } catch (e) {
          // Continue to next path
        }
      }

      // Find the correct include directory
      for (const testDir of possibleIncludeDirs) {
        try {
          if (require('fs').existsSync(testDir)) {
            includeDir = testDir;
            break;
          }
        } catch (e) {
          // Continue to next directory
        }
      }

      if (!protoPath) {
        throw new Error('Could not find mcp.proto file in any expected location');
      }

      if (!includeDir) {
        throw new Error('Could not find proto include directory');
      }

      this.packageDefinition = protoLoader.loadSync(
        protoPath,
        {
          keepCase: true,
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
          includeDirs: [includeDir]
        }
      );
    } catch (error) {
      console.error('Failed to load proto definition:', error);
      throw new Error(`Proto loading failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async connect(config: McpConnectionConfig): Promise<void> {
    try {
      console.log(`Connecting to MCP server: ${config.serverName} at ${config.serverUrl}`);

      this.updateServerStatus(config.serverName, McpServerStatus.CONNECTING);

      // Load gRPC service definition
      const mcpProto = grpc.loadPackageDefinition(this.packageDefinition).cline as any;

      // Create secure connection for production
      const credentials = this.createCredentials(config.serverUrl);

      // Create gRPC client
      const grpcClient = new mcpProto.McpService(config.serverUrl, credentials, {
        'grpc.keepalive_time_ms': 30000,
        'grpc.keepalive_timeout_ms': 5000,
        'grpc.keepalive_permit_without_calls': true,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.http2.min_time_between_pings_ms': 10000,
        'grpc.http2.min_ping_interval_without_data_ms': 300000
      });

      // Test connection with ping
      await this.pingServer(grpcClient, config.timeout || 10000);

      // Store the connection
      this.connections.set(config.serverName, grpcClient);

      this.updateServerStatus(config.serverName, McpServerStatus.CONNECTED);
      await this.performToolDiscovery(config.serverName);

      console.log(`Successfully connected to MCP server: ${config.serverName}`);

    } catch (error) {
      console.error(`Failed to connect to MCP server ${config.serverName}:`, error);
      this.updateServerStatus(config.serverName, McpServerStatus.DISCONNECTED, error instanceof Error ? error.message : 'Connection failed');
      throw new Error(`MCP connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async disconnect(serverName: string): Promise<void> {
    try {
      console.log(`Disconnecting from MCP server: ${serverName}`);

      const connection = this.connections.get(serverName);
      if (connection) {
        // Close gRPC connection
        connection.close();
        this.connections.delete(serverName);
      }

      this.updateServerStatus(serverName, McpServerStatus.DISCONNECTED);
      console.log(`Successfully disconnected from MCP server: ${serverName}`);
    } catch (error) {
      console.error(`Failed to disconnect from MCP server ${serverName}:`, error);
      throw error;
    }
  }

  async getServers(): Promise<McpServers> {
    return {
      mcpServers: Array.from(this.servers.values())
    };
  }

  async toggleServer(serverName: string, disabled: boolean): Promise<McpServers> {
    const server = this.servers.get(serverName);
    if (server) {
      server.disabled = disabled;
      if (disabled) {
        await this.disconnect(serverName);
      }
    }
    return this.getServers();
  }

  async updateTimeout(serverName: string, timeout: number): Promise<McpServers> {
    const server = this.servers.get(serverName);
    if (server) {
      server.timeout = timeout;
    }
    return this.getServers();
  }

  async addRemoteServer(serverName: string, serverUrl: string): Promise<McpServers> {
    const newServer: McpServer = {
      name: serverName,
      command: '', // Remote servers don't have local commands
      args: [], // Remote servers don't have local args
      config: JSON.stringify({ url: serverUrl }),
      status: McpServerStatus.DISCONNECTED,
      tools: [],
      resources: [],
      resourceTemplates: [],
      disabled: false,
      timeout: 30000
    };

    this.servers.set(serverName, newServer);
    return this.getServers();
  }

  async downloadServer(serverIdentifier: string): Promise<void> {
    // TODO: Implement server download logic
    console.log(`Downloading MCP server: ${serverIdentifier}`);
    // This would typically involve downloading and installing an MCP server package
    throw new Error('Server download not yet implemented');
  }

  async discoverTools(serverName?: string): Promise<McpTool[]> {
    const tools: McpTool[] = [];
    
    if (serverName) {
      const server = this.servers.get(serverName);
      if (server && server.tools) {
        tools.push(...server.tools);
      }
    } else {
      Array.from(this.servers.values()).forEach(server => {
        if (server.status === McpServerStatus.CONNECTED && server.tools) {
          tools.push(...server.tools);
        }
      });
    }

    return tools;
  }

  async callTool(toolCall: McpToolCall): Promise<McpToolResult> {
    try {
      console.log(`Calling MCP tool: ${toolCall.toolName}`, toolCall.arguments);

      // Security validation
      if (this.securityService) {
        const validation = await this.securityService.validateMcpToolCall(toolCall);
        if (!validation.allowed) {
          await this.securityService.logSecurityEvent({
            type: 'tool_call_blocked',
            toolName: toolCall.toolName,
            reason: validation.reason,
            arguments: toolCall.arguments
          });

          return {
            success: false,
            error: `Security policy violation: ${validation.reason}`
          };
        }

        // Log successful validation
        await this.securityService.logSecurityEvent({
          type: 'tool_call_allowed',
          toolName: toolCall.toolName,
          arguments: toolCall.arguments
        });
      }

      // Find which server has this tool
      const serverWithTool = this.findServerWithTool(toolCall.toolName);
      if (!serverWithTool) {
        throw new Error(`Tool ${toolCall.toolName} not found on any connected server`);
      }

      // Execute tool via gRPC
      const result = await this.executeTool(toolCall);

      return {
        success: true,
        result,
        metadata: {
          serverName: serverWithTool.name,
          executionTime: Date.now()
        }
      };
    } catch (error) {
      console.error(`Failed to call tool ${toolCall.toolName}:`, error);

      // Log security event for failed tool calls
      if (this.securityService) {
        await this.securityService.logSecurityEvent({
          type: 'tool_call_failed',
          toolName: toolCall.toolName,
          error: error instanceof Error ? error.message : 'Unknown error',
          arguments: toolCall.arguments
        });
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tool call failed'
      };
    }
  }

  async getResources(serverName?: string): Promise<McpResource[]> {
    const resources: McpResource[] = [];
    
    if (serverName) {
      const server = this.servers.get(serverName);
      if (server && server.resources) {
        resources.push(...server.resources);
      }
    } else {
      Array.from(this.servers.values()).forEach(server => {
        if (server.status === McpServerStatus.CONNECTED && server.resources) {
          resources.push(...server.resources);
        }
      });
    }

    return resources;
  }

  async getResourceTemplates(serverName?: string): Promise<McpResourceTemplate[]> {
    const templates: McpResourceTemplate[] = [];
    
    if (serverName) {
      const server = this.servers.get(serverName);
      if (server && server.resourceTemplates) {
        templates.push(...server.resourceTemplates);
      }
    } else {
      Array.from(this.servers.values()).forEach(server => {
        if (server.status === McpServerStatus.CONNECTED && server.resourceTemplates) {
          templates.push(...server.resourceTemplates);
        }
      });
    }

    return templates;
  }

  async readResource(uri: string, serverName?: string): Promise<McpResourceContent> {
    try {
      console.log(`Reading MCP resource: ${uri}`);
      
      // Find which server has this resource
      const serverWithResource = serverName ? 
        this.servers.get(serverName) : 
        this.findServerWithResource(uri);
        
      if (!serverWithResource) {
        throw new Error(`Resource ${uri} not found on any connected server`);
      }

      // Read resource via gRPC
      const content = await this.readResourceViaGrpc(uri, serverWithResource.name);

      return {
        uri,
        content,
        mimeType: this.inferMimeType(uri),
        metadata: {
          serverName: serverWithResource.name,
          readTime: Date.now()
        }
      };
    } catch (error) {
      console.error(`Failed to read resource ${uri}:`, error);
      throw error;
    }
  }

  async sendContext(context: McpContextData, serverNames?: string[]): Promise<void> {
    const targetServers = serverNames || Array.from(this.servers.keys());

    for (const serverName of targetServers) {
      const server = this.servers.get(serverName);
      if (server && server.status === McpServerStatus.CONNECTED) {
        try {
          await this.sendContextViaGrpc(context, serverName);
          console.log(`Successfully sent context to MCP server ${serverName}`);
        } catch (error) {
          console.error(`Failed to send context to server ${serverName}:`, error);
        }
      }
    }
  }

  private async sendContextViaGrpc(context: McpContextData, serverName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const grpcClient = this.connections.get(serverName);
      if (!grpcClient) {
        reject(new Error('No active gRPC connection'));
        return;
      }

      const request = {
        metadata: {
          timestamp: Date.now(),
          requestId: this.generateRequestId()
        },
        context: JSON.stringify(context),
        serverName: serverName
      };

      grpcClient.sendContext(request, (error: any, response: any) => {
        if (error) {
          console.error(`Context sending failed for ${serverName}:`, error);
          reject(this.normalizeGrpcError(error));
          return;
        }

        resolve();
      });
    });
  }

  isConnected(serverName: string): boolean {
    const server = this.servers.get(serverName);
    return server?.status === McpServerStatus.CONNECTED;
  }

  getServerStatus(serverName: string): McpServerStatus | undefined {
    return this.servers.get(serverName)?.status;
  }

  onServerStatusChange(callback: (serverName: string, status: McpServerStatus) => void): () => void {
    this.statusCallbacks.add(callback);
    return () => this.statusCallbacks.delete(callback);
  }

  onToolsDiscovered(callback: (serverName: string, tools: McpTool[]) => void): () => void {
    this.toolsCallbacks.add(callback);
    return () => this.toolsCallbacks.delete(callback);
  }

  dispose(): void {
    // Disconnect all servers
    Array.from(this.servers.keys()).forEach(serverName => {
      this.disconnect(serverName).catch(console.error);
    });
    
    // Clear all callbacks
    this.statusCallbacks.clear();
    this.toolsCallbacks.clear();
    
    // Clear all data
    this.servers.clear();
    this.connections.clear();
  }

  // Private gRPC helper methods
  private createCredentials(serverUrl: string): grpc.ChannelCredentials {
    if (serverUrl.startsWith('https://') || serverUrl.includes(':443')) {
      // Use SSL for secure connections
      return grpc.credentials.createSsl();
    } else {
      // Use insecure for local development
      return grpc.credentials.createInsecure();
    }
  }

  private async pingServer(grpcClient: any, timeout: number = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!grpcClient) {
        reject(new Error('No gRPC client available'));
        return;
      }

      const deadline = new Date();
      deadline.setMilliseconds(deadline.getMilliseconds() + timeout);

      // Use a simple call to test connectivity
      grpcClient.toggleMcpServer({
        metadata: { timestamp: Date.now() },
        serverName: 'ping-test',
        disabled: false
      }, { deadline }, (error: any, response: any) => {
        if (error && error.code !== grpc.status.NOT_FOUND) {
          // NOT_FOUND is acceptable for ping test
          reject(this.normalizeGrpcError(error));
        } else {
          resolve();
        }
      });
    });
  }

  private async executeTool(toolCall: McpToolCall): Promise<any> {
    return new Promise((resolve, reject) => {
      const serverWithTool = this.findServerWithTool(toolCall.toolName);
      if (!serverWithTool) {
        reject(new Error(`Tool ${toolCall.toolName} not found on any connected server`));
        return;
      }

      const grpcClient = this.connections.get(serverWithTool.name);
      if (!grpcClient) {
        reject(new Error('No active gRPC connection'));
        return;
      }

      const request = {
        metadata: {
          timestamp: Date.now(),
          requestId: this.generateRequestId()
        },
        toolName: toolCall.toolName,
        arguments: JSON.stringify(toolCall.arguments),
        serverName: serverWithTool.name
      };

      // Set deadline for tool execution
      const deadline = new Date();
      deadline.setSeconds(deadline.getSeconds() + 30); // 30 second timeout

      grpcClient.executeTool(request, { deadline }, (error: any, response: any) => {
        if (error) {
          console.error(`Tool execution failed for ${toolCall.toolName}:`, error);
          reject(this.normalizeGrpcError(error));
          return;
        }

        try {
          const result = {
            success: true,
            result: JSON.parse(response.result || '{}'),
            metadata: {
              serverName: response.serverName || serverWithTool.name,
              executionTime: response.executionTime || Date.now(),
              requestId: response.requestId
            }
          };

          console.log(`Tool ${toolCall.toolName} executed successfully`);
          resolve(result);

        } catch (parseError) {
          reject(new Error(`Failed to parse tool result: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`));
        }
      });
    });
  }

  private async readResourceViaGrpc(uri: string, serverName: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const grpcClient = this.connections.get(serverName);
      if (!grpcClient) {
        reject(new Error('No active gRPC connection'));
        return;
      }

      const request = {
        metadata: {
          timestamp: Date.now(),
          requestId: this.generateRequestId()
        },
        uri: uri,
        serverName: serverName
      };

      grpcClient.readResource(request, (error: any, response: any) => {
        if (error) {
          console.error(`Resource read failed for ${uri}:`, error);
          reject(this.normalizeGrpcError(error));
          return;
        }

        resolve(response.content || '');
      });
    });
  }

  private normalizeGrpcError(error: any): Error {
    // Normalize gRPC errors to user-friendly messages
    switch (error.code) {
      case grpc.status.UNAVAILABLE:
        return new Error('MCP server is unavailable. Please check connection and server status.');
      case grpc.status.UNAUTHENTICATED:
        return new Error('Authentication failed. Please check credentials and permissions.');
      case grpc.status.DEADLINE_EXCEEDED:
        return new Error('Request timeout. Server may be overloaded or unresponsive.');
      case grpc.status.NOT_FOUND:
        return new Error('Requested resource or method not found on MCP server.');
      case grpc.status.PERMISSION_DENIED:
        return new Error('Permission denied. Check server configuration and access rights.');
      case grpc.status.RESOURCE_EXHAUSTED:
        return new Error('Server resources exhausted. Please try again later.');
      case grpc.status.FAILED_PRECONDITION:
        return new Error('Server precondition failed. Check request parameters.');
      case grpc.status.INTERNAL:
        return new Error('Internal server error. Please contact server administrator.');
      default:
        return new Error(`MCP error (${error.code}): ${error.message || 'Unknown error'}`);
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Private helper methods
  private updateServerStatus(serverName: string, status: McpServerStatus, error?: string): void {
    let server = this.servers.get(serverName);
    if (!server) {
      server = {
        name: serverName,
        command: '', // Default empty command for dynamically created servers
        args: [], // Default empty args
        config: '{}',
        status,
        tools: [],
        resources: [],
        resourceTemplates: []
      };
      this.servers.set(serverName, server);
    }

    if (server) {
      server.status = status;
      if (error) {
        server.error = error;
      } else {
        delete server.error;
      }
    }

    // Notify callbacks
    Array.from(this.statusCallbacks).forEach(callback => {
      try {
        callback(serverName, status);
      } catch (error) {
        console.error('Error in status callback:', error);
      }
    });
  }



  private async performToolDiscovery(serverName: string): Promise<void> {
    try {
      const grpcClient = this.connections.get(serverName);
      if (!grpcClient) {
        console.warn(`No gRPC client available for tool discovery on server: ${serverName}`);
        return;
      }

      const tools = await this.discoverToolsViaGrpc(serverName);

      const server = this.servers.get(serverName);
      if (server) {
        server.tools = tools;

        // Notify callbacks
        Array.from(this.toolsCallbacks).forEach(callback => {
          try {
            callback(serverName, tools);
          } catch (error) {
            console.error('Error in tools callback:', error);
          }
        });
      }
    } catch (error) {
      console.error(`Tool discovery failed for server ${serverName}:`, error);
      // Fall back to empty tools array
      const server = this.servers.get(serverName);
      if (server) {
        server.tools = [];
      }
    }
  }

  private async discoverToolsViaGrpc(serverName: string): Promise<McpTool[]> {
    return new Promise((resolve, reject) => {
      const grpcClient = this.connections.get(serverName);
      if (!grpcClient) {
        reject(new Error('No active gRPC connection'));
        return;
      }

      const request = {
        metadata: {
          timestamp: Date.now(),
          requestId: this.generateRequestId()
        },
        serverName: serverName
      };

      grpcClient.discoverTools(request, (error: any, response: any) => {
        if (error) {
          console.error(`Tool discovery failed for ${serverName}:`, error);
          reject(this.normalizeGrpcError(error));
          return;
        }

        try {
          const tools: McpTool[] = response.tools || [];
          console.log(`Discovered ${tools.length} tools on server ${serverName}`);
          resolve(tools);
        } catch (parseError) {
          reject(new Error(`Failed to parse tools response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`));
        }
      });
    });
  }

  private findServerWithTool(toolName: string): McpServer | undefined {
    return Array.from(this.servers.values()).find(server =>
      server.status === McpServerStatus.CONNECTED &&
      server.tools && server.tools.some(tool => tool.name === toolName)
    );
  }

  private findServerWithResource(uri: string): McpServer | undefined {
    return Array.from(this.servers.values()).find(server =>
      server.status === McpServerStatus.CONNECTED &&
      server.resources && server.resources.some(resource => resource.uri === uri)
    );
  }



  private inferMimeType(uri: string): string {
    const extension = uri.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'txt': return 'text/plain';
      case 'json': return 'application/json';
      case 'js': case 'ts': return 'text/javascript';
      case 'html': return 'text/html';
      case 'css': return 'text/css';
      default: return 'application/octet-stream';
    }
  }
}
