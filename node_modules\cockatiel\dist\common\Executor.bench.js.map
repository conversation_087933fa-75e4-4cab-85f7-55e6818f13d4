{"version": 3, "file": "Executor.bench.js", "sourceRoot": "", "sources": ["../../src/common/Executor.bench.ts"], "names": [], "mappings": ";;AAAA,mCAAuC;AAIvC,MAAM,QAAQ,GAAG,IAAI,oBAAY,EAAE,CAAC;AACpC,MAAM,QAAQ,GAAG,IAAI,oBAAY,EAAE,CAAC;AACpC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;AACtC,MAAM,QAAQ,GAAG,IAAI,oBAAY,EAAE,CAAC;AACpC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;AACtC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;AACtC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;AAEtC,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/C,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/C,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC", "sourcesContent": ["import { EventEmitter } from './Event';\n\ndeclare function bench(name: string, fn: () => void): void;\n\nconst emitter0 = new EventEmitter();\nconst emitter1 = new EventEmitter();\nemitter1.addListener(() => undefined);\nconst emitter3 = new EventEmitter();\nemitter3.addListener(() => undefined);\nemitter3.addListener(() => undefined);\nemitter3.addListener(() => undefined);\n\nbench('0 listener', () => emitter0.emit(true));\nbench('1 listener', () => emitter1.emit(true));\nbench('3 listener', () => emitter3.emit(true));\n"]}