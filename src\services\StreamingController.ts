// Streaming Controller for managing LLM stream states
// Handles pause/resume, cancellation, and reconnection logic

export interface StreamState {
    streamId: string;
    status: 'active' | 'paused' | 'cancelled' | 'completed' | 'error';
    abortController: AbortController;
    pauseResolver?: () => void;
    retryCount: number;
    maxRetries: number;
    lastChunkTime: number;
    reconnectTimeout?: NodeJS.Timeout;
}

export interface StreamingConfig {
    maxRetries: number;
    retryDelay: number;
    reconnectTimeout: number;
    pauseBufferSize: number;
}

export class StreamingController {
    private streams: Map<string, StreamState> = new Map();
    private pausedChunks: Map<string, string[]> = new Map();
    private config: StreamingConfig;

    constructor(config: Partial<StreamingConfig> = {}) {
        this.config = {
            maxRetries: 3,
            retryDelay: 1000,
            reconnectTimeout: 30000,
            pauseBufferSize: 100,
            ...config
        };
    }

    /**
     * Register a new stream
     */
    public registerStream(streamId: string, abortController: AbortController): void {
        const streamState: StreamState = {
            streamId,
            status: 'active',
            abortController,
            retryCount: 0,
            maxRetries: this.config.maxRetries,
            lastChunkTime: Date.now()
        };

        this.streams.set(streamId, streamState);
        this.pausedChunks.set(streamId, []);
    }

    /**
     * Pause a stream
     */
    public pauseStream(streamId: string): Promise<void> {
        const stream = this.streams.get(streamId);
        if (!stream || stream.status !== 'active') {
            return Promise.resolve();
        }

        stream.status = 'paused';
        
        return new Promise<void>((resolve) => {
            stream.pauseResolver = resolve;
        });
    }

    /**
     * Resume a paused stream
     */
    public resumeStream(streamId: string): void {
        const stream = this.streams.get(streamId);
        if (!stream || stream.status !== 'paused') {
            return;
        }

        stream.status = 'active';
        
        // Resolve the pause promise
        if (stream.pauseResolver) {
            stream.pauseResolver();
            stream.pauseResolver = undefined;
        }

        // Flush any buffered chunks
        this.flushPausedChunks(streamId);
    }

    /**
     * Cancel a stream
     */
    public cancelStream(streamId: string): void {
        const stream = this.streams.get(streamId);
        if (!stream) {
            return;
        }

        stream.status = 'cancelled';
        stream.abortController.abort();

        // Clear any timeouts
        if (stream.reconnectTimeout) {
            clearTimeout(stream.reconnectTimeout);
        }

        // Resolve pause if stream was paused
        if (stream.pauseResolver) {
            stream.pauseResolver();
        }

        // Clean up
        this.cleanupStream(streamId);
    }

    /**
     * Mark stream as completed
     */
    public completeStream(streamId: string): void {
        const stream = this.streams.get(streamId);
        if (!stream) {
            return;
        }

        stream.status = 'completed';
        this.cleanupStream(streamId);
    }

    /**
     * Handle stream error and attempt reconnection if appropriate
     */
    public async handleStreamError(
        streamId: string, 
        error: Error,
        reconnectFn: () => Promise<void>
    ): Promise<boolean> {
        const stream = this.streams.get(streamId);
        if (!stream) {
            return false;
        }

        stream.retryCount++;

        // Check if we should retry
        if (stream.retryCount <= stream.maxRetries && this.isRetryableError(error)) {
            console.log(`Attempting to reconnect stream ${streamId}, retry ${stream.retryCount}/${stream.maxRetries}`);
            
            // Wait before retrying
            await this.delay(this.config.retryDelay * stream.retryCount);
            
            try {
                await reconnectFn();
                stream.retryCount = 0; // Reset on successful reconnection
                return true;
            } catch (reconnectError) {
                console.error(`Reconnection attempt ${stream.retryCount} failed:`, reconnectError);
                return this.handleStreamError(streamId, reconnectError as Error, reconnectFn);
            }
        }

        // Max retries exceeded or non-retryable error
        stream.status = 'error';
        this.cleanupStream(streamId);
        return false;
    }

    /**
     * Check if stream should wait (paused)
     */
    public async checkPauseState(streamId: string): Promise<void> {
        const stream = this.streams.get(streamId);
        if (!stream) {
            return;
        }

        if (stream.status === 'paused' && stream.pauseResolver) {
            // Wait until resumed
            await new Promise<void>((resolve) => {
                stream.pauseResolver = resolve;
            });
        }
    }

    /**
     * Buffer chunk if stream is paused
     */
    public bufferChunk(streamId: string, chunk: string): boolean {
        const stream = this.streams.get(streamId);
        if (!stream || stream.status !== 'paused') {
            return false;
        }

        const buffer = this.pausedChunks.get(streamId);
        if (buffer) {
            buffer.push(chunk);
            
            // Limit buffer size to prevent memory issues
            if (buffer.length > this.config.pauseBufferSize) {
                buffer.shift(); // Remove oldest chunk
            }
        }

        return true;
    }

    /**
     * Get stream status
     */
    public getStreamStatus(streamId: string): string | undefined {
        return this.streams.get(streamId)?.status;
    }

    /**
     * Check if stream is active
     */
    public isStreamActive(streamId: string): boolean {
        const stream = this.streams.get(streamId);
        return stream?.status === 'active' || stream?.status === 'paused';
    }

    /**
     * Update last chunk time (for timeout detection)
     */
    public updateLastChunkTime(streamId: string): void {
        const stream = this.streams.get(streamId);
        if (stream) {
            stream.lastChunkTime = Date.now();
        }
    }

    /**
     * Get all active streams
     */
    public getActiveStreams(): string[] {
        return Array.from(this.streams.entries())
            .filter(([_, stream]) => this.isStreamActive(stream.streamId))
            .map(([streamId]) => streamId);
    }

    private flushPausedChunks(streamId: string): void {
        const buffer = this.pausedChunks.get(streamId);
        if (buffer && buffer.length > 0) {
            // Chunks will be processed by the stream handler
            // This is just to clear the buffer
            buffer.length = 0;
        }
    }

    private cleanupStream(streamId: string): void {
        const stream = this.streams.get(streamId);
        if (stream?.reconnectTimeout) {
            clearTimeout(stream.reconnectTimeout);
        }

        this.streams.delete(streamId);
        this.pausedChunks.delete(streamId);
    }

    private isRetryableError(error: Error): boolean {
        const message = error.message.toLowerCase();
        
        // Network errors that are worth retrying
        const retryablePatterns = [
            'network',
            'timeout',
            'econnreset',
            'enotfound',
            'econnrefused',
            'socket hang up',
            '502', // Bad Gateway
            '503', // Service Unavailable
            '504'  // Gateway Timeout
        ];

        return retryablePatterns.some(pattern => message.includes(pattern));
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Dispose all streams and cleanup
     */
    public dispose(): void {
        for (const streamId of this.streams.keys()) {
            this.cancelStream(streamId);
        }
        
        this.streams.clear();
        this.pausedChunks.clear();
    }
}
