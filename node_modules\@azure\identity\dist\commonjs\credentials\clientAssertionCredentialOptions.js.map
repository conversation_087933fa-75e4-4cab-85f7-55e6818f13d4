{"version": 3, "file": "clientAssertionCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientAssertionCredentialOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link ClientAssertionCredential}\n */\nexport interface ClientAssertionCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {}\n"]}