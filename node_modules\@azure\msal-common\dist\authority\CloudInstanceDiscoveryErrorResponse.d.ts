/**
 * The OpenID Configuration Endpoint Response type. Used by the authority class to get relevant OAuth endpoints.
 */
export type CloudInstanceDiscoveryErrorResponse = {
    error: String;
    error_description: String;
    error_codes?: Array<Number>;
    timestamp?: String;
    trace_id?: String;
    correlation_id?: String;
    error_uri?: String;
};
export declare function isCloudInstanceDiscoveryErrorResponse(response: object): boolean;
//# sourceMappingURL=CloudInstanceDiscoveryErrorResponse.d.ts.map