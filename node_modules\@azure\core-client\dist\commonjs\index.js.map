{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,iDAAoE;AAA3D,iHAAA,gBAAgB,OAAA;AAAE,gHAAA,eAAe,OAAA;AAC1C,uDAAyE;AAAhE,iHAAA,aAAa,OAAA;AACtB,6CAAoF;AAA3E,mHAAA,oBAAoB,OAAA;AAC7B,iDAqCyB;AAPvB,4GAAA,WAAW,OAAA;AACX,4GAAA,WAAW,OAAA;AAOb,uEAKoC;AAJlC,iIAAA,qBAAqB,OAAA;AACrB,qIAAA,yBAAyB,OAAA;AAI3B,mEAIkC;AAHhC,6HAAA,mBAAmB,OAAA;AACnB,iIAAA,uBAAuB,OAAA;AAGzB,6FAAyF;AAAhF,uJAAA,gCAAgC,OAAA;AACzC,+FAA2F;AAAlF,yJAAA,iCAAiC,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { createSerializer, MapperTypeNames } from \"./serializer.js\";\nexport { ServiceClient, ServiceClientOptions } from \"./serviceClient.js\";\nexport { createClientPipeline, InternalClientPipelineOptions } from \"./pipeline.js\";\nexport {\n  OperationSpec,\n  OperationArguments,\n  OperationOptions,\n  OperationResponseMap,\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter,\n  Serializer,\n  BaseMapper,\n  Mapper,\n  MapperType,\n  SimpleMapperType,\n  EnumMapper,\n  EnumMapperType,\n  SequenceMapper,\n  SequenceMapperType,\n  DictionaryMapper,\n  DictionaryMapperType,\n  CompositeMapper,\n  CompositeMapperType,\n  MapperConstraints,\n  OperationRequest,\n  OperationRequestOptions,\n  OperationRequestInfo,\n  QueryCollectionFormat,\n  ParameterPath,\n  FullOperationResponse,\n  PolymorphicDiscriminator,\n  SpanConfig,\n  XML_ATTRKEY,\n  XML_CHARKEY,\n  XmlOptions,\n  SerializerOptions,\n  RawResponseCallback,\n  CommonClientOptions,\n  AdditionalPolicyConfig,\n} from \"./interfaces.js\";\nexport {\n  deserializationPolicy,\n  deserializationPolicyName,\n  DeserializationPolicyOptions,\n  DeserializationContentTypes,\n} from \"./deserializationPolicy.js\";\nexport {\n  serializationPolicy,\n  serializationPolicyName,\n  SerializationPolicyOptions,\n} from \"./serializationPolicy.js\";\nexport { authorizeRequestOnClaimChallenge } from \"./authorizeRequestOnClaimChallenge.js\";\nexport { authorizeRequestOnTenantChallenge } from \"./authorizeRequestOnTenantChallenge.js\";\n"]}