{"version": 3, "file": "defaultRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/defaultRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EACL,sBAAsB,IAAI,yBAAyB,EACnD,kBAAkB,IAAI,qBAAqB,GAC5C,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,yBAAyB,CAAC;AAOhE;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAAqC,EAAE;IACxE,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRetryOptions } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  defaultRetryPolicyName as tspDefaultRetryPolicyName,\n  defaultRetryPolicy as tspDefaultRetryPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nexport const defaultRetryPolicyName = tspDefaultRetryPolicyName;\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface DefaultRetryPolicyOptions extends PipelineRetryOptions {}\n\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nexport function defaultRetryPolicy(options: DefaultRetryPolicyOptions = {}): PipelinePolicy {\n  return tspDefaultRetryPolicy(options);\n}\n"]}