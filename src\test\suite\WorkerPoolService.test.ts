import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import { WorkerPoolService, WorkerTask } from '../../services/WorkerPoolService';

suite('WorkerPoolService Test Suite', () => {
    let workerPoolService: WorkerPoolService;
    let mockContext: vscode.ExtensionContext;
    let sandbox: sinon.SinonSandbox;

    setup(() => {
        sandbox = sinon.createSandbox();
        
        // Mock VS Code extension context
        mockContext = {
            extensionPath: '/mock/extension/path',
            globalState: {
                get: sandbox.stub(),
                update: sandbox.stub()
            },
            workspaceState: {
                get: sandbox.stub(),
                update: sandbox.stub()
            }
        } as any;

        // Mock Worker constructor
        (global as any).Worker = sandbox.stub().callsFake((scriptPath: string) => {
            const mockWorker = {
                postMessage: sandbox.stub(),
                terminate: sandbox.stub(),
                onmessage: null,
                onerror: null,
                onmessageerror: null
            };
            return mockWorker;
        });

        // Mock OS module
        const mockOS = {
            cpus: () => Array(8).fill({}), // Mock 8 CPU cores
            totalmem: () => 16 * 1024 * 1024 * 1024, // 16GB
            freemem: () => 8 * 1024 * 1024 * 1024    // 8GB free
        };
        sandbox.stub(require.cache, 'os' as any).value(mockOS);
    });

    teardown(() => {
        if (workerPoolService) {
            workerPoolService.terminate();
        }
        sandbox.restore();
    });

    test('should initialize with optimal worker count based on system resources', () => {
        workerPoolService = new WorkerPoolService(mockContext);
        const stats = workerPoolService.getPoolStats();
        
        // Should create 4 workers (8 CPUs / 2) with sufficient memory
        assert.strictEqual(stats.totalWorkers, 4);
        assert.strictEqual(stats.availableWorkers, 4);
        assert.strictEqual(stats.busyWorkers, 0);
    });

    test('should reduce worker count when memory is constrained', () => {
        // Mock low memory scenario
        const mockOSLowMem = {
            cpus: () => Array(8).fill({}),
            totalmem: () => 4 * 1024 * 1024 * 1024,  // 4GB total
            freemem: () => 2 * 1024 * 1024 * 1024    // 2GB free (< 4GB threshold)
        };
        sandbox.stub(require.cache, 'os' as any).value(mockOSLowMem);

        workerPoolService = new WorkerPoolService(mockContext);
        const stats = workerPoolService.getPoolStats();
        
        // Should create fewer workers due to memory constraints
        assert.ok(stats.totalWorkers <= 2);
    });

    test('should execute task and return result', async () => {
        workerPoolService = new WorkerPoolService(mockContext);
        
        // Mock worker response
        const mockResult = { success: true, data: 'test result' };
        
        // Execute task
        const taskPromise = workerPoolService.executeTask('test', { input: 'test data' });
        
        // Simulate worker response
        setTimeout(() => {
            const workers = (workerPoolService as any).workers;
            if (workers.length > 0) {
                const worker = workers[0];
                if (worker.onmessage) {
                    worker.onmessage({
                        data: {
                            id: 'task_123',
                            type: 'success',
                            payload: mockResult
                        }
                    });
                }
            }
        }, 10);
        
        const result = await taskPromise;
        assert.deepStrictEqual(result, mockResult);
    });

    test('should handle worker errors gracefully', async () => {
        workerPoolService = new WorkerPoolService(mockContext);
        
        // Execute task that will fail
        const taskPromise = workerPoolService.executeTask('test', { input: 'test data' });
        
        // Simulate worker error
        setTimeout(() => {
            const workers = (workerPoolService as any).workers;
            if (workers.length > 0) {
                const worker = workers[0];
                if (worker.onerror) {
                    worker.onerror({ message: 'Worker error' });
                }
            }
        }, 10);
        
        try {
            await taskPromise;
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.ok(error instanceof Error);
            assert.ok(error.message.includes('Worker error'));
        }
    });

    test('should queue tasks when all workers are busy', async () => {
        workerPoolService = new WorkerPoolService(mockContext, { maxWorkers: 1 });
        
        // Start first task (will occupy the single worker)
        const task1Promise = workerPoolService.executeTask('test1', { input: 'data1' });
        
        // Start second task (should be queued)
        const task2Promise = workerPoolService.executeTask('test2', { input: 'data2' });
        
        const stats = workerPoolService.getPoolStats();
        assert.strictEqual(stats.busyWorkers, 1);
        assert.strictEqual(stats.queuedTasks, 1);
        
        // Complete tasks
        setTimeout(() => {
            const workers = (workerPoolService as any).workers;
            const worker = workers[0];
            if (worker.onmessage) {
                worker.onmessage({
                    data: { id: 'task_1', type: 'success', payload: 'result1' }
                });
                worker.onmessage({
                    data: { id: 'task_2', type: 'success', payload: 'result2' }
                });
            }
        }, 10);
        
        const [result1, result2] = await Promise.all([task1Promise, task2Promise]);
        assert.strictEqual(result1, 'result1');
        assert.strictEqual(result2, 'result2');
    });

    test('should restart failed workers', async () => {
        workerPoolService = new WorkerPoolService(mockContext, { maxWorkers: 1 });
        
        const initialStats = workerPoolService.getPoolStats();
        assert.strictEqual(initialStats.totalWorkers, 1);
        
        // Simulate worker failure
        const workers = (workerPoolService as any).workers;
        const worker = workers[0];
        if (worker.onerror) {
            worker.onerror({ message: 'Critical worker error' });
        }
        
        // Wait for worker restart
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const finalStats = workerPoolService.getPoolStats();
        assert.strictEqual(finalStats.totalWorkers, 1);
    });

    test('should handle task timeout', async () => {
        workerPoolService = new WorkerPoolService(mockContext, { taskTimeout: 100 });
        
        // Execute task that will timeout
        const taskPromise = workerPoolService.executeTask('test', { input: 'test data' });
        
        try {
            await taskPromise;
            assert.fail('Should have timed out');
        } catch (error) {
            assert.ok(error instanceof Error);
            assert.ok(error.message.includes('timed out'));
        }
    });

    test('should provide accurate pool statistics', () => {
        workerPoolService = new WorkerPoolService(mockContext, { maxWorkers: 3 });
        
        const stats = workerPoolService.getPoolStats();
        
        assert.strictEqual(stats.totalWorkers, 3);
        assert.strictEqual(stats.availableWorkers, 3);
        assert.strictEqual(stats.busyWorkers, 0);
        assert.strictEqual(stats.queuedTasks, 0);
        assert.strictEqual(stats.activeTasks, 0);
        assert.strictEqual(stats.utilization, 0);
        assert.ok(typeof stats.averageQueueTime === 'number');
        assert.ok(typeof stats.workerEfficiency === 'number');
    });

    test('should terminate all workers on dispose', async () => {
        workerPoolService = new WorkerPoolService(mockContext, { maxWorkers: 2 });
        
        const workers = (workerPoolService as any).workers;
        const terminateStubs = workers.map((worker: any) => worker.terminate);
        
        await workerPoolService.terminate();
        
        terminateStubs.forEach((stub: sinon.SinonStub) => {
            assert.ok(stub.calledOnce);
        });
        
        const stats = workerPoolService.getPoolStats();
        assert.strictEqual(stats.totalWorkers, 0);
    });

    test('should handle convenience methods correctly', async () => {
        workerPoolService = new WorkerPoolService(mockContext);
        
        // Test diff generation
        const diffPromise = workerPoolService.generateDiff('original', 'modified', 'test.txt');
        
        // Test file search
        const searchPromise = workerPoolService.searchFiles('/test/path', { pattern: '*.ts' });
        
        // Test directory indexing
        const indexPromise = workerPoolService.indexDirectory('/root', ['node_modules'], ['**/*.ts']);
        
        // Test file analysis
        const analyzePromise = workerPoolService.analyzeFile('/test/file.ts', 'complexity');
        
        // Simulate responses
        setTimeout(() => {
            const workers = (workerPoolService as any).workers;
            workers.forEach((worker: any, index: number) => {
                if (worker.onmessage) {
                    worker.onmessage({
                        data: {
                            id: `task_${index}`,
                            type: 'success',
                            payload: `result_${index}`
                        }
                    });
                }
            });
        }, 10);
        
        const results = await Promise.all([diffPromise, searchPromise, indexPromise, analyzePromise]);
        assert.strictEqual(results.length, 4);
        results.forEach(result => {
            assert.ok(typeof result === 'string');
        });
    });
});
