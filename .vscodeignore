# Source files
src/**
out/**
webview-ui/node_modules/**

# Build files
.vscode/**
.vscode-test/**
tsconfig*.json
webpack*.js
.eslintrc.json
.gitignore
.npmignore

# Documentation (keep only essential)
old_docs/**
*.md
!README.md
!CHANGELOG.md

# Test files
test/**
**/*.test.ts
**/*.test.js
.nyc_output/**
coverage/**

# Development files
scripts/**
evals/**
standalone/**
proto/build-proto.js
proto/package.json

# Node modules (keep only production dependencies in dist)
node_modules/**

# Build artifacts
*.vsix
*.tgz
*.log

# OS files
.DS_Store
Thumbs.db

# IDE files
.idea/**
*.swp
*.swo

# Temporary files
tmp/**
temp/**

# Lock files
package-lock.json
yarn.lock

# Analysis files
bundle-report.html
bundle-stats.json

# Previous versions
v1b3-sama-*.vsix

# Keep only essential files
!dist/**
!assets/**
!proto/*.proto
!package.json
!LICENSE.txt
!README.md
