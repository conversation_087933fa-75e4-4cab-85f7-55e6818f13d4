{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/error.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,OAAO,EAAE,MAAM,yCAAyC,CAAC;AAElE;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,CAAU;IACxC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,OAAO,CAAC,CAAC,OAAO,CAAC;IACnB,CAAC;SAAM,CAAC;QACN,IAAI,WAAmB,CAAC;QACxB,IAAI,CAAC;YACH,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC;gBAC/B,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,WAAW,GAAG,6BAA6B,CAAC;QAC9C,CAAC;QACD,OAAO,iBAAiB,WAAW,EAAE,CAAC;IACxC,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isError } from \"@typespec/ts-http-runtime/internal/util\";\n\n/**\n * Given what is thought to be an error object, return the message if possible.\n * If the message is missing, returns a stringified version of the input.\n * @param e - Something thrown from a try block\n * @returns The error message or a string of the input\n */\nexport function getErrorMessage(e: unknown): string {\n  if (isError(e)) {\n    return e.message;\n  } else {\n    let stringified: string;\n    try {\n      if (typeof e === \"object\" && e) {\n        stringified = JSON.stringify(e);\n      } else {\n        stringified = String(e);\n      }\n    } catch (err: any) {\n      stringified = \"[unable to stringify input]\";\n    }\n    return `Unknown error ${stringified}`;\n  }\n}\n"]}