import * as vscode from 'vscode';
import * as http from 'http';
import * as path from 'path';
import * as fs from 'fs';
import { spawn, ChildProcess } from 'child_process';

export interface LivePreviewOptions {
    port?: number;
    autoOpen?: boolean;
    enableHotReload?: boolean;
}

export interface PreviewResult {
    success: boolean;
    url?: string;
    error?: string;
    serverProcess?: ChildProcess;
}

export class LivePreviewService {
    private _serverProcess: ChildProcess | null = null;
    private _currentPort: number = 8080;
    private _outputChannel: vscode.OutputChannel;
    private _previewPanel: vscode.WebviewPanel | null = null;

    constructor() {
        this._outputChannel = vscode.window.createOutputChannel('V1b3-Sama Live Preview');
    }

    /**
     * Start a local web server to serve files from workspace
     */
    public async startWebServer(options: LivePreviewOptions = {}): Promise<PreviewResult> {
        const port = options.port || this._findAvailablePort();
        
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                throw new Error('No workspace folder found to serve.');
            }

            const workspacePath = workspaceFolders[0].uri.fsPath;
            
            // Stop any existing server
            if (this._serverProcess) {
                this._serverProcess.kill();
                this._serverProcess = null;
            }

            // Start new server using npx http-server with proper configuration
            return new Promise((resolve, reject) => {
                this._serverProcess = spawn('npx', [
                    'http-server',
                    '.',
                    '-p', `${port}`,
                    '-c-1', // Disable caching
                    '--cors', // Enable CORS
                    '-o', // Don't auto-open browser
                    '--ext', 'html', // Default extension
                    '--gzip', // Enable gzip compression
                    '--brotli', // Enable brotli compression
                    '-d', 'false' // Disable directory browsing
                ], {
                    cwd: workspacePath,
                    shell: true
                });

                this._currentPort = port;

                this._serverProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    this._outputChannel.appendLine(`Server: ${output}`);
                    
                    if (output.includes('Available on')) {
                        const url = `http://localhost:${port}`;
                        this._outputChannel.appendLine(`✅ Live server started at ${url}`);
                        resolve({
                            success: true,
                            url,
                            serverProcess: this._serverProcess!
                        });
                    }
                });

                this._serverProcess.stderr?.on('data', (data) => {
                    const error = data.toString();
                    this._outputChannel.appendLine(`Server Error: ${error}`);
                    reject({
                        success: false,
                        error: `Server error: ${error}`
                    });
                });

                this._serverProcess.on('error', (error) => {
                    this._outputChannel.appendLine(`Failed to start server: ${error.message}`);
                    reject({
                        success: false,
                        error: `Failed to start server: ${error.message}`
                    });
                });

                // Timeout after 10 seconds
                setTimeout(() => {
                    if (this._serverProcess && !this._serverProcess.killed) {
                        reject({
                            success: false,
                            error: 'Server startup timeout'
                        });
                    }
                }, 10000);
            });

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Create and display live preview in webview panel
     */
    public async showLivePreview(fileName: string, options: LivePreviewOptions = {}): Promise<void> {
        try {
            // Ensure we have a proper HTML file to serve
            await this._ensureProperWebStructure(fileName);

            // Start web server if not running
            const serverResult = await this.startWebServer(options);
            if (!serverResult.success) {
                throw new Error(serverResult.error || 'Failed to start server');
            }

            // Close existing preview panel
            if (this._previewPanel) {
                this._previewPanel.dispose();
            }

            // Create new webview panel
            this._previewPanel = vscode.window.createWebviewPanel(
                'v1b3-sama-live-preview',
                `Live Preview - ${fileName}`,
                vscode.ViewColumn.Beside,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true,
                    localResourceRoots: []
                }
            );

            // Determine the correct URL to serve
            const targetFile = this._getTargetFile(fileName);
            const url = `${serverResult.url}/${targetFile}`;
            this._previewPanel.webview.html = this._generatePreviewHtml(url, fileName);

            // Handle panel disposal
            this._previewPanel.onDidDispose(() => {
                this._previewPanel = null;
            });

            this._outputChannel.appendLine(`✅ Live preview opened for ${fileName} at ${url}`);
            vscode.window.showInformationMessage(`Live preview started: ${url}`);

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to show live preview: ${errorMsg}`);
            vscode.window.showErrorMessage(`Failed to show live preview: ${errorMsg}`);
        }
    }

    /**
     * Generate HTML content for the preview webview
     */
    private _generatePreviewHtml(url: string, fileName: string): string {
        const port = this._currentPort;

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="
                    default-src 'none';
                    style-src 'unsafe-inline';
                    img-src 'self' data: http://localhost:${port};
                    frame-src http://localhost:${port};
                    script-src 'unsafe-inline';
                ">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Live Preview - ${fileName}</title>
                <style>
                    html, body {
                        height: 100%;
                        width: 100%;
                        margin: 0;
                        padding: 0;
                        overflow: hidden;
                        background: var(--vscode-editor-background);
                        color: var(--vscode-editor-foreground);
                    }

                    .preview-container {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                    }

                    .preview-header {
                        background: var(--vscode-titleBar-activeBackground);
                        color: var(--vscode-titleBar-activeForeground);
                        padding: 8px 16px;
                        font-size: 12px;
                        border-bottom: 1px solid var(--vscode-panel-border);
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }

                    .preview-url {
                        font-family: monospace;
                        opacity: 0.8;
                    }

                    .preview-controls {
                        display: flex;
                        gap: 8px;
                    }

                    .control-btn {
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                    }

                    .control-btn:hover {
                        background: var(--vscode-button-hoverBackground);
                    }

                    .preview-frame {
                        flex: 1;
                        border: none;
                        background: white;
                    }

                    .loading {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        font-size: 14px;
                        color: var(--vscode-descriptionForeground);
                    }
                </style>
            </head>
            <body>
                <div class="preview-container">
                    <div class="preview-header">
                        <div>
                            <span>🌐 Live Preview</span>
                            <span class="preview-url">${url}</span>
                        </div>
                        <div class="preview-controls">
                            <button class="control-btn" onclick="refreshPreview()">🔄 Refresh</button>
                            <button class="control-btn" onclick="openInBrowser()">🌍 Open in Browser</button>
                        </div>
                    </div>
                    <iframe
                        id="preview-frame"
                        class="preview-frame"
                        src="${url}"
                        onload="hideLoading()"
                    ></iframe>
                    <div id="loading" class="loading">
                        Loading preview...
                    </div>
                </div>

                <script>
                    function refreshPreview() {
                        const frame = document.getElementById('preview-frame');
                        frame.src = frame.src;
                    }

                    function openInBrowser() {
                        // This would need to be handled by the extension
                        console.log('Open in browser requested');
                    }

                    function hideLoading() {
                        document.getElementById('loading').style.display = 'none';
                    }

                    // Show loading initially
                    document.getElementById('loading').style.display = 'flex';
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Ensure proper web structure for serving
     */
    private async _ensureProperWebStructure(fileName: string): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            throw new Error('No workspace folder found');
        }

        const workspacePath = workspaceFolders[0].uri.fsPath;
        const filePath = path.join(workspacePath, fileName);

        // Check if the file exists
        if (!fs.existsSync(filePath)) {
            throw new Error(`File ${fileName} does not exist in workspace`);
        }

        // If it's not index.html, create a symlink or copy to index.html for default serving
        if (fileName !== 'index.html' && path.extname(fileName) === '.html') {
            const indexPath = path.join(workspacePath, 'index.html');

            // Only create index.html if it doesn't exist
            if (!fs.existsSync(indexPath)) {
                try {
                    // Copy the file content to index.html
                    const content = fs.readFileSync(filePath, 'utf8');
                    fs.writeFileSync(indexPath, content, 'utf8');
                    this._outputChannel.appendLine(`📄 Created index.html from ${fileName} for default serving`);
                } catch (error) {
                    this._outputChannel.appendLine(`⚠️ Could not create index.html: ${error}`);
                }
            }
        }

        // Ensure CSS files are properly linked and exist
        await this._validateCssLinks(filePath);
    }

    /**
     * Validate and ensure CSS files are properly linked
     */
    private async _validateCssLinks(htmlFilePath: string): Promise<void> {
        try {
            const content = fs.readFileSync(htmlFilePath, 'utf8');
            const workspacePath = path.dirname(htmlFilePath);

            // Find CSS link tags
            const cssLinkRegex = /<link[^>]*href=["']([^"']*\.css)["'][^>]*>/gi;
            let match;

            while ((match = cssLinkRegex.exec(content)) !== null) {
                const cssPath = match[1];
                const fullCssPath = path.resolve(workspacePath, cssPath);

                // Check if CSS file exists
                if (!fs.existsSync(fullCssPath)) {
                    this._outputChannel.appendLine(`⚠️ CSS file not found: ${cssPath}`);

                    // Create a basic CSS file if it doesn't exist
                    const basicCss = `/* Generated CSS file for ${path.basename(cssPath)} */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}
`;
                    fs.writeFileSync(fullCssPath, basicCss, 'utf8');
                    this._outputChannel.appendLine(`📄 Created basic CSS file: ${cssPath}`);
                }
            }
        } catch (error) {
            this._outputChannel.appendLine(`⚠️ Error validating CSS links: ${error}`);
        }
    }

    /**
     * Get the target file to serve (prefer index.html for directory serving)
     */
    private _getTargetFile(fileName: string): string {
        // If the file is an HTML file and index.html exists, serve index.html
        if (path.extname(fileName) === '.html') {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (workspaceFolders) {
                const indexPath = path.join(workspaceFolders[0].uri.fsPath, 'index.html');
                if (fs.existsSync(indexPath)) {
                    return 'index.html';
                }
            }
        }
        return fileName;
    }

    /**
     * Find an available port starting from 8080
     */
    private _findAvailablePort(): number {
        // Simple implementation - in production, you'd want to check if port is actually available
        return 8080 + Math.floor(Math.random() * 1000);
    }

    /**
     * Stop the web server
     */
    public stopWebServer(): void {
        if (this._serverProcess) {
            this._serverProcess.kill();
            this._serverProcess = null;
            this._outputChannel.appendLine('🛑 Web server stopped');
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.stopWebServer();
        if (this._previewPanel) {
            this._previewPanel.dispose();
        }
        this._outputChannel.dispose();
    }
}
