{"version": 3, "file": "defaultHttpClient-browser.mjs", "sourceRoot": "", "sources": ["../../src/defaultHttpClient-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,qBAAqB,EAAE,MAAM,sBAAsB,CAAC;AAE7D;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,OAAO,qBAAqB,EAAE,CAAC;AACjC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createFetchHttpClient } from \"./fetchHttpClient.js\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createFetchHttpClient();\n}\n"]}