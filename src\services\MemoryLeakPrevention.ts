// Memory Leak Prevention Service
// Monitors and prevents memory leaks in the extension

import * as vscode from 'vscode';

export interface MemoryStats {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    timestamp: number;
}

export interface LeakDetectionResult {
    isLeakDetected: boolean;
    severity: 'low' | 'medium' | 'high';
    memoryGrowth: number;
    recommendations: string[];
}

export class MemoryLeakPrevention {
    private context: vscode.ExtensionContext;
    private memoryHistory: MemoryStats[] = [];
    private disposables: vscode.Disposable[] = [];
    private eventListeners: Map<string, Function[]> = new Map();
    private timers: Set<NodeJS.Timeout> = new Set();
    private intervals: Set<NodeJS.Timeout> = new Set();
    private webviewPanels: Set<vscode.WebviewPanel> = new Set();
    private monitoringInterval?: NodeJS.Timeout;
    private maxHistorySize = 100;
    private memoryThreshold = 200 * 1024 * 1024; // 200MB

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.startMemoryMonitoring();
        this.setupCleanupHandlers();
    }

    /**
     * Register a disposable for automatic cleanup
     */
    public registerDisposable(disposable: vscode.Disposable): void {
        this.disposables.push(disposable);
        this.context.subscriptions.push(disposable);
    }

    /**
     * Register an event listener for tracking
     */
    public registerEventListener(eventName: string, listener: Function): void {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName)!.push(listener);
    }

    /**
     * Register a timer for tracking
     */
    public registerTimer(timer: NodeJS.Timeout): void {
        this.timers.add(timer);
    }

    /**
     * Register an interval for tracking
     */
    public registerInterval(interval: NodeJS.Timeout): void {
        this.intervals.add(interval);
    }

    /**
     * Register a webview panel for tracking
     */
    public registerWebviewPanel(panel: vscode.WebviewPanel): void {
        this.webviewPanels.add(panel);
        
        // Auto-remove when disposed
        panel.onDidDispose(() => {
            this.webviewPanels.delete(panel);
        });
    }

    /**
     * Get current memory statistics
     */
    public getCurrentMemoryStats(): MemoryStats {
        const memUsage = process.memoryUsage();
        return {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
            timestamp: Date.now()
        };
    }

    /**
     * Get memory history
     */
    public getMemoryHistory(): MemoryStats[] {
        return [...this.memoryHistory];
    }

    /**
     * Detect potential memory leaks
     */
    public detectMemoryLeaks(): LeakDetectionResult {
        if (this.memoryHistory.length < 10) {
            return {
                isLeakDetected: false,
                severity: 'low',
                memoryGrowth: 0,
                recommendations: ['Insufficient data for leak detection']
            };
        }

        // Analyze memory growth over time
        const recent = this.memoryHistory.slice(-10);
        const older = this.memoryHistory.slice(-20, -10);

        const recentAvg = recent.reduce((sum, stat) => sum + stat.heapUsed, 0) / recent.length;
        const olderAvg = older.reduce((sum, stat) => sum + stat.heapUsed, 0) / older.length;

        const memoryGrowth = recentAvg - olderAvg;
        const growthPercentage = (memoryGrowth / olderAvg) * 100;

        let isLeakDetected = false;
        let severity: 'low' | 'medium' | 'high' = 'low';
        const recommendations: string[] = [];

        // Detect significant memory growth
        if (growthPercentage > 50) {
            isLeakDetected = true;
            severity = 'high';
            recommendations.push('Significant memory growth detected (>50%)');
        } else if (growthPercentage > 25) {
            isLeakDetected = true;
            severity = 'medium';
            recommendations.push('Moderate memory growth detected (>25%)');
        } else if (growthPercentage > 10) {
            isLeakDetected = true;
            severity = 'low';
            recommendations.push('Minor memory growth detected (>10%)');
        }

        // Check absolute memory usage
        const currentMemory = this.getCurrentMemoryStats();
        if (currentMemory.heapUsed > this.memoryThreshold) {
            isLeakDetected = true;
            severity = 'high';
            recommendations.push(`High memory usage detected (${Math.round(currentMemory.heapUsed / 1024 / 1024)}MB)`);
        }

        // Add specific recommendations
        if (isLeakDetected) {
            recommendations.push(...this.generateRecommendations());
        }

        return {
            isLeakDetected,
            severity,
            memoryGrowth,
            recommendations
        };
    }

    /**
     * Force garbage collection (if available)
     */
    public forceGarbageCollection(): void {
        if (global.gc) {
            global.gc();
            console.log('V1b3-Sama: Forced garbage collection');
        } else {
            console.warn('V1b3-Sama: Garbage collection not available (run with --expose-gc)');
        }
    }

    /**
     * Clean up all tracked resources
     */
    public cleanupResources(): void {
        // Clear timers
        for (const timer of this.timers) {
            clearTimeout(timer);
        }
        this.timers.clear();

        // Clear intervals
        for (const interval of this.intervals) {
            clearInterval(interval);
        }
        this.intervals.clear();

        // Dispose webview panels
        for (const panel of this.webviewPanels) {
            if (panel && typeof panel.dispose === 'function') {
                panel.dispose();
            }
        }
        this.webviewPanels.clear();

        // Clear event listeners (if they support removal)
        this.eventListeners.clear();

        // Dispose all registered disposables
        for (const disposable of this.disposables) {
            try {
                disposable.dispose();
            } catch (error) {
                console.error('Error disposing resource:', error);
            }
        }
        this.disposables.length = 0;

        console.log('V1b3-Sama: Cleaned up all tracked resources');
    }

    /**
     * Get resource usage summary
     */
    public getResourceSummary(): any {
        return {
            disposables: this.disposables.length,
            eventListeners: Array.from(this.eventListeners.entries()).reduce((sum, [_, listeners]) => sum + listeners.length, 0),
            timers: this.timers.size,
            intervals: this.intervals.size,
            webviewPanels: this.webviewPanels.size,
            memoryHistory: this.memoryHistory.length
        };
    }

    /**
     * Show memory report in VS Code
     */
    public async showMemoryReport(): Promise<void> {
        const currentStats = this.getCurrentMemoryStats();
        const leakDetection = this.detectMemoryLeaks();
        const resourceSummary = this.getResourceSummary();

        const panel = vscode.window.createWebviewPanel(
            'v1b3-memory-report',
            'V1b3-Sama Memory Report',
            vscode.ViewColumn.One,
            { enableScripts: false }
        );

        this.registerWebviewPanel(panel);

        panel.webview.html = this.generateMemoryReportHTML(currentStats, leakDetection, resourceSummary);
    }

    private startMemoryMonitoring(): void {
        // Monitor memory every 30 seconds
        this.monitoringInterval = setInterval(() => {
            const stats = this.getCurrentMemoryStats();
            this.memoryHistory.push(stats);

            // Trim history if too large
            if (this.memoryHistory.length > this.maxHistorySize) {
                this.memoryHistory = this.memoryHistory.slice(-this.maxHistorySize);
            }

            // Check for memory leaks
            const leakDetection = this.detectMemoryLeaks();
            if (leakDetection.isLeakDetected && leakDetection.severity === 'high') {
                this.handleMemoryLeak(leakDetection);
            }
        }, 30000);

        this.registerInterval(this.monitoringInterval);
    }

    private setupCleanupHandlers(): void {
        // Clean up on extension deactivation
        this.context.subscriptions.push({
            dispose: () => {
                this.dispose();
            }
        });

        // Clean up on VS Code shutdown
        vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration('v1b3-sama')) {
                // Configuration changed, might need cleanup
                this.cleanupResources();
            }
        });
    }

    private handleMemoryLeak(detection: LeakDetectionResult): void {
        console.warn('V1b3-Sama: Memory leak detected', detection);

        // Show warning to user
        vscode.window.showWarningMessage(
            `V1b3-Sama: High memory usage detected (${Math.round(this.getCurrentMemoryStats().heapUsed / 1024 / 1024)}MB). Consider restarting VS Code.`,
            'Show Report',
            'Force Cleanup'
        ).then(selection => {
            if (selection === 'Show Report') {
                this.showMemoryReport();
            } else if (selection === 'Force Cleanup') {
                this.cleanupResources();
                this.forceGarbageCollection();
            }
        });
    }

    private generateRecommendations(): string[] {
        const recommendations = [];
        const resourceSummary = this.getResourceSummary();

        if (resourceSummary.disposables > 100) {
            recommendations.push('High number of disposables - ensure proper cleanup');
        }

        if (resourceSummary.eventListeners > 50) {
            recommendations.push('High number of event listeners - check for proper removal');
        }

        if (resourceSummary.timers > 10) {
            recommendations.push('High number of active timers - ensure they are cleared');
        }

        if (resourceSummary.webviewPanels > 5) {
            recommendations.push('Multiple webview panels open - consider closing unused panels');
        }

        recommendations.push('Consider restarting VS Code to free memory');
        recommendations.push('Check for circular references in your code');
        recommendations.push('Ensure all async operations are properly awaited or handled');

        return recommendations;
    }

    private generateMemoryReportHTML(
        currentStats: MemoryStats,
        leakDetection: LeakDetectionResult,
        resourceSummary: any
    ): string {
        const formatBytes = (bytes: number) => {
            return `${Math.round(bytes / 1024 / 1024)}MB`;
        };

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Memory Report</title>
                <style>
                    body { 
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                        padding: 20px; 
                        background-color: var(--vscode-editor-background);
                        color: var(--vscode-editor-foreground);
                    }
                    .metric-card { 
                        background: var(--vscode-editor-inactiveSelectionBackground);
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 4px;
                        padding: 15px; 
                        margin: 10px 0; 
                    }
                    .leak-detected { border-left: 4px solid #ff4757; }
                    .leak-warning { border-left: 4px solid #ffa502; }
                    .leak-ok { border-left: 4px solid #2ed573; }
                    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
                    .stat-card { 
                        background: var(--vscode-button-background);
                        padding: 15px; 
                        border-radius: 4px; 
                        text-align: center; 
                    }
                    .stat-number { font-size: 1.5em; font-weight: bold; }
                    .recommendations { background: var(--vscode-textCodeBlock-background); padding: 15px; border-radius: 4px; }
                </style>
            </head>
            <body>
                <h1>V1b3-Sama Memory Report</h1>
                
                <div class="metric-card ${leakDetection.isLeakDetected ? 
                    (leakDetection.severity === 'high' ? 'leak-detected' : 'leak-warning') : 'leak-ok'}">
                    <h2>Memory Status: ${leakDetection.isLeakDetected ? 
                        `⚠️ ${leakDetection.severity.toUpperCase()} USAGE` : '✅ NORMAL'}</h2>
                    <p>Growth: ${leakDetection.memoryGrowth > 0 ? '+' : ''}${formatBytes(leakDetection.memoryGrowth)}</p>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${formatBytes(currentStats.heapUsed)}</div>
                        <div>Heap Used</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${formatBytes(currentStats.heapTotal)}</div>
                        <div>Heap Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${formatBytes(currentStats.rss)}</div>
                        <div>RSS</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${resourceSummary.disposables}</div>
                        <div>Disposables</div>
                    </div>
                </div>

                <h2>Resource Usage</h2>
                <div class="metric-card">
                    <ul>
                        <li>Event Listeners: ${resourceSummary.eventListeners}</li>
                        <li>Active Timers: ${resourceSummary.timers}</li>
                        <li>Active Intervals: ${resourceSummary.intervals}</li>
                        <li>Webview Panels: ${resourceSummary.webviewPanels}</li>
                        <li>Memory History: ${resourceSummary.memoryHistory} entries</li>
                    </ul>
                </div>

                ${leakDetection.recommendations.length > 0 ? `
                    <h2>Recommendations</h2>
                    <div class="recommendations">
                        <ul>
                            ${leakDetection.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </body>
            </html>
        `;
    }

    /**
     * Dispose of the memory leak prevention service
     */
    public dispose(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        this.cleanupResources();
        this.memoryHistory.length = 0;
    }
}
