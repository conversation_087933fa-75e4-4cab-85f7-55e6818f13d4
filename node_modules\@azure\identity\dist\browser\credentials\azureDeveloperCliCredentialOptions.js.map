{"version": 3, "file": "azureDeveloperCliCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureDeveloperCliCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link AzureDeveloperCliCredential}\n */\nexport interface AzureDeveloperCliCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Allows specifying a tenant ID\n   */\n  tenantId?: string;\n  /**\n   * Process timeout configurable for making token requests, provided in milliseconds\n   */\n  processTimeoutInMs?: number;\n}\n"]}