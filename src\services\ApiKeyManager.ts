import * as vscode from 'vscode';

/**
 * Supported LLM providers for API key management
 */
export type SupportedProvider = 'deepseek' | 'openai' | 'anthropic' | 'google' | 'openrouter' | 'azure' | 'local' | 'groq';

/**
 * API key information for display purposes
 */
export interface ApiKeyInfo {
    provider: SupportedProvider;
    hasKey: boolean;
    maskedKey: string;
    lastUpdated?: Date;
}

/**
 * Provider configuration with API endpoints and model information
 */
export interface ProviderConfig {
    name: string;
    displayName: string;
    apiEndpoint: string;
    requiresApiKey: boolean;
    defaultModels: string[];
    supportsStreaming: boolean;
    maxTokens?: number;
}

/**
 * Enhanced API Key Manager for V1b3-Sama Extension
 * Provides secure storage, validation, and management of API keys using VS Code's SecretStorage
 */
export class ApiKeyManager {
    private static readonly SECRET_KEY_PREFIX = 'v1b3-sama.apiKey';
    private static readonly LEGACY_STORAGE_KEY = 'apiKeys';
    private static readonly PROVIDER_CONFIG_KEY = 'providerConfigs';
    
    private _disposables: vscode.Disposable[] = [];
    private _changeEmitter = new vscode.EventEmitter<{ provider: SupportedProvider; hasKey: boolean }>();
    
    /**
     * Event fired when an API key is added, updated, or removed
     */
    public readonly onDidChangeApiKey = this._changeEmitter.event;
    
    /**
     * Default provider configurations
     */
    private static readonly DEFAULT_PROVIDER_CONFIGS: Record<SupportedProvider, ProviderConfig> = {
        deepseek: {
            name: 'deepseek',
            displayName: 'DeepSeek',
            apiEndpoint: 'https://api.deepseek.com/v1',
            requiresApiKey: true,
            defaultModels: ['deepseek-chat', 'deepseek-reasoner'],
            supportsStreaming: true,
            maxTokens: 64000
        },
        groq: {
            name: 'groq',
            displayName: 'Groq',
            apiEndpoint: 'https://api.groq.com/openai/v1',
            requiresApiKey: true,
            defaultModels: ['llama-3.3-70b-versatile', 'llama-3.1-8b-instant', 'qwen3-32b'],
            supportsStreaming: true,
            maxTokens: 131072
        },
        openai: {
            name: 'openai',
            displayName: 'OpenAI',
            apiEndpoint: 'https://api.openai.com/v1',
            requiresApiKey: true,
            defaultModels: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            supportsStreaming: true,
            maxTokens: 4096
        },
        anthropic: {
            name: 'anthropic',
            displayName: 'Anthropic',
            apiEndpoint: 'https://api.anthropic.com/v1',
            requiresApiKey: true,
            defaultModels: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
            supportsStreaming: true,
            maxTokens: 4096
        },
        google: {
            name: 'google',
            displayName: 'Google AI',
            apiEndpoint: 'https://generativelanguage.googleapis.com/v1',
            requiresApiKey: true,
            defaultModels: ['gemini-pro', 'gemini-pro-vision'],
            supportsStreaming: true,
            maxTokens: 2048
        },
        openrouter: {
            name: 'openrouter',
            displayName: 'OpenRouter',
            apiEndpoint: 'https://openrouter.ai/api/v1',
            requiresApiKey: true,
            defaultModels: ['openai/gpt-4', 'anthropic/claude-3-opus', 'meta-llama/llama-3-70b-instruct'],
            supportsStreaming: true,
            maxTokens: 4096
        },
        azure: {
            name: 'azure',
            displayName: 'Azure OpenAI',
            apiEndpoint: 'https://{resource}.openai.azure.com/openai/deployments/{deployment}',
            requiresApiKey: true,
            defaultModels: ['gpt-4', 'gpt-35-turbo'],
            supportsStreaming: true,
            maxTokens: 4096
        },
        local: {
            name: 'local',
            displayName: 'Local Server',
            apiEndpoint: 'http://localhost:1234/v1',
            requiresApiKey: false,
            defaultModels: ['local-model'],
            supportsStreaming: true,
            maxTokens: 2048
        }
    };
    
    constructor(private context: vscode.ExtensionContext) {
        this.initialize();
    }
    
    /**
     * Initialize the API key manager
     */
    private initialize(): void {
        // Migrate legacy API keys if they exist
        this.migrateLegacyApiKeys();
        
        // Set up context subscriptions
        this.context.subscriptions.push(...this._disposables);
    }
    
    /**
     * Store an API key securely for a provider
     */
    public async storeApiKey(provider: SupportedProvider, apiKey: string): Promise<void> {
        try {
            if (!apiKey || apiKey.trim().length === 0) {
                throw new Error('API key cannot be empty');
            }
            
            // Validate API key format (basic validation)
            this.validateApiKeyFormat(provider, apiKey);
            
            const secretKey = `${ApiKeyManager.SECRET_KEY_PREFIX}.${provider}`;
            await this.context.secrets.store(secretKey, apiKey.trim());
            
            // Update legacy storage for UI compatibility (without actual key)
            await this.updateLegacyStorage(provider, true);
            
            // Fire change event
            this._changeEmitter.fire({ provider, hasKey: true });
            
            vscode.window.showInformationMessage(`API key for ${this.getProviderDisplayName(provider)} saved securely`);
        } catch (error) {
            console.error(`Failed to store API key for ${provider}:`, error);
            vscode.window.showErrorMessage(`Failed to save API key for ${provider}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }
    
    /**
     * Retrieve an API key for a provider
     */
    public async getApiKey(provider: SupportedProvider): Promise<string | undefined> {
        try {
            const secretKey = `${ApiKeyManager.SECRET_KEY_PREFIX}.${provider}`;
            return await this.context.secrets.get(secretKey);
        } catch (error) {
            console.error(`Failed to retrieve API key for ${provider}:`, error);
            return undefined;
        }
    }
    
    /**
     * Check if an API key exists for a provider
     */
    public async hasApiKey(provider: SupportedProvider): Promise<boolean> {
        const apiKey = await this.getApiKey(provider);
        return apiKey !== undefined && apiKey.length > 0;
    }
    
    /**
     * Delete an API key for a provider
     */
    public async deleteApiKey(provider: SupportedProvider): Promise<void> {
        try {
            const secretKey = `${ApiKeyManager.SECRET_KEY_PREFIX}.${provider}`;
            await this.context.secrets.delete(secretKey);

            // Update legacy storage
            await this.updateLegacyStorage(provider, false);

            // Fire change event
            this._changeEmitter.fire({ provider, hasKey: false });

            vscode.window.showInformationMessage(`API key for ${this.getProviderDisplayName(provider)} deleted`);
        } catch (error) {
            console.error(`Failed to delete API key for ${provider}:`, error);
            vscode.window.showErrorMessage(`Failed to delete API key for ${provider}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            throw error;
        }
    }

    /**
     * Remove API key (alias for deleteApiKey for compatibility)
     */
    public async removeApiKey(provider: SupportedProvider): Promise<void> {
        return this.deleteApiKey(provider);
    }
    
    /**
     * Get API key information for all providers (for UI display)
     */
    public async getAllApiKeyInfo(): Promise<ApiKeyInfo[]> {
        const providers = Object.keys(ApiKeyManager.DEFAULT_PROVIDER_CONFIGS) as SupportedProvider[];
        const keyInfoPromises = providers.map(async (provider) => {
            const hasKey = await this.hasApiKey(provider);
            return {
                provider,
                hasKey,
                maskedKey: hasKey ? this.getMaskedKey(provider) : '',
                lastUpdated: hasKey ? new Date() : undefined
            };
        });
        
        return Promise.all(keyInfoPromises);
    }
    
    /**
     * Get masked API key for display purposes
     */
    public getMaskedKey(provider: SupportedProvider): string {
        return '••••••••••••••••';
    }
    
    /**
     * Get provider configuration
     */
    public getProviderConfig(provider: SupportedProvider): ProviderConfig {
        return ApiKeyManager.DEFAULT_PROVIDER_CONFIGS[provider];
    }
    
    /**
     * Get all provider configurations
     */
    public getAllProviderConfigs(): Record<SupportedProvider, ProviderConfig> {
        return { ...ApiKeyManager.DEFAULT_PROVIDER_CONFIGS };
    }
    
    /**
     * Get provider display name
     */
    public getProviderDisplayName(provider: SupportedProvider): string {
        return ApiKeyManager.DEFAULT_PROVIDER_CONFIGS[provider]?.displayName || provider;
    }
    
    /**
     * Get providers that require API keys
     */
    public getProvidersRequiringApiKeys(): SupportedProvider[] {
        return Object.entries(ApiKeyManager.DEFAULT_PROVIDER_CONFIGS)
            .filter(([_, config]) => config.requiresApiKey)
            .map(([provider, _]) => provider as SupportedProvider);
    }

    /**
     * Validate API key format for a specific provider
     */
    private validateApiKeyFormat(provider: SupportedProvider, apiKey: string): void {
        const trimmedKey = apiKey.trim();

        switch (provider) {
            case 'openai':
                if (!trimmedKey.startsWith('sk-') || trimmedKey.length < 20) {
                    throw new Error('OpenAI API key must start with "sk-" and be at least 20 characters long');
                }
                break;
            case 'anthropic':
                if (!trimmedKey.startsWith('sk-ant-') || trimmedKey.length < 20) {
                    throw new Error('Anthropic API key must start with "sk-ant-" and be at least 20 characters long');
                }
                break;
            case 'deepseek':
                if (!trimmedKey.startsWith('sk-') || trimmedKey.length < 20) {
                    throw new Error('DeepSeek API key must start with "sk-" and be at least 20 characters long');
                }
                break;
            case 'groq':
                if (!trimmedKey.startsWith('gsk_') || trimmedKey.length < 20) {
                    throw new Error('Groq API key must start with "gsk_" and be at least 20 characters long');
                }
                break;
            case 'google':
                if (trimmedKey.length < 20) {
                    throw new Error('Google AI API key must be at least 20 characters long');
                }
                break;
            case 'openrouter':
                if (!trimmedKey.startsWith('sk-or-') || trimmedKey.length < 20) {
                    throw new Error('OpenRouter API key must start with "sk-or-" and be at least 20 characters long');
                }
                break;
            case 'azure':
                if (trimmedKey.length < 20) {
                    throw new Error('Azure OpenAI API key must be at least 20 characters long');
                }
                break;
            case 'local':
                // Local server doesn't require API key validation
                break;
            default:
                if (trimmedKey.length < 10) {
                    throw new Error('API key must be at least 10 characters long');
                }
        }
    }

    /**
     * Public method to validate API key format (returns result instead of throwing)
     */
    public validateApiKeyFormatSafe(provider: SupportedProvider, apiKey: string): { valid: boolean; error?: string } {
        try {
            this.validateApiKeyFormat(provider, apiKey);
            return { valid: true };
        } catch (error) {
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Invalid API key format'
            };
        }
    }

    /**
     * Update legacy storage for backward compatibility
     */
    private async updateLegacyStorage(provider: SupportedProvider, hasKey: boolean): Promise<void> {
        try {
            const legacyKeys = this.context.globalState.get<Record<string, string>>(ApiKeyManager.LEGACY_STORAGE_KEY, {});

            if (hasKey) {
                legacyKeys[provider] = '***STORED_SECURELY***';
            } else {
                delete legacyKeys[provider];
            }

            await this.context.globalState.update(ApiKeyManager.LEGACY_STORAGE_KEY, legacyKeys);
        } catch (error) {
            console.error('Failed to update legacy storage:', error);
        }
    }

    /**
     * Migrate legacy API keys to secure storage
     */
    private async migrateLegacyApiKeys(): Promise<void> {
        try {
            const legacyKeys = this.context.globalState.get<Record<string, string>>(ApiKeyManager.LEGACY_STORAGE_KEY, {});

            for (const [provider, key] of Object.entries(legacyKeys)) {
                // Only migrate if it's not already the secure storage indicator
                if (key && key !== '***STORED_SECURELY***' && key.length > 10) {
                    console.log(`Migrating legacy API key for ${provider}`);
                    await this.storeApiKey(provider as SupportedProvider, key);
                }
            }
        } catch (error) {
            console.error('Failed to migrate legacy API keys:', error);
        }
    }

    /**
     * Test an API key by making a simple request
     */
    public async testApiKey(provider: SupportedProvider): Promise<{ isValid: boolean; error?: string }> {
        try {
            const apiKey = await this.getApiKey(provider);
            if (!apiKey) {
                return { isValid: false, error: 'No API key found' };
            }

            const config = this.getProviderConfig(provider);
            if (!config.requiresApiKey) {
                return { isValid: true }; // Local server doesn't need testing
            }

            // For now, just validate the format - actual API testing would require HTTP requests
            this.validateApiKeyFormat(provider, apiKey);
            return { isValid: true };

        } catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Get available models for a provider
     */
    public getAvailableModels(provider: SupportedProvider): string[] {
        const config = this.getProviderConfig(provider);
        return [...config.defaultModels];
    }

    /**
     * Check if a provider supports streaming
     */
    public supportsStreaming(provider: SupportedProvider): boolean {
        const config = this.getProviderConfig(provider);
        return config.supportsStreaming;
    }

    /**
     * Get the API endpoint for a provider
     */
    public getApiEndpoint(provider: SupportedProvider): string {
        const config = this.getProviderConfig(provider);
        return config.apiEndpoint;
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._changeEmitter.dispose();
    }
}
