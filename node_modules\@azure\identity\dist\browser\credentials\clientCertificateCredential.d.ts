import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * Enables authentication to Microsoft Entra ID using a PEM-encoded
 * certificate that is assigned to an App Registration.
 */
export declare class ClientCertificateCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=clientCertificateCredential-browser.d.mts.map