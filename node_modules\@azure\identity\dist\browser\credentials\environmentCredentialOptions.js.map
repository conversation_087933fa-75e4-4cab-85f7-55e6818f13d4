{"version": 3, "file": "environmentCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/environmentCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Enables authentication to Microsoft Entra ID depending on the available environment variables.\n * Defines options for the EnvironmentCredential class.\n */\nexport interface EnvironmentCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {}\n"]}