{"version": 3, "file": "table.js", "sourceRoot": "", "sources": ["../../src/formatters/table.ts"], "names": [], "mappings": "AAEA,gFAAgF;AAChF,eAAe;AACf,gFAAgF;AAChF,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC9B,OAAO,SAAS,MAAM,YAAY,CAAC;AACnC,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,gFAAgF;AAChF,UAAU;AACV,gFAAgF;AAEhF;;;;GAIG;AACH,SAAS,SAAS,CAAC,QAAuC;IACtD,IAAI,IAAI,GAAQ,EAAE,CAAC;IAEnB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE,CAAC;KACb;IAED,IAAI,CAAC,IAAI,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAClB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;QACrB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;KACxB,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO;QAC9B,IAAI,WAAW,CAAC;QAEhB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC9B,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACpC;aAAM;YACH,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAChH,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE;QACvB,OAAO,EAAE;YACL,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACjB;YACD,CAAC,EAAE;gBACC,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,IAAI;aACjB;SACJ;QACD,kBAAkB,EAAE,UAAU,KAAa;YACvC,OAAO,KAAK,KAAK,CAAC,CAAC;QACvB,CAAC;KACJ,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CAAC,OAA+B;IAC/C,IAAI,KAAK,CAAC;IAEV,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM;QAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzB,OAAO,EAAE,CAAC;SACb;QAED,OAAO,IAAI,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,OAAe;QAC1C,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,MAAM,SAAS,GAAwB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IACxD,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC;IACvC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;QAC3B,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChC,YAAY,IAAI,CAAC,CAAC;aACrB;iBAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;gBACrC,UAAU,IAAI,CAAC,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,IAAI,UAAU,IAAI,YAAY,EAAE;QAC5B,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;KAChC;IAED,MAAM;QACF,IAAI;YACJ,KAAK,CACD;gBACI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;gBACjD,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;aAC3D,EACD;gBACI,OAAO,EAAE;oBACL,CAAC,EAAE;wBACC,KAAK,EAAE,GAAG;wBACV,QAAQ,EAAE,IAAI;qBACjB;iBACJ;gBACD,kBAAkB,EAAE;oBAChB,OAAO,IAAI,CAAC;gBAChB,CAAC;aACJ,CACJ,CAAC;IAEN,IAAI,CAAC,QAAQ,EAAE;QACX,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;KAC5B;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,eAAe,SAAS,CAAC"}