{"version": 3, "file": "msal-browser.mjs", "sourceRoot": "", "sources": ["../../../src/msal/msal-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,UAAU,MAAM,qBAAqB,CAAC;AAElD,OAAO,EAAE,UAAU,EAAE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as msalCommon from \"@azure/msal-browser\";\n\nexport { msalCommon };\n"]}