{"version": 3, "file": "defaultAzureCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Provides options to configure the {@link DefaultAzureCredential} class.\n * This variation supports `managedIdentityClientId` and not `managedIdentityResourceId`, since only one of both is supported.\n */\nexport interface DefaultAzureCredentialClientIdOptions extends DefaultAzureCredentialOptions {\n  /**\n   * Optionally pass in a user assigned client ID to be used by the {@link ManagedIdentityCredential}.\n   * This client ID can also be passed through to the {@link ManagedIdentityCredential} through the environment variable: AZURE_CLIENT_ID.\n   */\n  managedIdentityClientId?: string;\n  /**\n   * Optionally pass in a user assigned client ID to be used by the {@link WorkloadIdentityCredential}.\n   * This client ID can also be passed through to the {@link WorkloadIdentityCredential} through the environment variable: AZURE_CLIENT_ID.\n   */\n  workloadIdentityClientId?: string;\n}\n\n/**\n * Provides options to configure the {@link DefaultAzureCredential} class.\n * This variation supports `managedIdentityResourceId` and not `managedIdentityClientId`, since only one of both is supported.\n */\nexport interface DefaultAzureCredentialResourceIdOptions extends DefaultAzureCredentialOptions {\n  /**\n   * Optionally pass in a resource ID to be used by the {@link ManagedIdentityCredential}.\n   * In scenarios such as when user assigned identities are created using an ARM template,\n   * where the resource Id of the identity is known but the client Id can't be known ahead of time,\n   * this parameter allows programs to use these user assigned identities\n   * without having to first determine the client Id of the created identity.\n   */\n  managedIdentityResourceId: string;\n}\n\n/**\n * Provides options to configure the {@link DefaultAzureCredential} class.\n */\nexport interface DefaultAzureCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {\n  /**\n   * Optionally pass in a Tenant ID to be used as part of the credential.\n   * By default it may use a generic tenant ID depending on the underlying credential.\n   */\n  tenantId?: string;\n\n  /**\n   * Timeout configurable for making token requests for developer credentials, namely, {@link AzurePowershellCredential},\n   * {@link AzureDeveloperCliCredential} and {@link AzureCliCredential}.\n   * Process timeout for credentials should be provided in milliseconds.\n   */\n  processTimeoutInMs?: number;\n}\n"]}