import * as vscode from 'vscode';
import * as path from 'path';
import { FileOperationsService } from './FileOperationsService';
import { ActionHistoryManager } from '../actionHistoryManager';
import { TerminalService } from './terminalService';
import { UnifiedExecutionService } from './UnifiedExecutionService';

export interface PlanOperation {
    id: string;
    type: 'create' | 'modify' | 'create_directory' | 'delete' | 'rename';
    path: string;
    content?: string;
    newPath?: string;
    language?: string;
}

export interface ExecutionResult {
    operationId: string;
    success: boolean;
    message: string;
    error?: string;
    uri?: vscode.Uri;
}

export interface PlanExecutionSummary {
    totalOperations: number;
    successful: number;
    failed: number;
    results: ExecutionResult[];
    executionTime: number;
}

/**
 * Service responsible for executing AI-generated plans directly in the workspace
 * without requiring user interaction through webview buttons
 */
export class PlanExecutorService {
    private fileOperationsService: FileOperationsService;
    private actionHistoryManager: ActionHistoryManager;
    private terminalService: TerminalService;
    private outputChannel: vscode.OutputChannel;
    private unifiedExecutionService: UnifiedExecutionService;

    constructor(
        fileOperationsService: FileOperationsService,
        actionHistoryManager: ActionHistoryManager,
        terminalService: TerminalService
    ) {
        this.fileOperationsService = fileOperationsService;
        this.actionHistoryManager = actionHistoryManager;
        this.terminalService = terminalService;
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama Plan Executor');

        // Initialize unified execution service
        this.unifiedExecutionService = new UnifiedExecutionService(
            fileOperationsService,
            terminalService,
            actionHistoryManager
        );
    }

    /**
     * Execute a complete plan of operations automatically
     */
    public async executePlan(operations: PlanOperation[]): Promise<PlanExecutionSummary> {
        const startTime = Date.now();
        const results: ExecutionResult[] = [];

        this.outputChannel.appendLine(`\n=== Plan Execution Started ===`);
        this.outputChannel.appendLine(`Operations to execute: ${operations.length}`);
        this.outputChannel.show(true);

        // Execute operations sequentially for safety and dependency management
        for (const operation of operations) {
            try {
                this.outputChannel.appendLine(`\nExecuting: ${operation.type} ${operation.path}`);
                
                const result = await this.executeOperation(operation);
                results.push(result);

                if (result.success) {
                    this.outputChannel.appendLine(`✅ Success: ${operation.path}`);
                    this.actionHistoryManager.logAction(`Executed ${operation.type}: ${operation.path}`);
                    
                    // Show non-blocking success notification
                    vscode.window.showInformationMessage(result.message);
                } else {
                    this.outputChannel.appendLine(`❌ Failed: ${operation.path} - ${result.error}`);
                    
                    // Show error notification
                    vscode.window.showErrorMessage(`Failed to ${operation.type} ${operation.path}: ${result.error}`);
                }

            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                const result: ExecutionResult = {
                    operationId: operation.id,
                    success: false,
                    message: `Failed to execute ${operation.type} operation`,
                    error: errorMessage
                };
                
                results.push(result);
                this.outputChannel.appendLine(`❌ Exception: ${operation.path} - ${errorMessage}`);
                
                // Show error notification
                vscode.window.showErrorMessage(`Error executing ${operation.type} ${operation.path}: ${errorMessage}`);
            }
        }

        const executionTime = Date.now() - startTime;
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        this.outputChannel.appendLine(`\n=== Plan Execution Completed ===`);
        this.outputChannel.appendLine(`Total: ${operations.length}, Successful: ${successful}, Failed: ${failed}`);
        this.outputChannel.appendLine(`Execution time: ${executionTime}ms`);

        // Log summary to action history
        this.actionHistoryManager.logAction(
            `Plan execution completed: ${successful} successful, ${failed} failed (${executionTime}ms)`
        );

        // Show summary notification
        if (failed === 0) {
            vscode.window.showInformationMessage(
                `✅ Plan executed successfully! ${successful} operations completed.`
            );
        } else {
            vscode.window.showWarningMessage(
                `⚠️ Plan execution completed with ${failed} failures. ${successful} operations succeeded.`
            );
        }

        return {
            totalOperations: operations.length,
            successful,
            failed,
            results,
            executionTime
        };
    }

    /**
     * Execute a single operation
     */
    private async executeOperation(operation: PlanOperation): Promise<ExecutionResult> {
        try {
            // Validate operation before execution
            this.validateOperation(operation);

            switch (operation.type) {
                case 'create':
                    return await this.executeCreateOperation(operation);
                case 'modify':
                    return await this.executeModifyOperation(operation);
                case 'create_directory':
                    return await this.executeCreateDirectoryOperation(operation);
                case 'delete':
                    return await this.executeDeleteOperation(operation);
                case 'rename':
                    return await this.executeRenameOperation(operation);
                default:
                    throw new Error(`Unsupported operation type: ${operation.type}`);
            }
        } catch (error) {
            return {
                operationId: operation.id,
                success: false,
                message: `Failed to execute ${operation.type} operation`,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * Execute create file operation (public method for ExecutionStateManager)
     */
    public async executeCreateFile(path: string, content: string): Promise<void> {
        const result = await this.fileOperationsService.createFile(path, content);
        if (!result.success) {
            throw new Error(result.error || 'Failed to create file');
        }

        // Open the created file in editor
        if (result.uri) {
            await vscode.window.showTextDocument(result.uri);
        }
    }

    /**
     * Execute modify file operation (public method for ExecutionStateManager)
     */
    public async executeModifyFile(path: string, content: string): Promise<void> {
        const result = await this.fileOperationsService.modifyFile(path, content);
        if (!result.success) {
            throw new Error(result.error || 'Failed to modify file');
        }

        // Open the modified file in editor
        if (result.uri) {
            await vscode.window.showTextDocument(result.uri);
        }
    }

    /**
     * Execute delete file operation (public method for ExecutionStateManager)
     */
    public async executeDeleteFile(path: string): Promise<void> {
        const result = await this.fileOperationsService.deleteFile(path);
        if (!result.success) {
            throw new Error(result.error || 'Failed to delete file');
        }
    }

    /**
     * Execute create directory operation (public method for ExecutionStateManager)
     */
    public async executeCreateDirectory(path: string): Promise<void> {
        const result = await this.fileOperationsService.createDirectory(path);
        if (!result.success) {
            throw new Error(result.error || 'Failed to create directory');
        }
    }

    /**
     * Execute terminal command (public method for ExecutionStateManager)
     */
    public async executeCommand(command: string, options?: {
        terminalName?: string;
        showTerminal?: boolean;
        timeout?: number;
    }): Promise<void> {
        try {
            this.outputChannel.appendLine(`🔧 Executing command: ${command}`);

            // Check if command is safe to execute automatically
            const isSafeCommand = await this.isSafeCommand(command);
            if (!isSafeCommand) {
                throw new Error(`Command not approved for automatic execution: ${command}`);
            }

            // Execute command using TerminalService
            await this.terminalService.executeCommand(command, {
                timeout: options?.timeout || 30000
            });

            this.outputChannel.appendLine(`✅ Command executed successfully: ${command}`);
            this.actionHistoryManager.logAction(`Executed command: ${command}`);

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`❌ Command execution failed: ${errorMessage}`);
            throw new Error(`Failed to execute command "${command}": ${errorMessage}`);
        }
    }

    /**
     * Check if a command is safe for automatic execution
     */
    private async isSafeCommand(command: string): Promise<boolean> {
        const safeCommands = [
            // File system operations (read-only)
            'ls', 'dir', 'pwd', 'whoami', 'date', 'echo', 'cat', 'type',

            // Git operations (safe)
            'git status', 'git log', 'git branch', 'git diff', 'git show',
            'git remote -v', 'git config --list',

            // Package manager operations (info only)
            'npm list', 'npm outdated', 'npm audit', 'npm --version',
            'yarn list', 'yarn outdated', 'yarn --version',
            'pnpm list', 'pnpm outdated', 'pnpm --version',

            // Version checks
            'node --version', 'python --version', 'java -version',
            'dotnet --version', 'go version', 'rustc --version',

            // Build operations (generally safe)
            'npm run build', 'npm run test', 'npm run lint',
            'yarn build', 'yarn test', 'yarn lint',
            'pnpm build', 'pnpm test', 'pnpm lint',
            'mvn compile', 'gradle build', 'dotnet build',

            // Development server operations
            'npm start', 'npm run dev', 'npm run serve',
            'yarn start', 'yarn dev', 'yarn serve',
            'pnpm start', 'pnpm dev', 'pnpm serve'
        ];

        // Check if command starts with any safe command
        const commandLower = command.toLowerCase().trim();
        return safeCommands.some(safe => commandLower.startsWith(safe.toLowerCase()));
    }

    /**
     * Execute create file operation (private method for legacy plan operations)
     */
    private async executeCreateOperation(operation: PlanOperation): Promise<ExecutionResult> {
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        // Check if file already exists
        const fileExists = await this.fileOperationsService.fileExists(operation.path);
        if (fileExists) {
            throw new Error(`File already exists: ${operation.path}`);
        }

        // Create the file using FileOperationsService
        const result = await this.fileOperationsService.createFile(
            operation.path, 
            operation.content || ''
        );

        if (!result.success) {
            throw new Error(result.error || 'Failed to create file');
        }

        // Open the created file in editor
        if (result.uri) {
            await vscode.window.showTextDocument(result.uri, { preview: false });
        }

        return {
            operationId: operation.id,
            success: true,
            message: `Created file: ${operation.path}`,
            uri: result.uri
        };
    }

    /**
     * Execute modify file operation
     */
    private async executeModifyOperation(operation: PlanOperation): Promise<ExecutionResult> {
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        // Check if file exists
        const fileExists = await this.fileOperationsService.fileExists(operation.path);
        if (!fileExists) {
            throw new Error(`File does not exist: ${operation.path}`);
        }

        // Modify the file using FileOperationsService
        const result = await this.fileOperationsService.modifyFile(
            operation.path,
            operation.content || ''
        );

        if (!result.success) {
            throw new Error(result.error || 'Failed to modify file');
        }

        // Open the modified file in editor
        const fullPath = path.resolve(workspaceFolder.uri.fsPath, operation.path);
        const uri = vscode.Uri.file(fullPath);
        await vscode.window.showTextDocument(uri, { preview: false });

        return {
            operationId: operation.id,
            success: true,
            message: `Modified file: ${operation.path}`,
            uri
        };
    }

    /**
     * Execute create directory operation
     */
    private async executeCreateDirectoryOperation(operation: PlanOperation): Promise<ExecutionResult> {
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        // Create directory using FileOperationsService
        const result = await this.fileOperationsService.createDirectory(operation.path);

        if (!result.success) {
            throw new Error(result.error || 'Failed to create directory');
        }

        return {
            operationId: operation.id,
            success: true,
            message: `Created directory: ${operation.path}`,
            uri: result.uri
        };
    }

    /**
     * Execute delete operation
     */
    private async executeDeleteOperation(operation: PlanOperation): Promise<ExecutionResult> {
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        // Delete using FileOperationsService
        const result = await this.fileOperationsService.deleteFile(operation.path);

        if (!result.success) {
            throw new Error(result.error || 'Failed to delete file');
        }

        return {
            operationId: operation.id,
            success: true,
            message: `Deleted: ${operation.path}`
        };
    }

    /**
     * Execute rename operation
     */
    private async executeRenameOperation(operation: PlanOperation): Promise<ExecutionResult> {
        if (!operation.newPath) {
            throw new Error('New path is required for rename operation');
        }

        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        // Rename using FileOperationsService
        const result = await this.fileOperationsService.renameFile(operation.path, operation.newPath);

        if (!result.success) {
            throw new Error(result.error || 'Failed to rename file');
        }

        return {
            operationId: operation.id,
            success: true,
            message: `Renamed ${operation.path} to ${operation.newPath}`,
            uri: result.uri
        };
    }

    /**
     * Validate operation before execution
     */
    private validateOperation(operation: PlanOperation): void {
        if (!operation.path || operation.path.trim() === '') {
            throw new Error('Operation path cannot be empty');
        }

        // Validate path is relative and safe
        if (path.isAbsolute(operation.path)) {
            throw new Error('Operation path must be relative to workspace');
        }

        // Check for path traversal attempts
        if (operation.path.includes('..')) {
            throw new Error('Path traversal not allowed');
        }

        // Validate operation-specific requirements
        if (operation.type === 'rename' && !operation.newPath) {
            throw new Error('New path is required for rename operation');
        }
    }

    /**
     * Get the current workspace folder
     */
    private getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
        return vscode.workspace.workspaceFolders?.[0];
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.outputChannel.dispose();
    }
}
