{"version": 3, "file": "defaultHttpClient-react-native.mjs", "sourceRoot": "", "sources": ["../../src/defaultHttpClient-react-native.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAEzD;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,OAAO,mBAAmB,EAAE,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createXhrHttpClient } from \"./xhrHttpClient.js\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createXhrHttpClient();\n}\n"]}