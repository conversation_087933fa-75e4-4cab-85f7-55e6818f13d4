/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

/**
 * DeviceCode returned by the security token service device code endpoint containing information necessary for device code flow.
 * - userCode: code which user needs to provide when authenticating at the verification URI
 * - deviceCode: code which should be included in the request for the access token
 * - verificationUri: URI where user can authenticate
 * - expiresIn: expiration time of the device code in seconds
 * - interval: interval at which the STS should be polled at
 * - message: message which should be displayed to the user
 */
export type DeviceCodeResponse = {
    userCode: string;
    deviceCode: string;
    verificationUri: string;
    expiresIn: number;
    interval: number;
    message: string;
};

export type ServerDeviceCodeResponse = {
    user_code: string;
    device_code: string;
    verification_uri: string;
    expires_in: number;
    interval: number;
    message: string;
};
