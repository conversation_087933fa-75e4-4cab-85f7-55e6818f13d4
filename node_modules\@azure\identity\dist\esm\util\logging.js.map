{"version": 3, "file": "logging.js", "sourceRoot": "", "sources": ["../../../src/util/logging.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AAEnD;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAOrD;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,gBAA0B;IACvD,OAAO,gBAAgB,CAAC,MAAM,CAC5B,CAAC,GAA2B,EAAE,WAAmB,EAAE,EAAE;QACnD,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAC9B,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,cAAsB,EAAE,gBAA0B;IAC3E,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,gBAAgB,CAAC,CAAC;IACtD,MAAM,CAAC,IAAI,CACT,GAAG,cAAc,kDAAkD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACzF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,KAAwB;IACpD,OAAO,oBAAoB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AAChF,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAoC,EAAE,KAAqB;IACrF,IAAI,OAAO,GAAG,QAAQ,CAAC;IACvB,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;QAClB,OAAO,IAAI,YAAY,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;IAC5E,CAAC;IACD,OAAO,GAAG,OAAO,mBAAmB,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;AAC3F,CAAC;AAcD;;;;;;;GAOG;AACH,MAAM,UAAU,wBAAwB,CACtC,KAAa,EACb,MAAiC,EACjC,MAAmB,MAAM;IAEzB,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAElE,SAAS,IAAI,CAAC,OAAe;QAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,SAAS,KAAK,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,SAAS,OAAO,CAAC,OAAe;QAC9B,GAAG,CAAC,OAAO,CAAC,GAAG,SAAS,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,SAAS,OAAO,CAAC,OAAe;QAC9B,GAAG,CAAC,OAAO,CAAC,GAAG,SAAS,KAAK,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,SAAS,KAAK,CAAC,OAAe;QAC5B,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,OAAO;QACL,KAAK;QACL,SAAS;QACT,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;KACN,CAAC;AACJ,CAAC;AAWD;;;;;;;;;GASG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAa,EAAE,MAAmB,MAAM;IACvE,MAAM,UAAU,GAAG,wBAAwB,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IACnE,uCACK,UAAU,KACb,MAAM,EAAE,GAAG,EACX,QAAQ,EAAE,wBAAwB,CAAC,eAAe,EAAE,UAAU,EAAE,GAAG,CAAC,IACpE;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AzureLogger } from \"@azure/logger\";\nimport { createClientLogger } from \"@azure/logger\";\n\n/**\n * The AzureLogger used for all clients within the identity package\n */\nexport const logger = createClientLogger(\"identity\");\n\ninterface EnvironmentAccumulator {\n  missing: string[];\n  assigned: string[];\n}\n\n/**\n * Separates a list of environment variable names into a plain object with two arrays: an array of missing environment variables and another array with assigned environment variables.\n * @param supportedEnvVars - List of environment variable names\n */\nexport function processEnvVars(supportedEnvVars: string[]): EnvironmentAccumulator {\n  return supportedEnvVars.reduce(\n    (acc: EnvironmentAccumulator, envVariable: string) => {\n      if (process.env[envVariable]) {\n        acc.assigned.push(envVariable);\n      } else {\n        acc.missing.push(envVariable);\n      }\n      return acc;\n    },\n    { missing: [], assigned: [] },\n  );\n}\n\n/**\n * Based on a given list of environment variable names,\n * logs the environment variables currently assigned during the usage of a credential that goes by the given name.\n * @param credentialName - Name of the credential in use\n * @param supportedEnvVars - List of environment variables supported by that credential\n */\nexport function logEnvVars(credentialName: string, supportedEnvVars: string[]): void {\n  const { assigned } = processEnvVars(supportedEnvVars);\n  logger.info(\n    `${credentialName} => Found the following environment variables: ${assigned.join(\", \")}`,\n  );\n}\n\n/**\n * Formatting the success event on the credentials\n */\nexport function formatSuccess(scope: string | string[]): string {\n  return `SUCCESS. Scopes: ${Array.isArray(scope) ? scope.join(\", \") : scope}.`;\n}\n\n/**\n * Formatting the success event on the credentials\n */\nexport function formatError(scope: string | string[] | undefined, error: Error | string): string {\n  let message = \"ERROR.\";\n  if (scope?.length) {\n    message += ` Scopes: ${Array.isArray(scope) ? scope.join(\", \") : scope}.`;\n  }\n  return `${message} Error message: ${typeof error === \"string\" ? error : error.message}.`;\n}\n\n/**\n * A CredentialLoggerInstance is a logger properly formatted to work in a credential's constructor, and its methods.\n */\nexport interface CredentialLoggerInstance {\n  title: string;\n  fullTitle: string;\n  info(message: string): void;\n  warning(message: string): void;\n  verbose(message: string): void;\n  error(err: string): void;\n}\n\n/**\n * Generates a CredentialLoggerInstance.\n *\n * It logs with the format:\n *\n *   `[title] => [message]`\n *\n */\nexport function credentialLoggerInstance(\n  title: string,\n  parent?: CredentialLoggerInstance,\n  log: AzureLogger = logger,\n): CredentialLoggerInstance {\n  const fullTitle = parent ? `${parent.fullTitle} ${title}` : title;\n\n  function info(message: string): void {\n    log.info(`${fullTitle} =>`, message);\n  }\n\n  function warning(message: string): void {\n    log.warning(`${fullTitle} =>`, message);\n  }\n\n  function verbose(message: string): void {\n    log.verbose(`${fullTitle} =>`, message);\n  }\n\n  function error(message: string): void {\n    log.error(`${fullTitle} =>`, message);\n  }\n\n  return {\n    title,\n    fullTitle,\n    info,\n    warning,\n    verbose,\n    error,\n  };\n}\n\n/**\n * A CredentialLogger is a logger declared at the credential's constructor, and used at any point in the credential.\n * It has all the properties of a CredentialLoggerInstance, plus other logger instances, one per method.\n */\nexport interface CredentialLogger extends CredentialLoggerInstance {\n  parent: AzureLogger;\n  getToken: CredentialLoggerInstance;\n}\n\n/**\n * Generates a CredentialLogger, which is a logger declared at the credential's constructor, and used at any point in the credential.\n * It has all the properties of a CredentialLoggerInstance, plus other logger instances, one per method.\n *\n * It logs with the format:\n *\n *   `[title] => [message]`\n *   `[title] => getToken() => [message]`\n *\n */\nexport function credentialLogger(title: string, log: AzureLogger = logger): CredentialLogger {\n  const credLogger = credentialLoggerInstance(title, undefined, log);\n  return {\n    ...credLogger,\n    parent: log,\n    getToken: credentialLoggerInstance(\"=> getToken()\", credLogger, log),\n  };\n}\n"]}