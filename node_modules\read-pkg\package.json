{"name": "read-pkg", "version": "8.1.0", "description": "Read a package.json file", "license": "MIT", "repository": "sindresorhus/read-pkg", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && tsd && cd test && ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "package", "normalize"], "dependencies": {"@types/normalize-package-data": "^2.4.1", "normalize-package-data": "^6.0.0", "parse-json": "^7.0.0", "type-fest": "^4.2.0"}, "devDependencies": {"ava": "^5.3.1", "tsd": "^0.28.1", "xo": "^0.56.0"}}