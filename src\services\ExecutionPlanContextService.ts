/**
 * Service that integrates execution plans with the context engine
 * Provides seamless tracking and context-aware execution planning
 */

import * as vscode from 'vscode';
import { ContextEngineService } from './ContextEngineService';
import { ExecutionStateManager } from './ExecutionStateManager';
import { ExecutionPlan, ExecutionStep } from '../interfaces/ILLMService';
import { ExecutionPlanContext } from '../interfaces/IContextEngineService';

export interface ExecutionPlanIntegration {
    contextEngine: ContextEngineService;
    executionStateManager: ExecutionStateManager;
}

export class ExecutionPlanContextService {
    public contextEngine: ContextEngineService;
    private executionStateManager: ExecutionStateManager;
    private disposables: vscode.Disposable[] = [];

    constructor(integration: ExecutionPlanIntegration) {
        this.contextEngine = integration.contextEngine;
        this.executionStateManager = integration.executionStateManager;
        
        this.setupEventListeners();
    }

    /**
     * Create an execution plan with context awareness
     */
    public async createContextAwareExecutionPlan(
        userPrompt: string,
        targetFiles?: string[]
    ): Promise<ExecutionPlan> {
        try {
            // Get enhanced context for plan generation
            const contextString = await this.contextEngine.getExecutionPlanContext(userPrompt, targetFiles);
            
            // Analyze potential impact
            const relatedFiles = targetFiles || [];
            const relatedContext = await this.contextEngine.getRelatedFiles(
                relatedFiles[0] || vscode.window.activeTextEditor?.document.uri.fsPath || '',
                2
            );

            // Create a basic execution plan structure
            // In a real implementation, this would be generated by the LLM
            const plan: ExecutionPlan = {
                id: `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                description: `Execute: ${userPrompt}`,
                steps: this.generateStepsFromPrompt(userPrompt, [...relatedFiles, ...relatedContext]),
                estimatedDuration: 300, // 5 minutes default
                dependencies: relatedContext
            };

            // Add to context engine for tracking
            await this.contextEngine.addExecutionPlan(plan, userPrompt);

            return plan;
        } catch (error) {
            console.error('ExecutionPlanContextService: Failed to create context-aware plan', error);
            throw error;
        }
    }

    /**
     * Execute a plan with context engine integration
     */
    public async executeWithContextTracking(plan: ExecutionPlan): Promise<void> {
        try {
            // Update plan status in context engine
            await this.contextEngine.updateExecutionPlan(plan.id, {
                status: 'executing',
                currentStep: 0
            });

            // Set plan in execution state manager
            this.executionStateManager.setPlan(plan);

            // Start execution
            await this.executionStateManager.executePlan();

            console.log(`ExecutionPlanContextService: Started execution of plan ${plan.id}`);
        } catch (error) {
            console.error('ExecutionPlanContextService: Failed to execute plan', error);
            
            // Update status to failed
            await this.contextEngine.updateExecutionPlan(plan.id, {
                status: 'failed',
                error: error instanceof Error ? error.message : String(error)
            });
            
            throw error;
        }
    }

    /**
     * Get execution plans related to current context
     */
    public async getContextualExecutionPlans(): Promise<ExecutionPlanContext[]> {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return [];
        }

        const currentFile = activeEditor.document.uri.fsPath;
        const relatedFiles = await this.contextEngine.getRelatedFiles(currentFile, 2);
        
        return this.contextEngine.getExecutionPlansForFiles([currentFile, ...relatedFiles]);
    }

    /**
     * Analyze execution plan impact using context engine
     */
    public async analyzeExecutionPlanImpact(plan: ExecutionPlan) {
        return this.contextEngine.analyzeExecutionPlanImpact(plan);
    }

    /**
     * Get execution plan recommendations based on current context
     */
    public async getExecutionPlanRecommendations(userPrompt: string): Promise<{
        suggestedFiles: string[];
        potentialConflicts: string[];
        relatedPlans: ExecutionPlanContext[];
        complexity: 'low' | 'medium' | 'high';
    }> {
        try {
            const activeEditor = vscode.window.activeTextEditor;
            const currentFile = activeEditor?.document.uri.fsPath;
            
            // Get related files
            const suggestedFiles = currentFile 
                ? await this.contextEngine.getRelatedFiles(currentFile, 2)
                : [];

            // Get related execution plans
            const relatedPlans = await this.contextEngine.getExecutionPlans({
                query: userPrompt,
                maxResults: 3,
                includeCompleted: false
            });

            // Create a temporary plan for impact analysis
            const tempPlan: ExecutionPlan = {
                id: 'temp',
                description: userPrompt,
                steps: this.generateStepsFromPrompt(userPrompt, suggestedFiles)
            };

            const impact = await this.contextEngine.analyzeExecutionPlanImpact(tempPlan);

            return {
                suggestedFiles,
                potentialConflicts: impact.potentialConflicts,
                relatedPlans,
                complexity: impact.complexity
            };
        } catch (error) {
            console.error('ExecutionPlanContextService: Failed to get recommendations', error);
            return {
                suggestedFiles: [],
                potentialConflicts: [],
                relatedPlans: [],
                complexity: 'low'
            };
        }
    }

    /**
     * Update execution plan progress
     */
    public async updateExecutionProgress(
        planId: string,
        stepIndex: number,
        status: 'completed' | 'failed',
        error?: string
    ): Promise<void> {
        const planContext = await this.contextEngine.getExecutionPlan(planId);
        if (!planContext) {
            return;
        }

        const updates: Partial<ExecutionPlanContext> = {
            currentStep: stepIndex
        };

        if (status === 'completed') {
            updates.executedSteps = [...planContext.executedSteps, planContext.plan.steps[stepIndex].id];
        } else if (status === 'failed') {
            updates.failedSteps = [...planContext.failedSteps, planContext.plan.steps[stepIndex].id];
            if (error) {
                updates.error = error;
            }
        }

        // Check if plan is complete
        if (stepIndex >= planContext.plan.steps.length - 1) {
            updates.status = status === 'completed' ? 'completed' : 'failed';
            updates.completedAt = new Date();
        }

        await this.contextEngine.updateExecutionPlan(planId, updates);
    }

    /**
     * Setup event listeners for execution state changes
     */
    private setupEventListeners(): void {
        // Listen for execution state changes
        const eventListener = async (event: any) => {
            const context = this.executionStateManager.context;
            if (context.currentPlan) {
                const updates: Partial<ExecutionPlanContext> = {};

                switch (event.state) {
                    case 'executing':
                        updates.status = 'executing';
                        updates.startedAt = new Date();
                        break;
                    case 'paused':
                        updates.pausedAt = new Date();
                        break;
                    case 'idle':
                        if (context.completedAt) {
                            updates.status = 'completed';
                            updates.completedAt = context.completedAt;
                        }
                        break;
                    case 'error':
                        updates.status = 'failed';
                        updates.error = context.error;
                        break;
                }

                if (Object.keys(updates).length > 0) {
                    await this.contextEngine.updateExecutionPlan(context.currentPlan.id, updates);
                }
            }
        };

        this.executionStateManager.addEventListener(eventListener);

        // Store the listener for cleanup
        this.disposables.push({
            dispose: () => {
                this.executionStateManager.removeEventListener(eventListener);
            }
        });
    }

    /**
     * Generate basic execution steps from user prompt
     * In a real implementation, this would be done by the LLM
     */
    private generateStepsFromPrompt(prompt: string, relatedFiles: string[]): ExecutionStep[] {
        const steps: ExecutionStep[] = [];
        let stepOrder = 1;

        // Simple heuristics for step generation
        if (prompt.toLowerCase().includes('create') || prompt.toLowerCase().includes('add')) {
            steps.push({
                id: `step_${stepOrder++}`,
                type: 'file_create',
                description: `Create file based on: ${prompt}`,
                order: stepOrder - 1
            });
        }

        if (prompt.toLowerCase().includes('modify') || prompt.toLowerCase().includes('update')) {
            for (const file of relatedFiles.slice(0, 3)) {
                steps.push({
                    id: `step_${stepOrder++}`,
                    type: 'file_modify',
                    description: `Modify ${file}`,
                    path: file,
                    order: stepOrder - 1
                });
            }
        }

        if (prompt.toLowerCase().includes('run') || prompt.toLowerCase().includes('execute')) {
            steps.push({
                id: `step_${stepOrder++}`,
                type: 'command_run',
                description: `Execute command for: ${prompt}`,
                order: stepOrder - 1
            });
        }

        // Default step if no specific actions detected
        if (steps.length === 0) {
            steps.push({
                id: `step_${stepOrder}`,
                type: 'file_modify',
                description: `Process request: ${prompt}`,
                order: 1
            });
        }

        return steps;
    }

    /**
     * Dispose resources
     */
    public dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}
