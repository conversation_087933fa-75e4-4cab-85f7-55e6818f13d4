{"name": "structured-source", "version": "4.0.0", "description": "Provides StructuredSource and functionality for converting range and loc vice versa.", "keywords": ["location", "range", "abstract", "syntax", "tree"], "homepage": "https://github.com/textlint/structured-source", "bugs": {"url": "https://github.com/textlint/structured-source/issues"}, "repository": {"type": "git", "url": "https://github.com/textlint/structured-source.git"}, "license": "BSD-2-<PERSON><PERSON>", "author": "<PERSON><PERSON>", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "main": "lib/structured-source.js", "types": "lib/structured-source.d.ts", "files": ["lib"], "scripts": {"build": "tsc -p .", "prepublishOnly": "npm run build", "test": "mocha \"test/**/*.{js,ts}\"", "watch": "tsc -p . --watch"}, "dependencies": {"boundary": "^2.0.0"}, "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^18.11.18", "mocha": "^10.2.0", "ts-node": "^10.9.1", "ts-node-test-register": "^10.0.0", "typescript": "^4.9.4"}, "packageManager": "npm@8.19.2"}