import type { AccessToken } from "@azure/core-auth";
import { ChainedTokenCredential } from "./chainedTokenCredential.js";
import type { TokenCredentialOptions } from "../tokenCredentialOptions.js";
/**
 * Provides a default {@link ChainedTokenCredential} configuration for
 * applications that will be deployed to Azure.
 *
 * Only available in Node.js.
 */
export declare class DefaultAzureCredential extends ChainedTokenCredential {
    /**
     * Creates an instance of the DefaultAzureCredential class.
     *
     * @param options - Options for configuring the client which makes the authentication request.
     */
    constructor(_tokenCredentialOptions?: TokenCredentialOptions);
    getToken(): Promise<AccessToken>;
}
//# sourceMappingURL=defaultAzureCredential-browser.d.mts.map