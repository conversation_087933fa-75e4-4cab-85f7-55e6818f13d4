"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentPolicyName = void 0;
exports.agentPolicy = agentPolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * Name of the Agent Policy
 */
exports.agentPolicyName = policies_1.agentPolicyName;
/**
 * Gets a pipeline policy that sets http.agent
 */
function agentPolicy(agent) {
    return (0, policies_1.agentPolicy)(agent);
}
//# sourceMappingURL=agentPolicy.js.map