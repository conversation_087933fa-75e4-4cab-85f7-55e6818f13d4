module.exports = {
    root: true,
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
        project: './tsconfig.json',
    },
    plugins: [
        '@typescript-eslint',
    ],
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
    ],
    env: {
        node: true,
        es6: true,
    },
    ignorePatterns: [
        'node_modules/',
        'dist/',
        'out/',
        'webview-ui/',
        '*.js',
        '*.d.ts',
    ],
    rules: {
        // Disable some strict rules for development
        '@typescript-eslint/no-unused-vars': 'warn',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        '@typescript-eslint/ban-ts-comment': 'off',
        
        // General rules
        'no-console': 'off',
        'prefer-const': 'warn',
        'no-var': 'error',
        'no-undef': 'off', // TypeScript handles this
        
        // Import/export rules
        'no-duplicate-imports': 'error',
        
        // Code style
        'semi': ['error', 'always'],
        'quotes': ['warn', 'single', { 'allowTemplateLiterals': true }],
        'comma-dangle': ['warn', 'never'],
        
        // Error prevention
        'no-unreachable': 'error',
        'no-unused-expressions': 'warn',
        'no-constant-condition': 'warn',
    },
    overrides: [
        {
            files: ['src/**/*.ts'],
            rules: {
                // Specific rules for TypeScript source files
                '@typescript-eslint/explicit-function-return-type': 'off',
                '@typescript-eslint/explicit-module-boundary-types': 'off',
            },
        },
        {
            files: ['src/test/**/*.ts'],
            rules: {
                // More relaxed rules for test files
                '@typescript-eslint/no-unused-vars': 'off',
                '@typescript-eslint/no-explicit-any': 'off',
            },
        },
    ],
};
