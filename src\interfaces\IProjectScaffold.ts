/**
 * Project Scaffold Interfaces - Industry-standard project generation
 */

export interface ProjectTemplate {
    id: string;
    name: string;
    description: string;
    category: ProjectCategory;
    stack: TechnologyStack;
    fileStructure: FileStructureTemplate;
    dependencies: DependencyConfig;
    toolingConfig: ToolingConfig;
    scripts: ScriptConfig;
    metadata: ProjectMetadata;
}

export interface FileStructureTemplate {
    directories: DirectoryTemplate[];
    files: FileTemplate[];
    entryPoints: EntryPointConfig;
}

export interface DirectoryTemplate {
    path: string;
    purpose: string;
    required: boolean;
    children?: DirectoryTemplate[];
}

export interface FileTemplate {
    path: string;
    content: string | (() => string);
    language: string;
    required: boolean;
    dependencies?: string[]; // Files this file depends on
    imports?: ImportConfig[];
}

export interface ImportConfig {
    from: string;
    imports: string[];
    type: 'default' | 'named' | 'namespace';
}

export interface DependencyConfig {
    runtime: Record<string, string>; // package name -> version
    development: Record<string, string>;
    peerDependencies?: Record<string, string>;
    packageManager: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'poetry' | 'cargo';
}

export interface ToolingConfig {
    linting: LintingConfig;
    formatting: FormattingConfig;
    testing: TestingConfig;
    building: BuildConfig;
    typeChecking?: TypeCheckingConfig;
}

export interface LintingConfig {
    tool: 'eslint' | 'pylint' | 'clippy' | 'golangci-lint';
    configFile: string;
    rules: Record<string, any>;
}

export interface FormattingConfig {
    tool: 'prettier' | 'black' | 'rustfmt' | 'gofmt';
    configFile: string;
    options: Record<string, any>;
}

export interface TestingConfig {
    framework: 'jest' | 'vitest' | 'pytest' | 'mocha' | 'cypress';
    configFile: string;
    testDirectory: string;
    setupFiles?: string[];
}

export interface BuildConfig {
    tool: 'vite' | 'webpack' | 'rollup' | 'esbuild' | 'tsc';
    configFile: string;
    outputDirectory: string;
    entryPoint: string;
}

export interface TypeCheckingConfig {
    tool: 'typescript' | 'mypy' | 'flow';
    configFile: string;
    strict: boolean;
}

export interface ScriptConfig {
    dev: string;
    build: string;
    test: string;
    lint: string;
    format: string;
    preview?: string;
    deploy?: string;
}

export interface ProjectMetadata {
    version: string;
    author?: string;
    license: string;
    repository?: string;
    keywords: string[];
    engines?: Record<string, string>;
}

export interface EntryPointConfig {
    main: string;
    index?: string;
    types?: string;
    module?: string;
}

export enum ProjectCategory {
    WEB_FRONTEND = 'web-frontend',
    WEB_BACKEND = 'web-backend',
    FULL_STACK = 'full-stack',
    MOBILE = 'mobile',
    DESKTOP = 'desktop',
    DATA_SCIENCE = 'data-science',
    GAME_DEVELOPMENT = 'game-development',
    CLI_TOOL = 'cli-tool',
    LIBRARY = 'library',
    API = 'api'
}

export enum TechnologyStack {
    // Frontend
    VANILLA_WEB = 'vanilla-web',
    REACT_VITE_TS = 'react-vite-ts',
    VUE_VITE_TS = 'vue-vite-ts',
    SVELTE_VITE_TS = 'svelte-vite-ts',
    ANGULAR_TS = 'angular-ts',
    
    // Backend
    NODE_EXPRESS_TS = 'node-express-ts',
    NODE_FASTIFY_TS = 'node-fastify-ts',
    PYTHON_FASTAPI = 'python-fastapi',
    PYTHON_FLASK = 'python-flask',
    PYTHON_DJANGO = 'python-django',
    
    // Mobile
    REACT_NATIVE_EXPO = 'react-native-expo',
    FLUTTER = 'flutter',
    
    // Desktop
    ELECTRON_TS = 'electron-ts',
    TAURI_RUST = 'tauri-rust',
    
    // Data Science
    PYTHON_JUPYTER = 'python-jupyter',
    PYTHON_DATA_SCIENCE = 'python-data-science',
    
    // Game Development
    PHASER_TS = 'phaser-ts',
    UNITY_CSHARP = 'unity-csharp',
    GODOT_GDSCRIPT = 'godot-gdscript',
    
    // Other
    RUST_CLI = 'rust-cli',
    GO_CLI = 'go-cli',
    PYTHON_CLI = 'python-cli'
}

export interface ProjectScaffoldRequest {
    projectName: string;
    projectType: TechnologyStack;
    features?: string[];
    customizations?: Record<string, any>;
    targetDirectory?: string;
}

export interface ProjectScaffoldResult {
    success: boolean;
    projectPath?: string;
    filesCreated?: string[];
    scriptsGenerated?: ScriptConfig;
    nextSteps?: string[];
    error?: string;
}

export interface FileNamingConvention {
    pattern: RegExp;
    replacement: string;
    description: string;
}

export interface NamingConventions {
    files: Record<string, FileNamingConvention>;
    directories: Record<string, FileNamingConvention>;
    variables: Record<string, FileNamingConvention>;
}

export interface ProjectValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
}

export interface IProjectScaffoldService {
    /**
     * Generate a complete project scaffold
     */
    generateProject(request: ProjectScaffoldRequest): Promise<ProjectScaffoldResult>;
    
    /**
     * Get available project templates
     */
    getAvailableTemplates(): ProjectTemplate[];
    
    /**
     * Get template by technology stack
     */
    getTemplate(stack: TechnologyStack): ProjectTemplate | null;
    
    /**
     * Validate project structure
     */
    validateProject(projectPath: string): Promise<ProjectValidationResult>;
    
    /**
     * Detect project type from existing files
     */
    detectProjectType(projectPath: string): Promise<TechnologyStack | null>;
    
    /**
     * Update project dependencies
     */
    updateDependencies(projectPath: string, dependencies: DependencyConfig): Promise<boolean>;
}

export interface IFileNamingService {
    /**
     * Standardize file name according to conventions
     */
    standardizeFileName(fileName: string, language: string, projectType: TechnologyStack): string;
    
    /**
     * Validate file name against conventions
     */
    validateFileName(fileName: string, language: string): boolean;
    
    /**
     * Get naming conventions for project type
     */
    getNamingConventions(projectType: TechnologyStack): NamingConventions;
    
    /**
     * Generate appropriate file name from content
     */
    generateFileNameFromContent(content: string, language: string, projectType: TechnologyStack): string;
}

export interface IProjectTypeDetector {
    /**
     * Detect project type from user requirements
     */
    detectFromRequirements(requirements: string): Promise<TechnologyStack>;
    
    /**
     * Detect project type from existing files
     */
    detectFromFiles(filePaths: string[]): Promise<TechnologyStack | null>;
    
    /**
     * Get confidence score for detection
     */
    getDetectionConfidence(requirements: string, detectedType: TechnologyStack): number;
}
