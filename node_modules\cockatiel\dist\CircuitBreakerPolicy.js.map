{"version": 3, "file": "CircuitBreakerPolicy.js", "sourceRoot": "", "sources": ["../src/CircuitBreakerPolicy.ts"], "names": [], "mappings": ";;;AAAA,+CAA+E;AAE/E,0CAAoD;AACpD,0CAA8C;AAC9C,gDAAkE;AAClE,4CAAgG;AAChG,wEAAqE;AAGrE,IAAY,YAsBX;AAtBD,WAAY,YAAY;IACtB;;OAEG;IACH,mDAAM,CAAA;IAEN;;OAEG;IACH,+CAAI,CAAA;IAEJ;;;;OAIG;IACH,uDAAQ,CAAA;IAER;;OAEG;IACH,uDAAQ,CAAA;AACV,CAAC,EAtBW,YAAY,4BAAZ,YAAY,QAsBvB;AAwDD,MAAa,oBAAoB;IA0C/B;;OAEG;IACH,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,YACmB,OAA+B,EAC/B,QAAwB;QADxB,YAAO,GAAP,OAAO,CAAwB;QAC/B,aAAQ,GAAR,QAAQ,CAAgB;QAvD1B,iBAAY,GAAG,IAAI,oBAAY,EAA+C,CAAC;QAC/E,iBAAY,GAAG,IAAI,oBAAY,EAAQ,CAAC;QACxC,oBAAe,GAAG,IAAI,oBAAY,EAAQ,CAAC;QAC3C,uBAAkB,GAAG,IAAI,oBAAY,EAAgB,CAAC;QAG/D,eAAU,GAAe,EAAE,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;QAEhE;;WAEG;QACa,YAAO,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;QAExD;;WAEG;QACa,YAAO,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;QAExD;;;WAGG;QACa,eAAU,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;QAE9D;;WAEG;QACa,kBAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;QAEpE;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAoBlD,IAAI,CAAC,2BAA2B;YAC9B,OAAO,OAAO,CAAC,aAAa,KAAK,QAAQ;gBACvC,CAAC,CAAC,IAAI,yBAAe,CAAC,OAAO,CAAC,aAAa,CAAC;gBAC5C,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAE5B,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,YAAY,GAAG,OAAO,CAAC,YAAgC,CAAC;YAC9D,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,QAAsB,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC;YAEvD,IACE,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI;gBAC3C,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,EAC/C,CAAC;gBACD,IAAI,CAAC,gBAAgB,GAAG,EAAE,KAAK,EAAE,IAAI,8BAAqB,EAAE,EAAE,CAAC;gBAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;oBAClD,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,IAAI,CAAC,gBAAgB;oBAC7B,MAAM,EAAE,0BAAkB;iBAC3B,CAAC,CAAC;gBACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;oBACpD,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;wBACrB,OAAO,EAAE,CAAC;wBACV,MAAM,EAAE,IAAI,CAAC,gBAAgB;wBAC7B,MAAM,EAAE,0BAAkB;qBAC3B,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAE3B,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,OAAO;YACL,OAAO,EAAE,GAAG,EAAE;gBACZ,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO;gBACT,CAAC;gBAED,QAAQ,GAAG,IAAI,CAAC;gBAChB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACnF,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;oBACjD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;oBACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,OAAO,CAClB,EAA0D,EAC1D,MAAM,GAAG,0BAAkB;QAE3B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9B,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;YACpB,KAAK,YAAY,CAAC,MAAM;gBACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1D,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;oBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;oBAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC;gBAED,OAAO,IAAA,wBAAa,EAAC,MAAM,CAAC,CAAC;YAE/B,KAAK,YAAY,CAAC,QAAQ;gBACxB,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACzD,MAAM,IAAI,2BAAkB,EAAE,CAAC;gBACjC,CAAC;gBAED,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE1B,KAAK,YAAY,CAAC,IAAI;gBACpB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzD,MAAM,IAAI,2BAAkB,EAAE,CAAC;gBACjC,CAAC;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;gBACvC,IAAI,CAAC,UAAU,GAAG;oBAChB,KAAK,EAAE,YAAY,CAAC,QAAQ;oBAC5B,IAAI;oBACJ,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC;iBAC/B,CAAC;gBACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpD,OAAO,IAAI,CAAC;YAEd,KAAK,YAAY,CAAC,QAAQ;gBACxB,MAAM,IAAI,2CAAoB,EAAE,CAAC;YAEnC;gBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9B,IAAI,QAA6B,CAAC;QAClC,IAAI,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC1C,QAAQ,GAAG;gBACT,KAAK,EAAE,YAAY,CAAC,IAAI;gBACxB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,KAAK,CAAC,SAAS;aAC3B,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YAC7C,QAAQ,GAAG;gBACT,KAAK,EAAE,YAAY,CAAC,IAAI;gBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,SAAS,EAAE,KAAK,CAAC,SAAS;aAC3B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAA6B,CAAC;IAC3F,CAAC;IAEO,KAAK,CAAC,QAAQ,CACpB,EAA0D,EAC1D,MAAmB;QAEnB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1D,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpD,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;gBAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5B,CAAC;YAED,OAAO,IAAA,wBAAa,EAAC,MAAM,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,+DAA+D;YAC/D,6DAA6D;YAC7D,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,IAAI,CAAC,MAA8B,EAAE,MAAmB;QAC9D,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YAC7E,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GACb,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC/D,MAAM,OAAO,GACX,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ;YAC7C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QACzF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK;QACX,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF;AAlQD,oDAkQC", "sourcesContent": ["import { ConstantBackoff, IBackoff, IBackoffFactory } from './backoff/Backoff';\nimport { IBreaker } from './breaker/Breaker';\nimport { neverAbortedSignal } from './common/abort';\nimport { EventEmitter } from './common/Event';\nimport { ExecuteWrapper, returnOrThrow } from './common/Executor';\nimport { BrokenCircuitError, HydratingCircuitError, TaskCancelledError } from './errors/Errors';\nimport { IsolatedCircuitError } from './errors/IsolatedCircuitError';\nimport { FailureReason, IDefaultPolicyContext, IPolicy } from './Policy';\n\nexport enum CircuitState {\n  /**\n   * Normal operation. Execution of actions allowed.\n   */\n  Closed,\n\n  /**\n   * The automated controller has opened the circuit. Execution of actions blocked.\n   */\n  Open,\n\n  /**\n   * Recovering from open state, after the automated break duration has\n   * expired. Execution of actions permitted. Success of subsequent action/s\n   * controls onward transition to Open or Closed state.\n   */\n  HalfOpen,\n\n  /**\n   * Circuit held manually in an open state. Execution of actions blocked.\n   */\n  Isolated,\n}\n\n/**\n * Context passed into halfOpenAfter backoff delegate.\n */\nexport interface IHalfOpenAfterBackoffContext extends IDefaultPolicyContext {\n  /**\n   * The consecutive number of times the circuit has entered the\n   * {@link CircuitState.Open} state.\n   */\n  attempt: number;\n  /**\n   * The result of the last method call that caused the circuit to enter the\n   * {@link CircuitState.Open} state. Either a thrown error, or a value that we\n   * determined should open the circuit.\n   */\n  result: FailureReason<unknown>;\n}\n\nexport interface ICircuitBreakerOptions {\n  breaker: IBreaker;\n\n  /**\n   * When to (potentially) enter the {@link CircuitState.HalfOpen} state from\n   * the {@link CircuitState.Open} state. Either a duration in milliseconds or a\n   * backoff factory.\n   */\n  halfOpenAfter: number | IBackoffFactory<IHalfOpenAfterBackoffContext>;\n\n  /**\n   * Initial state from a previous call to {@link CircuitBreakerPolicy.toJSON}.\n   */\n  initialState?: unknown;\n}\n\ntype InnerState =\n  | { value: CircuitState.Closed }\n  | { value: CircuitState.Isolated; counters: number }\n  | {\n      value: CircuitState.Open;\n      openedAt: number;\n      attemptNo: number;\n      backoff: IBackoff<IHalfOpenAfterBackoffContext>;\n    }\n  | {\n      value: CircuitState.HalfOpen;\n      test: Promise<any>;\n      attemptNo: number;\n      backoff: IBackoff<IHalfOpenAfterBackoffContext>;\n    };\n\ninterface ISerializedState {\n  ownState: Partial<InnerState>;\n  breakerState: unknown;\n}\n\nexport class CircuitBreakerPolicy implements IPolicy {\n  declare readonly _altReturn: never;\n\n  private readonly breakEmitter = new EventEmitter<FailureReason<unknown> | { isolated: true }>();\n  private readonly resetEmitter = new EventEmitter<void>();\n  private readonly halfOpenEmitter = new EventEmitter<void>();\n  private readonly stateChangeEmitter = new EventEmitter<CircuitState>();\n  private readonly halfOpenAfterBackoffFactory: IBackoffFactory<IHalfOpenAfterBackoffContext>;\n  private innerLastFailure?: FailureReason<unknown>;\n  private innerState: InnerState = { value: CircuitState.Closed };\n\n  /**\n   * Event emitted when the circuit breaker opens.\n   */\n  public readonly onBreak = this.breakEmitter.addListener;\n\n  /**\n   * Event emitted when the circuit breaker resets.\n   */\n  public readonly onReset = this.resetEmitter.addListener;\n\n  /**\n   * Event emitted when the circuit breaker is half open (running a test call).\n   * Either `onBreak` on `onReset` will subsequently fire.\n   */\n  public readonly onHalfOpen = this.halfOpenEmitter.addListener;\n\n  /**\n   * Fired whenever the circuit breaker state changes.\n   */\n  public readonly onStateChange = this.stateChangeEmitter.addListener;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onSuccess = this.executor.onSuccess;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onFailure = this.executor.onFailure;\n\n  /**\n   * Gets the current circuit breaker state.\n   */\n  public get state(): CircuitState {\n    return this.innerState.value;\n  }\n\n  /**\n   * Gets the last reason the circuit breaker failed.\n   */\n  public get lastFailure() {\n    return this.innerLastFailure;\n  }\n\n  constructor(\n    private readonly options: ICircuitBreakerOptions,\n    private readonly executor: ExecuteWrapper,\n  ) {\n    this.halfOpenAfterBackoffFactory =\n      typeof options.halfOpenAfter === 'number'\n        ? new ConstantBackoff(options.halfOpenAfter)\n        : options.halfOpenAfter;\n\n    if (options.initialState) {\n      const initialState = options.initialState as ISerializedState;\n      this.innerState = initialState.ownState as InnerState;\n      this.options.breaker.state = initialState.breakerState;\n\n      if (\n        this.innerState.value === CircuitState.Open ||\n        this.innerState.value === CircuitState.HalfOpen\n      ) {\n        this.innerLastFailure = { error: new HydratingCircuitError() };\n        let backoff = this.halfOpenAfterBackoffFactory.next({\n          attempt: 1,\n          result: this.innerLastFailure,\n          signal: neverAbortedSignal,\n        });\n        for (let i = 2; i <= this.innerState.attemptNo; i++) {\n          backoff = backoff.next({\n            attempt: i,\n            result: this.innerLastFailure,\n            signal: neverAbortedSignal,\n          });\n        }\n        this.innerState.backoff = backoff;\n      }\n    }\n  }\n\n  /**\n   * Manually holds open the circuit breaker.\n   * @returns A handle that keeps the breaker open until `.dispose()` is called.\n   */\n  public isolate() {\n    if (this.innerState.value !== CircuitState.Isolated) {\n      this.innerState = { value: CircuitState.Isolated, counters: 0 };\n      this.breakEmitter.emit({ isolated: true });\n      this.stateChangeEmitter.emit(CircuitState.Isolated);\n    }\n\n    this.innerState.counters++;\n\n    let disposed = false;\n    return {\n      dispose: () => {\n        if (disposed) {\n          return;\n        }\n\n        disposed = true;\n        if (this.innerState.value === CircuitState.Isolated && !--this.innerState.counters) {\n          this.innerState = { value: CircuitState.Closed };\n          this.resetEmitter.emit();\n          this.stateChangeEmitter.emit(CircuitState.Closed);\n        }\n      },\n    };\n  }\n\n  /**\n   * Executes the given function.\n   * @param fn Function to run\n   * @throws a {@link BrokenCircuitError} if the circuit is open\n   * @throws a {@link IsolatedCircuitError} if the circuit is held\n   * open via {@link CircuitBreakerPolicy.isolate}\n   * @returns a Promise that resolves or rejects with the function results.\n   */\n  public async execute<T>(\n    fn: (context: IDefaultPolicyContext) => PromiseLike<T> | T,\n    signal = neverAbortedSignal,\n  ): Promise<T> {\n    const state = this.innerState;\n    switch (state.value) {\n      case CircuitState.Closed:\n        const result = await this.executor.invoke(fn, { signal });\n        if ('success' in result) {\n          this.options.breaker.success(state.value);\n        } else {\n          this.innerLastFailure = result;\n          if (this.options.breaker.failure(state.value)) {\n            this.open(result, signal);\n          }\n        }\n\n        return returnOrThrow(result);\n\n      case CircuitState.HalfOpen:\n        await state.test.catch(() => undefined);\n        if (this.state === CircuitState.Closed && signal.aborted) {\n          throw new TaskCancelledError();\n        }\n\n        return this.execute(fn);\n\n      case CircuitState.Open:\n        if (Date.now() - state.openedAt < state.backoff.duration) {\n          throw new BrokenCircuitError();\n        }\n        const test = this.halfOpen(fn, signal);\n        this.innerState = {\n          value: CircuitState.HalfOpen,\n          test,\n          backoff: state.backoff,\n          attemptNo: state.attemptNo + 1,\n        };\n        this.stateChangeEmitter.emit(CircuitState.HalfOpen);\n        return test;\n\n      case CircuitState.Isolated:\n        throw new IsolatedCircuitError();\n\n      default:\n        throw new Error(`Unexpected circuit state ${state}`);\n    }\n  }\n\n  /**\n   * Captures circuit breaker state that can later be used to recreate the\n   * breaker by passing `state` to the `circuitBreaker` function. This is\n   * useful in cases like serverless functions where you may want to keep\n   * the breaker state across multiple executions.\n   */\n  public toJSON(): unknown {\n    const state = this.innerState;\n    let ownState: Partial<InnerState>;\n    if (state.value === CircuitState.HalfOpen) {\n      ownState = {\n        value: CircuitState.Open,\n        openedAt: 0,\n        attemptNo: state.attemptNo,\n      };\n    } else if (state.value === CircuitState.Open) {\n      ownState = {\n        value: CircuitState.Open,\n        openedAt: state.openedAt,\n        attemptNo: state.attemptNo,\n      };\n    } else {\n      ownState = state;\n    }\n\n    return { ownState, breakerState: this.options.breaker.state } satisfies ISerializedState;\n  }\n\n  private async halfOpen<T>(\n    fn: (context: IDefaultPolicyContext) => PromiseLike<T> | T,\n    signal: AbortSignal,\n  ): Promise<T> {\n    this.halfOpenEmitter.emit();\n\n    try {\n      const result = await this.executor.invoke(fn, { signal });\n      if ('success' in result) {\n        this.options.breaker.success(CircuitState.HalfOpen);\n        this.close();\n      } else {\n        this.innerLastFailure = result;\n        this.options.breaker.failure(CircuitState.HalfOpen);\n        this.open(result, signal);\n      }\n\n      return returnOrThrow(result);\n    } catch (err) {\n      // It's an error, but not one the circuit is meant to retry, so\n      // for our purposes it's a success. Task failed successfully!\n      this.close();\n      throw err;\n    }\n  }\n\n  private open(reason: FailureReason<unknown>, signal: AbortSignal) {\n    if (this.state === CircuitState.Isolated || this.state === CircuitState.Open) {\n      return;\n    }\n\n    const attemptNo =\n      this.innerState.value === CircuitState.HalfOpen ? this.innerState.attemptNo : 1;\n    const context = { attempt: attemptNo, result: reason, signal };\n    const backoff =\n      this.innerState.value === CircuitState.HalfOpen\n        ? this.innerState.backoff.next(context)\n        : this.halfOpenAfterBackoffFactory.next(context);\n\n    this.innerState = { value: CircuitState.Open, openedAt: Date.now(), backoff, attemptNo };\n    this.breakEmitter.emit(reason);\n    this.stateChangeEmitter.emit(CircuitState.Open);\n  }\n\n  private close() {\n    if (this.state === CircuitState.HalfOpen) {\n      this.innerState = { value: CircuitState.Closed };\n      this.resetEmitter.emit();\n      this.stateChangeEmitter.emit(CircuitState.Closed);\n    }\n  }\n}\n"]}