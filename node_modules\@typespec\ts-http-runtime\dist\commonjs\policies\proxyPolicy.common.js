"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.proxyPolicyName = void 0;
exports.getDefaultProxySettings = getDefaultProxySettings;
exports.proxyPolicy = proxyPolicy;
exports.resetCachedProxyAgents = resetCachedProxyAgents;
exports.proxyPolicyName = "proxyPolicy";
const errorMessage = "proxyPolicy is not supported in browser environment";
function getDefaultProxySettings() {
    throw new Error(errorMessage);
}
/**
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 */
function proxyPolicy() {
    throw new Error(errorMessage);
}
/**
 * A function to reset the cached agents.
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 * @internal
 */
function resetCachedProxyAgents() {
    throw new Error(errorMessage);
}
//# sourceMappingURL=proxyPolicy.common.js.map