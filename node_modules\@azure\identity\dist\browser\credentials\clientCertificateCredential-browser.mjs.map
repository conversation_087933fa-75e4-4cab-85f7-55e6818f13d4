{"version": 3, "file": "clientCertificateCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/clientCertificateCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEnE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,8DAA8D,CAC/D,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D;;;GAGG;AACH,MAAM,OAAO,2BAA2B;IACtC;;OAEG;IACH;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\n\nconst BrowserNotSupportedError = new Error(\n  \"ClientCertificateCredential is not supported in the browser.\",\n);\nconst logger = credentialLogger(\"ClientCertificateCredential\");\n\n/**\n * Enables authentication to Microsoft Entra ID using a PEM-encoded\n * certificate that is assigned to an App Registration.\n */\nexport class ClientCertificateCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}