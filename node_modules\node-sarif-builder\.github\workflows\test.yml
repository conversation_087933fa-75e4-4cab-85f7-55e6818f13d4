---
#
# Documentation:
# https://help.github.com/en/articles/workflow-syntax-for-github-actions
#

#######################################
# Start the job on all push to master #
#######################################
name: 'Test'
on:
  push: # Comment this line to trigger action only on pull-requests (not recommended if you don't pay for GH Actions)
  pull_request:
    branches: [master, main]

###############
# Set the Job #
###############
jobs:
  test:
    runs-on: ubuntu-latest
    if: (github.event_name == 'push' || github.event.pull_request.head.repo.full_name != github.repository) && !contains(github.event.head_commit.message, 'skip deploy')
    steps:
      - uses: actions/checkout@v3
      # Setup .npmrc file to publish to npm
      - uses: actions/setup-node@v1
        with:
          node-version: '14.x'
          registry-url: 'https://registry.npmjs.org'
          # Defaults to the user or organization that owns the workflow file
          scope: 'nvuillam'
      - run: yarn
      - run: yarn test
