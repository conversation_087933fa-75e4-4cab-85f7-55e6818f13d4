"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.exponentialRetryPolicyName = void 0;
exports.exponentialRetryPolicy = exponentialRetryPolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * The programmatic identifier of the exponentialRetryPolicy.
 */
exports.exponentialRetryPolicyName = policies_1.exponentialRetryPolicyName;
/**
 * A policy that attempts to retry requests while introducing an exponentially increasing delay.
 * @param options - Options that configure retry logic.
 */
function exponentialRetryPolicy(options = {}) {
    return (0, policies_1.exponentialRetryPolicy)(options);
}
//# sourceMappingURL=exponentialRetryPolicy.js.map