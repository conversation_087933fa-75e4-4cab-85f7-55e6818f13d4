{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/models.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken } from \"@azure/core-auth\";\n\nimport type { IdentityClient } from \"../../client/identityClient.js\";\n\n/**\n * @internal\n */\nexport interface MSIConfiguration {\n  retryConfig: {\n    maxRetries: number;\n    startDelayInMs: number;\n    intervalIncrement: number;\n  };\n  identityClient: IdentityClient;\n  scopes: string | string[];\n  clientId?: string;\n  resourceId?: string;\n}\n\n/**\n * @internal\n * Represents an access token for {@link ManagedIdentity} for internal usage,\n * with an expiration time and the time in which token should refresh.\n */\nexport declare interface MSIToken extends AccessToken {}\n"]}