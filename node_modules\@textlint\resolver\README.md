# @textlint/resolver

Resolver/Dynamic Import helper for textlint

## Install

Install with [npm](https://www.npmjs.com/):

    npm install @textlint/resolver

## Feature

- Try to resolve package name to file path
- Resolver/Import Hooks
  - UseCase: use this hooks to resolve dynamic import as statically for Bun

## Changelog

See [Releases page](https://github.com/textlint/textlint/releases).

## Running tests

Install devDependencies and Run `npm test`:

    npm test

## Contributing

Pull requests and stars are always welcome.

For bugs and feature requests, [please create an issue](https://github.com/textlint/textlint/issues).

1. Fork it!
2. Create your feature branch: `git checkout -b my-new-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin my-new-feature`
5. Submit a pull request :D

## Author

- [github/azu](https://github.com/azu)
- [twitter/azu_re](https://twitter.com/azu_re)

## License

MIT © azu
