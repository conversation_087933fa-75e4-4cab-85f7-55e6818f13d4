import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * This credential will use the currently logged-in user login information
 * via the Azure CLI ('az') commandline tool.
 */
export declare class AzureCliCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=azureCliCredential-browser.d.mts.map