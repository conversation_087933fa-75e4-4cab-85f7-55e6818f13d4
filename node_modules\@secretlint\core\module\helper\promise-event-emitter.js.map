{"version": 3, "file": "promise-event-emitter.js", "sourceRoot": "", "sources": ["../../src/helper/promise-event-emitter.ts"], "names": [], "mappings": "AAAA,iBAAiB;AACjB,oBAAoB;AACpB,oCAAoC;AAIpC,MAAM,OAAO,YAAY;IACrB,UAAU,GAAG,IAAI,GAAG,EAA2B,CAAC;IAEhD,EAAE,CAAC,IAAqB,EAAE,QAAW;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,OAAO,IAAI,IAAI,GAAG,EAAK,CAAC;QAC5C,WAAW,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,IAAqB,EAAE,GAAG,IAAW;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,EAAE;YACd,OAAO;SACV;QACD,KAAK,MAAM,kBAAkB,IAAI,WAAW,EAAE;YAC1C,kBAAkB,CAAC,GAAG,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED,GAAG,CAAC,IAAqB,EAAE,QAAW;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,EAAE;YACd,OAAO;SACV;QACD,KAAK,MAAM,kBAAkB,IAAI,WAAW,EAAE;YAC1C,IAAI,kBAAkB,KAAK,QAAQ,EAAE;gBACjC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aAChC;SACJ;IACL,CAAC;IAED,kBAAkB;QACd,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,aAAa,CAAC,IAAqB;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,CAAC,IAAqB;QAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACvD,CAAC;CACJ;AAED,MAAM,OAAO,mBAAmB;IACpB,MAAM,CAAyD;IAEvE;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;IACrC,CAAC;IAED,aAAa,CAAC,IAAqB;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,EAAE,CAAC,KAAsB,EAAE,QAAkD;QACzE,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;QACvC,MAAM,QAAQ,GAA6B,EAAE,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC9C,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;CACJ"}