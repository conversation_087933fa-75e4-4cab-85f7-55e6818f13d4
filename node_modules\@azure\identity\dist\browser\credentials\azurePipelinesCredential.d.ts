import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * Enables authentication to Microsoft Entra ID using a PEM-encoded
 * certificate that is assigned to an App Registration.
 */
export declare class AzurePipelinesCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=azurePipelinesCredential-browser.d.mts.map