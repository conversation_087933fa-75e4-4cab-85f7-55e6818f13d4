import type { PipelinePolicy } from "../pipeline.js";
/**
 * The programmatic identifier of the setClientRequestIdPolicy.
 */
export declare const setClientRequestIdPolicyName = "setClientRequestIdPolicy";
/**
 * Each PipelineRequest gets a unique id upon creation.
 * This policy passes that unique id along via an HTTP header to enable better
 * telemetry and tracing.
 * @param requestIdHeaderName - The name of the header to pass the request ID to.
 */
export declare function setClientRequestIdPolicy(requestIdHeaderName?: string): PipelinePolicy;
//# sourceMappingURL=setClientRequestIdPolicy.d.ts.map