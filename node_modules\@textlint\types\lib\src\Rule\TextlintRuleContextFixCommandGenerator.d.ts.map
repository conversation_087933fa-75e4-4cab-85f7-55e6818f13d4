{"version": 3, "file": "TextlintRuleContextFixCommandGenerator.d.ts", "sourceRoot": "", "sources": ["../../../src/Rule/TextlintRuleContextFixCommandGenerator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AACnD,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AACvE,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF;;;;;;GAMG;AACH,MAAM,WAAW,sCAAsC;IACnD;;;;;;OAMG;IACH,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAE5E;;;;;;;;OAQG;IACH,oBAAoB,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAElG;;;;;;OAMG;IACH,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAE7E;;;;;;;;OAQG;IACH,qBAAqB,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAEnG;;;;;;OAMG;IACH,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAExE;;;;;;;;OAQG;IACH,gBAAgB,CAAC,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,MAAM,GAAG,6BAA6B,CAAC;IAE9F;;;;;OAKG;IACH,MAAM,CAAC,IAAI,EAAE,OAAO,GAAG,6BAA6B,CAAC;IAErD;;;;;;;OAOG;IACH,WAAW,CAAC,KAAK,EAAE,uBAAuB,GAAG,6BAA6B,CAAC;CAC9E"}