{"name": "@secretlint/types", "version": "9.3.4", "description": "A typing package for @secretlint", "keywords": ["secretlint"], "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/@secretlint/types/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "sideEffects": false, "type": "module", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "lib/", "module/", "src/"], "scripts": {"build": "tsc --build . && tsc --build ./tsconfig.cjs.json && tsconfig-to-dual-package", "clean": "tsc --build --clean && tsc --build --clean ./tsconfig.cjs.json", "prepublishOnly": "npm run clean && npm run build", "prettier": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublish": "npm run --if-present build", "test": "tsc -p ./tsconfig.test.json"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "devDependencies": {"@types/node": "^20.17.51", "prettier": "^2.8.1", "ts-node": "^10.9.2", "tsconfig-to-dual-package": "^1.2.0", "tsd": "^0.32.0", "typescript": "^5.1.6"}, "packageManager": "yarn@1.22.22", "engines": {"node": "^14.13.1 || >=16.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "1b0bebd91380d94ee0a756900ce9b39c8b3cfd1b"}