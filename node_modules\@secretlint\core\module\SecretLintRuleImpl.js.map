{"version": 3, "file": "SecretLintRuleImpl.js", "sourceRoot": "", "sources": ["../src/SecretLintRuleImpl.ts"], "names": [], "mappings": "AAcA,MAAM,OAAO,cAAc;IACf,gBAAgB,CAA8B;IAC9C,WAAW,CAAwB;IACnC,cAAc,CAA2B;IAEjD,YAAY,EAAE,cAAc,EAAE,OAAO,EAAyB;QAC1D,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC;QACvC,yBAAyB;QACzB,MAAM,kBAAkB,GAAG,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;QACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;IACjF,CAAC;IAED,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;YACtC,OAAO,EAAE,CAAC;SACb;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YAC9D,OAAO;gBACH,SAAS,EAAE,cAAc;gBACzB,MAAM;aACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,iBAAiB,CAAC,UAAgC;QAC9C,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;QAC3C,IAAI,WAAW,KAAK,SAAS,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC7D,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAA4B;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;IACxB,CAAC;CACJ"}