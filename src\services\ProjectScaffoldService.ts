/**
 * Project Scaffold Service - Industry-standard project generation
 */

import * as vscode from 'vscode';
import * as path from 'path';
import {
    IProjectScaffoldService,
    ProjectScaffoldRequest,
    ProjectScaffoldResult,
    ProjectTemplate,
    TechnologyStack,
    ProjectValidationResult,
    FileTemplate
} from '../interfaces/IProjectScaffold';
import { ProjectTemplateRegistry } from './ProjectTemplateRegistry';
import { FileNamingService } from './FileNamingService';
import { ProjectTypeDetector } from './ProjectTypeDetector';
import { FileOperationsService } from './FileOperationsService';

export class ProjectScaffoldService implements IProjectScaffoldService {
    private templateRegistry: ProjectTemplateRegistry;
    private fileNamingService: FileNamingService;
    private projectTypeDetector: ProjectTypeDetector;
    private fileOperationsService: FileOperationsService;
    private outputChannel: vscode.OutputChannel;

    constructor(fileOperationsService: FileOperationsService) {
        this.templateRegistry = new ProjectTemplateRegistry();
        this.fileNamingService = new FileNamingService();
        this.projectTypeDetector = new ProjectTypeDetector();
        this.fileOperationsService = fileOperationsService;
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama Project Scaffold');
    }

    public async generateProject(request: ProjectScaffoldRequest): Promise<ProjectScaffoldResult> {
        try {
            this.outputChannel.appendLine(`🚀 Starting project generation: ${request.projectName}`);
            this.outputChannel.appendLine(`📋 Project type: ${request.projectType}`);

            // 1. Get project template
            const template = this.getTemplate(request.projectType);
            if (!template) {
                return {
                    success: false,
                    error: `No template found for project type: ${request.projectType}`
                };
            }

            // 2. Determine project directory
            const projectPath = await this.determineProjectPath(request);
            if (!projectPath) {
                return {
                    success: false,
                    error: 'Could not determine project path'
                };
            }

            // 3. Create project directory structure
            await this.createDirectoryStructure(projectPath, template);

            // 4. Generate and create files
            const filesCreated = await this.generateProjectFiles(projectPath, template, request);

            // 5. Generate package.json or equivalent
            await this.generatePackageConfig(projectPath, template, request);

            // 6. Generate tooling configuration files
            await this.generateToolingConfig(projectPath, template);

            // 7. Generate additional files (README, .gitignore, etc.)
            await this.generateAdditionalFiles(projectPath, template, request);

            // 8. Validate generated project
            const validation = await this.validateProject(projectPath);
            if (!validation.valid) {
                this.outputChannel.appendLine(`⚠️ Project validation warnings: ${validation.warnings.join(', ')}`);
            }

            this.outputChannel.appendLine(`✅ Project generated successfully at: ${projectPath}`);

            return {
                success: true,
                projectPath,
                filesCreated,
                scriptsGenerated: template.scripts,
                nextSteps: this.generateNextSteps(template)
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`❌ Project generation failed: ${errorMsg}`);
            return {
                success: false,
                error: errorMsg
            };
        }
    }

    public getAvailableTemplates(): ProjectTemplate[] {
        return this.templateRegistry.getAllTemplates();
    }

    public getTemplate(stack: TechnologyStack): ProjectTemplate | null {
        return this.templateRegistry.getTemplate(stack);
    }

    public async validateProject(projectPath: string): Promise<ProjectValidationResult> {
        const errors: string[] = [];
        const warnings: string[] = [];
        const suggestions: string[] = [];

        try {
            // Check if project directory exists
            const projectUri = vscode.Uri.file(projectPath);
            const stat = await vscode.workspace.fs.stat(projectUri);
            if (stat.type !== vscode.FileType.Directory) {
                errors.push('Project path is not a directory');
            }

            // Check for essential files
            const essentialFiles = ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod'];
            let hasPackageFile = false;

            for (const file of essentialFiles) {
                try {
                    await vscode.workspace.fs.stat(vscode.Uri.file(path.join(projectPath, file)));
                    hasPackageFile = true;
                    break;
                } catch {
                    // File doesn't exist, continue checking
                }
            }

            if (!hasPackageFile) {
                warnings.push('No package configuration file found');
            }

            // Check for README
            try {
                await vscode.workspace.fs.stat(vscode.Uri.file(path.join(projectPath, 'README.md')));
            } catch {
                suggestions.push('Consider adding a README.md file');
            }

            // Check for .gitignore
            try {
                await vscode.workspace.fs.stat(vscode.Uri.file(path.join(projectPath, '.gitignore')));
            } catch {
                suggestions.push('Consider adding a .gitignore file');
            }

        } catch (error) {
            errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings,
            suggestions
        };
    }

    public async detectProjectType(projectPath: string): Promise<TechnologyStack | null> {
        return this.projectTypeDetector.detectFromFiles([projectPath]);
    }

    public async updateDependencies(projectPath: string, dependencies: any): Promise<boolean> {
        // Implementation for updating dependencies
        return true;
    }

    private async determineProjectPath(request: ProjectScaffoldRequest): Promise<string | null> {
        if (request.targetDirectory) {
            return path.join(request.targetDirectory, request.projectName);
        }

        // Use workspace folder if available
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return path.join(workspaceFolders[0].uri.fsPath, request.projectName);
        }

        // Ask user to select directory
        const selectedFolder = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Project Directory'
        });

        if (selectedFolder && selectedFolder[0]) {
            return path.join(selectedFolder[0].fsPath, request.projectName);
        }

        return null;
    }

    private async createDirectoryStructure(projectPath: string, template: ProjectTemplate): Promise<void> {
        // Create main project directory
        await this.fileOperationsService.createDirectory(projectPath);

        // Create subdirectories
        for (const dir of template.fileStructure.directories) {
            if (dir.required) {
                const dirPath = path.join(projectPath, dir.path);
                await this.fileOperationsService.createDirectory(dirPath);
                this.outputChannel.appendLine(`📁 Created directory: ${dir.path}`);
            }
        }
    }

    private async generateProjectFiles(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<string[]> {
        const filesCreated: string[] = [];

        for (const fileTemplate of template.fileStructure.files) {
            if (fileTemplate.required) {
                const filePath = path.join(projectPath, fileTemplate.path);
                
                // Generate file content
                let content: string;
                if (typeof fileTemplate.content === 'function') {
                    content = fileTemplate.content();
                } else {
                    content = fileTemplate.content;
                }

                // Customize content based on project name
                content = this.customizeFileContent(content, request);

                // Standardize file name
                const standardizedPath = this.fileNamingService.standardizeFileName(
                    fileTemplate.path,
                    fileTemplate.language,
                    template.stack
                );

                const finalPath = path.join(projectPath, standardizedPath);

                // Create the file
                const result = await this.fileOperationsService.createFile(finalPath, content);
                if (result.success) {
                    filesCreated.push(standardizedPath);
                    this.outputChannel.appendLine(`📄 Created file: ${standardizedPath}`);
                } else {
                    this.outputChannel.appendLine(`❌ Failed to create file: ${standardizedPath} - ${result.error}`);
                }
            }
        }

        return filesCreated;
    }

    private async generatePackageConfig(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<void> {
        const packageManager = template.dependencies.packageManager;

        switch (packageManager) {
            case 'npm':
            case 'yarn':
            case 'pnpm':
                await this.generatePackageJson(projectPath, template, request);
                break;
            case 'pip':
            case 'poetry':
                await this.generatePythonConfig(projectPath, template, request);
                break;
            case 'cargo':
                await this.generateCargoToml(projectPath, template, request);
                break;
        }
    }

    private async generatePackageJson(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<void> {
        const packageJson = {
            name: this.fileNamingService.standardizeFileName(request.projectName, 'json', template.stack),
            version: template.metadata.version,
            description: `${template.description} - Generated by V1b3-Sama`,
            main: template.fileStructure.entryPoints.main,
            type: 'module',
            scripts: template.scripts,
            dependencies: template.dependencies.runtime,
            devDependencies: template.dependencies.development,
            keywords: template.metadata.keywords,
            author: template.metadata.author || '',
            license: template.metadata.license,
            engines: template.metadata.engines || {}
        };

        const content = JSON.stringify(packageJson, null, 2);
        const filePath = path.join(projectPath, 'package.json');
        
        await this.fileOperationsService.createFile(filePath, content);
        this.outputChannel.appendLine('📦 Generated package.json');
    }

    private async generatePythonConfig(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<void> {
        if (template.dependencies.packageManager === 'poetry') {
            // Generate pyproject.toml for Poetry
            const pyprojectContent = this.generatePyprojectToml(template, request);
            await this.fileOperationsService.createFile(
                path.join(projectPath, 'pyproject.toml'),
                pyprojectContent
            );
        } else {
            // Generate requirements.txt for pip
            const requirements = Object.entries(template.dependencies.runtime)
                .map(([pkg, version]) => `${pkg}${version}`)
                .join('\n');
            
            await this.fileOperationsService.createFile(
                path.join(projectPath, 'requirements.txt'),
                requirements
            );
        }
        
        this.outputChannel.appendLine('🐍 Generated Python configuration');
    }

    private async generateCargoToml(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<void> {
        const cargoToml = `[package]
name = "${request.projectName}"
version = "${template.metadata.version}"
edition = "2021"

[dependencies]
${Object.entries(template.dependencies.runtime)
    .map(([pkg, version]) => `${pkg} = "${version}"`)
    .join('\n')}`;

        await this.fileOperationsService.createFile(
            path.join(projectPath, 'Cargo.toml'),
            cargoToml
        );
        this.outputChannel.appendLine('🦀 Generated Cargo.toml');
    }

    private async generateToolingConfig(projectPath: string, template: ProjectTemplate): Promise<void> {
        // Generate ESLint config
        if (template.toolingConfig.linting.tool === 'eslint') {
            const eslintConfig = this.generateEslintConfig(template);
            await this.fileOperationsService.createFile(
                path.join(projectPath, '.eslintrc.js'),
                eslintConfig
            );
        }

        // Generate Prettier config
        if (template.toolingConfig.formatting.tool === 'prettier') {
            const prettierConfig = this.generatePrettierConfig(template);
            await this.fileOperationsService.createFile(
                path.join(projectPath, '.prettierrc'),
                prettierConfig
            );
        }

        // Generate TypeScript config
        if (template.toolingConfig.typeChecking?.tool === 'typescript') {
            // TypeScript config is already included in file templates
        }

        this.outputChannel.appendLine('🔧 Generated tooling configuration');
    }

    private async generateAdditionalFiles(
        projectPath: string,
        template: ProjectTemplate,
        request: ProjectScaffoldRequest
    ): Promise<void> {
        // Generate README.md
        const readmeContent = this.generateReadme(template, request);
        await this.fileOperationsService.createFile(
            path.join(projectPath, 'README.md'),
            readmeContent
        );

        // Generate .gitignore
        const gitignoreContent = this.generateGitignore(template);
        await this.fileOperationsService.createFile(
            path.join(projectPath, '.gitignore'),
            gitignoreContent
        );

        this.outputChannel.appendLine('📝 Generated additional files');
    }

    private customizeFileContent(content: string, request: ProjectScaffoldRequest): string {
        return content
            .replace(/{{PROJECT_NAME}}/g, request.projectName)
            .replace(/{{PROJECT_NAME_KEBAB}}/g, this.toKebabCase(request.projectName))
            .replace(/{{PROJECT_NAME_PASCAL}}/g, this.toPascalCase(request.projectName));
    }

    private generateNextSteps(template: ProjectTemplate): string[] {
        const steps = [
            'Navigate to your project directory',
            `Install dependencies: ${this.getInstallCommand(template.dependencies.packageManager)}`,
            `Start development server: ${template.scripts.dev}`,
            'Open the project in your browser or editor'
        ];

        if (template.toolingConfig.testing) {
            steps.push(`Run tests: ${template.scripts.test}`);
        }

        return steps;
    }

    private getInstallCommand(packageManager: string): string {
        switch (packageManager) {
            case 'npm': return 'npm install';
            case 'yarn': return 'yarn install';
            case 'pnpm': return 'pnpm install';
            case 'pip': return 'pip install -r requirements.txt';
            case 'poetry': return 'poetry install';
            case 'cargo': return 'cargo build';
            default: return 'Install dependencies';
        }
    }

    private generatePyprojectToml(template: ProjectTemplate, request: ProjectScaffoldRequest): string {
        return `[tool.poetry]
name = "${request.projectName}"
version = "${template.metadata.version}"
description = "${template.description}"
authors = ["${template.metadata.author || 'Your Name <<EMAIL>>'}"]

[tool.poetry.dependencies]
python = "^3.8"
${Object.entries(template.dependencies.runtime)
    .map(([pkg, version]) => `${pkg} = "${version}"`)
    .join('\n')}

[tool.poetry.group.dev.dependencies]
${Object.entries(template.dependencies.development)
    .map(([pkg, version]) => `${pkg} = "${version}"`)
    .join('\n')}

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"`;
    }

    private generateEslintConfig(template: ProjectTemplate): string {
        return `module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true
    },
    extends: [
        'eslint:recommended',
        '@typescript-eslint/recommended'
    ],
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
    },
    plugins: ['@typescript-eslint'],
    rules: {
        // Add your custom rules here
    }
};`;
    }

    private generatePrettierConfig(template: ProjectTemplate): string {
        return JSON.stringify({
            semi: true,
            trailingComma: 'es5',
            singleQuote: true,
            printWidth: 80,
            tabWidth: 2,
            useTabs: false
        }, null, 2);
    }

    private generateReadme(template: ProjectTemplate, request: ProjectScaffoldRequest): string {
        return `# ${request.projectName}

${template.description}

## Getting Started

### Prerequisites

- ${this.getPrerequisites(template)}

### Installation

1. Clone the repository
2. Install dependencies:
   \`\`\`bash
   ${this.getInstallCommand(template.dependencies.packageManager)}
   \`\`\`

### Development

Start the development server:
\`\`\`bash
${template.scripts.dev}
\`\`\`

### Building

Build for production:
\`\`\`bash
${template.scripts.build}
\`\`\`

### Testing

Run tests:
\`\`\`bash
${template.scripts.test}
\`\`\`

## Project Structure

\`\`\`
${this.generateProjectStructureTree(template)}
\`\`\`

## License

${template.metadata.license}

---

Generated by V1b3-Sama Extension`;
    }

    private generateGitignore(template: ProjectTemplate): string {
        const commonIgnores = [
            '# Dependencies',
            'node_modules/',
            '__pycache__/',
            '*.pyc',
            'target/',
            '',
            '# Build outputs',
            'dist/',
            'build/',
            '*.exe',
            '',
            '# Environment',
            '.env',
            '.env.local',
            '',
            '# IDE',
            '.vscode/',
            '.idea/',
            '*.swp',
            '*.swo',
            '',
            '# OS',
            '.DS_Store',
            'Thumbs.db'
        ];

        return commonIgnores.join('\n');
    }

    private getPrerequisites(template: ProjectTemplate): string {
        switch (template.dependencies.packageManager) {
            case 'npm':
            case 'yarn':
            case 'pnpm':
                return 'Node.js 18+ and npm/yarn/pnpm';
            case 'pip':
            case 'poetry':
                return 'Python 3.8+';
            case 'cargo':
                return 'Rust 1.70+';
            default:
                return 'Check project requirements';
        }
    }

    private generateProjectStructureTree(template: ProjectTemplate): string {
        const structure = template.fileStructure.directories
            .map(dir => `${dir.path}/`)
            .concat(template.fileStructure.files.map(file => file.path))
            .sort()
            .map(item => `├── ${item}`)
            .join('\n');

        return structure;
    }

    private toKebabCase(str: string): string {
        return str.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
    }

    private toPascalCase(str: string): string {
        return str.replace(/(?:^|[-_\s])(\w)/g, (_, char) => char.toUpperCase()).replace(/[-_\s]/g, '');
    }
}
