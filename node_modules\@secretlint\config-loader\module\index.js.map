{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAU1C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,wBAAwB,EAAE,4BAA4B,EAAE,MAAM,gBAAgB,CAAC;AACxF,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,MAAM,UAAU,uBAAuB,CACnC,aAA8D;IAE9D,IAAI,CAAC,aAAa,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;KACrF;IACD,IAAI,CAAC,CAAC,SAAS,IAAI,aAAa,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,mFAAmF,CAAC,CAAC;KACxG;IACD,OAAO,aAAa,CAAC,OAAqC,CAAC;AAC/D,CAAC;AAiED;;;GAGG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAAG,KAAK,EACjD,OAAmD,EACD,EAAE;IACpD,sDAAsD;IACtD,MAAM,8BAA8B,GAAG,wBAAwB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC1F,IAAI,CAAC,8BAA8B,CAAC,EAAE,EAAE;QACpC,MAAM,8BAA8B,CAAC,KAAK,CAAC;KAC9C;IACD,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,uCAAuC;KAChD,CAAC,CAAC;IACH,6BAA6B;IAC7B,MAAM,cAAc,GAAG,IAAI,wBAAwB,CAAC;QAChD,aAAa,EAAE,OAAO,CAAC,cAAc;KACxC,CAAC,CAAC;IACH,mBAAmB;IACnB,MAAM,gCAAgC,GAAG,CAAC,CAA6B,EAAoC,EAAE;QACzG,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;IACpC,CAAC,CAAC;IACF,MAAM,MAAM,GAAY,EAAE,CAAC;IAC3B,MAAM,KAAK,GAAoC,EAAE,CAAC;IAClD,KAAK,MAAM,oBAAoB,IAAI,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE;QAC/D,IAAI;YACA,kBAAkB,CAAC,IAAI,CAAC;gBACpB,IAAI,EAAE,sCAAsC;gBAC5C,EAAE,EAAE,oBAAoB,CAAC,EAAE;aAC9B,CAAC,CAAC;YACH,MAAM,kBAAkB,GACpB,OAAO,CAAC,sBAAsB;gBAC9B,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;oBAC3C,OAAO,EAAE,KAAK,oBAAoB,CAAC,EAAE,CAAC;gBAC1C,CAAC,CAAC,CAAC;YACP,yBAAyB;YACzB,MAAM,WAAW,GAA+B,kBAAkB;gBAC9D,CAAC,CAAC,kBAAkB,CAAC,IAAI;gBACzB,CAAC,CAAC,uBAAuB,CACnB,CACI,MAAM,aAAa,CAAC,cAAc,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,EAAE,CAAC,EAAE;oBAChF,YAAY,EAAE,eAAe;oBAC7B,gBAAgB,EAAE,MAAM,CAAC,IAAI;iBAChC,CAAC,CACL,CAAC,OAAO,CACZ,CAAC;YACR,IAAI,gCAAgC,CAAC,WAAW,CAAC,EAAE;gBAC/C,MAAM,0BAA0B,GAAG,oBAA4D,CAAC;gBAChG,KAAK,CAAC,IAAI,CAAC;oBACP,EAAE,EAAE,oBAAoB,CAAC,EAAE;oBAC3B,IAAI,EAAE,WAAW;oBACjB,oBAAoB;oBACpB,KAAK,EAAE,0BAA0B,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;wBACnD,OAAO;4BACH,GAAG,IAAI;4BACP,IAAI,EAAE,MAAM;yBACf,CAAC;oBACN,CAAC,CAAC;oBACF,OAAO,EAAE,oBAAoB,CAAC,OAAO;oBACrC,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;iBAC1C,CAAC,CAAC;aACN;iBAAM;gBACH,KAAK,CAAC,IAAI,CAAC;oBACP,EAAE,EAAE,oBAAoB,CAAC,EAAE;oBAC3B,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,oBAAoB,CAAC,OAAO;oBACrC,QAAQ,EAAE,UAAU,IAAI,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;oBACxF,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;oBACvC,eAAe,EACX,iBAAiB,IAAI,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS;iBACnG,CAAC,CAAC;aACN;YACD,kBAAkB,CAAC,IAAI,CAAC;gBACpB,IAAI,EAAE,oCAAoC;gBAC1C,EAAE,EAAE,oBAAoB,CAAC,EAAE;aAC9B,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,QAAQ,GACV,KAAK,YAAY,KAAK;gBAClB,CAAC,CAAC,IAAI,KAAK,CACL,+BAA+B,oBAAoB,CAAC,EAAE;;SAEvE,KAAK,CAAC,OAAO,EAAE,EACE;oBACI,KAAK,EAAE,KAAK;iBACf,CACJ;gBACH,CAAC,CAAC,IAAI,KAAK,CAAC,+BAA+B,oBAAoB,CAAC,EAAE;;SAE7E,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACd,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzB;KACJ;IACD,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,qCAAqC;KAC9C,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,MAAM,IAAI,gBAAgB,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;KACnE;IACD,MAAM,YAAY,GAAyB;QACvC,aAAa,EAAE,OAAO,CAAC,gBAAgB,CAAC,aAAa;QACrD,KAAK;KACR,CAAC;IACF,gDAAgD;IAChD,6EAA6E;IAC7E,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;QACtD,MAAM,EAAE,YAAY;KACvB,CAAC,CAAC;IACH,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE;QAC1B,MAAM,oBAAoB,CAAC,KAAK,CAAC;KACpC;IACD,OAAO;QACH,EAAE,EAAE,IAAI;QACR,MAAM,EAAE,YAAY;KACvB,CAAC;AACN,CAAC,CAAC;AACF;;;GAGG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,KAAK,EAAE,OAAsC,EAAyC,EAAE;IAC9G,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,wCAAwC;KACjD,CAAC,CAAC;IACH,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACjF,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,sCAAsC;KAC/C,CAAC,CAAC;IACH,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,qCAAqC;KAC9C,CAAC,CAAC;IACH,MAAM,gBAAgB,GAAG,MAAM,gCAAgC,CAAC;QAC5D,gBAAgB;QAChB,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;KACzD,CAAC,CAAC;IACH,kBAAkB,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,mCAAmC;KAC5C,CAAC,CAAC;IACH,OAAO;QACH,EAAE,EAAE,IAAI;QACR,MAAM,EAAE,gBAAgB,CAAC,MAAM;QAC/B,gBAAgB;QAChB,cAAc;KACjB,CAAC;AACN,CAAC,CAAC;AACF;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,KAAK,EACrC,OAAsC,EACE,EAAE;IAC1C,MAAM,OAAO,GAAG,MAAM,CAA6B,YAAY,EAAE;QAC7D,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,WAAW,EAAE;YACT,SAAS,EAAE,YAAY;SAC1B;KACJ,CAAC,CAAC;IACH,YAAY;IACZ,IAAI,CAAC,OAAO,EAAE;QACV,MAAM,IAAI,KAAK,CAAC;;;yCAGiB,CAAC,CAAC;KACtC;IACD,OAAO;QACH,EAAE,EAAE,IAAI;QACR,gBAAgB,EAAE,OAAO,CAAC,MAAM;QAChC,cAAc,EAAE,OAAO,CAAC,QAAQ;KACnC,CAAC;AACN,CAAC,CAAC;AAUF,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,EAAE,OAAsC,EAAiC,EAAE;IAC1G,IAAI;QACA,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO;YACH,EAAE,EAAE,IAAI;SACX,CAAC;KACL;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO;YACH,EAAE,EAAE,KAAK;YACT,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACnE,CAAC;KACL;AACL,CAAC,CAAC"}