import type { PipelineRequest } from "../../interfaces.js";
/**
 * Ensures that authentication is only allowed over HTTPS unless explicitly allowed.
 * Throws an error if the connection is not secure and not explicitly allowed.
 */
export declare function ensureSecureConnection(request: PipelineRequest, options: {
    allowInsecureConnection?: boolean;
}): void;
//# sourceMappingURL=checkInsecureConnection.d.ts.map