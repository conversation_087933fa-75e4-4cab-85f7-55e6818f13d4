export declare const proxyPolicyName = "proxyPolicy";
export declare function getDefaultProxySettings(): never;
/**
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 */
export declare function proxyPolicy(): never;
/**
 * A function to reset the cached agents.
 * proxyPolicy is not supported in the browser and attempting
 * to use it will raise an error.
 * @internal
 */
export declare function resetCachedProxyAgents(): never;
//# sourceMappingURL=proxyPolicy.common.d.ts.map