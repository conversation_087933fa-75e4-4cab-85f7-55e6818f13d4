"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.tlsPolicyName = void 0;
exports.tlsPolicy = tlsPolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * Name of the TLS Policy
 */
exports.tlsPolicyName = policies_1.tlsPolicyName;
/**
 * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.
 */
function tlsPolicy(tlsSettings) {
    return (0, policies_1.tlsPolicy)(tlsSettings);
}
//# sourceMappingURL=tlsPolicy.js.map