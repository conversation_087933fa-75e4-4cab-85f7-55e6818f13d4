// Web Worker for heavy FileSystem operations
// Runs in background thread to keep main extension responsive
// Note: This worker runs in a web worker context and doesn't have access to Node.js APIs

// Type declarations for worker environment
declare var self: any;
interface MessageEvent<T = any> {
    data: T;
}

export interface WorkerMessage {
    id: string;
    type: 'diff' | 'search' | 'index' | 'analyze';
    payload: any;
}

export interface WorkerResponse {
    id: string;
    type: 'success' | 'error' | 'progress';
    payload: any;
}

export interface DiffRequest {
    original: string;
    modified: string;
    filename?: string;
}

export interface SearchRequest {
    searchPath: string;
    pattern: string;
    content?: string;
    recursive: boolean;
    includeHidden: boolean;
    maxResults: number;
}

export interface IndexRequest {
    rootPath: string;
    excludePatterns: string[];
    includePatterns: string[];
}

export interface AnalyzeRequest {
    filePath: string;
    analysisType: 'complexity' | 'dependencies' | 'metrics';
}

class FileSystemWorker {
    constructor() {
        // Listen for messages from main thread
        if (typeof self !== 'undefined') {
            self.onmessage = this.handleMessage.bind(this);
        }
    }

    private async handleMessage(event: MessageEvent<WorkerMessage>) {
        const { id, type, payload } = event.data;

        try {
            let result: any;

            switch (type) {
                case 'diff':
                    result = await this.generateDiff(payload as DiffRequest);
                    break;
                case 'search':
                    result = await this.searchFiles(payload as SearchRequest);
                    break;
                case 'index':
                    result = await this.indexDirectory(payload as IndexRequest);
                    break;
                case 'analyze':
                    result = await this.analyzeFile(payload as AnalyzeRequest);
                    break;
                default:
                    throw new Error(`Unknown operation type: ${type}`);
            }

            this.postMessage({
                id,
                type: 'success',
                payload: result
            });

        } catch (error) {
            this.postMessage({
                id,
                type: 'error',
                payload: {
                    message: error instanceof Error ? error.message : 'Unknown error',
                    stack: error instanceof Error ? error.stack : undefined
                }
            });
        }
    }

    private async generateDiff(request: DiffRequest): Promise<any> {
        const { original, modified, filename = 'file' } = request;

        // Use a more efficient diff algorithm for large files
        const lines1 = original.split('\n');
        const lines2 = modified.split('\n');

        const diff = this.computeUnifiedDiff(lines1, lines2, filename);
        const { additions, deletions } = this.countDiffLines(diff);

        return {
            additions,
            deletions,
            diff,
            originalLines: lines1.length,
            modifiedLines: lines2.length
        };
    }

    private async searchFiles(request: SearchRequest): Promise<string[]> {
        // File search operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('File search operations must be handled by the main thread');
    }

    private async indexDirectory(request: IndexRequest): Promise<any> {
        // Directory indexing operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('Directory indexing operations must be handled by the main thread');
    }

    private async analyzeFile(request: AnalyzeRequest): Promise<any> {
        // File analysis operations are not supported in web worker context
        // This would need to be handled by the main thread with VS Code APIs
        throw new Error('File analysis operations must be handled by the main thread');
    }

    private computeUnifiedDiff(lines1: string[], lines2: string[], filename: string): string {
        // Simplified unified diff implementation
        // In production, you might want to use a more sophisticated algorithm
        const result: string[] = [];
        result.push(`--- a/${filename}`);
        result.push(`+++ b/${filename}`);

        let i = 0, j = 0;
        while (i < lines1.length || j < lines2.length) {
            if (i < lines1.length && j < lines2.length && lines1[i] === lines2[j]) {
                result.push(` ${lines1[i]}`);
                i++;
                j++;
            } else if (i < lines1.length && (j >= lines2.length || lines1[i] !== lines2[j])) {
                result.push(`-${lines1[i]}`);
                i++;
            } else {
                result.push(`+${lines2[j]}`);
                j++;
            }
        }

        return result.join('\n');
    }

    private countDiffLines(diff: string): { additions: number; deletions: number } {
        const lines = diff.split('\n');
        let additions = 0;
        let deletions = 0;

        for (const line of lines) {
            if (line.startsWith('+') && !line.startsWith('+++')) {
                additions++;
            } else if (line.startsWith('-') && !line.startsWith('---')) {
                deletions++;
            }
        }

        return { additions, deletions };
    }



    private analyzeComplexity(content: string, lines: string[]): any {
        // Simple complexity analysis
        const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
        const cognitiveComplexity = this.calculateCognitiveComplexity(content);

        return {
            cyclomaticComplexity,
            cognitiveComplexity,
            linesOfCode: lines.length,
            nonEmptyLines: lines.filter(line => line.trim().length > 0).length,
            commentLines: lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*')).length
        };
    }

    private analyzeDependencies(content: string, filePath: string): any {
        const imports: string[] = [];
        const requires: string[] = [];
        const exports: string[] = [];

        // Extract imports/requires (simplified)
        const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
        const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
        const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g;

        let match;
        while ((match = importRegex.exec(content)) !== null) {
            imports.push(match[1]);
        }

        while ((match = requireRegex.exec(content)) !== null) {
            requires.push(match[1]);
        }

        while ((match = exportRegex.exec(content)) !== null) {
            exports.push(match[1]);
        }

        // Get file extension without using path module
        const lastDot = filePath.lastIndexOf('.');
        const fileExtension = lastDot > 0 ? filePath.substring(lastDot) : '';

        return {
            imports,
            requires,
            exports,
            totalDependencies: imports.length + requires.length,
            fileExtension
        };
    }

    private analyzeMetrics(content: string, lines: string[]): any {
        return {
            fileSize: new TextEncoder().encode(content).length, // Use TextEncoder instead of Buffer
            lineCount: lines.length,
            characterCount: content.length,
            wordCount: content.split(/\s+/).length,
            averageLineLength: content.length / lines.length,
            maxLineLength: Math.max(...lines.map(line => line.length))
        };
    }

    private calculateCyclomaticComplexity(content: string): number {
        // Simplified cyclomatic complexity calculation
        const keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?'];
        let complexity = 1; // Base complexity

        for (const keyword of keywords) {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        }

        return complexity;
    }

    private calculateCognitiveComplexity(content: string): number {
        // Simplified cognitive complexity calculation
        let complexity = 0;
        let nestingLevel = 0;

        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            
            // Increase nesting for blocks
            if (trimmed.includes('{')) {
                nestingLevel++;
            }
            if (trimmed.includes('}')) {
                nestingLevel = Math.max(0, nestingLevel - 1);
            }

            // Add complexity for control structures
            if (/\b(if|while|for|switch)\b/.test(trimmed)) {
                complexity += 1 + nestingLevel;
            }
        }

        return complexity;
    }

    private postMessage(response: WorkerResponse) {
        if (typeof self !== 'undefined') {
            self.postMessage(response);
        }
    }
}

// Initialize worker
new FileSystemWorker();
