// Security Service Interface
// Provides abstraction for security and sandboxing operations

import { McpToolCall } from './IMcpClientService';

export interface SecurityPolicy {
    allowFileSystemAccess: boolean;
    allowNetworkAccess: boolean;
    allowTerminalAccess: boolean;
    allowedFileExtensions: string[];
    blockedPaths: string[];
    maxFileSize: number; // in bytes
    requireApprovalForCommands: string[];
}

export interface PermissionRequest {
    operation: string;
    resource: string;
    details: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityContext {
    userId: string;
    sessionId: string;
    timestamp: number;
    permissions: string[];
}

export interface SecurityValidationResult {
    allowed: boolean;
    reason?: string;
}

export interface ISecurityService {
    /**
     * Get current security policy
     */
    getSecurityPolicy(): SecurityPolicy;

    /**
     * Update security policy
     * @param policy Partial security policy to update
     */
    updateSecurityPolicy(policy: Partial<SecurityPolicy>): Promise<void>;

    /**
     * Validate if an MCP tool call is safe to execute
     * @param toolCall MCP tool call to validate
     * @returns Promise resolving to validation result
     */
    validateMcpToolCall(toolCall: McpToolCall): Promise<SecurityValidationResult>;

    /**
     * Request permission for a high-risk operation
     * @param request Permission request details
     * @returns Promise resolving to true if permission granted
     */
    requestPermission(request: PermissionRequest): Promise<boolean>;

    /**
     * Clear session permissions
     */
    clearSessionPermissions(): void;

    /**
     * Get security audit log
     * @returns Array of security events
     */
    getSecurityAuditLog(): any[];

    /**
     * Log security event
     * @param event Security event to log
     */
    logSecurityEvent(event: any): Promise<void>;
}
