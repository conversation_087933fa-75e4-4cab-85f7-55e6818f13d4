{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../src/log.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,kDAAwD;AAC3C,QAAA,MAAM,GAAG,IAAA,8BAAkB,EAAC,iBAAiB,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createClientLogger } from \"./logger/logger.js\";\nexport const logger = createClientLogger(\"ts-http-runtime\");\n"]}