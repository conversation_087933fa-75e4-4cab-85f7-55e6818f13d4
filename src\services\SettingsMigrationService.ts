import * as vscode from 'vscode';
import { SettingsService } from './SettingsService';
import { ApiKeyManager } from './ApiKeyManager';

/**
 * Migration result interface
 */
export interface MigrationResult {
    success: boolean;
    migratedSettings: string[];
    errors: string[];
    warnings: string[];
}

/**
 * Settings version information
 */
export interface SettingsVersion {
    version: string;
    timestamp: Date;
    migratedFrom?: string;
}

/**
 * Settings Migration Service for V1b3-Sama Extension
 * Handles migration of settings from older versions and validation of configuration
 */
export class SettingsMigrationService {
    private static readonly CURRENT_VERSION = '5.0.3';
    private static readonly VERSION_KEY = 'settingsVersion';
    private static readonly LEGACY_KEYS = [
        'apiKey',
        'theme',
        'fontSize',
        'showLineNumbers',
        'enableSyntaxHighlighting'
    ];
    
    constructor(
        private context: vscode.ExtensionContext,
        private settingsService: SettingsService,
        private apiKeyManager: ApiKeyManager
    ) {}
    
    /**
     * Perform settings migration if needed
     */
    public async migrateIfNeeded(): Promise<MigrationResult> {
        const currentVersion = this.getCurrentSettingsVersion();
        const targetVersion = SettingsMigrationService.CURRENT_VERSION;
        
        if (currentVersion === targetVersion) {
            return {
                success: true,
                migratedSettings: [],
                errors: [],
                warnings: []
            };
        }
        
        console.log(`Migrating settings from version ${currentVersion || 'unknown'} to ${targetVersion}`);
        
        const result = await this.performMigration(currentVersion, targetVersion);
        
        if (result.success) {
            await this.setSettingsVersion(targetVersion);
            console.log('Settings migration completed successfully');
        } else {
            console.error('Settings migration failed:', result.errors);
        }
        
        return result;
    }
    
    /**
     * Get current settings version
     */
    private getCurrentSettingsVersion(): string | undefined {
        const versionInfo = this.context.globalState.get<SettingsVersion>(SettingsMigrationService.VERSION_KEY);
        return versionInfo?.version;
    }
    
    /**
     * Set settings version
     */
    private async setSettingsVersion(version: string): Promise<void> {
        const versionInfo: SettingsVersion = {
            version,
            timestamp: new Date(),
            migratedFrom: this.getCurrentSettingsVersion()
        };
        
        await this.context.globalState.update(SettingsMigrationService.VERSION_KEY, versionInfo);
    }
    
    /**
     * Perform the actual migration
     */
    private async performMigration(fromVersion: string | undefined, toVersion: string): Promise<MigrationResult> {
        const result: MigrationResult = {
            success: true,
            migratedSettings: [],
            errors: [],
            warnings: []
        };
        
        try {
            // Migrate from legacy settings (pre-5.0.0)
            if (!fromVersion || this.isVersionLessThan(fromVersion, '5.0.0')) {
                await this.migrateLegacySettings(result);
            }
            
            // Migrate from 5.0.0 to 5.0.1
            if (!fromVersion || this.isVersionLessThan(fromVersion, '5.0.1')) {
                await this.migrateToV501(result);
            }
            
            // Migrate from 5.0.1 to 5.0.2
            if (!fromVersion || this.isVersionLessThan(fromVersion, '5.0.2')) {
                await this.migrateToV502(result);
            }
            
            // Migrate from 5.0.2 to 5.0.3
            if (!fromVersion || this.isVersionLessThan(fromVersion, '5.0.3')) {
                await this.migrateToV503(result);
            }
            
            // Validate final configuration
            await this.validateMigratedSettings(result);
            
        } catch (error) {
            result.success = false;
            result.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        
        return result;
    }
    
    /**
     * Migrate legacy settings (pre-5.0.0)
     */
    private async migrateLegacySettings(result: MigrationResult): Promise<void> {
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        
        // Migrate legacy API key
        const legacyApiKey = config.get<string>('apiKey');
        if (legacyApiKey && legacyApiKey.length > 0 && legacyApiKey !== '***STORED_SECURELY***') {
            try {
                await this.apiKeyManager.storeApiKey('openai', legacyApiKey);
                await config.update('apiKey', undefined, vscode.ConfigurationTarget.Global);
                result.migratedSettings.push('apiKey -> secure storage');
            } catch (error) {
                result.errors.push(`Failed to migrate API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        
        // Migrate UI settings to new structure
        const uiMigrations = [
            { old: 'theme', new: 'ui.theme' },
            { old: 'fontSize', new: 'ui.fontSize' },
            { old: 'showLineNumbers', new: 'ui.showLineNumbers' },
            { old: 'enableSyntaxHighlighting', new: 'ui.enableSyntaxHighlighting' }
        ];
        
        for (const migration of uiMigrations) {
            const oldValue = config.get(migration.old);
            if (oldValue !== undefined) {
                try {
                    await config.update(migration.new, oldValue, vscode.ConfigurationTarget.Global);
                    await config.update(migration.old, undefined, vscode.ConfigurationTarget.Global);
                    result.migratedSettings.push(`${migration.old} -> ${migration.new}`);
                } catch (error) {
                    result.errors.push(`Failed to migrate ${migration.old}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        }
    }
    
    /**
     * Migrate to version 5.0.1
     */
    private async migrateToV501(result: MigrationResult): Promise<void> {
        // Add new performance settings with defaults
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        
        const newSettings = [
            { key: 'performance.enablePerformanceMonitoring', defaultValue: true },
            { key: 'performance.alertThreshold', defaultValue: 15 }
        ];
        
        for (const setting of newSettings) {
            if (config.get(setting.key) === undefined) {
                try {
                    await config.update(setting.key, setting.defaultValue, vscode.ConfigurationTarget.Global);
                    result.migratedSettings.push(`Added ${setting.key} with default value`);
                } catch (error) {
                    result.warnings.push(`Failed to add ${setting.key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        }
    }
    
    /**
     * Migrate to version 5.0.2
     */
    private async migrateToV502(result: MigrationResult): Promise<void> {
        // Add new security and context engine settings
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        
        const newSettings = [
            { key: 'security.validateApiKeys', defaultValue: true },
            { key: 'security.requireConfirmationForDeletion', defaultValue: true },
            { key: 'contextEngine.enabled', defaultValue: true },
            { key: 'contextEngine.maxContextFiles', defaultValue: 50 }
        ];
        
        for (const setting of newSettings) {
            if (config.get(setting.key) === undefined) {
                try {
                    await config.update(setting.key, setting.defaultValue, vscode.ConfigurationTarget.Global);
                    result.migratedSettings.push(`Added ${setting.key} with default value`);
                } catch (error) {
                    result.warnings.push(`Failed to add ${setting.key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        }
    }
    
    /**
     * Migrate to version 5.0.3
     */
    private async migrateToV503(result: MigrationResult): Promise<void> {
        // Update provider enum to include new providers
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        const currentProvider = config.get<string>('provider');
        
        // If provider is not set or is an old value, set to deepseek
        if (!currentProvider || !['deepseek', 'groq', 'openai', 'anthropic', 'google', 'openrouter', 'azure', 'local'].includes(currentProvider)) {
            try {
                await config.update('provider', 'deepseek', vscode.ConfigurationTarget.Global);
                await config.update('model', 'deepseek-coder', vscode.ConfigurationTarget.Global);
                result.migratedSettings.push('Updated provider to deepseek with deepseek-coder model');
            } catch (error) {
                result.warnings.push(`Failed to update provider: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        
        // Add execution settings
        const executionSettings = [
            { key: 'execution.confirmBeforeRun', defaultValue: true },
            { key: 'execution.timeoutSeconds', defaultValue: 30 },
            { key: 'execution.preserveWorkingDirectory', defaultValue: true }
        ];
        
        for (const setting of executionSettings) {
            if (config.get(setting.key) === undefined) {
                try {
                    await config.update(setting.key, setting.defaultValue, vscode.ConfigurationTarget.Global);
                    result.migratedSettings.push(`Added ${setting.key} with default value`);
                } catch (error) {
                    result.warnings.push(`Failed to add ${setting.key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        }
    }
    
    /**
     * Validate migrated settings
     */
    private async validateMigratedSettings(result: MigrationResult): Promise<void> {
        try {
            const validation = this.settingsService.validateConfiguration();
            
            if (!validation.isValid) {
                result.warnings.push(...validation.errors.map(error => `Validation warning: ${error}`));
            }
        } catch (error) {
            result.warnings.push(`Settings validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    
    /**
     * Check if version A is less than version B
     */
    private isVersionLessThan(versionA: string, versionB: string): boolean {
        const parseVersion = (version: string) => version.split('.').map(Number);
        const a = parseVersion(versionA);
        const b = parseVersion(versionB);
        
        for (let i = 0; i < Math.max(a.length, b.length); i++) {
            const numA = a[i] || 0;
            const numB = b[i] || 0;
            
            if (numA < numB) return true;
            if (numA > numB) return false;
        }
        
        return false;
    }
    
    /**
     * Clean up legacy settings
     */
    public async cleanupLegacySettings(): Promise<void> {
        const config = vscode.workspace.getConfiguration('v1b3-sama');
        
        for (const legacyKey of SettingsMigrationService.LEGACY_KEYS) {
            try {
                await config.update(legacyKey, undefined, vscode.ConfigurationTarget.Global);
            } catch (error) {
                console.warn(`Failed to clean up legacy setting ${legacyKey}:`, error);
            }
        }
    }
}
