// Enhanced Conversation Persistence Service
// Handles saving and loading chat conversations with threading, checkpoints, and memory integration

import * as vscode from 'vscode';
import { LLMMessage } from '../interfaces/ILLMService';
import {
    EnhancedConversationData,
    ThreadedMessage,
    SessionCheckpoint,
    ConversationMemoryContext,
    IEnhancedConversationPersistence
} from '../interfaces/IEnhancedMemorySystem';

// Legacy interface for backward compatibility
export interface ConversationData {
    id: string;
    title: string;
    messages: LLMMessage[];
    createdAt: Date;
    updatedAt: Date;
    provider: string;
    model: string;
    totalCost: number;
    totalTokens: { input: number; output: number };
}

export interface ConversationSummary {
    id: string;
    title: string;
    createdAt: Date;
    updatedAt: Date;
    messageCount: number;
    provider: string;
    model: string;
}

export class ConversationPersistenceService implements IEnhancedConversationPersistence {
    private readonly _context: vscode.ExtensionContext;
    private readonly _maxConversations = 100; // Maximum conversations to keep
    private readonly _maxMessagesPerConversation = 200; // Maximum messages per conversation
    private readonly _maxCheckpointsPerConversation = 20; // Maximum checkpoints per conversation

    constructor(context: vscode.ExtensionContext) {
        this._context = context;
    }

    /**
     * Get current project ID from workspace
     */
    private getCurrentProjectId(): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].name;
        }
        return 'default';
    }

    /**
     * Save a conversation to persistent storage (Legacy interface - converts to enhanced)
     */
    public async saveConversation(conversation: ConversationData): Promise<void> {
        // Convert legacy conversation to enhanced format
        const enhanced: EnhancedConversationData = {
            ...conversation,
            messages: conversation.messages.map((msg, index) => ({
                ...msg,
                id: `msg_${Date.now()}_${index}`,
                timestamp: Date.now()
            })),
            checkpoints: [],
            memoryContext: {
                userPreferences: [],
                projectMemories: [],
                conversationLearnings: [],
                codePatterns: []
            },
            projectId: this.getCurrentProjectId()
        };

        await this.saveEnhancedConversation(enhanced);
    }

    /**
     * Load a specific conversation by ID (Enhanced interface implementation)
     */
    public async loadConversation(conversationId: string): Promise<EnhancedConversationData | undefined> {
        try {
            const enhanced = await this.loadEnhancedConversation(conversationId);
            return enhanced;
        } catch (error) {
            console.error('Failed to load conversation:', error);
            return undefined;
        }
    }

    /**
     * Get all conversations (Enhanced interface implementation)
     */
    public async getAllConversations(projectId?: string): Promise<EnhancedConversationData[]> {
        try {
            return await this.getAllEnhancedConversations(projectId);
        } catch (error) {
            console.error('Failed to get conversations:', error);
            return [];
        }
    }

    /**
     * Get conversation summaries (without full message content)
     */
    public async getConversationSummaries(): Promise<ConversationSummary[]> {
        try {
            const conversations = await this.getAllConversations();
            return conversations.map(conv => ({
                id: conv.id,
                title: conv.title,
                createdAt: conv.createdAt,
                updatedAt: conv.updatedAt,
                messageCount: conv.messages.length,
                provider: conv.provider,
                model: conv.model
            }));
        } catch (error) {
            console.error('Failed to get conversation summaries:', error);
            return [];
        }
    }

    /**
     * Delete a conversation
     */
    public async deleteConversation(conversationId: string): Promise<void> {
        try {
            const conversations = await this.getAllConversations();
            const filteredConversations = conversations.filter(c => c.id !== conversationId);
            await this._context.globalState.update('conversations', filteredConversations);
        } catch (error) {
            console.error('Failed to delete conversation:', error);
        }
    }

    /**
     * Update conversation metadata (title, etc.)
     */
    public async updateConversationMetadata(
        conversationId: string, 
        updates: Partial<Pick<ConversationData, 'title' | 'provider' | 'model'>>
    ): Promise<void> {
        try {
            const conversations = await this.getAllConversations();
            const conversation = conversations.find(c => c.id === conversationId);
            
            if (conversation) {
                Object.assign(conversation, updates);
                conversation.updatedAt = new Date();
                await this.saveConversation(conversation);
            }
        } catch (error) {
            console.error('Failed to update conversation metadata:', error);
        }
    }

    /**
     * Generate a conversation title from the first user message
     */
    public generateConversationTitle(messages: LLMMessage[]): string {
        const firstUserMessage = messages.find(m => m.role === 'user');
        if (firstUserMessage) {
            // Take first 50 characters and add ellipsis if longer
            const content = firstUserMessage.content.trim();
            return content.length > 50 ? content.substring(0, 47) + '...' : content;
        }
        return `New Chat - ${new Date().toLocaleDateString()}`;
    }

    /**
     * Clear all conversations (for privacy/reset)
     */
    public async clearAllConversations(): Promise<void> {
        try {
            // Clear both legacy and enhanced conversations
            await this._context.globalState.update('conversations', []);
            await this._context.globalState.update('enhancedConversations', []);
            console.log('ConversationPersistenceService: All conversations cleared');
        } catch (error) {
            console.error('Failed to clear conversations:', error);
            throw error;
        }
    }

    /**
     * Export a single conversation to file
     */
    public async exportConversation(conversation: ConversationData): Promise<void> {
        try {
            const fileName = `conversation_${conversation.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;
            const jsonContent = JSON.stringify(conversation, null, 2);

            const saveUri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(fileName),
                filters: {
                    'JSON Files': ['json'],
                    'All Files': ['*']
                }
            });

            if (saveUri) {
                await vscode.workspace.fs.writeFile(saveUri, Buffer.from(jsonContent, 'utf8'));
                vscode.window.showInformationMessage(`Conversation exported to ${saveUri.fsPath}`);
            }
        } catch (error) {
            console.error('Failed to export conversation:', error);
            throw error;
        }
    }

    /**
     * Export conversations to JSON
     */
    public async exportConversations(): Promise<string> {
        try {
            const conversations = await this.getAllConversations();
            return JSON.stringify(conversations, null, 2);
        } catch (error) {
            console.error('Failed to export conversations:', error);
            return '[]';
        }
    }

    /**
     * Import conversations from JSON
     */
    public async importConversations(jsonData: string): Promise<void> {
        try {
            const importedConversations = JSON.parse(jsonData) as ConversationData[];
            
            // Validate and merge with existing conversations
            const existingConversations = await this.getAllConversations();
            const allConversations = [...existingConversations];

            for (const imported of importedConversations) {
                // Convert to enhanced format
                const enhanced: EnhancedConversationData = {
                    ...imported,
                    messages: imported.messages.map((msg, index) => ({
                        ...msg,
                        id: `msg_${Date.now()}_${index}`,
                        timestamp: Date.now()
                    })),
                    checkpoints: [],
                    memoryContext: {
                        userPreferences: [],
                        projectMemories: [],
                        conversationLearnings: [],
                        codePatterns: []
                    },
                    projectId: this.getCurrentProjectId()
                };

                // Check if conversation already exists
                const existingIndex = allConversations.findIndex(c => c.id === enhanced.id);
                if (existingIndex >= 0) {
                    // Update existing
                    allConversations[existingIndex] = enhanced;
                } else {
                    // Add new
                    allConversations.push(enhanced);
                }
            }

            // Sort and trim
            allConversations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
            if (allConversations.length > this._maxConversations) {
                allConversations.splice(this._maxConversations);
            }

            await this._context.globalState.update('conversations', allConversations);
        } catch (error) {
            console.error('Failed to import conversations:', error);
            throw new Error('Invalid conversation data format');
        }
    }

    // Enhanced Memory System Methods

    /**
     * Save enhanced conversation with threading and memory context
     */
    public async saveEnhancedConversation(conversation: EnhancedConversationData): Promise<void> {
        try {
            const conversations = await this.getAllEnhancedConversations();

            // Update existing or add new conversation
            const existingIndex = conversations.findIndex(c => c.id === conversation.id);
            if (existingIndex >= 0) {
                conversations[existingIndex] = conversation;
            } else {
                conversations.push(conversation);
            }

            // Trim messages if too many
            if (conversation.messages.length > this._maxMessagesPerConversation) {
                conversation.messages = conversation.messages.slice(-this._maxMessagesPerConversation);
            }

            // Trim checkpoints if too many
            if (conversation.checkpoints.length > this._maxCheckpointsPerConversation) {
                conversation.checkpoints = conversation.checkpoints.slice(-this._maxCheckpointsPerConversation);
            }

            // Sort by updated date (most recent first)
            conversations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

            // Keep only the most recent conversations
            if (conversations.length > this._maxConversations) {
                conversations.splice(this._maxConversations);
            }

            await this._context.globalState.update('enhancedConversations', conversations);
        } catch (error) {
            console.error('Failed to save enhanced conversation:', error);
        }
    }

    /**
     * Load enhanced conversation by ID
     */
    public async loadEnhancedConversation(conversationId: string): Promise<EnhancedConversationData | undefined> {
        try {
            const conversations = await this.getAllEnhancedConversations();
            return conversations.find(c => c.id === conversationId);
        } catch (error) {
            console.error('Failed to load enhanced conversation:', error);
            return undefined;
        }
    }

    /**
     * Get all enhanced conversations
     */
    public async getAllEnhancedConversations(projectId?: string): Promise<EnhancedConversationData[]> {
        try {
            const conversations = this._context.globalState.get<EnhancedConversationData[]>('enhancedConversations', []);

            // Convert date strings back to Date objects
            const processedConversations = conversations.map(conv => ({
                ...conv,
                createdAt: new Date(conv.createdAt),
                updatedAt: new Date(conv.updatedAt),
                messages: conv.messages.map(msg => ({
                    ...msg,
                    timestamp: msg.timestamp || Date.now()
                })),
                checkpoints: conv.checkpoints || [],
                memoryContext: conv.memoryContext || {
                    userPreferences: [],
                    projectMemories: [],
                    conversationLearnings: [],
                    codePatterns: []
                }
            }));

            if (projectId) {
                return processedConversations.filter(c => c.projectId === projectId);
            }

            return processedConversations;
        } catch (error) {
            console.error('Failed to get enhanced conversations:', error);
            return [];
        }
    }

    /**
     * Create a checkpoint for a conversation
     */
    public async createCheckpoint(
        conversationId: string,
        name: string,
        description?: string
    ): Promise<SessionCheckpoint> {
        try {
            const conversation = await this.loadEnhancedConversation(conversationId);
            if (!conversation) {
                throw new Error(`Conversation ${conversationId} not found`);
            }

            const checkpoint: SessionCheckpoint = {
                id: `checkpoint_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                name,
                description,
                timestamp: Date.now(),
                conversationId,
                messageIndex: conversation.messages.length - 1,
                userNotes: '',
                tags: []
            };

            conversation.checkpoints.push(checkpoint);
            await this.saveEnhancedConversation(conversation);

            return checkpoint;
        } catch (error) {
            console.error('Failed to create checkpoint:', error);
            throw error;
        }
    }

    /**
     * Load a specific checkpoint
     */
    public async loadCheckpoint(checkpointId: string): Promise<SessionCheckpoint | undefined> {
        try {
            const conversations = await this.getAllEnhancedConversations();

            for (const conversation of conversations) {
                const checkpoint = conversation.checkpoints.find(cp => cp.id === checkpointId);
                if (checkpoint) {
                    return checkpoint;
                }
            }

            return undefined;
        } catch (error) {
            console.error('Failed to load checkpoint:', error);
            return undefined;
        }
    }

    /**
     * Get all checkpoints for a conversation
     */
    public async getCheckpoints(conversationId: string): Promise<SessionCheckpoint[]> {
        try {
            const conversation = await this.loadEnhancedConversation(conversationId);
            return conversation?.checkpoints || [];
        } catch (error) {
            console.error('Failed to get checkpoints:', error);
            return [];
        }
    }

    /**
     * Delete a checkpoint
     */
    public async deleteCheckpoint(checkpointId: string): Promise<void> {
        try {
            const conversations = await this.getAllEnhancedConversations();

            for (const conversation of conversations) {
                const checkpointIndex = conversation.checkpoints.findIndex(cp => cp.id === checkpointId);
                if (checkpointIndex >= 0) {
                    conversation.checkpoints.splice(checkpointIndex, 1);
                    await this.saveEnhancedConversation(conversation);
                    return;
                }
            }
        } catch (error) {
            console.error('Failed to delete checkpoint:', error);
        }
    }

    // Legacy compatibility methods - removed duplicates

    /**
     * Create a branch from an existing conversation
     */
    public async createBranch(
        parentId: string,
        branchFromMessageId: string,
        title?: string
    ): Promise<EnhancedConversationData> {
        try {
            const parentConversation = await this.loadEnhancedConversation(parentId);
            if (!parentConversation) {
                throw new Error(`Parent conversation ${parentId} not found`);
            }

            // Find the branch point message
            const branchPointIndex = parentConversation.messages.findIndex(m => m.id === branchFromMessageId);
            if (branchPointIndex === -1) {
                throw new Error(`Message ${branchFromMessageId} not found in conversation`);
            }

            // Create new conversation with messages up to branch point
            const branchedConversation: EnhancedConversationData = {
                id: `branch_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                title: title || `Branch from ${parentConversation.title}`,
                messages: parentConversation.messages.slice(0, branchPointIndex + 1),
                createdAt: new Date(),
                updatedAt: new Date(),
                provider: parentConversation.provider,
                model: parentConversation.model,
                totalCost: 0,
                totalTokens: { input: 0, output: 0 },
                parentId: parentId,
                branchPoint: branchFromMessageId,
                checkpoints: [],
                memoryContext: { ...parentConversation.memoryContext },
                projectId: parentConversation.projectId,
                workspaceRoot: parentConversation.workspaceRoot
            };

            await this.saveEnhancedConversation(branchedConversation);
            return branchedConversation;
        } catch (error) {
            console.error('Failed to create branch:', error);
            throw error;
        }
    }

    /**
     * Get all branches of a conversation
     */
    public async getBranches(conversationId: string): Promise<EnhancedConversationData[]> {
        try {
            const conversations = await this.getAllEnhancedConversations();
            return conversations.filter(c => c.parentId === conversationId);
        } catch (error) {
            console.error('Failed to get branches:', error);
            return [];
        }
    }

    /**
     * Search conversations by content
     */
    public async searchConversations(query: string, projectId?: string): Promise<EnhancedConversationData[]> {
        try {
            const conversations = await this.getAllEnhancedConversations(projectId);
            const queryLower = query.toLowerCase();

            return conversations.filter(conversation => {
                // Search in title
                if (conversation.title.toLowerCase().includes(queryLower)) {
                    return true;
                }

                // Search in messages
                return conversation.messages.some(message =>
                    message.content.toLowerCase().includes(queryLower)
                );
            });
        } catch (error) {
            console.error('Failed to search conversations:', error);
            return [];
        }
    }

    /**
     * Get conversations by time range
     */
    public async getConversationsByTimeRange(
        startDate: Date,
        endDate: Date,
        projectId?: string
    ): Promise<EnhancedConversationData[]> {
        try {
            const conversations = await this.getAllEnhancedConversations(projectId);

            return conversations.filter(conversation => {
                const createdAt = new Date(conversation.createdAt);
                return createdAt >= startDate && createdAt <= endDate;
            });
        } catch (error) {
            console.error('Failed to get conversations by time range:', error);
            return [];
        }
    }

    /**
     * Get conversations by provider/model
     */
    public async getConversationsByProvider(provider: string, model?: string): Promise<EnhancedConversationData[]> {
        try {
            const conversations = await this.getAllEnhancedConversations();

            return conversations.filter(conversation => {
                if (conversation.provider !== provider) {
                    return false;
                }

                if (model && conversation.model !== model) {
                    return false;
                }

                return true;
            });
        } catch (error) {
            console.error('Failed to get conversations by provider:', error);
            return [];
        }
    }


}
