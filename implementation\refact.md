# Refact Implementation Details

This document provides a detailed overview of how Refact works internally, with a focus on memory storage, file operations, and other core functionalities.

## Memory Management

Refact stores memories using a combination of local database and API calls. Here's how the memory system works:

### Memory Data Structure

```rust
#[derive(Default, Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct MemoRecord {
    pub iknow_id: String,
    pub iknow_tags: Vec<String>,
    pub iknow_memory: String,
}
```

### Adding Memories

Memories are added using the `memories_add` function:

```rust
pub async fn memories_add(
    gcx: Arc<ARwLock<GlobalContext>>,
    m_type: &str,
    m_memory: &str,
    unknown_project: bool
) -> Result<(), String> {
    let client = reqwest::Client::new();
    let api_key = gcx.read().await.cmdline.api_key.clone();
    let active_group_id = gcx.read().await.active_group_id.clone()
        .ok_or("active_group_id must be set")?;
    let mut body = serde_json::json!({
        "group_id": active_group_id,
        "iknow_tags": vec![m_type.to_string()],
        "knowledge_memory": m_memory
    });
    if !unknown_project {
        body["group_id"] = Value::from(active_group_id.clone());
    }
    // ... API call implementation
}
```

### Memory Migration

Refact includes functionality to migrate memories from legacy storage:

```rust
pub async fn memories_migration(
    gcx: Arc<ARwLock<GlobalContext>>,
    config_dir: PathBuf
) {
    // Disable migration for now
    if true {
        return;
    }  
    
    if let None = gcx.read().await.active_group_id.clone() {
        info!("No active group set up, skipping memory migration");
        return;
    }
    
    let legacy_db_path = config_dir.join("memories.sqlite");
    if !legacy_db_path.exists() {
        return;
    }
    
    info!("Found legacy memory database at {:?}, starting migration", legacy_db_path);
    
    // ... migration implementation
}
```

## File Operations

Refact has robust file handling capabilities for reading, writing, and tracking files in the workspace.

### Document Structure

```rust
#[derive(Debug, Eq, Hash, PartialEq, Clone)]
pub struct Document {
    pub doc_path: PathBuf,
    pub doc_text: Option<Rope>,
}

impl Document {
    pub fn new(doc_path: &PathBuf) -> Self {
        Self { doc_path: doc_path.clone(), doc_text: None }
    }

    pub async fn update_text_from_disk(&mut self, gcx: Arc<ARwLock<GlobalContext>>) -> Result<(), String> {
        match read_file_from_disk(load_privacy_if_needed(gcx.clone()).await, &self.doc_path).await {
            Ok(res) => {
                self.doc_text = Some(res);
                return Ok(());
            },
            Err(e) => {
                return Err(e)
            }
        }
    }

    pub async fn get_text_or_read_from_disk(&mut self, gcx: Arc<ARwLock<GlobalContext>>) -> Result<String, String> {
        if self.doc_text.is_some() {
            return Ok(self.doc_text.as_ref().unwrap().to_string());
        }
        read_file_from_disk(load_privacy_if_needed(gcx.clone()).await, &self.doc_path).await.map(|x|x.to_string())
    }
}
```

### Reading Files

Refact reads files from memory or disk using this function:

```rust
pub async fn get_file_text_from_memory_or_disk(global_context: Arc<ARwLock<GlobalContext>>, file_path: &PathBuf) -> Result<String, String>
{
    check_file_privacy(load_privacy_if_needed(global_context.clone()).await, &file_path, &FilePrivacyLevel::AllowToSendAnywhere)?;

    if let Some(doc) = global_context.read().await.documents_state.memory_document_map.get(file_path) {
        let doc = doc.read().await;
        if doc.doc_text.is_some() {
            return Ok(doc.doc_text.as_ref().unwrap().to_string());
        }
    }
    read_file_from_disk_without_privacy_check(&file_path)
        .await.map(|x|x.to_string())
        .map_err(|e|format!("Not found in memory, not found on disk: {}", e))
}

async fn read_file_from_disk_without_privacy_check(
    path: &PathBuf,
) -> Result<Rope, String> {
    tokio::fs::read_to_string(path).await
        .map(|x|Rope::from_str(&x))
        .map_err(|e|
            format!("failed to read file {}: {}", crate::nicer_logs::last_n_chars(&path.display().to_string(), 30), e)
        )
}
```

### Writing Files

Refact writes files using this function:

```rust
pub async fn write_file(gcx: Arc<ARwLock<GlobalContext>>, path: &PathBuf, file_text: &String, dry: bool) -> Result<(String, String), String> {
    let parent = path.parent().ok_or(format!(
        "Failed to Add: {:?}. Path is invalid.\nReason: path must have had a parent directory",
        path
    ))?;
    
    if !parent.exists() {
        if !dry {
            fs::create_dir_all(&parent).map_err(|e| {
                let err = format!("Failed to Add: {:?}; Its parent dir {:?} did not exist and attempt to create it failed.\nERROR: {}", path, parent, e);
                warn!("{err}");
                err
            })?;
        }
    }
    
    let before_text = if path.exists() {
        get_file_text_from_memory_or_disk(gcx.clone(), path).await?
    } else {
        "".to_string()
    };
    
    // ... implementation for writing the file
}
```

### File Change Tracking

Refact tracks file changes with these functions:

```rust
pub async fn on_did_open(
    gcx: Arc<ARwLock<GlobalContext>>,
    cpath: &PathBuf,
    text: &String,
    _language_id: &String,
) {
    let mut doc = Document::new(cpath);
    doc.update_text(text);
    info!("on_did_open {}", crate::nicer_logs::last_n_chars(&cpath.display().to_string(), 30));
    let (_doc_arc, dirty_arc, mark_dirty) = mem_overwrite_or_create_document(gcx.clone(), doc).await;
    if mark_dirty {
        let now = std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs_f64();
        *dirty_arc.lock().await = now;
    }
    gcx.write().await.documents_state.active_file_path = Some(cpath.clone());
}

pub async fn on_did_change(
    gcx: Arc<ARwLock<GlobalContext>>,
    path: &PathBuf,
    text: &String,
) {
    let t0 = Instant::now();
    let (doc_arc, dirty_arc, mark_dirty) = {
        let mut doc = Document::new(path);
        doc.update_text(text);
        let (doc_arc, dirty_arc, set_mark_dirty) = mem_overwrite_or_create_document(gcx.clone(), doc).await;
        (doc_arc, dirty_arc, set_mark_dirty)
    };

    if mark_dirty {
        let now = std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs_f64();
        *dirty_arc.lock().await = now;
    }
    // ... additional implementation
}

pub async fn on_did_delete(gcx: Arc<ARwLock<GlobalContext>>, path: &PathBuf)
{
    info!("on_did_delete {}", crate::nicer_logs::last_n_chars(&path.to_string_lossy().to_string(), 30));

    let (vec_db_module, ast_service, dirty_arc) = {
        let mut cx = gcx.write().await;
        cx.documents_state.memory_document_map.remove(path);
        (cx.vec_db.clone(), cx.ast_service.clone(), cx.documents_state.cache_dirty.clone())
    };

    let now = std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs_f64();
    (*dirty_arc.lock().await) = now;

    // ... additional implementation for vector database updates
}
```

## AST (Abstract Syntax Tree) Management

Refact maintains an AST index for code understanding and navigation.

### AST Database Structure

```rust
pub struct AstDB {
    pub sleddb: Arc<sled::Db>,
    pub sledbatch: Arc<AMutex<sled::Batch>>,
    pub batch_counter: usize,
    pub counters_increase: HashMap<String, i32>,
    pub ast_max_files: usize,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct AstStatus {
    #[serde(skip)]
    pub astate_notify: Arc<ANotify>,
    #[serde(rename = "state")]
    pub astate: String,
    pub files_unparsed: usize,
    pub files_total: usize,
    pub ast_index_files_total: i32,
    pub ast_index_symbols_total: i32,
    pub ast_index_usages_total: i32,
    pub ast_max_files_hit: bool,
}
```

### AST Initialization

```rust
pub async fn ast_index_init(ast_permanent: String, ast_max_files: usize, want_perf_report: bool) -> Arc<AMutex<AstDB>>
{
    let mut config = sled::Config::default()
        .cache_capacity(CACHE_CAPACITY_BYTES)
        .use_compression(false)
        .print_profile_on_drop(want_perf_report)
        .mode(sled::Mode::HighThroughput)
        .flush_every_ms(Some(5000));

    if ast_permanent.is_empty() {
        config = config.temporary(true).create_new(true);
    } else {
        config = config.path(ast_permanent.clone());
    }
    // ... additional implementation
}
```

### Adding Documents to AST

```rust
pub async fn doc_add(
    ast_index: Arc<AMutex<AstDB>>,
    cpath: &String,
    text: &String,
    errors: &mut AstErrorStats,
) -> Result<(Vec<Arc<AstDefinition>>, String), String>
{
    let file_global_path = filesystem_path_to_double_colon_path(cpath);
    let (defs, language) = parse_anything_and_add_file_path(&cpath, text, errors)?;   // errors mostly "no such parser" here
    let db = ast_index.lock().await.sleddb.clone();
    let batch_arc = flush_sled_batch(ast_index.clone(), 1000).await;
    let mut batch = batch_arc.lock().await;
    let mut added_defs: i32 = 0;
    let mut added_usages: i32 = 0;
    let mut unresolved_usages: i32 = 0;
    // ... additional implementation
}
```

## File Path Correction

Refact includes sophisticated path correction to handle file references:

```rust
async fn _correct_to_nearest(
    gcx: Arc<ARwLock<GlobalContext>>,
    correction_candidate: &String,
    is_dir: bool,
    fuzzy: bool,
    top_n: usize,
) -> Vec<String> {
    if let Some(fixed) = complete_path_with_project_dir(gcx.clone(), correction_candidate, is_dir).await {
        return vec![fixed.to_string_lossy().to_string()];
    }

    let cache_correction_arc = files_cache_rebuild_as_needed(gcx.clone()).await;
    // it's dangerous to use cache_correction_arc without a mutex, but should be fine as long as it's read-only
    // (another thread never writes to the map itself, it can only replace the arc with a different map)

    // NOTE: do we need top_n here?
    let correction_cache = if is_dir {
        &cache_correction_arc.directories
    } else {
        &cache_correction_arc.filenames
    };
    let matches = correction_cache.find_matches(&PathBuf::from(correction_candidate));
    if matches.is_empty() {
        info!("not found {:?} in cache_correction, is_dir={}", correction_candidate, is_dir);
    } else {
        return matches.iter().map(|p| p.to_string_lossy().to_string()).collect::<Vec<String>>();
    }

    if fuzzy {
        info!("fuzzy search {:?} is_dir={}, cache_fuzzy_arc.len={}", correction_candidate, is_dir, correction_cache.len());
        return fuzzy_search(correction_candidate, correction_cache.short_paths_iter(), top_n, &['/', '\\']);
    }

    vec![]
}

pub async fn correct_to_nearest_filename(
    gcx: Arc<ARwLock<GlobalContext>>,
    correction_candidate: &String,
    fuzzy: bool,
    top_n: usize,
) -> Vec<String> {
    _correct_to_nearest(gcx, correction_candidate, false, fuzzy, top_n).await
}
```

## Git Integration

Refact includes Git integration for creating checkpoints:

```rust
// Creating Git checkpoints
let checkpoint = {
    let branch = get_or_create_branch(&repo, &format!("refact-{chat_id}"))?;

    let (_, mut file_changes) = get_diff_statuses(git2::StatusShow::Workdir, &repo, false)?;

    let (nested_file_changes, flatened_nested_file_changes) = 
        get_file_changes_from_nested_repos(&repo, &nested_repos, false)?;
    file_changes.extend(flatened_nested_file_changes);

    stage_changes(&repo, &file_changes, &abort_flag)?;
    let commit_oid = commit(&repo, &branch, &format!("Auto commit for chat {chat_id}"), "Refact Agent", "<EMAIL>")?;
    
    for (nested_repo, changes) in nested_file_changes {
        stage_changes(&nested_repo, &changes, &abort_flag)?;
    }

    Checkpoint {workspace_folder, commit_hash: commit_oid.to_string()}
};
```

## Document State Management

Refact maintains a comprehensive state of all documents in the workspace:

```rust
pub struct DocumentsState {
    pub workspace_folders: Arc<StdMutex<Vec<PathBuf>>>,
    pub workspace_files: Arc<StdMutex<Vec<PathBuf>>>,
    pub workspace_vcs_roots: Arc<StdMutex<Vec<PathBuf>>>,
    pub active_file_path: Option<PathBuf>,
    pub jsonl_files: Arc<StdMutex<Vec<PathBuf>>>,
    // document_map on windows: c%3A/Users/<USER>/file.ext
    // query on windows: C:/Users/<USER>/Documents/file.ext
    pub memory_document_map: HashMap<PathBuf, Arc<ARwLock<Document>>>,   // if a file is open in IDE, and it's outside workspace dirs, it will be in this map and not in workspace_files
    pub cache_dirty: Arc<AMutex<f64>>,
    pub cache_correction: Arc<CacheCorrection>,
    pub fs_watcher: Arc<ARwLock<RecommendedWatcher>>,
}

impl DocumentsState {
    pub async fn new(
        workspace_dirs: Vec<PathBuf>,
    ) -> Self {
        let watcher = RecommendedWatcher::new(|_|{}, Default::default()).unwrap();
        Self {
            workspace_folders: Arc::new(StdMutex::new(workspace_dirs)),
            workspace_files: Arc::new(StdMutex::new(Vec::new())),
            workspace_vcs_roots: Arc::new(StdMutex::new(Vec::new())),
            active_file_path: None,
            jsonl_files: Arc::new(StdMutex::new(Vec::new())),
            memory_document_map: HashMap::new(),
            cache_dirty: Arc::new(AMutex::<f64>::new(0.0)),
            cache_correction: Arc::new(CacheCorrection::new()),
            fs_watcher: Arc::new(ARwLock::new(watcher)),
        }
    }
}
```

## Text Diffing

Refact includes utilities for comparing text:

```rust
pub fn get_add_del_chars_from_texts(
    text_a: &String,
    text_b: &String,
) -> (String, String) {
    let diff = TextDiff::from_chars(text_a, text_b);
    let mut added = "".to_string();
    let mut removed = "".to_string();
    for change in diff.iter_all_changes() {
        match change.tag() {
            ChangeTag::Delete => {
                removed += change.value();
            }
            ChangeTag::Insert => {
                added += change.value();
            }
            ChangeTag::Equal => {
            }
        }
    }

    (added, removed)
}
```

## File Creation Tool

Refact includes a tool for creating text documents:

```rust
pub async fn tool_create_text_doc_exec(
    gcx: Arc<ARwLock<GlobalContext>>,
    args: &HashMap<String, Value>,
    dry: bool
) -> Result<(String, String, Vec<DiffChunk>), String> {
    let privacy_settings = load_privacy_if_needed(gcx.clone()).await;
    let args = parse_args(gcx.clone(), args, privacy_settings).await?;
    await_ast_indexing(gcx.clone()).await?;
    let (before_text, after_text) = write_file(gcx.clone(), &args.path, &args.content, dry).await?;
    sync_documents_ast(gcx.clone(), &args.path).await?;
    let diff_chunks = convert_edit_to_diffchunks(args.path.clone(), &before_text, &after_text)?;
    Ok((before_text, after_text, diff_chunks))
}
```

## Configuration and Storage

Refact uses several directories for configuration and storage:

```python
PERMDIR = os.environ.get("REFACT_PERM_DIR", "") or os.path.expanduser("~/.refact/perm-storage")
TMPDIR = os.environ.get("REFACT_TMP_DIR", "") or os.path.expanduser("~/.refact/tmp")
FLAG_FACTORY_RESET = os.path.join(PERMDIR, "_factory_reset.flag")
FLAG_HF_HUB_OFFLINE = os.path.join(PERMDIR, "hf_hub_offline.flag")

DIR_CONFIG     = os.path.join(PERMDIR, "cfg")
DIR_WATCHDOG_D = os.path.join(PERMDIR, "cfg", "watchdog.d")
DIR_WEIGHTS    = os.path.join(PERMDIR, "weights")
DIR_LORAS      = os.path.join(PERMDIR, "loras")
DIR_LOGS       = os.path.join(PERMDIR, "logs")
DIR_PROJECTS   = os.path.join(PERMDIR, "projects")
DIR_SSH_KEYS   = os.path.join(PERMDIR, "ssh-keys")
DIR_TOKENIZERS   = os.path.join(PERMDIR, "tokenizers")
```

## Command Line Options

Refact supports various command line options for configuration:

```rust
#[structopt(long, help="Use AST, for it to start working, give it a jsonl files list or LSP workspace folders.")]
pub ast: bool,
//