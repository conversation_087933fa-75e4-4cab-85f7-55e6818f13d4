// Test the WorkspaceValidationService fix
const path = require('path');

// Mock the workspace validation logic to test our fix
class MockWorkspaceValidationService {
    validateFilePath(filePath) {
        try {
            // Basic path validation
            if (!filePath || filePath.trim() === '') {
                return { valid: false, error: 'File path cannot be empty' };
            }

            // Check for dangerous path patterns
            if (filePath.includes('..')) {
                return { valid: false, error: 'Path traversal (..) is not allowed for security reasons' };
            }

            if (path.isAbsolute(filePath)) {
                return { valid: false, error: 'Absolute paths are not allowed. Use relative paths within the workspace.' };
            }

            // Simulate workspace folder
            const workspacePath = 'C:\\workspace';
            const resolvedPath = path.resolve(workspacePath, filePath);

            // Ensure the resolved path is within workspace boundaries
            if (!resolvedPath.startsWith(workspacePath)) {
                return { 
                    valid: false, 
                    error: `File path '${filePath}' resolves outside the workspace boundary.` 
                };
            }

            // NEW FIX: Additional security checks for malicious patterns
            // Note: We don't check normalizedPath !== filePath because path separators 
            // may differ between platforms (/ vs \) and this causes false positives
            const suspiciousPatterns = [
                /[\x00-\x1f\x7f]/,  // Control characters
                /[<>:"|?*]/,        // Invalid filename characters on Windows
                /^\s|\s$/,          // Leading/trailing whitespace
                /\/{2,}/,           // Multiple consecutive slashes
                /\\{2,}/            // Multiple consecutive backslashes
            ];

            for (const pattern of suspiciousPatterns) {
                if (pattern.test(filePath)) {
                    return { 
                        valid: false, 
                        error: `File path contains invalid characters or sequences. Use clean relative paths.` 
                    };
                }
            }

            return { valid: true, resolvedPath };

        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : 'Unknown validation error' 
            };
        }
    }
}

// Test the fix
function testValidationFix() {
    const validator = new MockWorkspaceValidationService();
    
    console.log('Testing WorkspaceValidationService fix...\n');
    
    // These should now PASS (previously failed due to path separator differences)
    const testCases = [
        'index.html',
        'src/main.js',
        'components/Header.jsx',
        'styles/main.css',
        'docs/README.md',
        'folder/subfolder/file.txt'
    ];
    
    console.log('Testing paths that should now PASS:');
    testCases.forEach(testPath => {
        const result = validator.validateFilePath(testPath);
        const status = result.valid ? '✅ PASS' : '❌ FAIL';
        console.log(`  ${testPath}: ${status}`);
        if (!result.valid) {
            console.log(`    Error: ${result.error}`);
        }
    });
    
    // These should still FAIL (security checks)
    const invalidCases = [
        '../outside.txt',
        '/absolute/path.txt',
        'file\x00name.txt',
        'file<name>.txt',
        '  leading-space.txt',
        'double//slash.txt'
    ];
    
    console.log('\nTesting paths that should still FAIL:');
    invalidCases.forEach(testPath => {
        const result = validator.validateFilePath(testPath);
        const status = result.valid ? '❌ UNEXPECTED PASS' : '✅ CORRECTLY FAILED';
        console.log(`  ${testPath}: ${status}`);
        if (!result.valid) {
            console.log(`    Error: ${result.error}`);
        }
    });
}

testValidationFix();
