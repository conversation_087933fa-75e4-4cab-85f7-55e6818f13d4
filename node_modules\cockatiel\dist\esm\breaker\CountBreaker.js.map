{"version": 3, "file": "CountBreaker.js", "sourceRoot": "", "sources": ["../../../src/breaker/CountBreaker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAgCvD,MAAM,OAAO,YAAY;IAavB;;OAEG;IACH,IAAW,KAAK;QACd,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;SACG,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK,CAAC,KAAc;QAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAwB;QA7B1E,cAAS,GAAG,CAAC,CAAC;QACd,aAAQ,GAAG,CAAC,CAAC;QACb,kBAAa,GAAG,CAAC,CAAC;QA4BxB,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,UAAU,CAAC,wDAAwD,SAAS,EAAE,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,UAAU,CAAC,uDAAuD,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;QACD,IACE,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC3C,oBAAoB,GAAG,CAAC;YACxB,oBAAoB,GAAG,IAAI,EAC3B,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,iEAAiE,oBAAoB,EAAE,CACxF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,KAAmB;QAChC,IAAI,KAAK,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,KAAmB;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEnB,IAAI,KAAK,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACpB,CAAC;IAEO,MAAM,CAAC,OAAgB;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACtE,CAAC;CACF", "sourcesContent": ["import { CircuitState } from '../CircuitBreakerPolicy';\nimport { IBreaker } from './Breaker';\n\nexport interface ICountBreakerOptions {\n  /**\n   * Percentage (from 0 to 1) of requests that need to fail before we'll\n   * open the circuit.\n   */\n  threshold: number;\n\n  /**\n   * Size of the count based sliding window.\n   */\n  size: number;\n\n  /**\n   * Minimum number of calls needed to (potentially) open the circuit.\n   * Useful to avoid unnecessarily tripping when there are only few samples yet.\n   * Defaults to {@link ICountBreakerOptions.size}.\n   */\n  minimumNumberOfCalls?: number;\n}\n\n\ninterface ICountBreakerState {\n  samples: (boolean | null)[];\n  currentSample: number;\n  failures: number;\n  successes: number;\n}\n\n\nexport class CountBreaker implements IBreaker {\n  private readonly threshold: number;\n  private readonly minimumNumberOfCalls: number;\n\n  /**\n   * The samples in the sliding window. `true` means \"success\", `false` means\n   * \"failure\" and `null` means that there is no sample yet.\n   */\n  private samples: (boolean | null)[];\n  private successes = 0;\n  private failures = 0;\n  private currentSample = 0;\n\n  /**\n   * @inheritdoc\n   */\n  public get state(): unknown {\n    return {\n      samples: this.samples,\n      currentSample: this.currentSample,\n      failures: this.failures,\n      successes: this.successes,\n    } satisfies ICountBreakerState;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public set state(value: unknown) {\n    Object.assign(this, value);\n  }\n\n  /**\n   * CountBreaker breaks if more than `threshold` percentage of the last `size`\n   * calls failed, so long as at least `minimumNumberOfCalls` calls have been\n   * performed (to avoid opening unnecessarily if there are only few samples\n   * in the sliding window yet).\n   */\n  constructor({ threshold, size, minimumNumberOfCalls = size }: ICountBreakerOptions) {\n    if (threshold <= 0 || threshold >= 1) {\n      throw new RangeError(`CountBreaker threshold should be between (0, 1), got ${threshold}`);\n    }\n    if (!Number.isSafeInteger(size) || size < 1) {\n      throw new RangeError(`CountBreaker size should be a positive integer, got ${size}`);\n    }\n    if (\n      !Number.isSafeInteger(minimumNumberOfCalls) ||\n      minimumNumberOfCalls < 1 ||\n      minimumNumberOfCalls > size\n    ) {\n      throw new RangeError(\n        `CountBreaker size should be an integer between (1, size), got ${minimumNumberOfCalls}`,\n      );\n    }\n\n    this.threshold = threshold;\n    this.minimumNumberOfCalls = minimumNumberOfCalls;\n    this.samples = Array.from({ length: size }, () => null);\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public success(state: CircuitState) {\n    if (state === CircuitState.HalfOpen) {\n      this.reset();\n    }\n\n    this.sample(true);\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public failure(state: CircuitState) {\n    this.sample(false);\n\n    if (state !== CircuitState.Closed) {\n      return true;\n    }\n\n    const total = this.successes + this.failures;\n\n    if (total < this.minimumNumberOfCalls) {\n      return false;\n    }\n\n    if (this.failures > this.threshold * total) {\n      return true;\n    }\n\n    return false;\n  }\n\n  private reset() {\n    this.samples.fill(null);\n    this.successes = 0;\n    this.failures = 0;\n  }\n\n  private sample(success: boolean) {\n    const current = this.samples[this.currentSample];\n    if (current === true) {\n      this.successes--;\n    } else if (current === false) {\n      this.failures--;\n    }\n\n    this.samples[this.currentSample] = success;\n    if (success) {\n      this.successes++;\n    } else {\n      this.failures++;\n    }\n\n    this.currentSample = (this.currentSample + 1) % this.samples.length;\n  }\n}\n"]}