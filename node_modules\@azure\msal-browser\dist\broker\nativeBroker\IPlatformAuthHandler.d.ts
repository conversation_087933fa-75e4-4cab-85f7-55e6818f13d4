import { PlatformAuthRequest } from "./PlatformAuthRequest.js";
import { PlatformAuthResponse } from "./PlatformAuthResponse.js";
/**
 * Interface for the Platform Broker Handlers
 */
export interface IPlatformAuthHandler {
    getExtensionId(): string | undefined;
    getExtensionVersion(): string | undefined;
    getExtensionName(): string | undefined;
    sendMessage(request: PlatformAuthRequest): Promise<PlatformAuthResponse>;
}
//# sourceMappingURL=IPlatformAuthHandler.d.ts.map