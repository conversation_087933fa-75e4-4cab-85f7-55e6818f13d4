{"version": 3, "file": "http_proxy.js", "sourceRoot": "", "sources": ["../../src/http_proxy.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAqHH,8BAaC;AAiCD,oCAwCC;AAED,oDA8FC;AAzSD,uCAAgC;AAChC,2CAA2C;AAC3C,6BAAqC;AACrC,6BAA6B;AAC7B,qCAAqC;AACrC,6DAI8B;AAE9B,6CAA6E;AAC7E,6BAA0B;AAC1B,iDAA8C;AAE9C,MAAM,WAAW,GAAG,OAAO,CAAC;AAE5B,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAOD,SAAS,YAAY;IACnB,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB;;;OAGG;IACH,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QAC3B,MAAM,GAAG,YAAY,CAAC;QACtB,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;IACpC,CAAC;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QACnC,MAAM,GAAG,aAAa,CAAC;QACvB,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;IACrC,CAAC;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,GAAG,YAAY,CAAC;QACtB,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;IACpC,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,QAAa,CAAC;IAClB,IAAI,CAAC;QACH,QAAQ,GAAG,IAAI,SAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAA,aAAG,EAAC,wBAAY,CAAC,KAAK,EAAE,0BAA0B,MAAM,WAAW,CAAC,CAAC;QACrE,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QAClC,IAAA,aAAG,EACD,wBAAY,CAAC,KAAK,EAClB,IAAI,QAAQ,CAAC,QAAQ,qCAAqC,CAC3D,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,QAAQ,GAAkB,IAAI,CAAC;IACnC,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAA,aAAG,EAAC,wBAAY,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;YACtD,QAAQ,GAAG,kBAAkB,CAAC,GAAG,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7E,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAC/B,CAAC;IACH,CAAC;IACD,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;IACzB;;8CAE0C;IAC1C,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;QAChB,IAAI,GAAG,IAAI,CAAC;IACd,CAAC;IACD,MAAM,MAAM,GAAc;QACxB,OAAO,EAAE,GAAG,QAAQ,IAAI,IAAI,EAAE;KAC/B,CAAC;IACF,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC;IAC1B,CAAC;IACD,KAAK,CACH,eAAe,GAAG,MAAM,CAAC,OAAO,GAAG,+BAA+B,GAAG,MAAM,CAC5E,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB;IACzB,4EAA4E;IAC5E,IAAI,UAAU,GAAuB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IAC/D,IAAI,MAAM,GAAG,eAAe,CAAC;IAC7B,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;QAClC,MAAM,GAAG,UAAU,CAAC;IACtB,CAAC;IACD,IAAI,UAAU,EAAE,CAAC;QACf,KAAK,CAAC,mDAAmD,GAAG,MAAM,CAAC,CAAC;QACpE,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAOD;;;;GAIG;AAEH,SAAgB,SAAS,CAAC,UAAkB;IAC1C,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACjD,IAAI,CAAC,IAAA,YAAM,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;QAClG,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO;QACL,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,EAAU;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,UAAU,CAAC,IAAkB,EAAE,UAAkB;IACxD,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IAEnC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB;IAChD,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,EAAE,CAAC;QACxC,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,iDAAiD;QACjD,IAAI,IAAA,YAAM,EAAC,UAAU,CAAC,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,8CAA8C;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAgB,YAAY,CAC1B,MAAe,EACf,OAAuB;;IAEvB,MAAM,aAAa,GAAmB;QACpC,MAAM,EAAE,MAAM;QACd,YAAY,EAAE,EAAE;KACjB,CAAC;IACF,IAAI,CAAC,MAAA,OAAO,CAAC,wBAAwB,CAAC,mCAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;QACnD,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC7B,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IACjC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,QAAQ,GAAG,IAAA,0BAAa,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC;IACjC,IAAI,sBAAsB,CAAC,UAAU,CAAC,EAAE,CAAC;QACvC,KAAK,CAAC,+CAA+C,GAAG,IAAA,wBAAW,EAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,YAAY,GAAmB;QACnC,0BAA0B,EAAE,IAAA,wBAAW,EAAC,MAAM,CAAC;KAChD,CAAC;IACF,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;QACpB,YAAY,CAAC,yBAAyB,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;IAC5D,CAAC;IACD,OAAO;QACL,MAAM,EAAE;YACN,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,SAAS,CAAC,OAAO;SACxB;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,OAA0B,EAC1B,cAA8B;;IAE9B,IAAI,CAAC,CAAC,0BAA0B,IAAI,cAAc,CAAC,EAAE,CAAC;QACpD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,MAAM,UAAU,GAAG,cAAc,CAAC,0BAA0B,CAAW,CAAC;IACxE,MAAM,YAAY,GAAG,IAAA,qBAAQ,EAAC,UAAU,CAAC,CAAC;IAC1C,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,MAAM,aAAa,GAAG,IAAA,0BAAa,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,MAAM,QAAQ,GAAG,GAAG,aAAa,CAAC,IAAI,IACpC,MAAA,aAAa,CAAC,IAAI,mCAAI,2BACxB,EAAE,CAAC;IACH,MAAM,OAAO,GAAwB;QACnC,MAAM,EAAE,SAAS;QACjB,IAAI,EAAE,QAAQ;KACf,CAAC;IACF,MAAM,OAAO,GAA6B;QACxC,IAAI,EAAE,QAAQ;KACf,CAAC;IACF,+CAA+C;IAC/C,IAAI,IAAA,2CAAsB,EAAC,OAAO,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC5B,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;IACpC,CAAC;IACD,IAAI,yBAAyB,IAAI,cAAc,EAAE,CAAC;QAChD,OAAO,CAAC,qBAAqB,CAAC;YAC5B,QAAQ;gBACR,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAW,CAAC,CAAC,QAAQ,CACvE,QAAQ,CACT,CAAC;IACN,CAAC;IACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,MAAM,kBAAkB,GAAG,IAAA,8CAAyB,EAAC,OAAO,CAAC,CAAC;IAC9D,KAAK,CAAC,cAAc,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9E,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;YAC5C,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7B,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC3B,KAAK,CACH,4BAA4B;oBAC1B,OAAO,CAAC,IAAI;oBACZ,iBAAiB;oBACjB,kBAAkB,CACrB,CAAC;gBACF,mEAAmE;gBACnE,iEAAiE;gBACjE,qDAAqD;gBACrD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvB,CAAC;gBACD,KAAK,CACH,qDAAqD;oBACnD,OAAO,CAAC,IAAI;oBACZ,iBAAiB;oBACjB,kBAAkB,CACrB,CAAC;gBACF,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,IAAA,aAAG,EACD,wBAAY,CAAC,KAAK,EAClB,uBAAuB;oBACrB,OAAO,CAAC,IAAI;oBACZ,iBAAiB;oBACjB,kBAAkB;oBAClB,eAAe;oBACf,GAAG,CAAC,UAAU,CACjB,CAAC;gBACF,MAAM,EAAE,CAAC;YACX,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YAC1B,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7B,IAAA,aAAG,EACD,wBAAY,CAAC,KAAK,EAClB,6BAA6B;gBAC3B,kBAAkB;gBAClB,cAAc;gBACd,GAAG,CAAC,OAAO,CACd,CAAC;YACF,MAAM,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC"}