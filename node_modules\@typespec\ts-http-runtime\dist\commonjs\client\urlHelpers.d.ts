import type { PathParameterWithOptions, RequestParameters } from "./common.js";
/**
 * Builds the request url, filling in query and path parameters
 * @param endpoint - base url which can be a template url
 * @param routePath - path to append to the endpoint
 * @param pathParameters - values of the path parameters
 * @param options - request parameters including query parameters
 * @returns a full url with path and query parameters
 */
export declare function buildRequestUrl(endpoint: string, routePath: string, pathParameters: (string | number | PathParameterWithOptions)[], options?: RequestParameters): string;
export declare function buildBaseUrl(endpoint: string, options: RequestParameters): string;
/**
 * Replace all of the instances of searchValue in value with the provided replaceValue.
 * @param value - The value to search and replace in.
 * @param searchValue - The value to search for in the value argument.
 * @param replaceValue - The value to replace searchValue with in the value argument.
 * @returns The value where each instance of searchValue was replaced with replacedValue.
 */
export declare function replaceAll(value: string | undefined, searchValue: string, replaceValue: string): string | undefined;
//# sourceMappingURL=urlHelpers.d.ts.map