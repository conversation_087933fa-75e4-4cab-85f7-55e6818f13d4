{"version": 3, "file": "defaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,uBAAuB,IAAI,0BAA0B,EAAE,MAAM,2BAA2B,CAAC;AAClG,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAGhE;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,MAAM,MAAM,GAAG,0BAA0B,EAAE,CAAC;IAC5C,OAAO;QACL,KAAK,CAAC,WAAW,CAAC,OAAO;YACvB,4FAA4F;YAC5F,mFAAmF;YACnF,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,WAAW;gBAClD,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC1C,CAAC,CAAC,EAAE,CAAC;YACP,IAAI,CAAC;gBACH,6CAA6C;gBAC7C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;gBAClC,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,OAA6B,CAAC,CAAC;YACjE,CAAC;oBAAS,CAAC;gBACT,OAAO,aAAP,OAAO,uBAAP,OAAO,EAAI,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createDefaultHttpClient as tspCreateDefaultHttpClient } from \"@typespec/ts-http-runtime\";\nimport { wrapAbortSignalLike } from \"./util/wrapAbortSignal.js\";\nimport { type PipelineRequest as TspPipelineRequest } from \"@typespec/ts-http-runtime\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  const client = tspCreateDefaultHttpClient();\n  return {\n    async sendRequest(request) {\n      // we wrap any AbortSignalLike here since the TypeSpec runtime expects a native AbortSignal.\n      // 99% of the time, this should be a no-op since a native AbortSignal is passed in.\n      const { abortSignal, cleanup } = request.abortSignal\n        ? wrapAbortSignalLike(request.abortSignal)\n        : {};\n      try {\n        // eslint-disable-next-line no-param-reassign\n        request.abortSignal = abortSignal;\n        return await client.sendRequest(request as TspPipelineRequest);\n      } finally {\n        cleanup?.();\n      }\n    },\n  };\n}\n"]}