{"version": 3, "file": "pipeline.js", "sourceRoot": "", "sources": ["../../src/pipeline.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAelC,MAAM,eAAe,GAAG,IAAI,GAAG,CAAgB,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAiG9F;;;;GAIG;AACH,MAAM,YAAY;IAIhB,YAAoB,QAA+B;;QAH3C,cAAS,GAAyB,EAAE,CAAC;QAI3C,IAAI,CAAC,SAAS,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAEM,SAAS,CAAC,MAAsB,EAAE,UAA4B,EAAE;QACrE,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,uBAAuB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,4BAA4B,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClB,MAAM;YACN,OAAO;SACR,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAEM,YAAY,CAAC,OAA0C;QAC5D,MAAM,eAAe,GAAqB,EAAE,CAAC;QAE7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,EAAE;YAC1D,IACE,CAAC,OAAO,CAAC,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC;gBAC/D,CAAC,OAAO,CAAC,KAAK,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,EACnE,CAAC;gBACD,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,OAAO,eAAe,CAAC;IACzB,CAAC;IAEM,WAAW,CAAC,UAAsB,EAAE,OAAwB;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CACnC,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACf,OAAO,CAAC,GAAoB,EAAE,EAAE;gBAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC;QACJ,CAAC,EACD,CAAC,GAAoB,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CACtD,CAAC;QAEF,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEM,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,MAAM;QAClB,OAAO,IAAI,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,aAAa;QACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAkCG;QACH,MAAM,MAAM,GAAqB,EAAE,CAAC;QAEpC,oCAAoC;QACpC,MAAM,SAAS,GAAiC,IAAI,GAAG,EAA2B,CAAC;QAEnF,SAAS,WAAW,CAAC,IAA4B;YAC/C,OAAO;gBACL,IAAI;gBACJ,QAAQ,EAAE,IAAI,GAAG,EAAmB;gBACpC,MAAM,EAAE,KAAK;gBACb,gBAAgB,EAAE,KAAK;aACxB,CAAC;QACJ,CAAC;QAED,iCAAiC;QACjC,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,gBAAgB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAEtC,4BAA4B;QAC5B,MAAM,aAAa,GAAG,CAAC,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAEzF,wDAAwD;QACxD,SAAS,QAAQ,CAAC,KAAgC;YAChD,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;gBACtB,OAAO,UAAU,CAAC;YACpB,CAAC;iBAAM,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;gBACjC,OAAO,cAAc,CAAC;YACxB,CAAC;iBAAM,IAAI,KAAK,KAAK,aAAa,EAAE,CAAC;gBACnC,OAAO,gBAAgB,CAAC;YAC1B,CAAC;iBAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBAC5B,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,8DAA8D;QAC9D,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACjC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACnC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,IAAI,GAAoB;gBAC5B,MAAM;gBACN,SAAS,EAAE,IAAI,GAAG,EAAmB;gBACrC,UAAU,EAAE,IAAI,GAAG,EAAmB;aACvC,CAAC;YACF,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC/C,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC1C,CAAC;YACD,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,kEAAkE;QAClE,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;YACvC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,2BAA2B,UAAU,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,KAAK,MAAM,eAAe,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBACpD,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBACjD,IAAI,SAAS,EAAE,CAAC;wBACd,yCAAyC;wBACzC,qCAAqC;wBACrC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC9B,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;oBACtD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;oBACnD,IAAI,UAAU,EAAE,CAAC;wBACf,0CAA0C;wBAC1C,8BAA8B;wBAC9B,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,SAAS,CAAC,KAAY;YAC7B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;YACpB,kCAAkC;YAClC,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClF,kDAAkD;oBAClD,8BAA8B;oBAC9B,oDAAoD;oBACpD,8CAA8C;oBAC9C,SAAS;gBACX,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC9B,oDAAoD;oBACpD,sCAAsC;oBACtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACzB,mDAAmD;oBACnD,iCAAiC;oBACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACxC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACnC,CAAC;oBACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACnC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,UAAU;YACjB,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAClC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjB,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;oBACjD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACpB,oEAAoE;wBACpE,yDAAyD;wBACzD,8CAA8C;wBAC9C,SAAS,CAAC,OAAO,CAAC,CAAC;oBACrB,CAAC;oBACD,6DAA6D;oBAC7D,OAAO;gBACT,CAAC;gBAED,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;oBAC3B,2CAA2C;oBAC3C,SAAS,CAAC,OAAO,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,CAAC;YACZ,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1C,kEAAkE;YAClE,UAAU,EAAE,CAAC;YACb,6DAA6D;YAC7D,6BAA6B;YAC7B,0CAA0C;YAC1C,IAAI,MAAM,CAAC,MAAM,IAAI,mBAAmB,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,UAAU,mBAAmB;IACjC,OAAO,YAAY,CAAC,MAAM,EAAE,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, PipelineRequest, PipelineResponse, SendRequest } from \"./interfaces.js\";\n\n/**\n * Policies are executed in phases.\n * The execution order is:\n * 1. Serialize Phase\n * 2. Policies not in a phase\n * 3. Deserialize Phase\n * 4. Retry Phase\n * 5. Sign Phase\n */\nexport type PipelinePhase = \"Deserialize\" | \"Serialize\" | \"Retry\" | \"Sign\";\n\nconst ValidPhaseNames = new Set<PipelinePhase>([\"Deserialize\", \"Serialize\", \"Retry\", \"Sign\"]);\n\n/**\n * Options when adding a policy to the pipeline.\n * Used to express dependencies on other policies.\n */\nexport interface AddPolicyOptions {\n  /**\n   * Policies that this policy must come before.\n   */\n  beforePolicies?: string[];\n  /**\n   * Policies that this policy must come after.\n   */\n  afterPolicies?: string[];\n  /**\n   * The phase that this policy must come after.\n   */\n  afterPhase?: PipelinePhase;\n  /**\n   * The phase this policy belongs to.\n   */\n  phase?: PipelinePhase;\n}\n\n/**\n * A pipeline policy manipulates a request as it travels through the pipeline.\n * It is conceptually a middleware that is allowed to modify the request before\n * it is made as well as the response when it is received.\n */\nexport interface PipelinePolicy {\n  /**\n   * The policy name. Must be a unique string in the pipeline.\n   */\n  name: string;\n  /**\n   * The main method to implement that manipulates a request/response.\n   * @param request - The request being performed.\n   * @param next - The next policy in the pipeline. Must be called to continue the pipeline.\n   */\n  sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse>;\n}\n\n/**\n * Represents a pipeline for making a HTTP request to a URL.\n * Pipelines can have multiple policies to manage manipulating each request\n * before and after it is made to the server.\n */\nexport interface Pipeline {\n  /**\n   * Add a new policy to the pipeline.\n   * @param policy - A policy that manipulates a request.\n   * @param options - A set of options for when the policy should run.\n   */\n  addPolicy(policy: PipelinePolicy, options?: AddPolicyOptions): void;\n  /**\n   * Remove a policy from the pipeline.\n   * @param options - Options that let you specify which policies to remove.\n   */\n  removePolicy(options: { name?: string; phase?: PipelinePhase }): PipelinePolicy[];\n  /**\n   * Uses the pipeline to make a HTTP request.\n   * @param httpClient - The HttpClient that actually performs the request.\n   * @param request - The request to be made.\n   */\n  sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse>;\n  /**\n   * Returns the current set of policies in the pipeline in the order in which\n   * they will be applied to the request. Later in the list is closer to when\n   * the request is performed.\n   */\n  getOrderedPolicies(): PipelinePolicy[];\n  /**\n   * Duplicates this pipeline to allow for modifying an existing one without mutating it.\n   */\n  clone(): Pipeline;\n}\n\ninterface PipelineDescriptor {\n  policy: PipelinePolicy;\n  options: AddPolicyOptions;\n}\n\ninterface PolicyGraphNode {\n  policy: PipelinePolicy;\n  dependsOn: Set<PolicyGraphNode>;\n  dependants: Set<PolicyGraphNode>;\n  afterPhase?: Phase;\n}\n\ninterface Phase {\n  name: PipelinePhase | \"None\";\n  policies: Set<PolicyGraphNode>;\n  hasRun: boolean;\n  hasAfterPolicies: boolean;\n}\n\n/**\n * A private implementation of Pipeline.\n * Do not export this class from the package.\n * @internal\n */\nclass HttpPipeline implements Pipeline {\n  private _policies: PipelineDescriptor[] = [];\n  private _orderedPolicies?: PipelinePolicy[];\n\n  private constructor(policies?: PipelineDescriptor[]) {\n    this._policies = policies?.slice(0) ?? [];\n    this._orderedPolicies = undefined;\n  }\n\n  public addPolicy(policy: PipelinePolicy, options: AddPolicyOptions = {}): void {\n    if (options.phase && options.afterPhase) {\n      throw new Error(\"Policies inside a phase cannot specify afterPhase.\");\n    }\n    if (options.phase && !ValidPhaseNames.has(options.phase)) {\n      throw new Error(`Invalid phase name: ${options.phase}`);\n    }\n    if (options.afterPhase && !ValidPhaseNames.has(options.afterPhase)) {\n      throw new Error(`Invalid afterPhase name: ${options.afterPhase}`);\n    }\n    this._policies.push({\n      policy,\n      options,\n    });\n    this._orderedPolicies = undefined;\n  }\n\n  public removePolicy(options: { name?: string; phase?: string }): PipelinePolicy[] {\n    const removedPolicies: PipelinePolicy[] = [];\n\n    this._policies = this._policies.filter((policyDescriptor) => {\n      if (\n        (options.name && policyDescriptor.policy.name === options.name) ||\n        (options.phase && policyDescriptor.options.phase === options.phase)\n      ) {\n        removedPolicies.push(policyDescriptor.policy);\n        return false;\n      } else {\n        return true;\n      }\n    });\n    this._orderedPolicies = undefined;\n\n    return removedPolicies;\n  }\n\n  public sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse> {\n    const policies = this.getOrderedPolicies();\n\n    const pipeline = policies.reduceRight<SendRequest>(\n      (next, policy) => {\n        return (req: PipelineRequest) => {\n          return policy.sendRequest(req, next);\n        };\n      },\n      (req: PipelineRequest) => httpClient.sendRequest(req),\n    );\n\n    return pipeline(request);\n  }\n\n  public getOrderedPolicies(): PipelinePolicy[] {\n    if (!this._orderedPolicies) {\n      this._orderedPolicies = this.orderPolicies();\n    }\n    return this._orderedPolicies;\n  }\n\n  public clone(): Pipeline {\n    return new HttpPipeline(this._policies);\n  }\n\n  public static create(): Pipeline {\n    return new HttpPipeline();\n  }\n\n  private orderPolicies(): PipelinePolicy[] {\n    /**\n     * The goal of this method is to reliably order pipeline policies\n     * based on their declared requirements when they were added.\n     *\n     * Order is first determined by phase:\n     *\n     * 1. Serialize Phase\n     * 2. Policies not in a phase\n     * 3. Deserialize Phase\n     * 4. Retry Phase\n     * 5. Sign Phase\n     *\n     * Within each phase, policies are executed in the order\n     * they were added unless they were specified to execute\n     * before/after other policies or after a particular phase.\n     *\n     * To determine the final order, we will walk the policy list\n     * in phase order multiple times until all dependencies are\n     * satisfied.\n     *\n     * `afterPolicies` are the set of policies that must be\n     * executed before a given policy. This requirement is\n     * considered satisfied when each of the listed policies\n     * have been scheduled.\n     *\n     * `beforePolicies` are the set of policies that must be\n     * executed after a given policy. Since this dependency\n     * can be expressed by converting it into a equivalent\n     * `afterPolicies` declarations, they are normalized\n     * into that form for simplicity.\n     *\n     * An `afterPhase` dependency is considered satisfied when all\n     * policies in that phase have scheduled.\n     *\n     */\n    const result: PipelinePolicy[] = [];\n\n    // Track all policies we know about.\n    const policyMap: Map<string, PolicyGraphNode> = new Map<string, PolicyGraphNode>();\n\n    function createPhase(name: PipelinePhase | \"None\"): Phase {\n      return {\n        name,\n        policies: new Set<PolicyGraphNode>(),\n        hasRun: false,\n        hasAfterPolicies: false,\n      };\n    }\n\n    // Track policies for each phase.\n    const serializePhase = createPhase(\"Serialize\");\n    const noPhase = createPhase(\"None\");\n    const deserializePhase = createPhase(\"Deserialize\");\n    const retryPhase = createPhase(\"Retry\");\n    const signPhase = createPhase(\"Sign\");\n\n    // a list of phases in order\n    const orderedPhases = [serializePhase, noPhase, deserializePhase, retryPhase, signPhase];\n\n    // Small helper function to map phase name to each Phase\n    function getPhase(phase: PipelinePhase | undefined): Phase {\n      if (phase === \"Retry\") {\n        return retryPhase;\n      } else if (phase === \"Serialize\") {\n        return serializePhase;\n      } else if (phase === \"Deserialize\") {\n        return deserializePhase;\n      } else if (phase === \"Sign\") {\n        return signPhase;\n      } else {\n        return noPhase;\n      }\n    }\n\n    // First walk each policy and create a node to track metadata.\n    for (const descriptor of this._policies) {\n      const policy = descriptor.policy;\n      const options = descriptor.options;\n      const policyName = policy.name;\n      if (policyMap.has(policyName)) {\n        throw new Error(\"Duplicate policy names not allowed in pipeline\");\n      }\n      const node: PolicyGraphNode = {\n        policy,\n        dependsOn: new Set<PolicyGraphNode>(),\n        dependants: new Set<PolicyGraphNode>(),\n      };\n      if (options.afterPhase) {\n        node.afterPhase = getPhase(options.afterPhase);\n        node.afterPhase.hasAfterPolicies = true;\n      }\n      policyMap.set(policyName, node);\n      const phase = getPhase(options.phase);\n      phase.policies.add(node);\n    }\n\n    // Now that each policy has a node, connect dependency references.\n    for (const descriptor of this._policies) {\n      const { policy, options } = descriptor;\n      const policyName = policy.name;\n      const node = policyMap.get(policyName);\n      if (!node) {\n        throw new Error(`Missing node for policy ${policyName}`);\n      }\n\n      if (options.afterPolicies) {\n        for (const afterPolicyName of options.afterPolicies) {\n          const afterNode = policyMap.get(afterPolicyName);\n          if (afterNode) {\n            // Linking in both directions helps later\n            // when we want to notify dependants.\n            node.dependsOn.add(afterNode);\n            afterNode.dependants.add(node);\n          }\n        }\n      }\n      if (options.beforePolicies) {\n        for (const beforePolicyName of options.beforePolicies) {\n          const beforeNode = policyMap.get(beforePolicyName);\n          if (beforeNode) {\n            // To execute before another node, make it\n            // depend on the current node.\n            beforeNode.dependsOn.add(node);\n            node.dependants.add(beforeNode);\n          }\n        }\n      }\n    }\n\n    function walkPhase(phase: Phase): void {\n      phase.hasRun = true;\n      // Sets iterate in insertion order\n      for (const node of phase.policies) {\n        if (node.afterPhase && (!node.afterPhase.hasRun || node.afterPhase.policies.size)) {\n          // If this node is waiting on a phase to complete,\n          // we need to skip it for now.\n          // Even if the phase is empty, we should wait for it\n          // to be walked to avoid re-ordering policies.\n          continue;\n        }\n        if (node.dependsOn.size === 0) {\n          // If there's nothing else we're waiting for, we can\n          // add this policy to the result list.\n          result.push(node.policy);\n          // Notify anything that depends on this policy that\n          // the policy has been scheduled.\n          for (const dependant of node.dependants) {\n            dependant.dependsOn.delete(node);\n          }\n          policyMap.delete(node.policy.name);\n          phase.policies.delete(node);\n        }\n      }\n    }\n\n    function walkPhases(): void {\n      for (const phase of orderedPhases) {\n        walkPhase(phase);\n        // if the phase isn't complete\n        if (phase.policies.size > 0 && phase !== noPhase) {\n          if (!noPhase.hasRun) {\n            // Try running noPhase to see if that unblocks this phase next tick.\n            // This can happen if a phase that happens before noPhase\n            // is waiting on a noPhase policy to complete.\n            walkPhase(noPhase);\n          }\n          // Don't proceed to the next phase until this phase finishes.\n          return;\n        }\n\n        if (phase.hasAfterPolicies) {\n          // Run any policies unblocked by this phase\n          walkPhase(noPhase);\n        }\n      }\n    }\n\n    // Iterate until we've put every node in the result list.\n    let iteration = 0;\n    while (policyMap.size > 0) {\n      iteration++;\n      const initialResultLength = result.length;\n      // Keep walking each phase in order until we can order every node.\n      walkPhases();\n      // The result list *should* get at least one larger each time\n      // after the first full pass.\n      // Otherwise, we're going to loop forever.\n      if (result.length <= initialResultLength && iteration > 1) {\n        throw new Error(\"Cannot satisfy policy dependencies due to requirements cycle.\");\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * Creates a totally empty pipeline.\n * Useful for testing or creating a custom one.\n */\nexport function createEmptyPipeline(): Pipeline {\n  return HttpPipeline.create();\n}\n"]}