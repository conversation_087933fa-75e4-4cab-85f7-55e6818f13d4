import * as vscode from 'vscode';
import * as diff from 'diff';
import { UnifiedDiffService, UnifiedDiffResult } from './UnifiedDiffService';

export interface DiffChange {
    type: 'added' | 'removed' | 'unchanged';
    value: string;
    lineNumber?: number;
}

export interface DiffResult {
    changes: DiffChange[];
    additions: number;
    deletions: number;
    filename: string;
    originalContent: string;
    newContent: string;
}

export interface FileModificationData {
    filename: string;
    originalContent: string;
    newContent: string;
    language?: string;
}

/**
 * Service for generating and managing diff previews for file modifications
 */
export class DiffPreviewService {
    private _outputChannel: vscode.OutputChannel;
    private _unifiedDiffService: UnifiedDiffService;

    constructor() {
        this._outputChannel = vscode.window.createOutputChannel('V1b3-Sama Diff Preview');
        this._unifiedDiffService = new UnifiedDiffService();
    }

    /**
     * Generate a structured diff for file modification
     */
    public generateDiff(originalContent: string, newContent: string, filename: string): DiffResult {
        try {
            // Use unified diff service with structured changes
            const result = this._unifiedDiffService.generateDiffSync(originalContent, newContent, filename, {
                includeStructuredChanges: true
            });

            // Convert to DiffPreviewService format
            return {
                changes: result.changes || [],
                additions: result.additions,
                deletions: result.deletions,
                filename,
                originalContent,
                newContent
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this._outputChannel.appendLine(`❌ Failed to generate diff for ${filename}: ${errorMsg}`);

            // Return a fallback diff result
            return {
                changes: [
                    { type: 'removed', value: 'Original content', lineNumber: 1 },
                    { type: 'added', value: 'New content', lineNumber: 1 }
                ],
                additions: 1,
                deletions: 1,
                filename,
                originalContent,
                newContent
            };
        }
    }

    /**
     * Read current file content before modification
     */
    public async readCurrentFileContent(filePath: string): Promise<string> {
        // Use unified diff service for file reading
        const content = await this._unifiedDiffService.readCurrentFileContent(filePath);
        return content || '';
    }

    /**
     * Prepare file modification data with diff
     */
    public async prepareFileModification(filePath: string, newContent: string, language?: string): Promise<FileModificationData> {
        const originalContent = await this.readCurrentFileContent(filePath);
        
        return {
            filename: filePath,
            originalContent,
            newContent,
            language
        };
    }

    /**
     * Generate diff preview HTML for webview
     */
    public generateDiffPreviewHtml(diffResult: DiffResult): string {
        const { changes, additions, deletions, filename } = diffResult;
        
        let diffHtml = '';
        let contextLines = 0;
        const maxContextLines = 3;

        for (let i = 0; i < changes.length; i++) {
            const change = changes[i];
            
            if (change.type === 'unchanged') {
                contextLines++;
                if (contextLines <= maxContextLines || 
                    (i < changes.length - maxContextLines)) {
                    diffHtml += `<div class="diff-line diff-unchanged">
                        <span class="line-number">${change.lineNumber}</span>
                        <span class="line-content">${this._escapeHtml(change.value)}</span>
                    </div>`;
                } else if (contextLines === maxContextLines + 1) {
                    diffHtml += `<div class="diff-line diff-context">
                        <span class="line-number">...</span>
                        <span class="line-content">...</span>
                    </div>`;
                }
            } else {
                contextLines = 0;
                const prefix = change.type === 'added' ? '+' : '-';
                const cssClass = change.type === 'added' ? 'diff-added' : 'diff-removed';
                
                diffHtml += `<div class="diff-line ${cssClass}">
                    <span class="line-number">${change.lineNumber || ''}</span>
                    <span class="line-prefix">${prefix}</span>
                    <span class="line-content">${this._escapeHtml(change.value)}</span>
                </div>`;
            }
        }

        return `
            <div class="diff-preview">
                <div class="diff-header">
                    <span class="diff-icon">📝</span>
                    <span class="diff-filename">${this._escapeHtml(filename)}</span>
                    <span class="diff-stats">+${additions} -${deletions}</span>
                </div>
                <div class="diff-content">
                    ${diffHtml}
                </div>
                <div class="diff-actions">
                    <button class="diff-action-btn" onclick="openFileInEditor('${this._escapeHtml(filename)}')">
                        📂 Open File in Editor
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Escape HTML characters
     */
    private _escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._outputChannel.dispose();
        this._unifiedDiffService.dispose();
    }
}
