import * as vscode from 'vscode';
import { SupportedProvider } from './ApiKeyManager';
import { ProviderRegistryService, ProviderDetail } from './ProviderRegistryService';
import { ProviderHealthService, ProviderHealthStatus } from './ProviderHealthService';
import { DynamicProviderService, ProviderStatus } from './DynamicProviderService';
import { SettingsService } from './SettingsService';

export interface ProviderStatusSummary {
    provider: SupportedProvider;
    status: 'online' | 'offline' | 'degraded' | 'unknown';
    lastChecked: Date;
    responseTime?: number;
    errorCount: number;
    uptime: number; // percentage
    isPreferred: boolean;
}

export interface MonitoringConfig {
    enabled: boolean;
    checkInterval: number; // minutes
    healthCheckTimeout: number; // seconds
    maxErrorsBeforeDegraded: number;
    autoFailover: boolean;
    preferredProviders: SupportedProvider[];
}

/**
 * Service for comprehensive provider status monitoring with automatic failover
 */
export class ProviderStatusMonitoringService {
    private static instance: ProviderStatusMonitoringService;
    private _disposables: vscode.Disposable[] = [];
    private _monitoringTimer?: NodeJS.Timeout;
    private _statusHistory = new Map<SupportedProvider, ProviderHealthStatus[]>();
    private _errorCounts = new Map<SupportedProvider, number>();
    private _lastSuccessfulCheck = new Map<SupportedProvider, Date>();
    private _statusChangeEmitter = new vscode.EventEmitter<ProviderStatusSummary>();
    
    public readonly onDidChangeProviderStatus = this._statusChangeEmitter.event;

    private _config: MonitoringConfig = {
        enabled: true,
        checkInterval: 5, // 5 minutes
        healthCheckTimeout: 10, // 10 seconds
        maxErrorsBeforeDegraded: 3,
        autoFailover: true,
        preferredProviders: ['deepseek', 'groq', 'openrouter', 'local']
    };

    public static getInstance(): ProviderStatusMonitoringService {
        if (!ProviderStatusMonitoringService.instance) {
            ProviderStatusMonitoringService.instance = new ProviderStatusMonitoringService();
        }
        return ProviderStatusMonitoringService.instance;
    }

    constructor() {
        // Services will be initialized when needed
    }

    private getRegistryService(): ProviderRegistryService {
        return ProviderRegistryService.getInstance();
    }

    private getHealthService(): ProviderHealthService {
        return ProviderHealthService.getInstance();
    }

    /**
     * Initialize monitoring service
     */
    public async initialize(context: vscode.ExtensionContext): Promise<void> {
        // Load configuration from VS Code settings
        await this.loadConfiguration();

        // Start monitoring if enabled
        if (this._config.enabled) {
            this.startMonitoring();
        }

        // Listen for configuration changes
        this._disposables.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration('v1b3-sama.monitoring')) {
                    this.loadConfiguration();
                }
            })
        );

        context.subscriptions.push(...this._disposables);
    }

    /**
     * Load monitoring configuration from VS Code settings
     */
    private async loadConfiguration(): Promise<void> {
        // Use centralized configuration loading
        const config = vscode.workspace.getConfiguration('v1b3-sama.monitoring');

        this._config = {
            enabled: config.get('enabled', true),
            checkInterval: config.get('checkInterval', 5),
            healthCheckTimeout: config.get('healthCheckTimeout', 10),
            maxErrorsBeforeDegraded: config.get('maxErrorsBeforeDegraded', 3),
            autoFailover: config.get('autoFailover', true),
            preferredProviders: config.get('preferredProviders', ['deepseek', 'groq'])
        };

        // Restart monitoring with new configuration
        if (this._config.enabled && !this._monitoringTimer) {
            this.startMonitoring();
        } else if (!this._config.enabled && this._monitoringTimer) {
            this.stopMonitoring();
        }
    }

    /**
     * Start periodic monitoring
     */
    public startMonitoring(): void {
        if (this._monitoringTimer) {
            clearInterval(this._monitoringTimer);
        }

        // Initial check
        this.performHealthChecks();

        // Set up periodic checks
        this._monitoringTimer = setInterval(() => {
            this.performHealthChecks();
        }, this._config.checkInterval * 60 * 1000);

        console.log(`Provider monitoring started with ${this._config.checkInterval} minute intervals`);
    }

    /**
     * Stop monitoring
     */
    public stopMonitoring(): void {
        if (this._monitoringTimer) {
            clearInterval(this._monitoringTimer);
            this._monitoringTimer = undefined;
        }
        console.log('Provider monitoring stopped');
    }

    /**
     * Perform health checks on all providers
     */
    private async performHealthChecks(): Promise<void> {
        const providers = this.getRegistryService().getAvailableProviders();
        
        const healthCheckPromises = providers.map(async (provider) => {
            try {
                await this.checkProviderHealth(provider);
            } catch (error) {
                console.error(`Health check failed for ${provider.id}:`, error);
                this.recordError(provider.id as SupportedProvider);
            }
        });

        await Promise.allSettled(healthCheckPromises);
    }

    /**
     * Check health for a specific provider
     */
    private async checkProviderHealth(provider: ProviderDetail): Promise<void> {
        const providerId = provider.id as SupportedProvider;
        
        try {
            // Get API key if required
            let apiKey: string | undefined;
            if (provider.requiresApiKey) {
                // For now, we'll skip API key retrieval since we don't have access to ApiKeyManager
                // This will be properly implemented when the service is integrated
                apiKey = undefined;
            }

            // Perform health check
            const healthStatus = await this.getHealthService().checkProviderHealth(provider, apiKey);
            
            // Record the health status
            this.recordHealthStatus(providerId, healthStatus);
            
            // Update error count
            if (healthStatus.isOnline && healthStatus.isAuthenticated) {
                this._errorCounts.set(providerId, 0);
                this._lastSuccessfulCheck.set(providerId, new Date());
            } else {
                this.recordError(providerId);
            }

            // Generate status summary and emit event
            const summary = this.generateStatusSummary(providerId, healthStatus);
            this._statusChangeEmitter.fire(summary);

            // Check for auto-failover if current provider is failing
            // Note: Auto-failover requires SettingsService integration
            // This will be implemented when the service is properly integrated

        } catch (error) {
            console.error(`Health check error for ${providerId}:`, error);
            this.recordError(providerId);
        }
    }

    /**
     * Record health status in history
     */
    private recordHealthStatus(provider: SupportedProvider, status: ProviderHealthStatus): void {
        if (!this._statusHistory.has(provider)) {
            this._statusHistory.set(provider, []);
        }

        const history = this._statusHistory.get(provider)!;
        history.push(status);

        // Keep only last 100 entries
        if (history.length > 100) {
            history.shift();
        }
    }

    /**
     * Record an error for a provider
     */
    private recordError(provider: SupportedProvider): void {
        const currentCount = this._errorCounts.get(provider) || 0;
        this._errorCounts.set(provider, currentCount + 1);
    }

    /**
     * Generate status summary for a provider
     */
    private generateStatusSummary(provider: SupportedProvider, healthStatus: ProviderHealthStatus): ProviderStatusSummary {
        const errorCount = this._errorCounts.get(provider) || 0;
        const history = this._statusHistory.get(provider) || [];
        
        // Calculate uptime percentage from recent history
        const recentHistory = history.slice(-20); // Last 20 checks
        const successfulChecks = recentHistory.filter(h => h.isOnline && h.isAuthenticated).length;
        const uptime = recentHistory.length > 0 ? (successfulChecks / recentHistory.length) * 100 : 0;

        // Determine status
        let status: 'online' | 'offline' | 'degraded' | 'unknown' = 'unknown';
        if (healthStatus.isOnline && healthStatus.isAuthenticated) {
            status = errorCount > this._config.maxErrorsBeforeDegraded ? 'degraded' : 'online';
        } else {
            status = 'offline';
        }

        return {
            provider,
            status,
            lastChecked: healthStatus.lastChecked,
            responseTime: healthStatus.responseTime,
            errorCount,
            uptime,
            isPreferred: this._config.preferredProviders.includes(provider)
        };
    }

    /**
     * Attempt automatic failover to a healthy provider
     */
    private async attemptAutoFailover(failedProvider: SupportedProvider): Promise<void> {
        // Auto-failover requires SettingsService integration
        return;

        console.log(`Attempting auto-failover from failed provider: ${failedProvider}`);

        // Get all provider statuses
        const allSummaries = await this.getAllProviderStatuses();
        
        // Find healthy preferred providers
        const healthyProviders = allSummaries
            .filter(s => s.status === 'online' && s.provider !== failedProvider)
            .sort((a, b) => {
                // Prefer providers in preferred list
                const aPreferred = this._config.preferredProviders.indexOf(a.provider);
                const bPreferred = this._config.preferredProviders.indexOf(b.provider);
                
                if (aPreferred !== -1 && bPreferred !== -1) {
                    return aPreferred - bPreferred;
                }
                if (aPreferred !== -1) return -1;
                if (bPreferred !== -1) return 1;
                
                // Then by uptime
                return b.uptime - a.uptime;
            });

        if (healthyProviders.length > 0) {
            const newProvider = healthyProviders[0].provider;
            // Note: Provider switching requires SettingsService integration
            console.log(`Would switch to provider: ${newProvider}`);
            
            vscode.window.showWarningMessage(
                `Provider ${failedProvider} is offline. Automatically switched to ${newProvider}.`,
                'OK'
            );
            
            console.log(`Auto-failover successful: ${failedProvider} → ${newProvider}`);
        } else {
            vscode.window.showErrorMessage(
                `Provider ${failedProvider} is offline and no healthy alternatives are available.`,
                'Check Settings'
            );
        }
    }

    /**
     * Get status summaries for all providers
     */
    public async getAllProviderStatuses(): Promise<ProviderStatusSummary[]> {
        const providers = this.getRegistryService().getAvailableProviders();
        const summaries: ProviderStatusSummary[] = [];

        for (const provider of providers) {
            const providerId = provider.id as SupportedProvider;
            const history = this._statusHistory.get(providerId);
            
            if (history && history.length > 0) {
                const latestStatus = history[history.length - 1];
                const summary = this.generateStatusSummary(providerId, latestStatus);
                summaries.push(summary);
            } else {
                // No history available, create unknown status
                summaries.push({
                    provider: providerId,
                    status: 'unknown',
                    lastChecked: new Date(),
                    errorCount: 0,
                    uptime: 0,
                    isPreferred: this._config.preferredProviders.includes(providerId)
                });
            }
        }

        return summaries;
    }

    /**
     * Get detailed status for a specific provider
     */
    public getProviderStatusDetails(provider: SupportedProvider): {
        summary: ProviderStatusSummary | undefined;
        history: ProviderHealthStatus[];
        errorCount: number;
        lastSuccessful?: Date;
    } {
        const history = this._statusHistory.get(provider) || [];
        const errorCount = this._errorCounts.get(provider) || 0;
        const lastSuccessful = this._lastSuccessfulCheck.get(provider);
        
        let summary: ProviderStatusSummary | undefined;
        if (history.length > 0) {
            const latestStatus = history[history.length - 1];
            summary = this.generateStatusSummary(provider, latestStatus);
        }

        return {
            summary,
            history,
            errorCount,
            lastSuccessful
        };
    }

    /**
     * Force health check for a specific provider
     */
    public async forceHealthCheck(provider: SupportedProvider): Promise<ProviderStatusSummary> {
        const providerDetail = this.getRegistryService().getProvider(provider);
        if (!providerDetail) {
            throw new Error(`Provider ${provider} not found`);
        }

        await this.checkProviderHealth(providerDetail);
        
        const details = this.getProviderStatusDetails(provider);
        if (!details.summary) {
            throw new Error(`Failed to get status for ${provider}`);
        }

        return details.summary;
    }

    /**
     * Update monitoring configuration
     */
    public async updateConfiguration(config: Partial<MonitoringConfig>): Promise<void> {
        this._config = { ...this._config, ...config };
        
        // Save to VS Code settings
        const vsCodeConfig = vscode.workspace.getConfiguration('v1b3-sama.monitoring');
        for (const [key, value] of Object.entries(config)) {
            await vsCodeConfig.update(key, value, vscode.ConfigurationTarget.Global);
        }

        // Restart monitoring if needed
        if (this._config.enabled) {
            this.startMonitoring();
        } else {
            this.stopMonitoring();
        }
    }

    /**
     * Get current monitoring configuration
     */
    public getConfiguration(): MonitoringConfig {
        return { ...this._config };
    }

    /**
     * Clear all monitoring data
     */
    public clearMonitoringData(): void {
        this._statusHistory.clear();
        this._errorCounts.clear();
        this._lastSuccessfulCheck.clear();
    }

    /**
     * Get monitoring statistics
     */
    public getMonitoringStats(): {
        totalProviders: number;
        onlineProviders: number;
        degradedProviders: number;
        offlineProviders: number;
        averageResponseTime: number;
        totalChecks: number;
        overallUptime: number;
    } {
        const allStatuses = this._statusHistory;
        let totalProviders = 0;
        let onlineProviders = 0;
        let degradedProviders = 0;
        let offlineProviders = 0;
        let totalResponseTime = 0;
        let totalChecks = 0;
        let totalUptime = 0;

        for (const [provider, history] of allStatuses.entries()) {
            totalProviders++;
            totalChecks += history.length;

            if (history.length > 0) {
                const latest = history[history.length - 1];
                const errorCount = this._errorCounts.get(provider) || 0;

                if (latest.isOnline && latest.isAuthenticated) {
                    if (errorCount > this._config.maxErrorsBeforeDegraded) {
                        degradedProviders++;
                    } else {
                        onlineProviders++;
                    }
                } else {
                    offlineProviders++;
                }

                // Calculate average response time
                const responseTimes = history.filter(h => h.responseTime).map(h => h.responseTime!);
                if (responseTimes.length > 0) {
                    totalResponseTime += responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                }

                // Calculate uptime for this provider
                const successfulChecks = history.filter(h => h.isOnline && h.isAuthenticated).length;
                const providerUptime = history.length > 0 ? (successfulChecks / history.length) * 100 : 0;
                totalUptime += providerUptime;
            }
        }

        return {
            totalProviders,
            onlineProviders,
            degradedProviders,
            offlineProviders,
            averageResponseTime: totalProviders > 0 ? totalResponseTime / totalProviders : 0,
            totalChecks,
            overallUptime: totalProviders > 0 ? totalUptime / totalProviders : 0
        };
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.stopMonitoring();
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._statusChangeEmitter.dispose();
    }
}
