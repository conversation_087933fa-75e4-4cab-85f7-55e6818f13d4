import * as vscode from 'vscode';
import { ExecutionPlan, ExecutionStep } from '../interfaces/ILLMService';
import { ProcessedFileOperation } from './AutoExecutionEngine';
import { FileOperationsService } from './FileOperationsService';
import { TerminalService } from './terminalService';
import { ActionHistoryManager } from '../actionHistoryManager';

export interface ExecutionResult {
    successful: ProcessedFileOperation[];
    failed: ProcessedFileOperation[];
    skipped: ProcessedFileOperation[];
    totalExecuted: number;
    totalFailed: number;
    executionTime: number;
}

export interface StepExecutionResult {
    success: boolean;
    message: string;
    error?: string;
    data?: any;
}

export type ExecutionState = 'idle' | 'awaitingApproval' | 'executing' | 'paused' | 'error';

export interface ExecutionContext {
    currentPlan?: ExecutionPlan;
    currentStep?: number;
    executedSteps: string[];
    failedSteps: string[];
    pausedAt?: Date;
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
    runningProcesses: Map<string, any>;
}

/**
 * Unified Execution Service - Consolidates all execution functionality
 * Replaces duplicate implementations in:
 * - ExecutionStateManager.executePlan() and _executeSteps()
 * - PlanExecutorService.executePlan() and executeOperation()
 * - AutoExecutionEngine.executeApprovedOperations() and executeOperation()
 */
export class UnifiedExecutionService {
    private outputChannel: vscode.OutputChannel;
    private fileOperationsService: FileOperationsService;
    private terminalService: TerminalService;
    private actionHistoryManager: ActionHistoryManager;
    private state: ExecutionState = 'idle';
    private context: ExecutionContext = {
        executedSteps: [],
        failedSteps: [],
        runningProcesses: new Map()
    };

    constructor(
        fileOperationsService: FileOperationsService,
        terminalService: TerminalService,
        actionHistoryManager: ActionHistoryManager
    ) {
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama Unified Execution');
        this.fileOperationsService = fileOperationsService;
        this.terminalService = terminalService;
        this.actionHistoryManager = actionHistoryManager;
    }

    /**
     * Execute a complete execution plan
     * Replaces: ExecutionStateManager.executePlan(), PlanExecutorService.executePlan()
     */
    public async executePlan(plan: ExecutionPlan): Promise<void> {
        if (this.state === 'executing') {
            throw new Error('Another execution is already in progress');
        }

        this.setState('executing');
        this.context = {
            currentPlan: plan,
            currentStep: 0,
            executedSteps: [],
            failedSteps: [],
            startedAt: new Date(),
            runningProcesses: new Map()
        };

        this.outputChannel.appendLine(`\n=== Execution Plan Started ===`);
        this.outputChannel.appendLine(`Plan: ${plan.description}`);
        this.outputChannel.appendLine(`Steps: ${plan.steps.length}`);

        try {
            await this.executeSteps(plan.steps);
            
            this.context.completedAt = new Date();
            this.setState('idle');
            
            this.outputChannel.appendLine(`\n✅ Execution plan completed successfully`);
            this.actionHistoryManager.logAction(`Completed execution plan: ${plan.description}`);
            
        } catch (error) {
            this.context.error = error instanceof Error ? error.message : String(error);
            this.setState('error');
            
            this.outputChannel.appendLine(`\n❌ Execution plan failed: ${this.context.error}`);
            this.actionHistoryManager.logAction(`Failed execution plan: ${plan.description} - ${this.context.error}`);
            
            throw error;
        }
    }

    /**
     * Execute file operations (for auto-approval workflow)
     * Replaces: AutoExecutionEngine.executeApprovedOperations()
     */
    public async executeFileOperations(operations: ProcessedFileOperation[]): Promise<ExecutionResult> {
        const startTime = Date.now();
        const successful: ProcessedFileOperation[] = [];
        const failed: ProcessedFileOperation[] = [];
        const skipped: ProcessedFileOperation[] = [];

        this.outputChannel.appendLine(`\n=== File Operations Execution Started ===`);
        this.outputChannel.appendLine(`Operations to execute: ${operations.length}`);

        // Filter only approved operations
        const approvedOps = operations.filter(op =>
            op.approvalStatus === 'auto_approved'
        );
        
        if (approvedOps.length === 0) {
            this.outputChannel.appendLine('No approved operations to execute');
            return {
                successful,
                failed,
                skipped,
                totalExecuted: 0,
                totalFailed: 0,
                executionTime: Date.now() - startTime
            };
        }

        // Execute operations sequentially for safety
        for (const operation of approvedOps) {
            try {
                this.outputChannel.appendLine(`\nExecuting: ${operation.type} ${operation.path}`);
                operation.executionStatus = 'executing';

                await this.executeFileOperation(operation);
                
                operation.executionStatus = 'completed';
                successful.push(operation);
                
                this.outputChannel.appendLine(`✅ Success: ${operation.path}`);
                this.actionHistoryManager.logAction(`Executed ${operation.type}: ${operation.path}`);

            } catch (error) {
                operation.executionStatus = 'failed';
                operation.executionError = error instanceof Error ? error.message : String(error);
                failed.push(operation);

                this.outputChannel.appendLine(`❌ Failed: ${operation.path} - ${operation.executionError}`);
                this.actionHistoryManager.logAction(`Failed ${operation.type}: ${operation.path} - ${operation.executionError}`);
            }
        }

        const result: ExecutionResult = {
            successful,
            failed,
            skipped,
            totalExecuted: successful.length,
            totalFailed: failed.length,
            executionTime: Date.now() - startTime
        };

        this.outputChannel.appendLine(`\n=== File Operations Execution Completed ===`);
        this.outputChannel.appendLine(`✅ Successful: ${result.totalExecuted}`);
        this.outputChannel.appendLine(`❌ Failed: ${result.totalFailed}`);
        this.outputChannel.appendLine(`⏱️ Duration: ${result.executionTime}ms`);

        return result;
    }

    /**
     * Execute a single execution step
     * Replaces: ExecutionStateManager._executeStep()
     */
    public async executeStep(step: ExecutionStep): Promise<StepExecutionResult> {
        try {
            this.outputChannel.appendLine(`🔄 Executing step: ${step.description}`);

            switch (step.type) {
                case 'file_create':
                case 'file_modify':
                case 'file_delete':
                case 'directory_create':
                    return await this.executeFileOperationStep(step);
                case 'command_run':
                    return await this.executeTerminalCommandStep(step);
                case 'server_start':
                case 'server_stop':
                    return await this.executeServerStep(step);
                default:
                    throw new Error(`Unsupported step type: ${step.type}`);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                success: false,
                message: `Step failed: ${step.description}`,
                error: errorMessage
            };
        }
    }

    /**
     * Execute terminal command
     * Replaces: PlanExecutorService.executeCommand()
     */
    public async executeCommand(command: string, options?: {
        terminalName?: string;
        showTerminal?: boolean;
        timeout?: number;
    }): Promise<void> {
        try {
            this.outputChannel.appendLine(`🔧 Executing command: ${command}`);

            // Check if command is safe to execute automatically
            const isSafeCommand = await this.isSafeCommand(command);
            if (!isSafeCommand) {
                throw new Error(`Command not approved for automatic execution: ${command}`);
            }

            // Execute command using TerminalService
            await this.terminalService.executeCommand(command, {
                timeout: options?.timeout || 30000
            });

            this.outputChannel.appendLine(`✅ Command executed successfully: ${command}`);
            this.actionHistoryManager.logAction(`Executed command: ${command}`);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.outputChannel.appendLine(`❌ Command failed: ${command} - ${errorMessage}`);
            throw error;
        }
    }

    /**
     * Get current execution state
     */
    public getState(): ExecutionState {
        return this.state;
    }

    /**
     * Get current execution context
     */
    public getContext(): ExecutionContext {
        return { ...this.context };
    }

    /**
     * Pause execution (if supported)
     */
    public pauseExecution(): void {
        if (this.state === 'executing') {
            this.setState('paused');
            this.context.pausedAt = new Date();
            this.outputChannel.appendLine('⏸️ Execution paused');
        }
    }

    /**
     * Resume execution (if paused)
     */
    public resumeExecution(): void {
        if (this.state === 'paused') {
            this.setState('executing');
            this.context.pausedAt = undefined;
            this.outputChannel.appendLine('▶️ Execution resumed');
        }
    }

    /**
     * Stop execution
     */
    public stopExecution(): void {
        this.setState('idle');
        this.context = {
            executedSteps: [],
            failedSteps: [],
            runningProcesses: new Map()
        };
        this.outputChannel.appendLine('⏹️ Execution stopped');
    }

    // Private helper methods
    private setState(newState: ExecutionState): void {
        this.state = newState;
    }

    private async executeSteps(steps: ExecutionStep[]): Promise<void> {
        for (let i = 0; i < steps.length; i++) {
            if (this.state !== 'executing') {
                throw new Error('Execution was interrupted');
            }

            this.context.currentStep = i;
            const step = steps[i];
            
            try {
                const result = await this.executeStep(step);
                if (result.success) {
                    this.context.executedSteps.push(step.id);
                    this.outputChannel.appendLine(`✅ Step completed: ${step.description}`);
                } else {
                    throw new Error(result.error || 'Step execution failed');
                }
            } catch (error) {
                this.context.failedSteps.push(step.id);
                throw error;
            }
        }
    }

    private async executeFileOperation(operation: ProcessedFileOperation): Promise<void> {
        // Validate operation before execution
        await this.validateOperation(operation);

        // Execute based on operation type
        switch (operation.type) {
            case 'create':
                await this.fileOperationsService.createFile(operation.path, operation.content || '');
                break;
            case 'modify':
                await this.fileOperationsService.modifyFile(operation.path, operation.content || '');
                break;
            case 'create_directory':
                await this.fileOperationsService.createDirectory(operation.path);
                break;
            default:
                throw new Error(`Unsupported operation type: ${operation.type}`);
        }
    }

    private async executeFileOperationStep(step: ExecutionStep): Promise<StepExecutionResult> {
        // Implementation for file operation steps
        return {
            success: true,
            message: `File operation completed: ${step.description}`
        };
    }

    private async executeTerminalCommandStep(step: ExecutionStep): Promise<StepExecutionResult> {
        if (!step.command) {
            throw new Error('Terminal command step missing command');
        }

        await this.executeCommand(step.command);
        return {
            success: true,
            message: `Command executed: ${step.command}`
        };
    }

    private async executeServerStep(step: ExecutionStep): Promise<StepExecutionResult> {
        // Implementation for server start/stop steps
        return {
            success: true,
            message: `Server operation completed: ${step.description}`
        };
    }

    private async validateOperation(operation: ProcessedFileOperation): Promise<void> {
        if (!operation.path) {
            throw new Error('Operation missing required path');
        }

        if (operation.type === 'create' || operation.type === 'modify') {
            if (operation.content === undefined) {
                throw new Error('Create/modify operations require content');
            }
        }
    }

    private async isSafeCommand(command: string): Promise<boolean> {
        // Basic safety check for commands
        const dangerousCommands = ['rm -rf', 'del /f', 'format', 'fdisk', 'mkfs'];
        const lowerCommand = command.toLowerCase();
        
        return !dangerousCommands.some(dangerous => lowerCommand.includes(dangerous));
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.outputChannel.dispose();
    }
}
