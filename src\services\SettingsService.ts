import * as vscode from 'vscode';

/**
 * Type-safe configuration interface for V1b3-Sama settings
 */
export interface V1b3SamaConfiguration {
    // Core LLM Settings
    provider: 'deepseek' | 'openai' | 'anthropic' | 'google' | 'openrouter' | 'azure' | 'local' | 'groq';
    model: string;
    
    // Auto-approval Settings (hardcoded - no user configuration)
    // Removed: Auto-approval is now hardcoded to always be enabled
    
    // Performance Settings
    performance: {
        workerPoolSize: number;
        enableStreaming: boolean;
        streamingTimeout: number;
        enablePerformanceMonitoring: boolean;
        alertThreshold: number;
    };
    
    // File Processing Settings
    files: {
        maxFileSize: number;
        workerThreshold: number;
    };
    
    // Cache Settings
    cache: {
        enableResponseCache: boolean;
        cacheTTL: number;
    };
    
    // Debug Settings
    debug: {
        enableVerboseLogging: boolean;
        logPerformanceMetrics: boolean;
    };
    
    // UI Settings
    ui: {
        theme: 'system' | 'light' | 'dark';
        fontSize: number;
        showLineNumbers: boolean;
        enableSyntaxHighlighting: boolean;
        compactMode: boolean;
    };

    // Feature Toggles
    enableWebSearch: boolean;
    mcpServers: any[];
}

/**
 * Event interface for settings changes
 */
export interface SettingsChangeEvent {
    section: string;
    key: string;
    oldValue: any;
    newValue: any;
    affectedConfiguration: string[];
}

/**
 * Enhanced Settings Service for V1b3-Sama Extension
 * Provides type-safe, real-time configuration management with VS Code integration
 */
export class SettingsService {
    private static readonly CONFIG_SECTION = 'v1b3-sama';
    private _disposables: vscode.Disposable[] = [];
    private _changeEmitter = new vscode.EventEmitter<SettingsChangeEvent>();
    private _cachedConfig: V1b3SamaConfiguration | null = null;
    
    /**
     * Event fired when any V1b3-Sama setting changes
     */
    public readonly onDidChangeSettings = this._changeEmitter.event;
    
    constructor(private context: vscode.ExtensionContext) {
        this.initialize();
    }
    
    /**
     * Initialize the settings service with change listeners
     */
    private initialize(): void {
        // Listen for configuration changes
        const configChangeListener = vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration(SettingsService.CONFIG_SECTION)) {
                this.handleConfigurationChange(e);
            }
        });
        
        this._disposables.push(configChangeListener);
        this.context.subscriptions.push(configChangeListener);
        
        // Load initial configuration
        this.refreshConfiguration();
    }
    
    /**
     * Handle configuration changes and emit events
     */
    private handleConfigurationChange(e: vscode.ConfigurationChangeEvent): void {
        const oldConfig = this._cachedConfig;
        this.refreshConfiguration();
        const newConfig = this._cachedConfig;
        
        if (oldConfig && newConfig) {
            // Detect specific changes and emit events
            this.detectAndEmitChanges(oldConfig, newConfig, e);
        }
    }
    
    /**
     * Detect specific configuration changes and emit appropriate events
     */
    private detectAndEmitChanges(
        oldConfig: V1b3SamaConfiguration, 
        newConfig: V1b3SamaConfiguration, 
        e: vscode.ConfigurationChangeEvent
    ): void {
        const affectedSections: string[] = [];
        
        // Check provider changes
        if (oldConfig.provider !== newConfig.provider) {
            affectedSections.push('provider');
            this._changeEmitter.fire({
                section: 'core',
                key: 'provider',
                oldValue: oldConfig.provider,
                newValue: newConfig.provider,
                affectedConfiguration: ['provider', 'model']
            });
        }
        
        // Check model changes
        if (oldConfig.model !== newConfig.model) {
            affectedSections.push('model');
            this._changeEmitter.fire({
                section: 'core',
                key: 'model',
                oldValue: oldConfig.model,
                newValue: newConfig.model,
                affectedConfiguration: ['model']
            });
        }
        
        // Auto-approval changes removed - now hardcoded
        
        // Check performance changes
        if (JSON.stringify(oldConfig.performance) !== JSON.stringify(newConfig.performance)) {
            affectedSections.push('performance');
            this._changeEmitter.fire({
                section: 'performance',
                key: 'any',
                oldValue: oldConfig.performance,
                newValue: newConfig.performance,
                affectedConfiguration: ['performance']
            });
        }
    }
    
    /**
     * Refresh the cached configuration from VS Code settings
     */
    private refreshConfiguration(): void {
        const config = vscode.workspace.getConfiguration(SettingsService.CONFIG_SECTION);
        
        this._cachedConfig = {
            provider: config.get('provider', 'deepseek') as any,
            model: config.get('model', 'deepseek-coder'),

            // Auto-approval settings removed - now hardcoded
            
            performance: {
                workerPoolSize: config.get('performance.workerPoolSize', 0),
                enableStreaming: config.get('performance.enableStreaming', true),
                streamingTimeout: config.get('performance.streamingTimeout', 30000),
                enablePerformanceMonitoring: config.get('performance.enablePerformanceMonitoring', true),
                alertThreshold: config.get('performance.alertThreshold', 15),
            },
            
            files: {
                maxFileSize: config.get('files.maxFileSize', 104857600),
                workerThreshold: config.get('files.workerThreshold', 102400),
            },
            
            cache: {
                enableResponseCache: config.get('cache.enableResponseCache', true),
                cacheTTL: config.get('cache.cacheTTL', 900000),
            },
            
            debug: {
                enableVerboseLogging: config.get('debug.enableVerboseLogging', false),
                logPerformanceMetrics: config.get('debug.logPerformanceMetrics', false),
            },

            ui: {
                theme: config.get('ui.theme', 'system') as any,
                fontSize: config.get('ui.fontSize', 14),
                showLineNumbers: config.get('ui.showLineNumbers', true),
                enableSyntaxHighlighting: config.get('ui.enableSyntaxHighlighting', true),
                compactMode: config.get('ui.compactMode', false),
            },

            enableWebSearch: config.get('enableWebSearch', true),
            mcpServers: config.get('mcpServers', []),
        };
    }

    /**
     * Get the current configuration (cached for performance)
     */
    public getConfiguration(): V1b3SamaConfiguration {
        if (!this._cachedConfig) {
            this.refreshConfiguration();
        }
        return this._cachedConfig!;
    }

    /**
     * Get a specific configuration value with type safety
     */
    public get<K extends keyof V1b3SamaConfiguration>(key: K): V1b3SamaConfiguration[K] {
        return this.getConfiguration()[key];
    }

    /**
     * Update a configuration value
     */
    public async updateConfiguration<K extends keyof V1b3SamaConfiguration>(
        key: K,
        value: V1b3SamaConfiguration[K],
        target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global
    ): Promise<void> {
        const config = vscode.workspace.getConfiguration(SettingsService.CONFIG_SECTION);
        await config.update(key, value, target);
        // Configuration will be automatically refreshed via the change listener
    }

    /**
     * Update multiple configuration values atomically
     */
    public async updateMultipleConfigurations(
        updates: Partial<V1b3SamaConfiguration>,
        target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global
    ): Promise<void> {
        const config = vscode.workspace.getConfiguration(SettingsService.CONFIG_SECTION);

        // Apply all updates
        const updatePromises = Object.entries(updates).map(([key, value]) =>
            config.update(key, value, target)
        );

        await Promise.all(updatePromises);
    }

    /**
     * Reset a configuration value to its default
     */
    public async resetConfiguration<K extends keyof V1b3SamaConfiguration>(
        key: K,
        target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global
    ): Promise<void> {
        const config = vscode.workspace.getConfiguration(SettingsService.CONFIG_SECTION);
        await config.update(key, undefined, target);
    }

    /**
     * Validate configuration values
     */
    public validateConfiguration(config?: V1b3SamaConfiguration): { isValid: boolean; errors: string[] } {
        const configToValidate = config || this.getConfiguration();
        const errors: string[] = [];

        // Validate provider
        const validProviders = ['deepseek', 'openai', 'anthropic', 'google', 'openrouter', 'azure', 'local', 'groq'];
        if (!validProviders.includes(configToValidate.provider)) {
            errors.push(`Invalid provider: ${configToValidate.provider}. Must be one of: ${validProviders.join(', ')}`);
        }

        // Validate model (basic check)
        if (!configToValidate.model || configToValidate.model.trim().length === 0) {
            errors.push('Model cannot be empty');
        }

        // Auto-approval validation removed - now hardcoded

        // Validate performance settings
        if (configToValidate.performance.workerPoolSize < 0 || configToValidate.performance.workerPoolSize > 16) {
            errors.push('workerPoolSize must be between 0 and 16');
        }

        if (configToValidate.performance.streamingTimeout < 5000 || configToValidate.performance.streamingTimeout > 120000) {
            errors.push('streamingTimeout must be between 5 seconds and 2 minutes');
        }

        if (configToValidate.performance.alertThreshold < 5 || configToValidate.performance.alertThreshold > 100) {
            errors.push('alertThreshold must be between 5% and 100%');
        }

        // Validate file settings
        if (configToValidate.files.maxFileSize < 1048576) {
            errors.push('maxFileSize must be at least 1MB');
        }

        if (configToValidate.files.workerThreshold < 10240) {
            errors.push('workerThreshold must be at least 10KB');
        }

        // Validate cache settings
        if (configToValidate.cache.cacheTTL < 60000 || configToValidate.cache.cacheTTL > 3600000) {
            errors.push('cacheTTL must be between 1 minute and 1 hour');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get configuration for a specific section
     */
    // Auto-approval config removed - now hardcoded

    public getPerformanceConfig() {
        return this.getConfiguration().performance;
    }

    public getCoreConfig() {
        return {
            provider: this.getConfiguration().provider,
            model: this.getConfiguration().model
        };
    }

    /**
     * Check if a specific feature is enabled
     */
    public isFeatureEnabled(feature: 'webSearch' | 'streaming' | 'performanceMonitoring' | 'responseCache' | 'verboseLogging'): boolean {
        const config = this.getConfiguration();

        switch (feature) {
            case 'webSearch':
                return config.enableWebSearch;
            case 'streaming':
                return config.performance.enableStreaming;
            case 'performanceMonitoring':
                return config.performance.enablePerformanceMonitoring;
            case 'responseCache':
                return config.cache.enableResponseCache;
            case 'verboseLogging':
                return config.debug.enableVerboseLogging;
            default:
                return false;
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this._disposables.forEach(d => d.dispose());
        this._disposables = [];
        this._changeEmitter.dispose();
    }
}
