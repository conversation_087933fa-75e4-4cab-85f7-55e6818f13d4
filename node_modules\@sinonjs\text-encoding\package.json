{"name": "@sinonjs/text-encoding", "scripts": {"postpublish": "git push --tags"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <filip.dupan<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "Author: <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "version": "0.7.3", "description": "Polyfill for the Encoding Living Standard's API.", "main": "index.js", "files": ["index.js", "lib/encoding.js", "lib/encoding-indexes.js"], "repository": {"type": "git", "url": "https://github.com/sinonjs/text-encoding.git"}, "keywords": ["encoding", "decoding", "living standard"], "bugs": {"url": "https://github.com/sinonjs/text-encoding/issues"}, "homepage": "https://github.com/sinonjs/text-encoding", "license": "(Unlicense OR Apache-2.0)"}