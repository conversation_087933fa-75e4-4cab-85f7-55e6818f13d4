{"version": 3, "file": "HydratingCircuitError.js", "sourceRoot": "", "sources": ["../../../src/errors/HydratingCircuitError.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,qBAAsB,SAAQ,KAAK;IAE9C;;;OAGG;IACH,YAAY,OAAO,GAAG,yDAAyD;QAC7E,KAAK,CAAC,OAAO,CAAC,CAAC;QAND,4BAAuB,GAAG,IAAI,CAAC;IAO/C,CAAC;CACF", "sourcesContent": ["export class HydratingCircuitError extends Error {\n  public readonly isHydratingCircuitError = true;\n  /**\n   * Exception thrown from {@link CircuitBreakerPolicy.execute} when the\n   * circuit breaker is open.\n   */\n  constructor(message = 'Execution prevented because the circuit breaker is open') {\n    super(message);\n  }\n}\n"]}