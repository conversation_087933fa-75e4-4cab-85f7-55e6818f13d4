{"version": 3, "file": "workloadIdentityCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/workloadIdentityCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link WorkloadIdentityCredential}\n */\nexport interface WorkloadIdentityCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {\n  /**\n   * ID of the application's Microsoft Entra tenant. Also called its directory ID.\n   */\n  tenantId?: string;\n  /**\n   * The client ID of a Microsoft Entra app registration.\n   */\n  clientId?: string;\n  /**\n   * The path to a file containing a Kubernetes service account token that authenticates the identity.\n   */\n  tokenFilePath?: string;\n}\n"]}