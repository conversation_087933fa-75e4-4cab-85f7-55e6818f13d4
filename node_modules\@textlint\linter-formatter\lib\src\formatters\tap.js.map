{"version": 3, "file": "tap.js", "sourceRoot": "", "sources": ["../../../src/formatters/tap.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,YAAY,CAAC;;;;;AAGb,sDAA2B;AAE3B,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF;;;;GAIG;AACH,SAAS,cAAc,CAAC,OAAY;IAChC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;QAC1C,OAAO,OAAO,CAAC;IACnB,CAAC;SAAM,CAAC;QACJ,OAAO,SAAS,CAAC;IACrB,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,UAAe;IACtC,MAAM,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;IAC9B,MAAM;QACF,MAAM;YACN,iBAAI;iBACC,QAAQ,CAAC,UAAU,CAAC;iBACpB,KAAK,CAAC,IAAI,CAAC;iBACX,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IAC7B,MAAM,IAAI,OAAO,CAAC;IAClB,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,OAAyB;IACxC,IAAI,MAAM,GAAG,qBAAqB,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAE3D,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,EAAE;QAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,WAAW,GAAQ,EAAE,CAAC;QAE1B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,UAAU,GAAG,QAAQ,CAAC;YAEtB,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO;gBAC9B,MAAM,UAAU,GAAG;oBACf,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC;oBACjC,IAAI,EAAE;wBACF,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;wBACvB,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC;wBAC3B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;qBAC/B;iBACJ,CAAC;gBAEF,+DAA+D;gBAC/D,gDAAgD;gBAChD,oFAAoF;gBACpF,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;oBAC3B,IAAI,OAAO,WAAW,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;wBAC9C,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC;oBAC9B,CAAC;oBACD,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACJ,WAAW,GAAG,UAAU,CAAC;gBAC7B,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QAED,MAAM,IAAI,UAAU,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEvE,0CAA0C;QAC1C,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,kBAAe,SAAS,CAAC"}