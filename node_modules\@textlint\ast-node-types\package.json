{"name": "@textlint/ast-node-types", "version": "14.8.0", "description": "textlint AST node type definition.", "keywords": ["textlint"], "repository": {"type": "git", "url": "https://github.com/textlint/textlint.git"}, "license": "MIT", "author": "azu", "type": "commonjs", "main": "./lib/src/index.js", "module": "./module/src/index.js", "types": "./lib/src/index.d.ts", "files": ["bin/", "lib/", "module/", "src/", "!*.tsbuil<PERSON><PERSON>"], "scripts": {"build": "tsc -b && tsc -b tsconfig.module.json", "clean": "rimraf lib/ module/", "prepack": "npm run build", "test": "mocha"}, "devDependencies": {"mocha": "^10.8.1", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "gitHead": "aeb76ceab6e7a38007b5a16db7302991f970a2a7"}