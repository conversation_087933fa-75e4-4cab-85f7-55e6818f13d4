/**
 * AccountContext is used to pass account information when the bridge is initialized
 *
 * NAA (MetaOS) apps are created and destroyed for the same session multiple times.
 * `AccountContext` helps in booting up the cached account when the bridge
 * is recreated for a new NAA instance in the same auth session.
 */
export interface AccountContext {
    homeAccountId: string;
    environment: string;
    tenantId: string;
}
//# sourceMappingURL=BridgeAccountContext.d.ts.map