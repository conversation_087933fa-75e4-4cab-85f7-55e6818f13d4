"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFileFromStream = exports.createFile = exports.agentPolicyName = exports.agentPolicy = exports.auxiliaryAuthenticationHeaderPolicyName = exports.auxiliaryAuthenticationHeaderPolicy = exports.ndJsonPolicyName = exports.ndJsonPolicy = exports.bearerTokenAuthenticationPolicyName = exports.bearerTokenAuthenticationPolicy = exports.formDataPolicyName = exports.formDataPolicy = exports.tlsPolicyName = exports.tlsPolicy = exports.userAgentPolicyName = exports.userAgentPolicy = exports.defaultRetryPolicy = exports.tracingPolicyName = exports.tracingPolicy = exports.retryPolicy = exports.throttlingRetryPolicyName = exports.throttlingRetryPolicy = exports.systemErrorRetryPolicyName = exports.systemErrorRetryPolicy = exports.redirectPolicyName = exports.redirectPolicy = exports.getDefaultProxySettings = exports.proxyPolicyName = exports.proxyPolicy = exports.multipartPolicyName = exports.multipartPolicy = exports.logPolicyName = exports.logPolicy = exports.setClientRequestIdPolicyName = exports.setClientRequestIdPolicy = exports.exponentialRetryPolicyName = exports.exponentialRetryPolicy = exports.decompressResponsePolicyName = exports.decompressResponsePolicy = exports.isRestError = exports.RestError = exports.createPipelineRequest = exports.createHttpHeaders = exports.createDefaultHttpClient = exports.createPipelineFromOptions = exports.createEmptyPipeline = void 0;
var pipeline_js_1 = require("./pipeline.js");
Object.defineProperty(exports, "createEmptyPipeline", { enumerable: true, get: function () { return pipeline_js_1.createEmptyPipeline; } });
var createPipelineFromOptions_js_1 = require("./createPipelineFromOptions.js");
Object.defineProperty(exports, "createPipelineFromOptions", { enumerable: true, get: function () { return createPipelineFromOptions_js_1.createPipelineFromOptions; } });
var defaultHttpClient_js_1 = require("./defaultHttpClient.js");
Object.defineProperty(exports, "createDefaultHttpClient", { enumerable: true, get: function () { return defaultHttpClient_js_1.createDefaultHttpClient; } });
var httpHeaders_js_1 = require("./httpHeaders.js");
Object.defineProperty(exports, "createHttpHeaders", { enumerable: true, get: function () { return httpHeaders_js_1.createHttpHeaders; } });
var pipelineRequest_js_1 = require("./pipelineRequest.js");
Object.defineProperty(exports, "createPipelineRequest", { enumerable: true, get: function () { return pipelineRequest_js_1.createPipelineRequest; } });
var restError_js_1 = require("./restError.js");
Object.defineProperty(exports, "RestError", { enumerable: true, get: function () { return restError_js_1.RestError; } });
Object.defineProperty(exports, "isRestError", { enumerable: true, get: function () { return restError_js_1.isRestError; } });
var decompressResponsePolicy_js_1 = require("./policies/decompressResponsePolicy.js");
Object.defineProperty(exports, "decompressResponsePolicy", { enumerable: true, get: function () { return decompressResponsePolicy_js_1.decompressResponsePolicy; } });
Object.defineProperty(exports, "decompressResponsePolicyName", { enumerable: true, get: function () { return decompressResponsePolicy_js_1.decompressResponsePolicyName; } });
var exponentialRetryPolicy_js_1 = require("./policies/exponentialRetryPolicy.js");
Object.defineProperty(exports, "exponentialRetryPolicy", { enumerable: true, get: function () { return exponentialRetryPolicy_js_1.exponentialRetryPolicy; } });
Object.defineProperty(exports, "exponentialRetryPolicyName", { enumerable: true, get: function () { return exponentialRetryPolicy_js_1.exponentialRetryPolicyName; } });
var setClientRequestIdPolicy_js_1 = require("./policies/setClientRequestIdPolicy.js");
Object.defineProperty(exports, "setClientRequestIdPolicy", { enumerable: true, get: function () { return setClientRequestIdPolicy_js_1.setClientRequestIdPolicy; } });
Object.defineProperty(exports, "setClientRequestIdPolicyName", { enumerable: true, get: function () { return setClientRequestIdPolicy_js_1.setClientRequestIdPolicyName; } });
var logPolicy_js_1 = require("./policies/logPolicy.js");
Object.defineProperty(exports, "logPolicy", { enumerable: true, get: function () { return logPolicy_js_1.logPolicy; } });
Object.defineProperty(exports, "logPolicyName", { enumerable: true, get: function () { return logPolicy_js_1.logPolicyName; } });
var multipartPolicy_js_1 = require("./policies/multipartPolicy.js");
Object.defineProperty(exports, "multipartPolicy", { enumerable: true, get: function () { return multipartPolicy_js_1.multipartPolicy; } });
Object.defineProperty(exports, "multipartPolicyName", { enumerable: true, get: function () { return multipartPolicy_js_1.multipartPolicyName; } });
var proxyPolicy_js_1 = require("./policies/proxyPolicy.js");
Object.defineProperty(exports, "proxyPolicy", { enumerable: true, get: function () { return proxyPolicy_js_1.proxyPolicy; } });
Object.defineProperty(exports, "proxyPolicyName", { enumerable: true, get: function () { return proxyPolicy_js_1.proxyPolicyName; } });
Object.defineProperty(exports, "getDefaultProxySettings", { enumerable: true, get: function () { return proxyPolicy_js_1.getDefaultProxySettings; } });
var redirectPolicy_js_1 = require("./policies/redirectPolicy.js");
Object.defineProperty(exports, "redirectPolicy", { enumerable: true, get: function () { return redirectPolicy_js_1.redirectPolicy; } });
Object.defineProperty(exports, "redirectPolicyName", { enumerable: true, get: function () { return redirectPolicy_js_1.redirectPolicyName; } });
var systemErrorRetryPolicy_js_1 = require("./policies/systemErrorRetryPolicy.js");
Object.defineProperty(exports, "systemErrorRetryPolicy", { enumerable: true, get: function () { return systemErrorRetryPolicy_js_1.systemErrorRetryPolicy; } });
Object.defineProperty(exports, "systemErrorRetryPolicyName", { enumerable: true, get: function () { return systemErrorRetryPolicy_js_1.systemErrorRetryPolicyName; } });
var throttlingRetryPolicy_js_1 = require("./policies/throttlingRetryPolicy.js");
Object.defineProperty(exports, "throttlingRetryPolicy", { enumerable: true, get: function () { return throttlingRetryPolicy_js_1.throttlingRetryPolicy; } });
Object.defineProperty(exports, "throttlingRetryPolicyName", { enumerable: true, get: function () { return throttlingRetryPolicy_js_1.throttlingRetryPolicyName; } });
var retryPolicy_js_1 = require("./policies/retryPolicy.js");
Object.defineProperty(exports, "retryPolicy", { enumerable: true, get: function () { return retryPolicy_js_1.retryPolicy; } });
var tracingPolicy_js_1 = require("./policies/tracingPolicy.js");
Object.defineProperty(exports, "tracingPolicy", { enumerable: true, get: function () { return tracingPolicy_js_1.tracingPolicy; } });
Object.defineProperty(exports, "tracingPolicyName", { enumerable: true, get: function () { return tracingPolicy_js_1.tracingPolicyName; } });
var defaultRetryPolicy_js_1 = require("./policies/defaultRetryPolicy.js");
Object.defineProperty(exports, "defaultRetryPolicy", { enumerable: true, get: function () { return defaultRetryPolicy_js_1.defaultRetryPolicy; } });
var userAgentPolicy_js_1 = require("./policies/userAgentPolicy.js");
Object.defineProperty(exports, "userAgentPolicy", { enumerable: true, get: function () { return userAgentPolicy_js_1.userAgentPolicy; } });
Object.defineProperty(exports, "userAgentPolicyName", { enumerable: true, get: function () { return userAgentPolicy_js_1.userAgentPolicyName; } });
var tlsPolicy_js_1 = require("./policies/tlsPolicy.js");
Object.defineProperty(exports, "tlsPolicy", { enumerable: true, get: function () { return tlsPolicy_js_1.tlsPolicy; } });
Object.defineProperty(exports, "tlsPolicyName", { enumerable: true, get: function () { return tlsPolicy_js_1.tlsPolicyName; } });
var formDataPolicy_js_1 = require("./policies/formDataPolicy.js");
Object.defineProperty(exports, "formDataPolicy", { enumerable: true, get: function () { return formDataPolicy_js_1.formDataPolicy; } });
Object.defineProperty(exports, "formDataPolicyName", { enumerable: true, get: function () { return formDataPolicy_js_1.formDataPolicyName; } });
var bearerTokenAuthenticationPolicy_js_1 = require("./policies/bearerTokenAuthenticationPolicy.js");
Object.defineProperty(exports, "bearerTokenAuthenticationPolicy", { enumerable: true, get: function () { return bearerTokenAuthenticationPolicy_js_1.bearerTokenAuthenticationPolicy; } });
Object.defineProperty(exports, "bearerTokenAuthenticationPolicyName", { enumerable: true, get: function () { return bearerTokenAuthenticationPolicy_js_1.bearerTokenAuthenticationPolicyName; } });
var ndJsonPolicy_js_1 = require("./policies/ndJsonPolicy.js");
Object.defineProperty(exports, "ndJsonPolicy", { enumerable: true, get: function () { return ndJsonPolicy_js_1.ndJsonPolicy; } });
Object.defineProperty(exports, "ndJsonPolicyName", { enumerable: true, get: function () { return ndJsonPolicy_js_1.ndJsonPolicyName; } });
var auxiliaryAuthenticationHeaderPolicy_js_1 = require("./policies/auxiliaryAuthenticationHeaderPolicy.js");
Object.defineProperty(exports, "auxiliaryAuthenticationHeaderPolicy", { enumerable: true, get: function () { return auxiliaryAuthenticationHeaderPolicy_js_1.auxiliaryAuthenticationHeaderPolicy; } });
Object.defineProperty(exports, "auxiliaryAuthenticationHeaderPolicyName", { enumerable: true, get: function () { return auxiliaryAuthenticationHeaderPolicy_js_1.auxiliaryAuthenticationHeaderPolicyName; } });
var agentPolicy_js_1 = require("./policies/agentPolicy.js");
Object.defineProperty(exports, "agentPolicy", { enumerable: true, get: function () { return agentPolicy_js_1.agentPolicy; } });
Object.defineProperty(exports, "agentPolicyName", { enumerable: true, get: function () { return agentPolicy_js_1.agentPolicyName; } });
var file_js_1 = require("./util/file.js");
Object.defineProperty(exports, "createFile", { enumerable: true, get: function () { return file_js_1.createFile; } });
Object.defineProperty(exports, "createFileFromStream", { enumerable: true, get: function () { return file_js_1.createFileFromStream; } });
//# sourceMappingURL=index.js.map