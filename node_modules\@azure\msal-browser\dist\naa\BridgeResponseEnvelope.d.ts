import { BridgeError } from "./BridgeError.js";
import { TokenResponse } from "./TokenResponse.js";
import { AccountInfo } from "./AccountInfo.js";
import { InitContext } from "./InitContext.js";
export type BridgeResponseEnvelope = {
    messageType: "NestedAppAuthResponse";
    requestId: string;
    success: boolean;
    token?: TokenResponse;
    error?: BridgeError;
    account?: AccountInfo;
    initContext?: InitContext;
};
//# sourceMappingURL=BridgeResponseEnvelope.d.ts.map