{"version": 3, "file": "checkEnvironment.js", "sourceRoot": "", "sources": ["../../../src/util/checkEnvironment.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAmClC;;GAEG;AACH,yDAAyD;AACzD,MAAM,CAAC,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC;AAEjG;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GACtB,OAAO,IAAI,KAAK,QAAQ;IACxB,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAA,KAAK,UAAU;IACzC,CAAC,CAAA,MAAA,IAAI,CAAC,WAAW,0CAAE,IAAI,MAAK,4BAA4B;QACtD,CAAA,MAAA,IAAI,CAAC,WAAW,0CAAE,IAAI,MAAK,0BAA0B;QACrD,CAAA,MAAA,IAAI,CAAC,WAAW,0CAAE,IAAI,MAAK,yBAAyB,CAAC,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GACjB,OAAO,IAAI,KAAK,WAAW;IAC3B,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW;IACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,WAAW,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,WAAW,CAAC;AAEtF;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GACrB,OAAO,UAAU,CAAC,OAAO,KAAK,WAAW;IACzC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;IACnC,OAAO,CAAC,MAAA,UAAU,CAAC,OAAO,CAAC,QAAQ,0CAAE,IAAI,CAAC,CAAC;AAE7C;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC;AAE7D;;GAEG;AACH,4GAA4G;AAC5G,MAAM,CAAC,MAAM,aAAa,GACxB,OAAO,SAAS,KAAK,WAAW,IAAI,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,MAAK,aAAa,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ninterface Window {\n  document: unknown;\n}\n\ninterface DedicatedWorkerGlobalScope {\n  constructor: {\n    name: string;\n  };\n\n  importScripts: (...paths: string[]) => void;\n}\n\ninterface Navigator {\n  product: string;\n}\n\ninterface DenoGlobal {\n  version: {\n    deno: string;\n  };\n}\n\ninterface BunGlobal {\n  version: string;\n}\n\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\ndeclare const window: Window;\ndeclare const self: DedicatedWorkerGlobalScope;\ndeclare const Deno: DenoGlobal;\ndeclare const Bun: BunGlobal;\ndeclare const navigator: Navigator;\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Browser.\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\nexport const isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Worker.\n */\nexport const isWebWorker =\n  typeof self === \"object\" &&\n  typeof self?.importScripts === \"function\" &&\n  (self.constructor?.name === \"DedicatedWorkerGlobalScope\" ||\n    self.constructor?.name === \"ServiceWorkerGlobalScope\" ||\n    self.constructor?.name === \"SharedWorkerGlobalScope\");\n\n/**\n * A constant that indicates whether the environment the code is running is Deno.\n */\nexport const isDeno =\n  typeof Deno !== \"undefined\" &&\n  typeof Deno.version !== \"undefined\" &&\n  typeof Deno.version.deno !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is Bun.sh.\n */\nexport const isBun = typeof Bun !== \"undefined\" && typeof Bun.version !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n */\nexport const isNodeLike =\n  typeof globalThis.process !== \"undefined\" &&\n  Boolean(globalThis.process.version) &&\n  Boolean(globalThis.process.versions?.node);\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNodeRuntime = isNodeLike && !isBun && !isDeno;\n\n/**\n * A constant that indicates whether the environment the code is running is in React-Native.\n */\n// https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Core/setUpNavigator.js\nexport const isReactNative =\n  typeof navigator !== \"undefined\" && navigator?.product === \"ReactNative\";\n"]}