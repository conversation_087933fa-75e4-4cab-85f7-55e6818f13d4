/**
 * Enhanced Memory System Interfaces
 * Comprehensive memory management for V1b3-Sama with multi-layer architecture
 */

import * as vscode from 'vscode';
import { LLMMessage } from './ILLMService';

// Enhanced Conversation Data with threading and checkpoints
export interface EnhancedConversationData {
    id: string;
    title: string;
    messages: ThreadedMessage[];
    createdAt: Date;
    updatedAt: Date;
    provider: string;
    model: string;
    totalCost: number;
    totalTokens: { input: number; output: number };
    
    // Threading support
    parentId?: string; // ID of conversation this was branched from
    branchPoint?: string; // Message ID where this conversation branched
    
    // Checkpoints
    checkpoints: SessionCheckpoint[];
    
    // Memory integration
    memoryContext: ConversationMemoryContext;
    
    // Project association
    projectId: string;
    workspaceRoot?: string;
}

export interface ThreadedMessage extends LLMMessage {
    id: string;
    timestamp: number;
    replyToMessageId?: string; // For threading within conversation
    metadata?: {
        tokensUsed?: { input: number; output: number };
        cost?: number;
        executionPlan?: any;
        attachedFiles?: string[];
        contextUsed?: string[];
    };
}

export interface SessionCheckpoint {
    id: string;
    name: string;
    description?: string;
    timestamp: number;
    conversationId: string;
    messageIndex: number; // Index in conversation where checkpoint was created
    
    // State capture
    executionState?: any;
    attachedFiles?: Array<{path: string, content: string}>;
    workspaceState?: any;
    
    // Memory snapshot
    memorySnapshot?: MemorySnapshot;
    
    // User annotations
    userNotes?: string;
    tags?: string[];
}

export interface ConversationMemoryContext {
    userPreferences: UserPreference[];
    projectMemories: ProjectMemory[];
    conversationLearnings: ConversationLearning[];
    codePatterns: CodePattern[];
}

export interface UserPreference {
    id: string;
    category: 'coding_style' | 'language_preference' | 'framework_preference' | 'naming_convention' | 'architecture_pattern';
    key: string;
    value: any;
    confidence: number; // 0-1, how confident we are about this preference
    learnedFrom: string[]; // Conversation IDs where this was learned
    lastUpdated: number;
    frequency: number; // How often this preference appears
}

export interface ProjectMemory {
    id: string;
    projectId: string;
    category: 'architecture' | 'dependencies' | 'conventions' | 'patterns' | 'issues' | 'goals';
    key: string;
    value: any;
    description?: string;
    relevance: number; // 0-1, how relevant this is to current work
    lastAccessed: number;
    accessCount: number;
    relatedFiles?: string[];
    tags?: string[];
}

export interface ConversationLearning {
    id: string;
    conversationId: string;
    category: 'user_intent' | 'problem_solving' | 'code_quality' | 'feedback' | 'correction';
    insight: string;
    confidence: number;
    applicability: 'global' | 'project' | 'conversation';
    learnedAt: number;
    appliedCount: number;
}

export interface CodePattern {
    id: string;
    name: string;
    pattern: string; // Regex or AST pattern
    language: string;
    category: 'function' | 'class' | 'import' | 'error_handling' | 'testing' | 'architecture';
    frequency: number;
    lastSeen: number;
    projectIds: string[];
    examples: Array<{
        filePath: string;
        code: string;
        context: string;
    }>;
}

export interface MemorySnapshot {
    timestamp: number;
    userPreferences: UserPreference[];
    projectMemories: ProjectMemory[];
    conversationLearnings: ConversationLearning[];
    codePatterns: CodePattern[];
    stats: {
        totalMemories: number;
        memoryCategories: Record<string, number>;
        confidenceDistribution: Record<string, number>;
    };
}

// Memory Layer Interfaces
export interface IUserPreferenceService {
    getPreferences(category?: string): Promise<UserPreference[]>;
    setPreference(category: string, key: string, value: any, confidence?: number): Promise<void>;
    updatePreferenceConfidence(id: string, confidence: number): Promise<void>;
    learnFromConversation(conversationId: string, messages: ThreadedMessage[]): Promise<UserPreference[]>;
    getPreferencesByRelevance(context: string): Promise<UserPreference[]>;
    getPreferencesAsContext(): Promise<string>;
}

export interface IProjectMemoryService {
    getProjectMemories(projectId: string, category?: string): Promise<ProjectMemory[]>;
    addProjectMemory(projectId: string, category: string, key: string, value: any, description?: string): Promise<void>;
    updateMemoryRelevance(id: string, relevance: number): Promise<void>;
    findRelevantMemories(projectId: string, query: string): Promise<ProjectMemory[]>;
    analyzeProjectPatterns(projectId: string): Promise<ProjectMemory[]>;
    getProjectMemoriesAsContext(): Promise<string>;
    autoAnalyzeCurrentProject(): Promise<void>;
}

export interface IConversationThreadingService {
    createBranch(parentConversationId: string, branchFromMessageId: string, title?: string): Promise<EnhancedConversationData>;
    getBranches(conversationId: string): Promise<EnhancedConversationData[]>;
    getConversationTree(rootConversationId: string): Promise<ConversationTree>;
    mergeConversations(sourceId: string, targetId: string): Promise<EnhancedConversationData>;
}

export interface ConversationTree {
    root: EnhancedConversationData;
    branches: Array<{
        conversation: EnhancedConversationData;
        branchPoint: ThreadedMessage;
        children: ConversationTree[];
    }>;
}

export interface IEnhancedConversationPersistence {
    saveConversation(conversation: EnhancedConversationData): Promise<void>;
    loadConversation(conversationId: string): Promise<EnhancedConversationData | undefined>;
    getAllConversations(projectId?: string): Promise<EnhancedConversationData[]>;
    deleteConversation(conversationId: string): Promise<void>;
    
    // Checkpoint management
    createCheckpoint(conversationId: string, name: string, description?: string): Promise<SessionCheckpoint>;
    loadCheckpoint(checkpointId: string): Promise<SessionCheckpoint | undefined>;
    getCheckpoints(conversationId: string): Promise<SessionCheckpoint[]>;
    deleteCheckpoint(checkpointId: string): Promise<void>;
    
    // Threading
    createBranch(parentId: string, branchFromMessageId: string, title?: string): Promise<EnhancedConversationData>;
    getBranches(conversationId: string): Promise<EnhancedConversationData[]>;
    
    // Search and filtering
    searchConversations(query: string, projectId?: string): Promise<EnhancedConversationData[]>;
    getConversationsByTimeRange(startDate: Date, endDate: Date, projectId?: string): Promise<EnhancedConversationData[]>;
    getConversationsByProvider(provider: string, model?: string): Promise<EnhancedConversationData[]>;
}

export interface ICrossSessionContextService {
    getSessionContext(projectId?: string): Promise<SessionContext>;
    updateSessionContext(context: Partial<SessionContext>): Promise<void>;
    restoreSessionState(checkpointId: string): Promise<void>;
    getRelevantPastContext(currentPrompt: string, projectId?: string): Promise<PastContext>;
    getSessionContextAsString(): Promise<string>;
}

export interface SessionContext {
    currentProject: string;
    activeFiles: string[];
    recentConversations: string[];
    userGoals: string[];
    workspaceState: any;
    preferences: UserPreference[];
    lastActivity: number;
}

export interface PastContext {
    relevantConversations: Array<{
        conversation: EnhancedConversationData;
        relevanceScore: number;
        relevantMessages: ThreadedMessage[];
    }>;
    applicablePatterns: CodePattern[];
    projectInsights: ProjectMemory[];
    userPreferences: UserPreference[];
}

export interface MemorySystemStats {
    conversations: {
        total: number;
        byProject: Record<string, number>;
        byProvider: Record<string, number>;
        withBranches: number;
        withCheckpoints: number;
    };
    memory: {
        userPreferences: number;
        projectMemories: number;
        conversationLearnings: number;
        codePatterns: number;
    };
    usage: {
        totalTokens: number;
        totalCost: number;
        averageConversationLength: number;
        mostActiveProject: string;
    };
    performance: {
        memoryRetrievalTime: number;
        contextGenerationTime: number;
        indexSize: number;
    };
}
