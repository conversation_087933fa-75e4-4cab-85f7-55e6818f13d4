{"version": 3, "file": "serviceClient.js", "sourceRoot": "", "sources": ["../../src/serviceClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAclC,OAAO,EAAE,qBAAqB,EAAE,MAAM,2BAA2B,CAAC;AAElE,OAAO,EAAE,oBAAoB,EAAE,MAAM,eAAe,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,0BAA0B,EAAE,MAAM,sBAAsB,CAAC;AAClE,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,+BAA+B,EAAE,MAAM,uBAAuB,CAAC;AACxE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAqClC;;GAEG;AACH,MAAM,OAAO,aAAa;IA4BxB;;;OAGG;IACH,YAAY,UAAgC,EAAE;;QAC5C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACtD,IAAI,CAAC,SAAS,GAAG,MAAA,OAAO,CAAC,QAAQ,mCAAI,OAAO,CAAC,OAAO,CAAC;QACrD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,CAAC,OAAO,CACZ,sFAAsF,CACvF,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,uBAAuB,CAAC;QAChE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,0BAA0B,EAAE,CAAC;QAEtE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,MAAA,OAAO,CAAC,kBAAkB,0CAAE,MAAM,EAAE,CAAC;YACvC,KAAK,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC9D,2DAA2D;gBAC3D,6CAA6C;gBAC7C,MAAM,UAAU,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;oBAC9B,UAAU;iBACX,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAAwB;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B;QAE5B,MAAM,QAAQ,GAAuB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;QAC7E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CACb,2IAA2I,CAC5I,CAAC;QACJ,CAAC;QAED,oFAAoF;QACpF,iFAAiF;QACjF,iCAAiC;QACjC,MAAM,GAAG,GAAG,aAAa,CAAC,QAAQ,EAAE,aAAa,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAE7E,MAAM,OAAO,GAAqB,qBAAqB,CAAC;YACtD,GAAG;SACJ,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;QAC1C,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACvD,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC;QAC5C,aAAa,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAEtD,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,CAAC;QAC1E,IAAI,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;YAC7C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;YAE9C,IAAI,cAAc,EAAE,CAAC;gBACnB,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC3B,OAAO,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;gBAC3C,CAAC;gBAED,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;oBACpC,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;gBAC7D,CAAC;gBAED,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;oBACtC,OAAO,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;gBACjE,CAAC;gBAED,IAAI,cAAc,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;oBACnD,aAAa,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;gBACrE,CAAC;gBAED,IAAI,cAAc,CAAC,uBAAuB,EAAE,CAAC;oBAC3C,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACzC,CAAC;YACH,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC5C,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,yBAAyB,KAAK,SAAS,EAAE,CAAC;YACpD,OAAO,CAAC,yBAAyB,GAAG,+BAA+B,CAAC,aAAa,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,YAAY,GAAG,eAAe,CAClC,WAAW,EACX,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CACvC,CAAC;YACP,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE,CAAC;gBACxB,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,OAAO,KAAK,KAAK,QAAQ,KAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,CAAA,EAAE,CAAC;gBACjD,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC;gBACnC,MAAM,YAAY,GAAG,eAAe,CAClC,WAAW,EACX,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAChF,CAAC;gBACF,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;gBAC7B,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE,CAAC;oBACxB,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAED,SAAS,qBAAqB,CAAC,OAA6B;IAC1D,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACtD,MAAM,iBAAiB,GACrB,OAAO,CAAC,UAAU,IAAI,gBAAgB;QACpC,CAAC,CAAC,EAAE,gBAAgB,EAAE,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE;QACtD,CAAC,CAAC,SAAS,CAAC;IAEhB,OAAO,oBAAoB,iCACtB,OAAO,KACV,iBAAiB,IACjB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,OAA6B;IACxD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC7B,OAAO,OAAO,CAAC,gBAAgB,CAAC;IAClC,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,OAAO,GAAG,OAAO,CAAC,QAAQ,WAAW,CAAC;IACxC,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,GAAG,OAAO,CAAC,OAAO,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACpD,MAAM,IAAI,KAAK,CACb,2JAA2J,CAC5J,CAAC;IACJ,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  CommonClientOptions,\n  OperationArguments,\n  OperationRequest,\n  OperationSpec,\n} from \"./interfaces.js\";\nimport type {\n  HttpClient,\n  Pipeline,\n  PipelineRequest,\n  PipelineResponse,\n} from \"@azure/core-rest-pipeline\";\nimport { createPipelineRequest } from \"@azure/core-rest-pipeline\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { createClientPipeline } from \"./pipeline.js\";\nimport { flattenResponse } from \"./utils.js\";\nimport { getCachedDefaultHttpClient } from \"./httpClientCache.js\";\nimport { getOperationRequestInfo } from \"./operationHelpers.js\";\nimport { getRequestUrl } from \"./urlHelpers.js\";\nimport { getStreamingResponseStatusCodes } from \"./interfaceHelpers.js\";\nimport { logger } from \"./log.js\";\n\n/**\n * Options to be provided while creating the client.\n */\nexport interface ServiceClientOptions extends CommonClientOptions {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   * @deprecated This property is deprecated and will be removed soon, please use endpoint instead\n   */\n  baseUri?: string;\n  /**\n   * If specified, this is the endpoint that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   * to encourage customer to use endpoint, we mark the baseUri as deprecated.\n   */\n  endpoint?: string;\n  /**\n   * If specified, will be used to build the BearerTokenAuthenticationPolicy.\n   */\n  credentialScopes?: string | string[];\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  requestContentType?: string;\n  /**\n   * Credential used to authenticate the request.\n   */\n  credential?: TokenCredential;\n  /**\n   * A customized pipeline to use, otherwise a default one will be created.\n   */\n  pipeline?: Pipeline;\n}\n\n/**\n * Initializes a new instance of the ServiceClient.\n */\nexport class ServiceClient {\n  /**\n   * If specified, this is the base URI that requests will be made against for this ServiceClient.\n   * If it is not specified, then all OperationSpecs must contain a baseUrl property.\n   */\n  private readonly _endpoint?: string;\n\n  /**\n   * The default request content type for the service.\n   * Used if no requestContentType is present on an OperationSpec.\n   */\n  private readonly _requestContentType?: string;\n\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  private readonly _allowInsecureConnection?: boolean;\n\n  /**\n   * The HTTP client that will be used to send requests.\n   */\n  private readonly _httpClient: HttpClient;\n\n  /**\n   * The pipeline used by this client to make requests\n   */\n  public readonly pipeline: Pipeline;\n\n  /**\n   * The ServiceClient constructor\n   * @param options - The service client options that govern the behavior of the client.\n   */\n  constructor(options: ServiceClientOptions = {}) {\n    this._requestContentType = options.requestContentType;\n    this._endpoint = options.endpoint ?? options.baseUri;\n    if (options.baseUri) {\n      logger.warning(\n        \"The baseUri option for SDK Clients has been deprecated, please use endpoint instead.\",\n      );\n    }\n    this._allowInsecureConnection = options.allowInsecureConnection;\n    this._httpClient = options.httpClient || getCachedDefaultHttpClient();\n\n    this.pipeline = options.pipeline || createDefaultPipeline(options);\n    if (options.additionalPolicies?.length) {\n      for (const { policy, position } of options.additionalPolicies) {\n        // Sign happens after Retry and is commonly needed to occur\n        // before policies that intercept post-retry.\n        const afterPhase = position === \"perRetry\" ? \"Sign\" : undefined;\n        this.pipeline.addPolicy(policy, {\n          afterPhase,\n        });\n      }\n    }\n  }\n\n  /**\n   * Send the provided httpRequest.\n   */\n  async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    return this.pipeline.sendRequest(this._httpClient, request);\n  }\n\n  /**\n   * Send an HTTP request that is populated using the provided OperationSpec.\n   * @typeParam T - The typed result of the request, based on the OperationSpec.\n   * @param operationArguments - The arguments that the HTTP request's templated values will be populated from.\n   * @param operationSpec - The OperationSpec to use to populate the httpRequest.\n   */\n  async sendOperationRequest<T>(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n  ): Promise<T> {\n    const endpoint: string | undefined = operationSpec.baseUrl || this._endpoint;\n    if (!endpoint) {\n      throw new Error(\n        \"If operationSpec.baseUrl is not specified, then the ServiceClient must have a endpoint string property that contains the base URL to use.\",\n      );\n    }\n\n    // Templatized URLs sometimes reference properties on the ServiceClient child class,\n    // so we have to pass `this` below in order to search these properties if they're\n    // not part of OperationArguments\n    const url = getRequestUrl(endpoint, operationSpec, operationArguments, this);\n\n    const request: OperationRequest = createPipelineRequest({\n      url,\n    });\n    request.method = operationSpec.httpMethod;\n    const operationInfo = getOperationRequestInfo(request);\n    operationInfo.operationSpec = operationSpec;\n    operationInfo.operationArguments = operationArguments;\n\n    const contentType = operationSpec.contentType || this._requestContentType;\n    if (contentType && operationSpec.requestBody) {\n      request.headers.set(\"Content-Type\", contentType);\n    }\n\n    const options = operationArguments.options;\n    if (options) {\n      const requestOptions = options.requestOptions;\n\n      if (requestOptions) {\n        if (requestOptions.timeout) {\n          request.timeout = requestOptions.timeout;\n        }\n\n        if (requestOptions.onUploadProgress) {\n          request.onUploadProgress = requestOptions.onUploadProgress;\n        }\n\n        if (requestOptions.onDownloadProgress) {\n          request.onDownloadProgress = requestOptions.onDownloadProgress;\n        }\n\n        if (requestOptions.shouldDeserialize !== undefined) {\n          operationInfo.shouldDeserialize = requestOptions.shouldDeserialize;\n        }\n\n        if (requestOptions.allowInsecureConnection) {\n          request.allowInsecureConnection = true;\n        }\n      }\n\n      if (options.abortSignal) {\n        request.abortSignal = options.abortSignal;\n      }\n\n      if (options.tracingOptions) {\n        request.tracingOptions = options.tracingOptions;\n      }\n    }\n\n    if (this._allowInsecureConnection) {\n      request.allowInsecureConnection = true;\n    }\n\n    if (request.streamResponseStatusCodes === undefined) {\n      request.streamResponseStatusCodes = getStreamingResponseStatusCodes(operationSpec);\n    }\n\n    try {\n      const rawResponse = await this.sendRequest(request);\n      const flatResponse = flattenResponse(\n        rawResponse,\n        operationSpec.responses[rawResponse.status],\n      ) as T;\n      if (options?.onResponse) {\n        options.onResponse(rawResponse, flatResponse);\n      }\n      return flatResponse;\n    } catch (error: any) {\n      if (typeof error === \"object\" && error?.response) {\n        const rawResponse = error.response;\n        const flatResponse = flattenResponse(\n          rawResponse,\n          operationSpec.responses[error.statusCode] || operationSpec.responses[\"default\"],\n        );\n        error.details = flatResponse;\n        if (options?.onResponse) {\n          options.onResponse(rawResponse, flatResponse, error);\n        }\n      }\n      throw error;\n    }\n  }\n}\n\nfunction createDefaultPipeline(options: ServiceClientOptions): Pipeline {\n  const credentialScopes = getCredentialScopes(options);\n  const credentialOptions =\n    options.credential && credentialScopes\n      ? { credentialScopes, credential: options.credential }\n      : undefined;\n\n  return createClientPipeline({\n    ...options,\n    credentialOptions,\n  });\n}\n\nfunction getCredentialScopes(options: ServiceClientOptions): string | string[] | undefined {\n  if (options.credentialScopes) {\n    return options.credentialScopes;\n  }\n\n  if (options.endpoint) {\n    return `${options.endpoint}/.default`;\n  }\n\n  if (options.baseUri) {\n    return `${options.baseUri}/.default`;\n  }\n\n  if (options.credential && !options.credentialScopes) {\n    throw new Error(\n      `When using credentials, the ServiceClientOptions must contain either a endpoint or a credentialScopes. Unable to create a bearerTokenAuthenticationPolicy`,\n    );\n  }\n\n  return undefined;\n}\n"]}