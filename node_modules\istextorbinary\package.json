{"title": "Is Text or Binary?", "name": "istextorbinary", "version": "9.5.0", "license": "Artistic-2.0", "description": "Determine if a filename and/or buffer is text or binary. Smarter detection than the other solutions.", "homepage": "https://github.com/bevry/istextorbinary", "funding": "https://bevry.me/fund", "repository": {"type": "git", "url": "git+https://github.com/bevry/istextorbinary.git"}, "bugs": {"url": "https://github.com/bevry/istextorbinary/issues"}, "keywords": ["bin", "binary", "browser", "check", "deno", "deno-edition", "deno-entry", "denoland", "detect", "encoding", "es2017", "es2022", "es5", "ext", "extension", "extensions", "file", "is", "is binary", "is binary file", "is text", "is text file", "is text or binary", "is text or binary file", "isbinary", "isbinaryfile", "istext", "istextfile", "module", "node", "path", "text", "typed", "types", "typescript"], "badges": {"list": ["githubworkflow", "npmversion", "npmdownloads", "---", "githubsponsors", "thanksdev", "patreon", "liberapay", "buymeacoffee", "opencollective", "crypto", "paypal", "---", "discord", "twitch"], "config": {"githubWorkflow": "bevry", "githubSponsorsUsername": "bal<PERSON><PERSON>", "thanksdevGithubUsername": "bevry", "buymeacoffeeUsername": "bal<PERSON><PERSON>", "cryptoURL": "https://bevry.me/crypto", "flattrUsername": "bal<PERSON><PERSON>", "liberapayUsername": "bevry", "opencollectiveUsername": "bevry", "patreonUsername": "bevry", "paypalURL": "https://bevry.me/paypal", "wishlistURL": "https://bevry.me/wishlist", "discordServerID": "1147436445783560193", "discordServerInvite": "nQuXddV7VP", "twitchUsername": "bal<PERSON><PERSON>", "githubUsername": "bevry", "githubRepository": "istextorbinary", "githubSlug": "bevry/istextorbinary", "npmPackageName": "istextorbinary"}}, "author": "<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton)", "authors": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton): Accelerating collaborative wisdom."], "maintainers": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton): Accelerating collaborative wisdom.", "<PERSON> <<EMAIL>> (https://mdm.cc) (https://github.com/mikeumus) (https://opencollective.com/mikeumus) (https://twitter.com/mikeumus): We are the space generation; and if you don't know, https://www.spaceforce.mil https://www.virgingalactic.com https://www.spacex.com now you know.", "<PERSON> <<EMAIL>> (https://github.com/robloach)"], "contributors": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton)", "<PERSON> <<EMAIL>> (https://sibnerian.com) (https://github.com/sibnerian)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/sainthkh)", "<PERSON> <<EMAIL>> (https://mdm.cc) (https://github.com/mikeumus) (https://opencollective.com/mikeumus) (https://twitter.com/mikeumus)", "<PERSON> <<EMAIL>> (https://github.com/robloach)", "<PERSON> <<EMAIL>> (https://albinodrought.com) (https://github.com/AlbinoDrought)", "shinnn <<EMAIL>> (https://qiita.com/shinnn) (https://github.com/shinnn)"], "sponsors": ["<PERSON> (https://nesbitt.io) (https://github.com/andrew): Software engineer and researcher", "Balsa <<EMAIL>> (https://balsa.com) (https://github.com/balsa): We're Balsa, and we're building tools for builders.", "Codecov <<EMAIL>> (https://codecov.io) (https://github.com/codecov): Empower developers with tools to improve code quality and testing.", "Poonacha Medappa (https://poonachamedappa.com) (https://github.com/km-Poonacha)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON><PERSON>)", "Sentry (https://sentry.io) (https://github.com/getsentry): Real-time crash reporting for your web apps, mobile apps, and games.", "Syntax <<EMAIL>> (https://syntax.fm) (https://github.com/syntaxfm): Syntax Podcast"], "donors": ["<PERSON> (https://nesbitt.io) (https://github.com/andrew)", "<PERSON><PERSON> Mk<PERSON>n (https://mogoni.dev) (https://github.com/Armenm)", "Balsa <<EMAIL>> (https://balsa.com) (https://github.com/balsa)", "Chad (https://opencollective.com/chad8)", "Codecov <<EMAIL>> (https://codecov.io) (https://github.com/codecov)", "dr.dimitru (https://veliovgroup.com) (https://github.com/dr-dimitru)", "<PERSON> (https://elliottditman.com) (https://github.com/elliottditman)", "entroniq (https://gitlab.com/entroniq) (https://thanks.dev/d/gl/entroniq)", "GitHub (https://github.com/about) (https://github.com/github)", "<PERSON> (https://cryptoquick.com) (https://github.com/cryptoquick)", "<PERSON><PERSON><PERSON> (https://github.com/jlgeering) (https://opencollective.com/jlgeering) (https://twitter.com/jlgeering)", "<PERSON> <<EMAIL>> (https://mdm.cc) (https://github.com/mikeumus) (https://opencollective.com/mikeumus) (https://twitter.com/mikeumus)", "<PERSON> <<EMAIL>> (https://michaelscepaniak.com) (https://github.com/hispanic)", "<PERSON> <<EMAIL>> (https://github.com/smashah) (https://thanks.dev/d/gh/smashah) (https://twitter.com/smashah)", "Mr. <PERSON> <<EMAIL>> (https://mrhenry.be) (https://github.com/mrhenry)", "Nermal <<EMAIL>> (https://arjunaditya.vercel.app) (https://github.com/nermalcat69)", "Pleo (https://pleo.io) (https://github.com/pleo-io)", "Poonacha Medappa (https://poonachamedappa.com) (https://github.com/km-Poonacha)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON><PERSON>)", "<PERSON> <<EMAIL>> (https://github.com/rdeforest)", "Sentry (https://sentry.io) (https://github.com/getsentry)", "ServieJS (https://github.com/serviejs) (https://thanks.dev/d/gh/serviejs)", "Skunk Team (https://skunk.team) (https://github.com/skunkteam)", "Syntax <<EMAIL>> (https://syntax.fm) (https://github.com/syntaxfm)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (https://github.com/WriterJ<PERSON>nBuck)"], "engines": {"node": ">=4"}, "editions": [{"description": "TypeScript source code with Import for modules", "directory": "source", "entry": "index.ts", "tags": ["source", "typescript", "import"], "engines": false}, {"description": "TypeScript compiled against ES2022 for web browsers with Import for modules", "directory": "edition-browsers", "entry": "index.js", "tags": ["compiled", "javascript", "import"], "engines": {"node": false, "browsers": "defaults"}}, {"description": "TypeScript compiled against ES2022 for Node.js 14 || 16 || 18 || 20 || 21 with Require for modules", "directory": "edition-es2022", "entry": "index.js", "tags": ["compiled", "javascript", "es2022", "require"], "engines": {"node": "14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled against ES2017 for Node.js 6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21 with Require for modules", "directory": "edition-es2017", "entry": "index.js", "tags": ["compiled", "javascript", "es2017", "require"], "engines": {"node": "6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled against ES5 for Node.js 4 || 6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21 with Require for modules", "directory": "edition-es5", "entry": "index.js", "tags": ["compiled", "javascript", "es5", "require"], "engines": {"node": "4 || 6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled against ES2017 for Node.js 12 || 14 || 16 || 18 || 20 || 21 with Import for modules", "directory": "edition-es2017-esm", "entry": "index.js", "tags": ["compiled", "javascript", "es2017", "import"], "engines": {"node": "12 || 14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled Types with Import for modules", "directory": "edition-types", "entry": "index.d.ts", "tags": ["compiled", "types", "import"], "engines": false}, {"description": "TypeScript source code made to be compatible with Deno", "directory": "edition-deno", "entry": "index.ts", "tags": ["typescript", "import", "deno"], "engines": {"deno": true, "browsers": true}}], "types": "edition-types/index.d.ts", "type": "module", "main": "index.cjs", "exports": {"node": {"types": "./edition-types/index.d.ts", "import": "./edition-es2017-esm/index.js", "default": "./index.cjs", "require": "./edition-es2022/index.js"}, "browser": {"types": "./edition-types/index.d.ts", "import": "./edition-browsers/index.js"}}, "deno": "edition-deno/index.ts", "browser": "edition-browsers/index.js", "module": "edition-browsers/index.js", "dependencies": {"binaryextensions": "^6.11.0", "editions": "^6.21.0", "textextensions": "^6.11.0"}, "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "assert-helpers": "^11.12.0", "eslint": "^8.56.0", "eslint-config-bevry": "^5.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "filedirname": "^3.3.0", "kava": "^7.8.0", "make-deno-edition": "^2.2.0", "prettier": "^3.1.1", "projectz": "^4.1.1", "typedoc": "^0.25.4", "typescript": "5.3.3", "valid-directory": "^4.8.0", "valid-module": "^2.6.0"}, "scripts": {"our:clean": "rm -rf ./docs ./edition* ./es2015 ./es5 ./out ./.next", "our:compile": "npm run our:compile:deno && npm run our:compile:edition-browsers && npm run our:compile:edition-es2017 && npm run our:compile:edition-es2017-esm && npm run our:compile:edition-es2022 && npm run our:compile:edition-es5 && npm run our:compile:edition-types", "our:compile:deno": "make-deno-edition --attempt", "our:compile:edition-browsers": "tsc --module ESNext --target ES2022 --outDir ./edition-browsers --project tsconfig.json && ( test ! -d edition-browsers/source || ( mv edition-browsers/source edition-temp && rm -rf edition-browsers && mv edition-temp edition-browsers ) )", "our:compile:edition-es2017": "tsc --module commonjs --target ES2017 --outDir ./edition-es2017 --project tsconfig.json && ( test ! -d edition-es2017/source || ( mv edition-es2017/source edition-temp && rm -rf edition-es2017 && mv edition-temp edition-es2017 ) ) && printf '%s' '{\"type\": \"commonjs\"}' > edition-es2017/package.json", "our:compile:edition-es2017-esm": "tsc --module ESNext --target ES2017 --outDir ./edition-es2017-esm --project tsconfig.json && ( test ! -d edition-es2017-esm/source || ( mv edition-es2017-esm/source edition-temp && rm -rf edition-es2017-esm && mv edition-temp edition-es2017-esm ) ) && printf '%s' '{\"type\": \"module\"}' > edition-es2017-esm/package.json", "our:compile:edition-es2022": "tsc --module commonjs --target ES2022 --outDir ./edition-es2022 --project tsconfig.json && ( test ! -d edition-es2022/source || ( mv edition-es2022/source edition-temp && rm -rf edition-es2022 && mv edition-temp edition-es2022 ) ) && printf '%s' '{\"type\": \"commonjs\"}' > edition-es2022/package.json", "our:compile:edition-es5": "tsc --module commonjs --target ES5 --outDir ./edition-es5 --project tsconfig.json && ( test ! -d edition-es5/source || ( mv edition-es5/source edition-temp && rm -rf edition-es5 && mv edition-temp edition-es5 ) ) && printf '%s' '{\"type\": \"commonjs\"}' > edition-es5/package.json", "our:compile:edition-types": "tsc --emitDeclarationOnly --declaration --declarationMap --declarationDir ./edition-types --project tsconfig.json && ( test ! -d edition-types/source || ( mv edition-types/source edition-temp && rm -rf edition-types && mv edition-temp edition-types ) )", "our:deploy": "printf '%s\n' 'no need for this project'", "our:meta": "npm run our:meta:docs && npm run our:meta:projectz", "our:meta:docs": "npm run our:meta:docs:typedoc", "our:meta:docs:typedoc": "rm -rf ./docs && typedoc --exclude '**/+(*test*|node_modules)' --excludeExternals --out ./docs ./source", "our:meta:projectz": "projectz --offline", "our:release": "npm run our:release:prepare && npm run our:release:check-changelog && npm run our:release:check-dirty && npm run our:release:tag && npm run our:release:push", "our:release:check-changelog": "cat ./HISTORY.md | grep \"v$npm_package_version\" || (printf '%s\n' \"add a changelog entry for v$npm_package_version\" && exit -1)", "our:release:check-dirty": "git diff --exit-code", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:push": "git push origin && git push origin --tags", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (printf '%s\n' 'proper changelog entry not found' && exit -1) && git tag \"v$npm_package_version\" -am \"$MESSAGE\"", "our:setup": "npm run our:setup:install", "our:setup:install": "npm install", "our:test": "npm run our:verify && npm test", "our:verify": "npm run our:verify:eslint && npm run our:verify:module && npm run our:verify:prettier", "our:verify:eslint": "eslint --fix --ignore-pattern '**/*.d.ts' --ignore-pattern '**/vendor/' --ignore-pattern '**/node_modules/' --ext .mjs,.js,.jsx,.ts,.tsx ./source", "our:verify:module": "valid-module", "our:verify:prettier": "prettier --write .", "test": "node ./test.cjs"}, "eslintConfig": {"extends": ["bevry"]}, "prettier": {"semi": false, "singleQuote": true, "trailingComma": "es5", "endOfLine": "lf"}}