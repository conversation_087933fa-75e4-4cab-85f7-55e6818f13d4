{"version": 3, "file": "descriptor-types.validator.js", "sourceRoot": "", "sources": ["../src/descriptor-types.validator.ts"], "names": [], "mappings": "AAAA,cAAc;AACd,iBAAiB;AACjB,gDAAgD;AAChD,OAAO,GAAG,MAAM,KAAK,CAAC;AAGtB,MAAM,CAAC,MAAM,MAAM,GAAG;IAClB,SAAS,EAAE,yCAAyC;IACpD,aAAa,EAAE;QACX,6BAA6B,EAAE;YAC3B,MAAM,EAAE,0CAA0C;SACrD;QACD,4BAA4B,EAAE;YAC1B,MAAM,EAAE,QAAQ;YAChB,YAAY,EAAE;gBACV,eAAe,EAAE;oBACb,MAAM,EAAE,2CAA2C;iBACtD;gBACD,OAAO,EAAE;oBACL,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE;wBACL,OAAO,EAAE;4BACL;gCACI,MAAM,EAAE,8CAA8C;6BACzD;4BACD;gCACI,MAAM,EAAE,oDAAoD;6BAC/D;yBACJ;qBACJ;iBACJ;aACJ;YACD,UAAU,EAAE;gBACR,OAAO;aACV;YACD,aAAa,EAAE,gCAAgC;SAClD;QACD,6BAA6B,EAAE;YAC3B,MAAM,EAAE,QAAQ;YAChB,aAAa,EAAE,iGAAiG;SACnH;QACD,gCAAgC,EAAE;YAC9B,MAAM,EAAE,QAAQ;YAChB,YAAY,EAAE;gBACV,IAAI,EAAE;oBACF,MAAM,EAAE,QAAQ;oBAChB,aAAa,EAAE,wMAAwM;iBAC1N;gBACD,SAAS,EAAE;oBACP,MAAM,EAAE,4CAA4C;oBACpD,aAAa,EAAE,+EAA+E;iBACjG;gBACD,UAAU,EAAE;oBACR,MAAM,EAAE,SAAS;oBACjB,aAAa,EAAE,2CAA2C;iBAC7D;gBACD,iBAAiB,EAAE;oBACf,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE;wBACL,MAAM,EAAE,QAAQ;qBACnB;oBACD,aAAa,EAAE,uFAAuF;iBACzG;gBACD,UAAU,EAAE;oBACR,MAAM,EAAE,2CAA2C;oBACnD,aAAa,EAAE,iDAAiD;iBACnE;aACJ;YACD,UAAU,EAAE;gBACR,IAAI;aACP;SACJ;QACD,8BAA8B,EAAE;YAC5B,MAAM,EAAE,QAAQ;SACnB;QACD,6BAA6B,EAAE;YAC3B,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;aACV;YACD,aAAa,EAAE,qBAAqB;SACvC;QACD,sCAAsC,EAAE;YACpC,MAAM,EAAE,QAAQ;YAChB,YAAY,EAAE;gBACV,IAAI,EAAE;oBACF,MAAM,EAAE,QAAQ;oBAChB,aAAa,EAAE,2OAA2O;iBAC7P;gBACD,SAAS,EAAE;oBACP,MAAM,EAAE,kDAAkD;oBAC1D,aAAa,EAAE,qEAAqE;iBACvF;gBACD,UAAU,EAAE;oBACR,MAAM,EAAE,SAAS;oBACjB,aAAa,EAAE,iCAAiC;iBACnD;gBACD,OAAO,EAAE;oBACL,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE;wBACL,MAAM,EAAE,8CAA8C;qBACzD;oBACD,aAAa,EAAE,4JAA4J;iBAC9K;aACJ;YACD,UAAU,EAAE;gBACR,IAAI;aACP;SACJ;QACD,oCAAoC,EAAE;YAClC,MAAM,EAAE,QAAQ;SACnB;QACD,iCAAiC,EAAE;YAC/B,MAAM,EAAE,8CAA8C;SACzD;QACD,uCAAuC,EAAE;YACrC,MAAM,EAAE,oDAAoD;SAC/D;KACJ;CACJ,CAAC;AACF,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5E,MAAM,UAAU,mCAAmC,CAAC,OAAgB;IAClE,oFAAoF;IACpF,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,iDAAiD,CAAC,CAAC;IACnF,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,6BAA6B,EAAC,CAAC,CAAC,CAAC;QAC9I,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC/B,MAAM,KAAK,CAAC;KACb;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,6BAA6B,CAAC,OAAgB;IAC5D,IAAI;QACF,mCAAmC,CAAC,OAAO,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,MAAM,UAAU,uCAAuC,CAAC,OAAgB;IACtE,wFAAwF;IACxF,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,qDAAqD,CAAC,CAAC;IACvF,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2CAA2C,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,iCAAiC,EAAC,CAAC,CAAC,CAAC;QACtJ,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC/B,MAAM,KAAK,CAAC;KACb;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,iCAAiC,CAAC,OAAgB;IAChE,IAAI;QACF,uCAAuC,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,MAAM,UAAU,6CAA6C,CAAC,OAAgB;IAC5E,8FAA8F;IAC9F,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,2DAA2D,CAAC,CAAC;IAC7F,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,iDAAiD,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC,CAAC,CAAC,CAAC;QAClK,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC/B,MAAM,KAAK,CAAC;KACb;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,uCAAuC,CAAC,OAAgB;IACtE,IAAI;QACF,6CAA6C,CAAC,OAAO,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;AACH,CAAC"}