{"version": 3, "file": "aborterUtils.js", "sourceRoot": "", "sources": ["../../src/aborterUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAyBlC;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,wBAA8D,EAC9D,OAA2C;;IAE3C,MAAM,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;IACtC,SAAS,YAAY;QACnB,OAAO,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;IACD,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,0CAAE,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAC9D,IAAI,CAAC;QACH,OAAO,MAAM,OAAO,CAAC,IAAI,CACvB,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CACxE,CAAC;IACJ,CAAC;YAAS,CAAC;QACT,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Options related to abort controller.\n */\nexport interface AbortOptions {\n  /**\n   * The abortSignal associated with containing operation.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * The abort error message associated with containing operation.\n   */\n  abortErrorMsg?: string;\n}\n\n/**\n * Represents a function that returns a promise that can be aborted.\n */\nexport type AbortablePromiseBuilder<T> = (abortOptions: {\n  abortSignal?: AbortSignalLike;\n}) => Promise<T>;\n\n/**\n * promise.race() wrapper that aborts rest of promises as soon as the first promise settles.\n */\nexport async function cancelablePromiseRace<T extends unknown[]>(\n  abortablePromiseBuilders: AbortablePromiseBuilder<T[number]>[],\n  options?: { abortSignal?: AbortSignalLike },\n): Promise<T[number]> {\n  const aborter = new AbortController();\n  function abortHandler(): void {\n    aborter.abort();\n  }\n  options?.abortSignal?.addEventListener(\"abort\", abortHandler);\n  try {\n    return await Promise.race(\n      abortablePromiseBuilders.map((p) => p({ abortSignal: aborter.signal })),\n    );\n  } finally {\n    aborter.abort();\n    options?.abortSignal?.removeEventListener(\"abort\", abortHandler);\n  }\n}\n"]}