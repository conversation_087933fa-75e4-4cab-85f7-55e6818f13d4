{"version": 3, "file": "tokenCycler.js", "sourceRoot": "", "sources": ["../../../src/util/tokenCycler.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAyGlC,8CA6HC;AAnOD,gDAAyC;AAkCzC,sDAAsD;AACzC,QAAA,sBAAsB,GAAuB;IACxD,uBAAuB,EAAE,IAAI,EAAE,0DAA0D;IACzF,iBAAiB,EAAE,IAAI,EAAE,kCAAkC;IAC3D,iBAAiB,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,oCAAoC;CACvE,CAAC;AAEF;;;;;;;;;GASG;AACH,KAAK,UAAU,YAAY,CACzB,cAAiD,EACjD,iBAAyB,EACzB,cAAsB;IAEtB,4EAA4E;IAC5E,eAAe;IACf,KAAK,UAAU,iBAAiB;QAC9B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,OAAO,MAAM,cAAc,EAAE,CAAC;YAChC,CAAC;YAAC,WAAM,CAAC;gBACP,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,UAAU,GAAG,MAAM,cAAc,EAAE,CAAC;YAE1C,6CAA6C;YAC7C,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAED,IAAI,KAAK,GAAuB,MAAM,iBAAiB,EAAE,CAAC;IAE1D,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;QACtB,MAAM,IAAA,iBAAK,EAAC,iBAAiB,CAAC,CAAC;QAE/B,KAAK,GAAG,MAAM,iBAAiB,EAAE,CAAC;IACpC,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,iBAAiB,CAC/B,UAA2B,EAC3B,kBAAgD;IAEhD,IAAI,aAAa,GAAgC,IAAI,CAAC;IACtD,IAAI,KAAK,GAAuB,IAAI,CAAC;IACrC,IAAI,QAA4B,CAAC;IAEjC,MAAM,OAAO,mCACR,8BAAsB,GACtB,kBAAkB,CACtB,CAAC;IAEF;;;OAGG;IACH,MAAM,MAAM,GAAG;QACb;;WAEG;QACH,IAAI,YAAY;YACd,OAAO,aAAa,KAAK,IAAI,CAAC;QAChC,CAAC;QACD;;;WAGG;QACH,IAAI,aAAa;;YACf,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,qBAAqB,KAAI,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;gBAC7E,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,CAAC,CAAC,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACnF,CAAC;QACD;;;WAGG;QACH,IAAI,WAAW;YACb,OAAO,CACL,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,GAAG,EAAE,CAC1F,CAAC;QACJ,CAAC;KACF,CAAC;IAEF;;;OAGG;IACH,SAAS,OAAO,CACd,MAAyB,EACzB,eAAgC;;QAEhC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACzB,yDAAyD;YACzD,MAAM,iBAAiB,GAAG,GAAgC,EAAE,CAC1D,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAE/C,wEAAwE;YACxE,6CAA6C;YAC7C,aAAa,GAAG,YAAY,CAC1B,iBAAiB,EACjB,OAAO,CAAC,iBAAiB;YACzB,+DAA+D;YAC/D,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,mCAAI,IAAI,CAAC,GAAG,EAAE,CACxC;iBACE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,MAAM,CAAC;gBACf,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBACpC,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,sEAAsE;gBACtE,qEAAqE;gBACrE,mBAAmB;gBACnB,aAAa,GAAG,IAAI,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;gBACb,QAAQ,GAAG,SAAS,CAAC;gBACrB,MAAM,MAAM,CAAC;YACf,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO,aAAqC,CAAC;IAC/C,CAAC;IAED,OAAO,KAAK,EAAE,MAAyB,EAAE,YAA6B,EAAwB,EAAE;QAC9F,EAAE;QACF,gBAAgB;QAChB,+DAA+D;QAC/D,6CAA6C;QAC7C,+DAA+D;QAC/D,yCAAyC;QACzC,6DAA6D;QAC7D,YAAY;QACZ,EAAE;QAEF,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,eAAe,GAAG,QAAQ,KAAK,YAAY,CAAC,QAAQ,CAAC;QAE3D,IAAI,iBAAiB,EAAE,CAAC;YACtB,oEAAoE;YACpE,iGAAiG;YACjG,KAAK,GAAG,IAAI,CAAC;QACf,CAAC;QAED,0EAA0E;QAC1E,kHAAkH;QAClH,oDAAoD;QACpD,MAAM,WAAW,GAAG,eAAe,IAAI,iBAAiB,IAAI,MAAM,CAAC,WAAW,CAAC;QAE/E,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,KAAoB,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { delay } from \"@azure/core-util\";\n\n/**\n * A function that gets a promise of an access token and allows providing\n * options.\n *\n * @param options - the options to pass to the underlying token provider\n */\nexport type AccessTokenGetter = (\n  scopes: string | string[],\n  options: GetTokenOptions,\n) => Promise<AccessToken>;\n\nexport interface TokenCyclerOptions {\n  /**\n   * The window of time before token expiration during which the token will be\n   * considered unusable due to risk of the token expiring before sending the\n   * request.\n   *\n   * This will only become meaningful if the refresh fails for over\n   * (refreshWindow - forcedRefreshWindow) milliseconds.\n   */\n  forcedRefreshWindowInMs: number;\n  /**\n   * Interval in milliseconds to retry failed token refreshes.\n   */\n  retryIntervalInMs: number;\n  /**\n   * The window of time before token expiration during which\n   * we will attempt to refresh the token.\n   */\n  refreshWindowInMs: number;\n}\n\n// Default options for the cycler if none are provided\nexport const DEFAULT_CYCLER_OPTIONS: TokenCyclerOptions = {\n  forcedRefreshWindowInMs: 1000, // Force waiting for a refresh 1s before the token expires\n  retryIntervalInMs: 3000, // Allow refresh attempts every 3s\n  refreshWindowInMs: 1000 * 60 * 2, // Start refreshing 2m before expiry\n};\n\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - A function that produces a promise of an access token that may fail by returning null.\n * @param retryIntervalInMs - The time (in milliseconds) to wait between retry attempts.\n * @param refreshTimeout - The timestamp after which the refresh attempt will fail, throwing an exception.\n * @returns - A promise that, if it resolves, will resolve with an access token.\n */\nasync function beginRefresh(\n  getAccessToken: () => Promise<AccessToken | null>,\n  retryIntervalInMs: number,\n  refreshTimeout: number,\n): Promise<AccessToken> {\n  // This wrapper handles exceptions gracefully as long as we haven't exceeded\n  // the timeout.\n  async function tryGetAccessToken(): Promise<AccessToken | null> {\n    if (Date.now() < refreshTimeout) {\n      try {\n        return await getAccessToken();\n      } catch {\n        return null;\n      }\n    } else {\n      const finalToken = await getAccessToken();\n\n      // Timeout is up, so throw if it's still null\n      if (finalToken === null) {\n        throw new Error(\"Failed to refresh access token.\");\n      }\n\n      return finalToken;\n    }\n  }\n\n  let token: AccessToken | null = await tryGetAccessToken();\n\n  while (token === null) {\n    await delay(retryIntervalInMs);\n\n    token = await tryGetAccessToken();\n  }\n\n  return token;\n}\n\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nexport function createTokenCycler(\n  credential: TokenCredential,\n  tokenCyclerOptions?: Partial<TokenCyclerOptions>,\n): AccessTokenGetter {\n  let refreshWorker: Promise<AccessToken> | null = null;\n  let token: AccessToken | null = null;\n  let tenantId: string | undefined;\n\n  const options = {\n    ...DEFAULT_CYCLER_OPTIONS,\n    ...tokenCyclerOptions,\n  };\n\n  /**\n   * This little holder defines several predicates that we use to construct\n   * the rules of refreshing the token.\n   */\n  const cycler = {\n    /**\n     * Produces true if a refresh job is currently in progress.\n     */\n    get isRefreshing(): boolean {\n      return refreshWorker !== null;\n    },\n    /**\n     * Produces true if the cycler SHOULD refresh (we are within the refresh\n     * window and not already refreshing)\n     */\n    get shouldRefresh(): boolean {\n      if (cycler.isRefreshing) {\n        return false;\n      }\n      if (token?.refreshAfterTimestamp && token.refreshAfterTimestamp < Date.now()) {\n        return true;\n      }\n\n      return (token?.expiresOnTimestamp ?? 0) - options.refreshWindowInMs < Date.now();\n    },\n    /**\n     * Produces true if the cycler MUST refresh (null or nearly-expired\n     * token).\n     */\n    get mustRefresh(): boolean {\n      return (\n        token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now()\n      );\n    },\n  };\n\n  /**\n   * Starts a refresh job or returns the existing job if one is already\n   * running.\n   */\n  function refresh(\n    scopes: string | string[],\n    getTokenOptions: GetTokenOptions,\n  ): Promise<AccessToken> {\n    if (!cycler.isRefreshing) {\n      // We bind `scopes` here to avoid passing it around a lot\n      const tryGetAccessToken = (): Promise<AccessToken | null> =>\n        credential.getToken(scopes, getTokenOptions);\n\n      // Take advantage of promise chaining to insert an assignment to `token`\n      // before the refresh can be considered done.\n      refreshWorker = beginRefresh(\n        tryGetAccessToken,\n        options.retryIntervalInMs,\n        // If we don't have a token, then we should timeout immediately\n        token?.expiresOnTimestamp ?? Date.now(),\n      )\n        .then((_token) => {\n          refreshWorker = null;\n          token = _token;\n          tenantId = getTokenOptions.tenantId;\n          return token;\n        })\n        .catch((reason) => {\n          // We also should reset the refresher if we enter a failed state.  All\n          // existing awaiters will throw, but subsequent requests will start a\n          // new retry chain.\n          refreshWorker = null;\n          token = null;\n          tenantId = undefined;\n          throw reason;\n        });\n    }\n\n    return refreshWorker as Promise<AccessToken>;\n  }\n\n  return async (scopes: string | string[], tokenOptions: GetTokenOptions): Promise<AccessToken> => {\n    //\n    // Simple rules:\n    // - If we MUST refresh, then return the refresh task, blocking\n    //   the pipeline until a token is available.\n    // - If we SHOULD refresh, then run refresh but don't return it\n    //   (we can still use the cached token).\n    // - Return the token, since it's fine if we didn't return in\n    //   step 1.\n    //\n\n    const hasClaimChallenge = Boolean(tokenOptions.claims);\n    const tenantIdChanged = tenantId !== tokenOptions.tenantId;\n\n    if (hasClaimChallenge) {\n      // If we've received a claim, we know the existing token isn't valid\n      // We want to clear it so that that refresh worker won't use the old expiration time as a timeout\n      token = null;\n    }\n\n    // If the tenantId passed in token options is different to the one we have\n    // Or if we are in claim challenge and the token was rejected and a new access token need to be issued, we need to\n    // refresh the token with the new tenantId or token.\n    const mustRefresh = tenantIdChanged || hasClaimChallenge || cycler.mustRefresh;\n\n    if (mustRefresh) {\n      return refresh(scopes, tokenOptions);\n    }\n\n    if (cycler.shouldRefresh) {\n      refresh(scopes, tokenOptions);\n    }\n\n    return token as AccessToken;\n  };\n}\n"]}