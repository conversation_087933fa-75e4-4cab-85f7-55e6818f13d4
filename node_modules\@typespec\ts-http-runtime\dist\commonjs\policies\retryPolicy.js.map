{"version": 3, "file": "retryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/retryPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAoClC,kCAqHC;AArJD,mDAA2C;AAG3C,qEAA+D;AAE/D,mDAAyD;AACzD,kDAA6D;AAE7D,MAAM,iBAAiB,GAAG,IAAA,8BAAkB,EAAC,6BAA6B,CAAC,CAAC;AAE5E;;GAEG;AACH,MAAM,eAAe,GAAG,aAAa,CAAC;AAgBtC;;GAEG;AACH,SAAgB,WAAW,CACzB,UAA2B,EAC3B,UAA8B,EAAE,UAAU,EAAE,yCAA0B,EAAE;IAExE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,iBAAiB,CAAC;IACnD,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,IAAI,QAAsC,CAAC;YAC3C,IAAI,aAAoC,CAAC;YACzC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;YAEpB,YAAY,EAAE,OAAO,IAAI,EAAE,CAAC;gBAC1B,UAAU,IAAI,CAAC,CAAC;gBAChB,QAAQ,GAAG,SAAS,CAAC;gBACrB,aAAa,GAAG,SAAS,CAAC;gBAE1B,IAAI,CAAC;oBACH,MAAM,CAAC,IAAI,CAAC,SAAS,UAAU,8BAA8B,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAClF,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,MAAM,CAAC,IAAI,CAAC,SAAS,UAAU,oCAAoC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC1F,CAAC;gBAAC,OAAO,CAAM,EAAE,CAAC;oBAChB,MAAM,CAAC,KAAK,CAAC,SAAS,UAAU,kCAAkC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAEvF,yDAAyD;oBACzD,gGAAgG;oBAChG,sEAAsE;oBACtE,aAAa,GAAG,CAAc,CAAC;oBAC/B,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,CAAC;oBACV,CAAC;oBAED,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;gBACpC,CAAC;gBAED,IAAI,MAAA,OAAO,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;oBACjC,MAAM,CAAC,KAAK,CAAC,SAAS,UAAU,oBAAoB,CAAC,CAAC;oBACtD,MAAM,UAAU,GAAG,IAAI,0BAAU,EAAE,CAAC;oBACpC,MAAM,UAAU,CAAC;gBACnB,CAAC;gBAED,IAAI,UAAU,IAAI,CAAC,MAAA,OAAO,CAAC,UAAU,mCAAI,yCAA0B,CAAC,EAAE,CAAC;oBACrE,MAAM,CAAC,IAAI,CACT,SAAS,UAAU,uGAAuG,CAC3H,CAAC;oBACF,IAAI,aAAa,EAAE,CAAC;wBAClB,MAAM,aAAa,CAAC;oBACtB,CAAC;yBAAM,IAAI,QAAQ,EAAE,CAAC;wBACpB,OAAO,QAAQ,CAAC;oBAClB,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;oBAChF,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,SAAS,UAAU,gBAAgB,UAAU,CAAC,MAAM,oBAAoB,CAAC,CAAC;gBAEtF,cAAc,EAAE,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;oBAClD,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC;oBACjD,cAAc,CAAC,IAAI,CAAC,SAAS,UAAU,+BAA+B,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;oBAExF,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;wBAC/B,UAAU;wBACV,QAAQ;wBACR,aAAa;qBACd,CAAC,CAAC;oBAEH,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;wBAC3B,cAAc,CAAC,IAAI,CAAC,SAAS,UAAU,YAAY,CAAC,CAAC;wBACrD,SAAS,cAAc,CAAC;oBAC1B,CAAC;oBAED,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;oBAE/D,IAAI,YAAY,EAAE,CAAC;wBACjB,cAAc,CAAC,KAAK,CAClB,SAAS,UAAU,oBAAoB,QAAQ,CAAC,IAAI,gBAAgB,EACpE,YAAY,CACb,CAAC;wBACF,MAAM,YAAY,CAAC;oBACrB,CAAC;oBAED,IAAI,cAAc,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;wBAC3C,cAAc,CAAC,IAAI,CACjB,SAAS,UAAU,oBAAoB,QAAQ,CAAC,IAAI,kBAAkB,cAAc,EAAE,CACvF,CAAC;wBACF,MAAM,IAAA,kBAAK,EAAC,cAAc,EAAE,SAAS,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;wBAC7E,SAAS,YAAY,CAAC;oBACxB,CAAC;oBAED,IAAI,UAAU,EAAE,CAAC;wBACf,cAAc,CAAC,IAAI,CACjB,SAAS,UAAU,oBAAoB,QAAQ,CAAC,IAAI,iBAAiB,UAAU,EAAE,CAClF,CAAC;wBACF,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC;wBACzB,SAAS,YAAY,CAAC;oBACxB,CAAC;gBACH,CAAC;gBAED,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,CAAC,IAAI,CACT,+EAA+E,CAChF,CAAC;oBACF,MAAM,aAAa,CAAC;gBACtB,CAAC;gBACD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CACT,mFAAmF,CACpF,CAAC;oBACF,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBAED,mDAAmD;gBACnD,+DAA+D;gBAC/D,iCAAiC;YACnC,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { delay } from \"../util/helpers.js\";\nimport type { RetryStrategy } from \"../retryStrategies/retryStrategy.js\";\nimport type { RestError } from \"../restError.js\";\nimport { AbortError } from \"../abort-controller/AbortError.js\";\nimport type { TypeSpecRuntimeLogger } from \"../logger/logger.js\";\nimport { createClientLogger } from \"../logger/logger.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\nconst retryPolicyLogger = createClientLogger(\"ts-http-runtime retryPolicy\");\n\n/**\n * The programmatic identifier of the retryPolicy.\n */\nconst retryPolicyName = \"retryPolicy\";\n\n/**\n * Options to the {@link retryPolicy}\n */\nexport interface RetryPolicyOptions {\n  /**\n   * Maximum number of retries. If not specified, it will limit to 3 retries.\n   */\n  maxRetries?: number;\n  /**\n   * Logger. If it's not provided, a default logger is used.\n   */\n  logger?: TypeSpecRuntimeLogger;\n}\n\n/**\n * retryPolicy is a generic policy to enable retrying requests when certain conditions are met\n */\nexport function retryPolicy(\n  strategies: RetryStrategy[],\n  options: RetryPolicyOptions = { maxRetries: DEFAULT_RETRY_POLICY_COUNT },\n): PipelinePolicy {\n  const logger = options.logger || retryPolicyLogger;\n  return {\n    name: retryPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      let response: PipelineResponse | undefined;\n      let responseError: RestError | undefined;\n      let retryCount = -1;\n\n      retryRequest: while (true) {\n        retryCount += 1;\n        response = undefined;\n        responseError = undefined;\n\n        try {\n          logger.info(`Retry ${retryCount}: Attempting to send request`, request.requestId);\n          response = await next(request);\n          logger.info(`Retry ${retryCount}: Received a response from request`, request.requestId);\n        } catch (e: any) {\n          logger.error(`Retry ${retryCount}: Received an error from request`, request.requestId);\n\n          // RestErrors are valid targets for the retry strategies.\n          // If none of the retry strategies can work with them, they will be thrown later in this policy.\n          // If the received error is not a RestError, it is immediately thrown.\n          responseError = e as RestError;\n          if (!e || responseError.name !== \"RestError\") {\n            throw e;\n          }\n\n          response = responseError.response;\n        }\n\n        if (request.abortSignal?.aborted) {\n          logger.error(`Retry ${retryCount}: Request aborted.`);\n          const abortError = new AbortError();\n          throw abortError;\n        }\n\n        if (retryCount >= (options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT)) {\n          logger.info(\n            `Retry ${retryCount}: Maximum retries reached. Returning the last received response, or throwing the last received error.`,\n          );\n          if (responseError) {\n            throw responseError;\n          } else if (response) {\n            return response;\n          } else {\n            throw new Error(\"Maximum retries reached with no response or error to throw\");\n          }\n        }\n\n        logger.info(`Retry ${retryCount}: Processing ${strategies.length} retry strategies.`);\n\n        strategiesLoop: for (const strategy of strategies) {\n          const strategyLogger = strategy.logger || logger;\n          strategyLogger.info(`Retry ${retryCount}: Processing retry strategy ${strategy.name}.`);\n\n          const modifiers = strategy.retry({\n            retryCount,\n            response,\n            responseError,\n          });\n\n          if (modifiers.skipStrategy) {\n            strategyLogger.info(`Retry ${retryCount}: Skipped.`);\n            continue strategiesLoop;\n          }\n\n          const { errorToThrow, retryAfterInMs, redirectTo } = modifiers;\n\n          if (errorToThrow) {\n            strategyLogger.error(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} throws error:`,\n              errorToThrow,\n            );\n            throw errorToThrow;\n          }\n\n          if (retryAfterInMs || retryAfterInMs === 0) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} retries after ${retryAfterInMs}`,\n            );\n            await delay(retryAfterInMs, undefined, { abortSignal: request.abortSignal });\n            continue retryRequest;\n          }\n\n          if (redirectTo) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} redirects to ${redirectTo}`,\n            );\n            request.url = redirectTo;\n            continue retryRequest;\n          }\n        }\n\n        if (responseError) {\n          logger.info(\n            `None of the retry strategies could work with the received error. Throwing it.`,\n          );\n          throw responseError;\n        }\n        if (response) {\n          logger.info(\n            `None of the retry strategies could work with the received response. Returning it.`,\n          );\n          return response;\n        }\n\n        // If all the retries skip and there's no response,\n        // we're still in the retry loop, so a new request will be sent\n        // until `maxRetries` is reached.\n      }\n    },\n  };\n}\n"]}