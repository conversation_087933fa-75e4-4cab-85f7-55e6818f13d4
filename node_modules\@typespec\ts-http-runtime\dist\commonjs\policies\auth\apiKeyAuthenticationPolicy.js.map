{"version": 3, "file": "apiKeyAuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/auth/apiKeyAuthenticationPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAmClC,gEAuBC;AApDD,6EAAsE;AAEtE;;GAEG;AACU,QAAA,8BAA8B,GAAG,4BAA4B,CAAC;AAqB3E;;GAEG;AACH,SAAgB,0BAA0B,CACxC,OAA0C;IAE1C,OAAO;QACL,IAAI,EAAE,sCAA8B;QACpC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;YAC1F,IAAA,mDAAsB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAA,CAAC,MAAA,OAAO,CAAC,WAAW,mCAAI,OAAO,CAAC,WAAW,CAAC,0CAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAE9F,iFAAiF;YACjF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YACD,IAAI,MAAM,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { ApiKeyCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the API Key Authentication Policy\n */\nexport const apiKeyAuthenticationPolicyName = \"apiKeyAuthenticationPolicy\";\n\n/**\n * Options for configuring the API key authentication policy\n */\nexport interface ApiKeyAuthenticationPolicyOptions {\n  /**\n   * The credential used to authenticate requests\n   */\n  credential: ApiKeyCredential;\n  /**\n   * Optional authentication schemes to use. If `authSchemes` is provided in both request and policy options, the request options will take precedence.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds API key authentication to requests\n */\nexport function apiKeyAuthenticationPolicy(\n  options: ApiKeyAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: apiKeyAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find((x) => x.kind === \"apiKey\");\n\n      // Skip adding authentication header if no API key authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n      if (scheme.apiKeyLocation !== \"header\") {\n        throw new Error(`Unsupported API key location: ${scheme.apiKeyLocation}`);\n      }\n\n      request.headers.set(scheme.name, options.credential.key);\n      return next(request);\n    },\n  };\n}\n"]}