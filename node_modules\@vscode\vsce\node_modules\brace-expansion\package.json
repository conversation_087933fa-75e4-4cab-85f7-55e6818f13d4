{"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}}