{"version": 3, "file": "visualStudioCodeCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAClF,OAAO,EACL,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AACtD,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAG7D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAE7B,MAAM,cAAc,GAAG,QAAQ,CAAC;AAChC,MAAM,oBAAoB,GAAG,sCAAsC,CAAC,CAAC,8CAA8C;AACnH,MAAM,MAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D,IAAI,eAAe,GAAuC,SAAS,CAAC;AAEpE,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,yBAAyB,CAAC,MAA8B;QACtD,eAAe,GAAG,MAAM,CAAC;IAC3B,CAAC;CACF,CAAC;AAEF,oEAAoE;AACpE,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,mFAAmF;CAC1F,CAAC;AAEF,SAAS,sBAAsB,CAAC,QAAgB;IAC9C,8CAA8C;IAC9C,MAAM,sBAAsB,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAC9D,IAAI,sBAAsB,EAAE,CAAC;QAC3B,MAAM,IAAI,0BAA0B,CAAC,sBAAsB,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC;AAID,MAAM,uBAAuB,GAAqC;IAChE,UAAU,EAAE,mBAAmB,CAAC,gBAAgB;IAChD,UAAU,EAAE,mBAAmB,CAAC,UAAU;IAC1C,gBAAgB,EAAE,mBAAmB,CAAC,YAAY;IAClD,iBAAiB,EAAE,mBAAmB,CAAC,eAAe;CACvD,CAAC;AAEF;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,QAAgB;IACpD,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAC/C,kEAAkE;IAClE,MAAM,YAAY,GAAG,MAAM,CAAC;IAC5B,MAAM,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;IAE7B,SAAS,YAAY,CAAC,GAAG,YAAsB;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,YAAY,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC,CAAC;QAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7E,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED,IAAI,CAAC;QACH,IAAI,OAAe,CAAC;QACpB,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,OAAO;gBACV,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,OAAQ,CAAC;gBAC/B,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACrD,KAAK,QAAQ;gBACX,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;YACjE,KAAK,OAAO;gBACV,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC1C;gBACE,OAAO;QACX,CAAC;IACH,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7F,OAAO;IACT,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,OAAO,0BAA0B;IAMrC;;;;;;;;;OASG;IACH,YAAY,OAA2C;QACrD,mFAAmF;QACnF,mCAAmC;QACnC,IAAI,CAAC,SAAS,GAAG,CAAC,qBAAqB,CAAC,aAAa,CAAC,IAAI,YAAY,CAAqB,CAAC;QAE5F,qDAAqD;QACrD,MAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE9D,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,iBACtC,aAAa,IACV,OAAO,EACV,CAAC;QAEH,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,OAAO;QACnB,kEAAkE;QAClE,MAAM,cAAc,GAAG,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QACjC,CAAC;QACD,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAOD;;OAEG;IACK,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,OAAyB;;QAEzB,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAEzB,MAAM,QAAQ,GACZ,yBAAyB,CACvB,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,IAAI,IAAI,CAAC,QAAQ,CAAC;QAErB,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,IAAI,0BAA0B,CAClC;gBACE,iEAAiE;gBACjE,uGAAuG;gBACvG,mFAAmF;gBACnF,mFAAmF;gBACnF,wFAAwF;aACzF,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzE,4DAA4D;QAC5D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YACrF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,WAAW,IAAI,iBAAiB,CAAC;QACnC,CAAC;QAED,+CAA+C;QAC/C,IAAI;QACJ,MAAM;QACN,mBAAmB;QACnB,oBAAoB;QACpB,OAAO;QACP,cAAc;QACd,IAAI;QACJ,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE,CAAC;QAE5C,yFAAyF;QACzF,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAC9B,MAAA,MAAA,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,mCAAI,WAAW,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;QAExF,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAChE,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,YAAY,EACZ,SAAS,CACV,CAAC;YAEF,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,OAAO,aAAa,CAAC,WAAW,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,0NAA0N,CAC3N,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,8MAA8M,CAC/M,CAAC;YACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging.js\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils.js\";\nimport { AzureAuthorityHosts } from \"../constants.js\";\nimport { CredentialUnavailableError } from \"../errors.js\";\nimport { IdentityClient } from \"../client/identityClient.js\";\nimport type { VisualStudioCodeCredentialOptions } from \"./visualStudioCodeCredentialOptions.js\";\nimport type { VSCodeCredentialFinder } from \"./visualStudioCodeCredentialPlugin.js\";\nimport { checkTenantId } from \"../util/tenantIdUtils.js\";\nimport fs from \"node:fs\";\nimport os from \"node:os\";\nimport path from \"node:path\";\n\nconst CommonTenantId = \"common\";\nconst AzureAccountClientId = \"aebc6443-996d-45c2-90f0-388ff96faa56\"; // VSC: 'aebc6443-996d-45c2-90f0-388ff96faa56'\nconst logger = credentialLogger(\"VisualStudioCodeCredential\");\n\nlet findCredentials: VSCodeCredentialFinder | undefined = undefined;\n\nexport const vsCodeCredentialControl = {\n  setVsCodeCredentialFinder(finder: VSCodeCredentialFinder): void {\n    findCredentials = finder;\n  },\n};\n\n// Map of unsupported Tenant IDs and the errors we will be throwing.\nconst unsupportedTenantIds: Record<string, string> = {\n  adfs: \"The VisualStudioCodeCredential does not support authentication with ADFS tenants.\",\n};\n\nfunction checkUnsupportedTenant(tenantId: string): void {\n  // If the Tenant ID isn't supported, we throw.\n  const unsupportedTenantError = unsupportedTenantIds[tenantId];\n  if (unsupportedTenantError) {\n    throw new CredentialUnavailableError(unsupportedTenantError);\n  }\n}\n\ntype VSCodeCloudNames = \"AzureCloud\" | \"AzureChina\" | \"AzureGermanCloud\" | \"AzureUSGovernment\";\n\nconst mapVSCodeAuthorityHosts: Record<VSCodeCloudNames, string> = {\n  AzureCloud: AzureAuthorityHosts.AzurePublicCloud,\n  AzureChina: AzureAuthorityHosts.AzureChina,\n  AzureGermanCloud: AzureAuthorityHosts.AzureGermany,\n  AzureUSGovernment: AzureAuthorityHosts.AzureGovernment,\n};\n\n/**\n * Attempts to load a specific property from the VSCode configurations of the current OS.\n * If it fails at any point, returns undefined.\n */\nexport function getPropertyFromVSCode(property: string): string | undefined {\n  const settingsPath = [\"User\", \"settings.json\"];\n  // Eventually we can add more folders for more versions of VSCode.\n  const vsCodeFolder = \"Code\";\n  const homedir = os.homedir();\n\n  function loadProperty(...pathSegments: string[]): string | undefined {\n    const fullPath = path.join(...pathSegments, vsCodeFolder, ...settingsPath);\n    const settings = JSON.parse(fs.readFileSync(fullPath, { encoding: \"utf8\" }));\n    return settings[property];\n  }\n\n  try {\n    let appData: string;\n    switch (process.platform) {\n      case \"win32\":\n        appData = process.env.APPDATA!;\n        return appData ? loadProperty(appData) : undefined;\n      case \"darwin\":\n        return loadProperty(homedir, \"Library\", \"Application Support\");\n      case \"linux\":\n        return loadProperty(homedir, \".config\");\n      default:\n        return;\n    }\n  } catch (e: any) {\n    logger.info(`Failed to load the Visual Studio Code configuration file. Error: ${e.message}`);\n    return;\n  }\n}\n\n/**\n * Connects to Azure using the credential provided by the VSCode extension 'Azure Account'.\n * Once the user has logged in via the extension, this credential can share the same refresh token\n * that is cached by the extension.\n *\n * It's a [known issue](https://github.com/Azure/azure-sdk-for-js/issues/20500) that this credential doesn't\n * work with [Azure Account extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-account)\n * versions newer than **0.9.11**. A long-term fix to this problem is in progress. In the meantime, consider\n * authenticating with {@link AzureCliCredential}.\n *\n * @deprecated This credential is deprecated because the VS Code Azure Account extension on which this credential\n * relies has been deprecated. Users should use other dev-time credentials, such as {@link AzureCliCredential},\n * {@link AzureDeveloperCliCredential}, or {@link AzurePowerShellCredential} for their\n * local development needs. See Azure Account extension deprecation notice [here](https://github.com/microsoft/vscode-azure-account/issues/964).\n *\n */\nexport class VisualStudioCodeCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private cloudName: VSCodeCloudNames;\n\n  /**\n   * Creates an instance of VisualStudioCodeCredential to use for automatically authenticating via VSCode.\n   *\n   * **Note**: `VisualStudioCodeCredential` is provided by a plugin package:\n   * `@azure/identity-vscode`. If this package is not installed and registered\n   * using the plugin API (`useIdentityPlugin`), then authentication using\n   * `VisualStudioCodeCredential` will not be available.\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(options?: VisualStudioCodeCredentialOptions) {\n    // We want to make sure we use the one assigned by the user on the VSCode settings.\n    // Or just `AzureCloud` by default.\n    this.cloudName = (getPropertyFromVSCode(\"azure.cloud\") || \"AzureCloud\") as VSCodeCloudNames;\n\n    // Picking an authority host based on the cloud name.\n    const authorityHost = mapVSCodeAuthorityHosts[this.cloudName];\n\n    this.identityClient = new IdentityClient({\n      authorityHost,\n      ...options,\n    });\n\n    if (options && options.tenantId) {\n      checkTenantId(logger, options.tenantId);\n      this.tenantId = options.tenantId;\n    } else {\n      this.tenantId = CommonTenantId;\n    }\n\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n\n    checkUnsupportedTenant(this.tenantId);\n  }\n\n  /**\n   * Runs preparations for any further getToken request.\n   */\n  private async prepare(): Promise<void> {\n    // Attempts to load the tenant from the VSCode configuration file.\n    const settingsTenant = getPropertyFromVSCode(\"azure.tenant\");\n    if (settingsTenant) {\n      this.tenantId = settingsTenant;\n    }\n    checkUnsupportedTenant(this.tenantId);\n  }\n\n  /**\n   * The promise of the single preparation that will be executed at the first getToken request for an instance of this class.\n   */\n  private preparePromise: Promise<void> | undefined;\n\n  /**\n   * Runs preparations for any further getToken, but only once.\n   */\n  private prepareOnce(): Promise<void> | undefined {\n    if (!this.preparePromise) {\n      this.preparePromise = this.prepare();\n    }\n    return this.preparePromise;\n  }\n\n  /**\n   * Returns the token found by searching VSCode's authentication cache or\n   * returns null if no token could be found.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                `TokenCredential` implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options?: GetTokenOptions,\n  ): Promise<AccessToken> {\n    await this.prepareOnce();\n\n    const tenantId =\n      processMultiTenantRequest(\n        this.tenantId,\n        options,\n        this.additionallyAllowedTenantIds,\n        logger,\n      ) || this.tenantId;\n\n    if (findCredentials === undefined) {\n      throw new CredentialUnavailableError(\n        [\n          \"No implementation of `VisualStudioCodeCredential` is available.\",\n          \"You must install the identity-vscode plugin package (`npm install --save-dev @azure/identity-vscode`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(vsCodePlugin)` before creating a `VisualStudioCodeCredential`.\",\n          \"To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\",\n        ].join(\" \"),\n      );\n    }\n\n    let scopeString = typeof scopes === \"string\" ? scopes : scopes.join(\" \");\n\n    // Check to make sure the scope we get back is a valid scope\n    if (!scopeString.match(/^[0-9a-zA-Z-.:/]+$/)) {\n      const error = new Error(\"Invalid scope was specified by the user or calling client\");\n      logger.getToken.info(formatError(scopes, error));\n      throw error;\n    }\n\n    if (scopeString.indexOf(\"offline_access\") < 0) {\n      scopeString += \" offline_access\";\n    }\n\n    // findCredentials returns an array similar to:\n    // [\n    //   {\n    //     account: \"\",\n    //     password: \"\",\n    //   },\n    //   /* ... */\n    // ]\n    const credentials = await findCredentials();\n\n    // If we can't find the credential based on the name, we'll pick the first one available.\n    const { password: refreshToken } =\n      credentials.find(({ account }) => account === this.cloudName) ?? credentials[0] ?? {};\n\n    if (refreshToken) {\n      const tokenResponse = await this.identityClient.refreshAccessToken(\n        tenantId,\n        AzureAccountClientId,\n        scopeString,\n        refreshToken,\n        undefined,\n      );\n\n      if (tokenResponse) {\n        logger.getToken.info(formatSuccess(scopes));\n        return tokenResponse.accessToken;\n      } else {\n        const error = new CredentialUnavailableError(\n          \"Could not retrieve the token associated with Visual Studio Code. Have you connected using the 'Azure Account' extension recently? To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\",\n        );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    } else {\n      const error = new CredentialUnavailableError(\n        \"Could not retrieve the token associated with Visual Studio Code. Did you connect using the 'Azure Account' extension? To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\",\n      );\n      logger.getToken.info(formatError(scopes, error));\n      throw error;\n    }\n  }\n}\n"]}