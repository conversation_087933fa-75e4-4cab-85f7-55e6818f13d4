import * as vscode from 'vscode';

export interface CodeBlock {
    language: string;
    code: string;
    filename?: string;
    lineNumbers?: boolean;
    highlight?: number[];
}

export interface StyledOutput {
    html: string;
    css: string;
}

/**
 * Service for styling code output in webviews with beautiful formatting
 * Inspired by modern code editors and documentation sites
 */
export class CodeOutputStylingService {
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    /**
     * Style a code block with syntax highlighting and beautiful formatting
     */
    public styleCodeBlock(codeBlock: CodeBlock): StyledOutput {
        const { language, code, filename, lineNumbers = true, highlight = [] } = codeBlock;
        
        const lines = code.split('\n');
        const styledLines = lines.map((line, index) => {
            const lineNumber = index + 1;
            const isHighlighted = highlight.includes(lineNumber);
            const lineClass = isHighlighted ? 'highlighted-line' : '';
            
            return `
                <div class="code-line ${lineClass}" data-line="${lineNumber}">
                    ${lineNumbers ? `<span class="line-number">${lineNumber}</span>` : ''}
                    <span class="line-content">${this.escapeHtml(line)}</span>
                </div>
            `;
        }).join('');

        const html = `
            <div class="code-container">
                ${filename ? `<div class="code-header">
                    <div class="file-info">
                        <span class="file-icon">${this.getFileIcon(filename)}</span>
                        <span class="filename">${filename}</span>
                        <span class="language-badge">${language}</span>
                    </div>
                    <div class="code-actions">
                        <button class="copy-btn" onclick="copyCode(this)" title="Copy code">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                    </div>
                </div>` : ''}
                <div class="code-content language-${language}">
                    ${styledLines}
                </div>
            </div>
        `;

        const css = this.getCodeStylingCSS();

        return { html, css };
    }

    /**
     * Style multiple code blocks in a conversation format
     */
    public styleConversation(blocks: Array<{ type: 'text' | 'code', content: string | CodeBlock }>): StyledOutput {
        const styledBlocks = blocks.map(block => {
            if (block.type === 'text') {
                return `<div class="text-block">${this.formatText(block.content as string)}</div>`;
            } else {
                const styled = this.styleCodeBlock(block.content as CodeBlock);
                return styled.html;
            }
        }).join('');

        const html = `
            <div class="conversation-container">
                ${styledBlocks}
            </div>
        `;

        const css = this.getCodeStylingCSS() + this.getConversationStylingCSS();

        return { html, css };
    }

    /**
     * Create a complete HTML page with styled code
     */
    public createStyledPage(title: string, content: StyledOutput): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                <style>
                    ${content.css}
                </style>
            </head>
            <body>
                <div class="page-container">
                    <header class="page-header">
                        <h1>${title}</h1>
                        <div class="header-actions">
                            <button onclick="toggleTheme()" class="theme-toggle" title="Toggle theme">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="5"></circle>
                                    <line x1="12" y1="1" x2="12" y2="3"></line>
                                    <line x1="12" y1="21" x2="12" y2="23"></line>
                                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                    <line x1="1" y1="12" x2="3" y2="12"></line>
                                    <line x1="21" y1="12" x2="23" y2="12"></line>
                                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                                </svg>
                            </button>
                        </div>
                    </header>
                    <main class="page-content">
                        ${content.html}
                    </main>
                </div>
                <script>
                    ${this.getJavaScript()}
                </script>
            </body>
            </html>
        `;
    }

    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    private formatText(text: string): string {
        // Convert markdown-like formatting to HTML
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code class="inline-code">$1</code>')
            .replace(/\n/g, '<br>');
    }

    private getFileIcon(filename: string): string {
        const ext = filename.split('.').pop()?.toLowerCase();
        const icons: Record<string, string> = {
            'js': '📄',
            'ts': '📘',
            'jsx': '⚛️',
            'tsx': '⚛️',
            'html': '🌐',
            'css': '🎨',
            'json': '📋',
            'md': '📝',
            'py': '🐍',
            'java': '☕',
            'cpp': '⚙️',
            'c': '⚙️',
            'go': '🐹',
            'rs': '🦀',
            'php': '🐘',
            'rb': '💎',
            'swift': '🦉',
            'kt': '🎯'
        };
        return icons[ext || ''] || '📄';
    }

    private getCodeStylingCSS(): string {
        return `
            :root {
                --bg-primary: #1e1e1e;
                --bg-secondary: #252526;
                --bg-tertiary: #2d2d30;
                --text-primary: #d4d4d4;
                --text-secondary: #969696;
                --accent-blue: #007acc;
                --accent-green: #4ec9b0;
                --accent-orange: #ce9178;
                --accent-purple: #c586c0;
                --accent-red: #f44747;
                --border-color: #3e3e42;
                --highlight-bg: rgba(255, 255, 255, 0.1);
                --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            [data-theme="light"] {
                --bg-primary: #ffffff;
                --bg-secondary: #f8f8f8;
                --bg-tertiary: #f0f0f0;
                --text-primary: #333333;
                --text-secondary: #666666;
                --border-color: #e0e0e0;
                --highlight-bg: rgba(0, 0, 0, 0.05);
                --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: var(--bg-primary);
                color: var(--text-primary);
                margin: 0;
                padding: 20px;
                line-height: 1.6;
            }

            .code-container {
                background: var(--bg-secondary);
                border-radius: 12px;
                overflow: hidden;
                margin: 16px 0;
                box-shadow: var(--shadow);
                border: 1px solid var(--border-color);
            }

            .code-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background: var(--bg-tertiary);
                border-bottom: 1px solid var(--border-color);
            }

            .file-info {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .file-icon {
                font-size: 16px;
            }

            .filename {
                font-weight: 600;
                color: var(--text-primary);
            }

            .language-badge {
                background: var(--accent-blue);
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
            }

            .code-actions {
                display: flex;
                gap: 8px;
            }

            .copy-btn, .theme-toggle {
                background: transparent;
                border: 1px solid var(--border-color);
                color: var(--text-secondary);
                padding: 6px;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .copy-btn:hover, .theme-toggle:hover {
                background: var(--highlight-bg);
                color: var(--text-primary);
            }

            .code-content {
                padding: 16px 0;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.5;
                overflow-x: auto;
            }

            .code-line {
                display: flex;
                align-items: center;
                padding: 0 16px;
                min-height: 20px;
                transition: background-color 0.2s ease;
            }

            .code-line:hover {
                background: var(--highlight-bg);
            }

            .highlighted-line {
                background: rgba(255, 255, 0, 0.1);
                border-left: 3px solid var(--accent-orange);
            }

            .line-number {
                color: var(--text-secondary);
                margin-right: 16px;
                min-width: 40px;
                text-align: right;
                user-select: none;
                font-size: 12px;
            }

            .line-content {
                flex: 1;
                white-space: pre;
            }

            .inline-code {
                background: var(--bg-tertiary);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 0.9em;
            }
        `;
    }

    private getConversationStylingCSS(): string {
        return `
            .conversation-container {
                max-width: 1200px;
                margin: 0 auto;
            }

            .text-block {
                background: var(--bg-secondary);
                padding: 20px;
                margin: 16px 0;
                border-radius: 12px;
                border-left: 4px solid var(--accent-blue);
                box-shadow: var(--shadow);
            }

            .page-container {
                max-width: 1400px;
                margin: 0 auto;
            }

            .page-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 0;
                border-bottom: 2px solid var(--border-color);
                margin-bottom: 30px;
            }

            .page-header h1 {
                margin: 0;
                color: var(--text-primary);
                font-size: 2em;
                font-weight: 700;
            }

            .header-actions {
                display: flex;
                gap: 12px;
            }
        `;
    }

    private getJavaScript(): string {
        return `
            function copyCode(button) {
                const codeContainer = button.closest('.code-container');
                const codeLines = codeContainer.querySelectorAll('.line-content');
                const code = Array.from(codeLines).map(line => line.textContent).join('\\n');
                
                navigator.clipboard.writeText(code).then(() => {
                    button.innerHTML = '✓';
                    setTimeout(() => {
                        button.innerHTML = \`
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        \`;
                    }, 2000);
                });
            }

            function toggleTheme() {
                const body = document.body;
                const currentTheme = body.getAttribute('data-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                body.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
            }

            // Load saved theme
            document.addEventListener('DOMContentLoaded', () => {
                const savedTheme = localStorage.getItem('theme') || 'dark';
                document.body.setAttribute('data-theme', savedTheme);
            });
        `;
    }
}
