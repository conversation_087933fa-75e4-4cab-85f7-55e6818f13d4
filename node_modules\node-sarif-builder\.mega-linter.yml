# Configuration file for <PERSON>Linter
# See all available variables at https://oxsecurity.github.io/megalinter/configuration/ and in linters documentation

APPLY_FIXES: none # all, none, or list of linter keys
# ENABLE: # If you use E<PERSON><PERSON>E variable, all other languages/formats/tooling-formats will be disabled by default
# ENABLE_LINTERS: # If you use ENABLE_LINTERS variable, all other linters will be disabled by default
# DISABLE:
# - COPYPASTE # Uncomment to disable checks of excessive copy-pastes
# - SPELL # Uncomment to disable checks of spelling mistakes
SHOW_ELAPSED_TIME: true
FILEIO_REPORTER: false
PRE_COMMANDS:
  - command: yarn install --frozen-lockfile
    cwd: workspace
  - command: yarn add eslint-plugin-eslint-comments
    cwd: workspace
JSON_JSONLINT_FILTER_REGEX_EXCLUDE: (\.vscode|tsconfig\.json)
MARKDOWN_MARKDOWNLINT_FILTER_REGEX_EXCLUDE: (\\.github)
REPOSITORY_TRIVY_ARGUMENTS:
  - --skip-dirs
  - ./node_modules/uri-js
