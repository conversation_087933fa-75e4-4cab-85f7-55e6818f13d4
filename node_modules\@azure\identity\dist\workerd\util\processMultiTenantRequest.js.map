{"version": 3, "file": "processMultiTenantRequest.js", "sourceRoot": "", "sources": ["../../../src/util/processMultiTenantRequest.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAG1D,SAAS,+BAA+B,CAAC,QAAgB;IACvD,OAAO,yEAAyE,QAAQ,qMAAqM,CAAC;AAChS,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,yBAAyB,CACvC,QAAiB,EACjB,eAAiC,EACjC,+BAAyC,EAAE,EAC3C,MAAyB;;IAEzB,IAAI,gBAAoC,CAAC;IACzC,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,CAAC;QACvD,gBAAgB,GAAG,QAAQ,CAAC;IAC9B,CAAC;SAAM,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,gBAAgB,GAAG,QAAQ,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,gBAAgB,GAAG,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,QAAQ,mCAAI,QAAQ,CAAC;IAC3D,CAAC;IACD,IACE,QAAQ;QACR,gBAAgB,KAAK,QAAQ;QAC7B,CAAC,4BAA4B,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC3C,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,gBAAiB,CAAC,KAAK,CAAC,CAAC,EACnF,CAAC;QACD,MAAM,OAAO,GAAG,+BAA+B,CAAC,gBAAiB,CAAC,CAAC;QACnE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtB,MAAM,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { GetTokenOptions } from \"@azure/core-auth\";\nimport { CredentialUnavailableError } from \"../errors.js\";\nimport type { CredentialLogger } from \"./logging.js\";\n\nfunction createConfigurationErrorMessage(tenantId: string): string {\n  return `The current credential is not configured to acquire tokens for tenant ${tenantId}. To enable acquiring tokens for this tenant add it to the AdditionallyAllowedTenants on the credential options, or add \"*\" to AdditionallyAllowedTenants to allow acquiring tokens for any tenant.`;\n}\n\n/**\n * Of getToken contains a tenantId, this functions allows picking this tenantId as the appropriate for authentication,\n * unless multitenant authentication has been disabled through the AZURE_IDENTITY_DISABLE_MULTITENANTAUTH (on Node.js),\n * or unless the original tenant Id is `adfs`.\n * @internal\n */\nexport function processMultiTenantRequest(\n  tenantId?: string,\n  getTokenOptions?: GetTokenOptions,\n  additionallyAllowedTenantIds: string[] = [],\n  logger?: CredentialLogger,\n): string | undefined {\n  let resolvedTenantId: string | undefined;\n  if (process.env.AZURE_IDENTITY_DISABLE_MULTITENANTAUTH) {\n    resolvedTenantId = tenantId;\n  } else if (tenantId === \"adfs\") {\n    resolvedTenantId = tenantId;\n  } else {\n    resolvedTenantId = getTokenOptions?.tenantId ?? tenantId;\n  }\n  if (\n    tenantId &&\n    resolvedTenantId !== tenantId &&\n    !additionallyAllowedTenantIds.includes(\"*\") &&\n    !additionallyAllowedTenantIds.some((t) => t.localeCompare(resolvedTenantId!) === 0)\n  ) {\n    const message = createConfigurationErrorMessage(resolvedTenantId!);\n    logger?.info(message);\n    throw new CredentialUnavailableError(message);\n  }\n\n  return resolvedTenantId;\n}\n"]}