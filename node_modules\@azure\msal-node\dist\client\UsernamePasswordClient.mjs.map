{"version": 3, "file": "UsernamePasswordClient.mjs", "sources": ["../../src/client/UsernamePasswordClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAwBH;;;;;AAKG;AACG,MAAO,sBAAuB,SAAQ,UAAU,CAAA;AAClD,IAAA,WAAA,CAAY,aAAkC,EAAA;QAC1C,KAAK,CAAC,aAAa,CAAC,CAAC;KACxB;AAED;;;;AAIG;IACH,MAAM,YAAY,CACd,OAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;AAErE,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAC3C,IAAI,CAAC,SAAS,EACd,OAAO,CACV,CAAC;AAEF,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;;AAGF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrD,QAAA,MAAM,aAAa,GAAG,eAAe,CAAC,yBAAyB,CAC3D,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,CACV,CAAC;AAEF,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;;AAIG;AACK,IAAA,MAAM,mBAAmB,CAC7B,SAAoB,EACpB,OAAsC,EAAA;QAEtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,SAAS,CAAC,aAAa,EACvB,qBAAqB,CACxB,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAA,MAAM,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAAC;YACnE,UAAU,EAAE,OAAO,CAAC,QAAQ;YAC5B,IAAI,EAAE,iBAAiB,CAAC,GAAG;AAC9B,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YAC1C,SAAS,EAAE,SAAS,CAAC,kBAAkB;YACvC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAClC,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,sBAAsB,CAChC,OAAsC,EAAA;AAEtC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,QAAA,uBAAuB,CAAC,WAAW,CAC/B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACnC,CAAC;QACF,uBAAuB,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClE,uBAAuB,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAElE,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAE9D,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,iBAAiB,CAAC,aAAa,CAClC,CAAC;QAEF,uBAAuB,CAAC,YAAY,CAChC,UAAU,EACV,SAAS,CAAC,6BAA6B,CAC1C,CAAC;AACF,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAElD,uBAAuB,CAAC,cAAc,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAA,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACF,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;AAED,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,uBAAuB,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAEpE,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;AAC5C,YAAA,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAC7C,CAAC;AACL,SAAA;QAED,MAAM,eAAe,GACjB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AAElD,QAAA,IAAI,eAAe,EAAE;YACjB,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,MAAM,kBAAkB,CACpB,eAAe,CAAC,SAAS,EACzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,OAAO,CAAC,kBAAkB,CAC7B,CACJ,CAAC;YACF,uBAAuB,CAAC,sBAAsB,CAC1C,UAAU,EACV,eAAe,CAAC,aAAa,CAChC,CAAC;AACL,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,uBAAuB,CAAC,SAAS,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,IACI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB;YAC9C,OAAO,CAAC,QAAQ,EAClB;YACE,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnE,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}