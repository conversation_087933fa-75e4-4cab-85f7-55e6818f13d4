{"version": 3, "file": "JoseHeader.mjs", "sources": ["../../src/crypto/JoseHeader.ts"], "sourcesContent": [null], "names": ["JoseHeaderErrorCodes.missingKidError", "JoseHeaderErrorCodes.missingAlgError"], "mappings": ";;;;;;AAAA;;;AAGG;AAcH;MACa,UAAU,CAAA;AAKnB,IAAA,WAAA,CAAY,OAA0B,EAAA;AAClC,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;KAC1B;AAED;;;;;;;AAOG;IACH,OAAO,kBAAkB,CAAC,gBAAmC,EAAA;;AAEzD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;AACvB,YAAA,MAAM,qBAAqB,CAACA,eAAoC,CAAC,CAAC;AACrE,SAAA;;AAGD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;AACvB,YAAA,MAAM,qBAAqB,CAACC,eAAoC,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC;;AAE7B,YAAA,GAAG,EAAE,gBAAgB,CAAC,GAAG,IAAI,iBAAiB,CAAC,GAAG;YAClD,GAAG,EAAE,gBAAgB,CAAC,GAAG;YACzB,GAAG,EAAE,gBAAgB,CAAC,GAAG;AAC5B,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;KACpC;AACJ;;;;"}