export declare const clientInfoDecodingError = "client_info_decoding_error";
export declare const clientInfoEmptyError = "client_info_empty_error";
export declare const tokenParsingError = "token_parsing_error";
export declare const nullOrEmptyToken = "null_or_empty_token";
export declare const endpointResolutionError = "endpoints_resolution_error";
export declare const networkError = "network_error";
export declare const openIdConfigError = "openid_config_error";
export declare const hashNotDeserialized = "hash_not_deserialized";
export declare const invalidState = "invalid_state";
export declare const stateMismatch = "state_mismatch";
export declare const stateNotFound = "state_not_found";
export declare const nonceMismatch = "nonce_mismatch";
export declare const authTimeNotFound = "auth_time_not_found";
export declare const maxAgeTranspired = "max_age_transpired";
export declare const multipleMatchingTokens = "multiple_matching_tokens";
export declare const multipleMatchingAccounts = "multiple_matching_accounts";
export declare const multipleMatchingAppMetadata = "multiple_matching_appMetadata";
export declare const requestCannotBeMade = "request_cannot_be_made";
export declare const cannotRemoveEmptyScope = "cannot_remove_empty_scope";
export declare const cannotAppendScopeSet = "cannot_append_scopeset";
export declare const emptyInputScopeSet = "empty_input_scopeset";
export declare const deviceCodePollingCancelled = "device_code_polling_cancelled";
export declare const deviceCodeExpired = "device_code_expired";
export declare const deviceCodeUnknownError = "device_code_unknown_error";
export declare const noAccountInSilentRequest = "no_account_in_silent_request";
export declare const invalidCacheRecord = "invalid_cache_record";
export declare const invalidCacheEnvironment = "invalid_cache_environment";
export declare const noAccountFound = "no_account_found";
export declare const noCryptoObject = "no_crypto_object";
export declare const unexpectedCredentialType = "unexpected_credential_type";
export declare const invalidAssertion = "invalid_assertion";
export declare const invalidClientCredential = "invalid_client_credential";
export declare const tokenRefreshRequired = "token_refresh_required";
export declare const userTimeoutReached = "user_timeout_reached";
export declare const tokenClaimsCnfRequiredForSignedJwt = "token_claims_cnf_required_for_signedjwt";
export declare const authorizationCodeMissingFromServerResponse = "authorization_code_missing_from_server_response";
export declare const bindingKeyNotRemoved = "binding_key_not_removed";
export declare const endSessionEndpointNotSupported = "end_session_endpoint_not_supported";
export declare const keyIdMissing = "key_id_missing";
export declare const noNetworkConnectivity = "no_network_connectivity";
export declare const userCanceled = "user_canceled";
export declare const missingTenantIdError = "missing_tenant_id_error";
export declare const methodNotImplemented = "method_not_implemented";
export declare const nestedAppAuthBridgeDisabled = "nested_app_auth_bridge_disabled";
//# sourceMappingURL=ClientAuthErrorCodes.d.ts.map