{"version": 3, "file": "provider.js", "sourceRoot": "", "sources": ["../../../src/plugins/provider.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCachePersistenceOptions } from \"../msal/nodeFlows/tokenCachePersistenceOptions.js\";\nimport type { VSCodeCredentialFinder } from \"../credentials/visualStudioCodeCredentialPlugin.js\";\n\n/**\n * The type of an Azure Identity plugin, a function accepting a plugin\n * context.\n */\nexport type IdentityPlugin = (context: unknown) => void;\n\n/**\n * Plugin context entries for controlling cache plugins.\n */\nexport interface CachePluginControl {\n  setPersistence(\n    persistenceFactory: (\n      options?: TokenCachePersistenceOptions,\n      // eslint-disable-next-line @typescript-eslint/consistent-type-imports\n    ) => Promise<import(\"@azure/msal-node\").ICachePlugin>,\n  ): void;\n}\n\nexport interface NativeBrokerPluginControl {\n  // eslint-disable-next-line @typescript-eslint/consistent-type-imports\n  setNativeBroker(nativeBroker: import(\"@azure/msal-node\").INativeBrokerPlugin): void;\n}\n\n/**\n * Plugin context entries for controlling VisualStudioCodeCredential.\n */\nexport interface VisualStudioCodeCredentialControl {\n  setVsCodeCredentialFinder(finder: VSCodeCredentialFinder): void;\n}\n\n/**\n * Context options passed to a plugin during initialization.\n *\n * Plugin authors are responsible for casting their plugin context values\n * to this type.\n *\n * @internal\n */\nexport interface AzurePluginContext {\n  cachePluginControl: CachePluginControl;\n  nativeBrokerPluginControl: NativeBrokerPluginControl;\n  vsCodeCredentialControl: VisualStudioCodeCredentialControl;\n}\n"]}