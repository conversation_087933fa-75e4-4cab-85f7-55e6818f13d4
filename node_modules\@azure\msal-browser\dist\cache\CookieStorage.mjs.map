{"version": 3, "file": "CookieStorage.mjs", "sources": ["../../src/cache/CookieStorage.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAQH;AACA,MAAM,sBAAsB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAEtC,MAAA,eAAe,GAAG;AAC3B,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,IAAI,EAAE,MAAM;EACL;MAIE,aAAa,CAAA;IACtB,UAAU,GAAA;AACN,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC5B;AAED,IAAA,OAAO,CAAC,GAAW,EAAA;QACf,MAAM,IAAI,GAAG,CAAG,EAAA,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,YAAA,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACpE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE7B,IAAI,GAAG,KAAK,IAAI,EAAE;AACd,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,EAAE,CAAC;KACb;IAED,WAAW,GAAA;AACP,QAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;KAC1E;AAED,IAAA,OAAO,CACH,GAAW,EACX,KAAa,EACb,cAAuB,EACvB,MAAA,GAAkB,IAAI,EACtB,QAA4B,GAAA,eAAe,CAAC,GAAG,EAAA;AAE/C,QAAA,IAAI,SAAS,GAAG,CAAG,EAAA,kBAAkB,CAAC,GAAG,CAAC,CAAI,CAAA,EAAA,kBAAkB,CAC5D,KAAK,CACR,CAAoB,iBAAA,EAAA,QAAQ,GAAG,CAAC;AAEjC,QAAA,IAAI,cAAc,EAAE;AAChB,YAAA,MAAM,UAAU,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;AAC3D,YAAA,SAAS,IAAI,CAAA,QAAA,EAAW,UAAU,CAAA,CAAA,CAAG,CAAC;AACzC,SAAA;AAED,QAAA,IAAI,MAAM,IAAI,QAAQ,KAAK,eAAe,CAAC,IAAI,EAAE;;YAE7C,SAAS,IAAI,SAAS,CAAC;AAC1B,SAAA;AAED,QAAA,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;KAC/B;AAED,IAAA,MAAM,WAAW,GAAA;QACb,OAAO,OAAO,CAAC,MAAM,CACjB,qBAAqB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CACnE,CAAC;KACL;AAED,IAAA,UAAU,CAAC,GAAW,EAAA;;QAElB,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KAC7B;IAED,OAAO,GAAA;QACH,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAkB,EAAE,CAAC;AAC/B,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC1B,YAAA,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;QACnB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACvC;AACJ,CAAA;AAED;;;AAGG;AACG,SAAU,uBAAuB,CAAC,cAAsB,EAAA;AAC1D,IAAA,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;AACzB,IAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CACjB,KAAK,CAAC,OAAO,EAAE,GAAG,cAAc,GAAG,sBAAsB,CAC5D,CAAC;AACF,IAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC9B;;;;"}