/// <reference types="node" />
export type ResolverContext = {
    parentModule: "config-loader" | "fixer-formatter" | "linter-formatter" | "textlint" | "textlint-legacy";
};
type ResolverSkipResult = undefined;
/**
 * Resolve Hook
 */
export type ResolveHook = (specifier: string, context: ResolverContext) => {
    url: string | undefined;
} | ResolverSkipResult;
/**
 * dynamic import() hook
 */
export type ImportHook = (specifier: string, context: ResolverContext) => Promise<{
    exports: Record<string, unknown>;
} | ResolverSkipResult>;
/**
 * Register Resolver Hook
 * Hook can return resolved URL
 * if hooks pass through, it should return `undefined` instead of object
 * @param hook
 */
export declare const registerResolveHook: (hook: ResolveHook) => void;
/**
 * Try to resolve package name
 * if `packageName` is found, return resolved absolute path.
 * if `packageName` is not found, return `undefined`
 * @param packageName
 * @param context
 */
export declare const tryResolve: (packageName: string, context: ResolverContext) => string | undefined;
/**
 * Register Import Hook
 * @param hook
 */
export declare const registerImportHook: (hook: ImportHook) => void;
/**
 * dynamic import() with hooks
 * @param specifier file path or package name
 * @param context
 */
export declare const dynamicImport: (specifier: string, context: ResolverContext) => Promise<{
    exports: Record<string, unknown> | undefined;
}>;
/**
 * Clear all hooks
 */
export declare const clearHooks: () => void;
export {};
//# sourceMappingURL=index.d.ts.map