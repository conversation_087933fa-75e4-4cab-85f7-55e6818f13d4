// Provider Registry Service
// Centralized provider configuration management
// Eliminates hardcoded provider lists and enables dynamic provider management

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export interface ProviderDetail {
    id: string;
    name: string;
    displayName: string;
    apiBaseUrl: string;
    defaultModel: string;
    requiresApiKey: boolean;
    supportedFeatures: string[];
    modelsDiscoveryUrl?: string;
    authType?: 'bearerToken' | 'apiKey' | 'none';
    healthCheckUrl?: string;
    maxTokens?: number;
    supportsStreaming?: boolean;
    rateLimit?: {
        requestsPerMinute: number;
        tokensPerMinute: number;
    };
}

export interface ProviderConfiguration {
    version: string;
    lastUpdated: string;
    providers: ProviderDetail[];
}

/**
 * Default provider configuration - used as fallback when JSON config is not available
 */
const DEFAULT_PROVIDERS: ProviderDetail[] = [
    {
        id: 'deepseek',
        name: 'DeepSeek',
        displayName: 'DeepSeek',
        apiBaseUrl: 'https://api.deepseek.com/v1',
        defaultModel: 'deepseek-chat',
        requiresApiKey: true,
        supportedFeatures: ['chat', 'code', 'streaming', 'reasoning'],
        modelsDiscoveryUrl: '/models',
        authType: 'bearerToken',
        healthCheckUrl: '/models',
        maxTokens: 64000,
        supportsStreaming: true,
        rateLimit: {
            requestsPerMinute: 60,
            tokensPerMinute: 1000000
        }
    },
    {
        id: 'groq',
        name: 'Groq',
        displayName: 'Groq',
        apiBaseUrl: 'https://api.groq.com/openai/v1',
        defaultModel: 'llama-3.3-70b-versatile',
        requiresApiKey: true,
        supportedFeatures: ['chat', 'streaming', 'fast-inference', 'reasoning'],
        modelsDiscoveryUrl: '/models',
        authType: 'bearerToken',
        healthCheckUrl: '/models',
        maxTokens: 131072,
        supportsStreaming: true,
        rateLimit: {
            requestsPerMinute: 30,
            tokensPerMinute: 6000
        }
    },
    {
        id: 'openrouter',
        name: 'OpenRouter',
        displayName: 'OpenRouter',
        apiBaseUrl: 'https://openrouter.ai/api/v1',
        defaultModel: 'anthropic/claude-3.5-sonnet',
        requiresApiKey: true,
        supportedFeatures: ['chat', 'streaming', 'multi-model'],
        modelsDiscoveryUrl: '/models',
        authType: 'bearerToken',
        healthCheckUrl: '/models',
        maxTokens: 200000,
        supportsStreaming: true,
        rateLimit: {
            requestsPerMinute: 200,
            tokensPerMinute: 1000000
        }
    },
    {
        id: 'local',
        name: 'Local',
        displayName: 'Local (Ollama/LM Studio)',
        apiBaseUrl: 'http://localhost:11434',
        defaultModel: 'llama3.2',
        requiresApiKey: false,
        supportedFeatures: ['chat', 'streaming', 'offline'],
        modelsDiscoveryUrl: '/api/tags',
        authType: 'none',
        healthCheckUrl: '/api/version',
        supportsStreaming: true
    }
];

export class ProviderRegistryService {
    private static instance: ProviderRegistryService;
    private providers: ProviderDetail[] = DEFAULT_PROVIDERS;
    private configPath?: string;
    private configWatcher?: vscode.FileSystemWatcher;

    public static getInstance(): ProviderRegistryService {
        if (!ProviderRegistryService.instance) {
            ProviderRegistryService.instance = new ProviderRegistryService();
        }
        return ProviderRegistryService.instance;
    }

    /**
     * Initialize the provider registry with dynamic configuration loading
     */
    public async initialize(context: vscode.ExtensionContext): Promise<void> {
        // Set up configuration path
        this.configPath = path.join(context.extensionPath, 'config', 'providers.json');

        // Load configuration from JSON file if it exists
        await this.loadConfiguration();

        // Set up file watcher for configuration changes
        this.setupConfigurationWatcher();
    }

    /**
     * Load provider configuration from JSON file
     */
    private async loadConfiguration(): Promise<void> {
        if (!this.configPath || !fs.existsSync(this.configPath)) {
            console.log('Provider configuration file not found, using default providers');
            return;
        }

        try {
            const configContent = fs.readFileSync(this.configPath, 'utf8');
            const config: ProviderConfiguration = JSON.parse(configContent);

            if (this.validateConfiguration(config)) {
                this.providers = config.providers;
                console.log(`Loaded ${config.providers.length} providers from configuration file`);
            } else {
                console.warn('Invalid provider configuration, using defaults');
            }
        } catch (error) {
            console.error('Failed to load provider configuration:', error);
            console.log('Using default provider configuration');
        }
    }

    /**
     * Validate provider configuration structure
     */
    private validateConfiguration(config: ProviderConfiguration): boolean {
        if (!config.version || !config.providers || !Array.isArray(config.providers)) {
            return false;
        }

        return config.providers.every(provider =>
            provider.id &&
            provider.name &&
            provider.displayName &&
            provider.apiBaseUrl &&
            provider.defaultModel &&
            typeof provider.requiresApiKey === 'boolean' &&
            Array.isArray(provider.supportedFeatures)
        );
    }

    /**
     * Set up file watcher for configuration changes
     */
    private setupConfigurationWatcher(): void {
        if (!this.configPath) return;

        const configDir = path.dirname(this.configPath);
        this.configWatcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(configDir, 'providers.json')
        );

        this.configWatcher.onDidChange(async () => {
            console.log('Provider configuration file changed, reloading...');
            await this.loadConfiguration();
        });

        this.configWatcher.onDidCreate(async () => {
            console.log('Provider configuration file created, loading...');
            await this.loadConfiguration();
        });
    }

    /**
     * Get all available providers
     */
    public getAvailableProviders(): ProviderDetail[] {
        return this.providers;
    }

    /**
     * Get provider IDs only
     */
    public getProviderIds(): string[] {
        return this.providers.map(p => p.id);
    }

    /**
     * Get provider by ID
     */
    public getProvider(id: string): ProviderDetail | undefined {
        return this.providers.find(p => p.id === id);
    }

    /**
     * Get provider display names
     */
    public getProviderDisplayNames(): Record<string, string> {
        const result: Record<string, string> = {};
        this.providers.forEach(p => {
            result[p.id] = p.displayName;
        });
        return result;
    }

    /**
     * Check if provider exists
     */
    public hasProvider(id: string): boolean {
        return this.providers.some(p => p.id === id);
    }

    /**
     * Get providers that require API keys
     */
    public getProvidersRequiringApiKeys(): ProviderDetail[] {
        return this.providers.filter(p => p.requiresApiKey);
    }

    /**
     * Get providers supporting specific feature
     */
    public getProvidersByFeature(feature: string): ProviderDetail[] {
        return this.providers.filter(p => 
            p.supportedFeatures && p.supportedFeatures.includes(feature)
        );
    }

    /**
     * Register a new provider (for future extensibility)
     */
    public registerProvider(provider: ProviderDetail): void {
        const existingIndex = this.providers.findIndex(p => p.id === provider.id);
        if (existingIndex >= 0) {
            this.providers[existingIndex] = provider;
        } else {
            this.providers.push(provider);
        }
    }

    /**
     * Remove a provider
     */
    public removeProvider(id: string): boolean {
        const index = this.providers.findIndex(p => p.id === id);
        if (index >= 0) {
            this.providers.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * Save current provider configuration to JSON file
     */
    public async saveConfiguration(): Promise<void> {
        if (!this.configPath) {
            throw new Error('Configuration path not set');
        }

        const config: ProviderConfiguration = {
            version: '1.0.0',
            lastUpdated: new Date().toISOString(),
            providers: this.providers
        };

        try {
            // Ensure config directory exists
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }

            // Write configuration file
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2), 'utf8');
            console.log('Provider configuration saved successfully');
        } catch (error) {
            console.error('Failed to save provider configuration:', error);
            throw error;
        }
    }

    /**
     * Reload configuration from file
     */
    public async reloadConfiguration(): Promise<void> {
        await this.loadConfiguration();
    }

    /**
     * Get provider configuration metadata
     */
    public getConfigurationInfo(): { path?: string; providersCount: number; lastLoaded: Date } {
        return {
            path: this.configPath,
            providersCount: this.providers.length,
            lastLoaded: new Date()
        };
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        if (this.configWatcher) {
            this.configWatcher.dispose();
            this.configWatcher = undefined;
        }
    }
}
