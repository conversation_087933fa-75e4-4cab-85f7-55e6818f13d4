"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHttpHeaders = createHttpHeaders;
const ts_http_runtime_1 = require("@typespec/ts-http-runtime");
/**
 * Creates an object that satisfies the `HttpHeaders` interface.
 * @param rawHeaders - A simple object representing initial headers
 */
function createHttpHeaders(rawHeaders) {
    return (0, ts_http_runtime_1.createHttpHeaders)(rawHeaders);
}
//# sourceMappingURL=httpHeaders.js.map