/**
 * Project Template Registry - Industry-standard project templates
 */

import {
    ProjectTemplate,
    TechnologyStack,
    ProjectCategory,
    FileStructureTemplate,
    DependencyConfig,
    ToolingConfig,
    ScriptConfig,
    ProjectMetadata
} from '../interfaces/IProjectScaffold';

export class ProjectTemplateRegistry {
    private templates: Map<TechnologyStack, ProjectTemplate> = new Map();

    constructor() {
        this.initializeTemplates();
    }

    public getTemplate(stack: TechnologyStack): ProjectTemplate | null {
        return this.templates.get(stack) || null;
    }

    public getAllTemplates(): ProjectTemplate[] {
        return Array.from(this.templates.values());
    }

    public getTemplatesByCategory(category: ProjectCategory): ProjectTemplate[] {
        return this.getAllTemplates().filter(template => template.category === category);
    }

    private initializeTemplates(): void {
        // Web Frontend Templates
        this.templates.set(TechnologyStack.VANILLA_WEB, this.createVanillaWebTemplate());
        this.templates.set(TechnologyStack.REACT_VITE_TS, this.createReactViteTemplate());
        this.templates.set(TechnologyStack.VUE_VITE_TS, this.createVueViteTemplate());
        this.templates.set(TechnologyStack.SVELTE_VITE_TS, this.createSvelteViteTemplate());

        // Backend Templates
        this.templates.set(TechnologyStack.NODE_EXPRESS_TS, this.createNodeExpressTemplate());
        this.templates.set(TechnologyStack.PYTHON_FASTAPI, this.createPythonFastAPITemplate());
        this.templates.set(TechnologyStack.PYTHON_FLASK, this.createPythonFlaskTemplate());

        // Mobile Templates
        this.templates.set(TechnologyStack.REACT_NATIVE_EXPO, this.createReactNativeExpoTemplate());

        // Desktop Templates
        this.templates.set(TechnologyStack.ELECTRON_TS, this.createElectronTemplate());

        // Data Science Templates
        this.templates.set(TechnologyStack.PYTHON_DATA_SCIENCE, this.createPythonDataScienceTemplate());

        // Game Development Templates
        this.templates.set(TechnologyStack.PHASER_TS, this.createPhaserTemplate());
    }

    private createVanillaWebTemplate(): ProjectTemplate {
        return {
            id: 'vanilla-web',
            name: 'Vanilla Web Project',
            description: 'Modern vanilla HTML, CSS, and JavaScript with Vite',
            category: ProjectCategory.WEB_FRONTEND,
            stack: TechnologyStack.VANILLA_WEB,
            fileStructure: {
                directories: [
                    { path: 'src', purpose: 'Source files', required: true },
                    { path: 'public', purpose: 'Static assets', required: true },
                    { path: 'dist', purpose: 'Build output', required: false }
                ],
                files: [
                    {
                        path: 'index.html',
                        content: this.getVanillaHtmlTemplate(),
                        language: 'html',
                        required: true
                    },
                    {
                        path: 'src/main.js',
                        content: this.getVanillaJsTemplate(),
                        language: 'javascript',
                        required: true
                    },
                    {
                        path: 'src/style.css',
                        content: this.getVanillaCssTemplate(),
                        language: 'css',
                        required: true
                    },
                    {
                        path: 'vite.config.js',
                        content: this.getViteConfigTemplate(),
                        language: 'javascript',
                        required: true
                    }
                ],
                entryPoints: {
                    main: 'src/main.js',
                    index: 'index.html'
                }
            },
            dependencies: {
                runtime: {},
                development: {
                    'vite': '^5.0.0'
                },
                packageManager: 'npm'
            },
            toolingConfig: {
                linting: {
                    tool: 'eslint',
                    configFile: '.eslintrc.js',
                    rules: {}
                },
                formatting: {
                    tool: 'prettier',
                    configFile: '.prettierrc',
                    options: {}
                },
                testing: {
                    framework: 'vitest',
                    configFile: 'vitest.config.js',
                    testDirectory: 'tests'
                },
                building: {
                    tool: 'vite',
                    configFile: 'vite.config.js',
                    outputDirectory: 'dist',
                    entryPoint: 'index.html'
                }
            },
            scripts: {
                dev: 'vite',
                build: 'vite build',
                test: 'vitest',
                lint: 'eslint src',
                format: 'prettier --write src',
                preview: 'vite preview'
            },
            metadata: {
                version: '1.0.0',
                license: 'MIT',
                keywords: ['web', 'vanilla', 'vite']
            }
        };
    }

    private createReactViteTemplate(): ProjectTemplate {
        return {
            id: 'react-vite-ts',
            name: 'React + Vite + TypeScript',
            description: 'Modern React application with Vite and TypeScript',
            category: ProjectCategory.WEB_FRONTEND,
            stack: TechnologyStack.REACT_VITE_TS,
            fileStructure: {
                directories: [
                    { path: 'src', purpose: 'Source files', required: true },
                    { path: 'src/components', purpose: 'React components', required: true },
                    { path: 'src/hooks', purpose: 'Custom hooks', required: false },
                    { path: 'src/utils', purpose: 'Utility functions', required: false },
                    { path: 'src/types', purpose: 'TypeScript types', required: false },
                    { path: 'public', purpose: 'Static assets', required: true }
                ],
                files: [
                    {
                        path: 'index.html',
                        content: this.getReactHtmlTemplate(),
                        language: 'html',
                        required: true
                    },
                    {
                        path: 'src/main.tsx',
                        content: this.getReactMainTemplate(),
                        language: 'typescript',
                        required: true
                    },
                    {
                        path: 'src/App.tsx',
                        content: this.getReactAppTemplate(),
                        language: 'typescript',
                        required: true
                    },
                    {
                        path: 'src/App.css',
                        content: this.getReactAppCssTemplate(),
                        language: 'css',
                        required: true
                    },
                    {
                        path: 'src/index.css',
                        content: this.getReactIndexCssTemplate(),
                        language: 'css',
                        required: true
                    },
                    {
                        path: 'tsconfig.json',
                        content: this.getReactTsConfigTemplate(),
                        language: 'json',
                        required: true
                    },
                    {
                        path: 'vite.config.ts',
                        content: this.getReactViteConfigTemplate(),
                        language: 'typescript',
                        required: true
                    }
                ],
                entryPoints: {
                    main: 'src/main.tsx',
                    index: 'index.html',
                    types: 'src/types/index.ts'
                }
            },
            dependencies: {
                runtime: {
                    'react': '^18.2.0',
                    'react-dom': '^18.2.0'
                },
                development: {
                    '@types/react': '^18.2.0',
                    '@types/react-dom': '^18.2.0',
                    '@vitejs/plugin-react': '^4.2.0',
                    'typescript': '^5.0.0',
                    'vite': '^5.0.0',
                    'eslint': '^8.0.0',
                    '@typescript-eslint/eslint-plugin': '^6.0.0',
                    '@typescript-eslint/parser': '^6.0.0',
                    'eslint-plugin-react': '^7.33.0',
                    'eslint-plugin-react-hooks': '^4.6.0',
                    'prettier': '^3.0.0'
                },
                packageManager: 'npm'
            },
            toolingConfig: {
                linting: {
                    tool: 'eslint',
                    configFile: '.eslintrc.js',
                    rules: {}
                },
                formatting: {
                    tool: 'prettier',
                    configFile: '.prettierrc',
                    options: {}
                },
                testing: {
                    framework: 'vitest',
                    configFile: 'vitest.config.ts',
                    testDirectory: 'src/__tests__'
                },
                building: {
                    tool: 'vite',
                    configFile: 'vite.config.ts',
                    outputDirectory: 'dist',
                    entryPoint: 'index.html'
                },
                typeChecking: {
                    tool: 'typescript',
                    configFile: 'tsconfig.json',
                    strict: true
                }
            },
            scripts: {
                dev: 'vite',
                build: 'tsc && vite build',
                test: 'vitest',
                lint: 'eslint src --ext ts,tsx',
                format: 'prettier --write src',
                preview: 'vite preview'
            },
            metadata: {
                version: '1.0.0',
                license: 'MIT',
                keywords: ['react', 'vite', 'typescript']
            }
        };
    }

    // Template content methods will be added in the next part
    private getVanillaHtmlTemplate(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanilla Web App</title>
</head>
<body>
    <div id="app">
        <h1>Hello, World!</h1>
        <p>Welcome to your vanilla web application.</p>
    </div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>`;
    }

    private getVanillaJsTemplate(): string {
        return `// Main application entry point
import './style.css';

document.querySelector('#app').innerHTML = \`
    <div class="container">
        <h1>Vanilla Web App</h1>
        <p>Built with modern JavaScript and Vite</p>
        <button id="counter-btn">Count: 0</button>
    </div>
\`;

let count = 0;
document.getElementById('counter-btn').addEventListener('click', () => {
    count++;
    document.getElementById('counter-btn').textContent = \`Count: \${count}\`;
});`;
    }

    private getVanillaCssTemplate(): string {
        return `:root {
    --primary-color: #646cff;
    --secondary-color: #535bf2;
    --background-color: #242424;
    --text-color: rgba(255, 255, 255, 0.87);
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: var(--secondary-color);
}`;
    }

    private getViteConfigTemplate(): string {
        return `import { defineConfig } from 'vite';

export default defineConfig({
    server: {
        port: 3000,
        open: true
    },
    build: {
        outDir: 'dist',
        sourcemap: true
    }
});`;
    }

    // React template methods will be implemented in the next part
    private getReactHtmlTemplate(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React App</title>
</head>
<body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
</body>
</html>`;
    }

    private getReactMainTemplate(): string {
        return `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
        <App />
    </React.StrictMode>
);`;
    }

    private getReactAppTemplate(): string {
        return `import React, { useState } from 'react';
import './App.css';

function App() {
    const [count, setCount] = useState(0);

    return (
        <div className="App">
            <header className="App-header">
                <h1>React + Vite + TypeScript</h1>
                <p>Modern React development setup</p>
                <button onClick={() => setCount(count + 1)}>
                    Count: {count}
                </button>
            </header>
        </div>
    );
}

export default App;`;
    }

    private getReactAppCssTemplate(): string {
        return `.App {
    text-align: center;
}

.App-header {
    background-color: #282c34;
    padding: 20px;
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

button {
    background-color: #61dafb;
    color: #282c34;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
}

button:hover {
    background-color: #21a9c7;
    transform: translateY(-2px);
}`;
    }

    private getReactIndexCssTemplate(): string {
        return `:root {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

#root {
    min-height: 100vh;
}`;
    }

    private getReactTsConfigTemplate(): string {
        return `{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "module": "ESNext",
        "skipLibCheck": true,
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true
    },
    "include": ["src"],
    "references": [{ "path": "./tsconfig.node.json" }]
}`;
    }

    private getReactViteConfigTemplate(): string {
        return `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [react()],
    server: {
        port: 3000,
        open: true
    },
    build: {
        outDir: 'dist',
        sourcemap: true
    }
});`;
    }

    // Placeholder methods for other templates
    private createVueViteTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createSvelteViteTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createNodeExpressTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createPythonFastAPITemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createPythonFlaskTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createReactNativeExpoTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createElectronTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createPythonDataScienceTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }

    private createPhaserTemplate(): ProjectTemplate {
        // Implementation will be added
        return {} as ProjectTemplate;
    }
}
