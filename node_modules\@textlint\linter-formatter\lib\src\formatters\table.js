/**
 * @fileoverview "table reporter.
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Gaj<PERSON> <<EMAIL>>. All rights reserved.
 */
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
//------------------------------------------------------------------------------
// Requirements
//------------------------------------------------------------------------------
const chalk_1 = __importDefault(require("chalk"));
const table_1 = require("table");
// @ts-expect-error no types
const pluralize_1 = __importDefault(require("pluralize"));
const strip_ansi_1 = __importDefault(require("strip-ansi"));
//------------------------------------------------------------------------------
// Helpers
//------------------------------------------------------------------------------
/**
 * Draws text table.
 * @param {Array<Object>} messages Error messages relating to a specific file.
 * @returns {string} A text table.
 */
function drawTable(messages) {
    let rows = [];
    if (messages.length === 0) {
        return "";
    }
    rows.push([
        chalk_1.default.bold("Line"),
        chalk_1.default.bold("Column"),
        chalk_1.default.bold("Type"),
        chalk_1.default.bold("Message"),
        chalk_1.default.bold("Rule ID")
    ]);
    messages.forEach(function (message) {
        let messageType;
        if (message.fatal || message.severity === 2) {
            messageType = chalk_1.default.red("error");
        }
        else {
            messageType = chalk_1.default.yellow("warning");
        }
        rows.push([message.line || 0, message.column || 0, messageType, message.message, message.ruleId || ""]);
    });
    const output = (0, table_1.table)(rows, {
        columns: {
            0: {
                width: 8,
                wrapWord: true
            },
            1: {
                width: 8,
                wrapWord: true
            },
            2: {
                width: 8,
                wrapWord: true
            },
            3: {
                paddingRight: 5,
                width: 50,
                wrapWord: true
            },
            4: {
                width: 20,
                wrapWord: true
            }
        },
        drawHorizontalLine: function (index) {
            return index === 1;
        }
    });
    return output;
}
/**
 * Draws a report (multiple tables).
 * @param {Array} results Report results for every file.
 * @returns {string} A column of text tables.
 */
function drawReport(results) {
    let files;
    files = results.map(function (result) {
        if (!result.messages.length) {
            return "";
        }
        return "\n" + result.filePath + "\n\n" + drawTable(result.messages);
    });
    files = files.filter(function (content) {
        return content.trim();
    });
    return files.join("");
}
//------------------------------------------------------------------------------
// Public Interface
//------------------------------------------------------------------------------
function formatter(report, options) {
    // default: true
    const useColor = options.color !== undefined ? options.color : true;
    let output = "";
    let errorCount = 0;
    let warningCount = 0;
    report.forEach(function (fileReport) {
        errorCount += fileReport.errorCount;
        warningCount += fileReport.warningCount;
    });
    if (errorCount || warningCount) {
        output = drawReport(report);
    }
    output +=
        "\n" +
            (0, table_1.table)([
                [chalk_1.default.red((0, pluralize_1.default)("Error", errorCount, true))],
                [chalk_1.default.yellow((0, pluralize_1.default)("Warning", warningCount, true))]
            ], {
                columns: {
                    0: {
                        width: 110,
                        wrapWord: true
                    }
                },
                drawHorizontalLine: function () {
                    return true;
                }
            });
    if (!useColor) {
        return (0, strip_ansi_1.default)(output);
    }
    return output;
}
exports.default = formatter;
//# sourceMappingURL=table.js.map