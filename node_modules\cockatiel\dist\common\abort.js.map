{"version": 3, "file": "abort.js", "sourceRoot": "", "sources": ["../../src/common/abort.ts"], "names": [], "mappings": ";;;AAAA,mCAA+C;AAElC,QAAA,kBAAkB,GAAG,IAAI,eAAe,EAAE,CAAC,MAAM,CAAC;AAE/D,MAAM,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;AAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;AACR,QAAA,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;AAEjD,MAAM,IAAI,GAAe,GAAG,EAAE,GAAE,CAAC,CAAC;AAElC;;;GAGG;AACI,MAAM,qBAAqB,GAAG,CACnC,MAAoB,EACqB,EAAE;IAC3C,MAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAC;IACnC,IAAI,OAAO,GAAe,IAAI,CAAC;IAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;SAAM,CAAC;QACN,MAAM,QAAQ,GAAG,IAAA,eAAO,EAAC,MAAM,CAAC,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACnC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC;AAlBW,QAAA,qBAAqB,yBAkBhC", "sourcesContent": ["import { IDisposable, onAbort } from './Event';\n\nexport const neverAbortedSignal = new AbortController().signal;\n\nconst cancelledSrc = new AbortController();\ncancelledSrc.abort();\nexport const abortedSignal = cancelledSrc.signal;\n\nconst noop: () => void = () => {};\n\n/**\n * Creates a new AbortController that is aborted when the parent signal aborts.\n * @private\n */\nexport const deriveAbortController = (\n  signal?: AbortSignal,\n): { ctrl: AbortController } & IDisposable => {\n  const ctrl = new AbortController();\n  let dispose: () => void = noop;\n  if (!signal) {\n    return { ctrl, dispose };\n  }\n\n  if (signal.aborted) {\n    ctrl.abort();\n  } else {\n    const abortEvt = onAbort(signal);\n    abortEvt.event(() => ctrl.abort());\n    dispose = abortEvt.dispose;\n  }\n\n  return { ctrl, dispose };\n};\n"]}