{"version": 3, "file": "SecretLintModuleResolver.js", "sourceRoot": "", "sources": ["../src/SecretLintModuleResolver.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;AAC/D,OAAO,MAAM,MAAM,OAAO,CAAC;AAE3B,MAAM,KAAK,GAAG,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAElD;;;GAGG;AACH,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAsB,EAAE;IACpE,OAAO,UAAU,CAAC,UAAU,EAAE;QAC1B,gBAAgB,EAAE,MAAM,CAAC,IAAI;QAC7B,YAAY,EAAE,eAAe;KAChC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,wBAAwB;IACzB,aAAa,CAAS;IAE9B,YAAY,MAAkC;QAC1C;;WAEG;QACH,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IACpF,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CAAC,WAAmB;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,eAAe,GAAG,qBAAqB,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;QAC/E,6CAA6C;QAC7C,MAAM,OAAO,GACT,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACzD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,KAAK,CAAC,yBAAyB,eAAe,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,cAAc,CAAC,6CAA6C,WAAW;;OAEtF,OAAO,CAAC,GAAG,EAAE;WACT,OAAO;CACjB,CAAC,CAAC;SACM;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,4BAA4B,CAAC,WAAmB;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,eAAe,GAAG,qBAAqB,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;QACtF,0EAA0E;QAC1E,MAAM,OAAO,GACT,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACzD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,KAAK,CAAC,gCAAgC,eAAe,EAAE,CAAC,CAAC;YACzD,MAAM,IAAI,cAAc,CAAC,oDAAoD,WAAW;;OAE7F,OAAO,CAAC,GAAG,EAAE;WACT,OAAO;CACjB,CAAC,CAAC;SACM;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,wBAAwB,CAAC,WAAmB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,eAAe,GAAG,qBAAqB,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;QACjF,iDAAiD;QACjD,MAAM,OAAO,GACT,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACzD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,KAAK,CAAC,2BAA2B,eAAe,EAAE,CAAC,CAAC;YACpD,MAAM,IAAI,cAAc,CAAC,+CAA+C,WAAW;;OAExF,OAAO,CAAC,GAAG,EAAE;WACT,OAAO;;CAEjB,CAAC,CAAC;SACM;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,wBAAwB,CAAC,WAAmB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,MAAM,GAAG,yBAAyB,CAAC;QACzC;;;;;;;;;;;;WAYG;QACH,iDAAiD;QACjD,+DAA+D;QAC/D,MAAM,wBAAwB,GAAG,WAAW;aACvC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;aACvB,OAAO,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,eAAe,GAAG,qBAAqB,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;QAChF,MAAM,mBAAmB,GAAG,GAAG,MAAM,GAAG,wBAAwB,EAAE,CAAC;QACnE,MAAM,OAAO;QACT,sFAAsF;QACtF,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;YAC7D,gBAAgB;YAChB,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC;YAClE,cAAc;YACd,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACzD,iBAAiB;YACjB,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,KAAK,CAAC,2BAA2B,eAAe,EAAE,CAAC,CAAC;YACpD,KAAK,CAAC,+BAA+B,mBAAmB,EAAE,CAAC,CAAC;YAC5D,MAAM,IAAI,cAAc,CAAC,+CAA+C,WAAW;;OAExF,OAAO,CAAC,GAAG,EAAE;WACT,OAAO;CACjB,CAAC,CAAC;SACM;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,wBAAwB,CAAC,WAAmB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,eAAe,GAAG,qBAAqB,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;QACjF,iDAAiD;QACjD,MAAM,OAAO,GACT,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YACzD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,cAAc,CAAC,+CAA+C,WAAW;;OAExF,OAAO,CAAC,GAAG,EAAE;WACT,OAAO;CACjB,CAAC,CAAC;SACM;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ"}