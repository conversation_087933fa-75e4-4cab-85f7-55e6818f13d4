{"version": 3, "file": "agentPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/agentPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAiBlC,kCAEC;AAfD,0EAGqD;AAErD;;GAEG;AACU,QAAA,eAAe,GAAG,0BAAkB,CAAC;AAElD;;GAEG;AACH,SAAgB,WAAW,CAAC,KAAa;IACvC,OAAO,IAAA,sBAAc,EAAC,KAAK,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { Agent } from \"../interfaces.js\";\nimport {\n  agentPolicyName as tspAgentPolicyName,\n  agentPolicy as tspAgentPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the Agent Policy\n */\nexport const agentPolicyName = tspAgentPolicyName;\n\n/**\n * Gets a pipeline policy that sets http.agent\n */\nexport function agentPolicy(agent?: Agent): PipelinePolicy {\n  return tspAgentPolicy(agent);\n}\n"]}