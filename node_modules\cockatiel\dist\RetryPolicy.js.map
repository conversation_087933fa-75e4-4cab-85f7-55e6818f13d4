{"version": 3, "file": "RetryPolicy.js", "sourceRoot": "", "sources": ["../src/RetryPolicy.ts"], "names": [], "mappings": ";;;AACA,+DAA4D;AAC5D,0CAAoD;AACpD,0CAA8C;AAI9C,MAAM,KAAK,GAAG,CAAC,QAAgB,EAAE,KAAc,EAAE,EAAE,CACjD,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;IACpB,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC5C,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,CAAC,KAAK,EAAE,CAAC;IAChB,CAAC;AACH,CAAC,CAAC,CAAC;AAkCL,MAAa,WAAW;IA6BtB,YACU,OAAqC,EAC5B,QAAwB;QADjC,YAAO,GAAP,OAAO,CAA8B;QAC5B,aAAQ,GAAR,QAAQ,CAAgB;QA5B1B,oBAAe,GAAG,IAAI,oBAAY,EAA0B,CAAC;QAC7D,mBAAc,GAAG,IAAI,oBAAY,EAE/C,CAAC;QAEJ;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;;WAGG;QACa,YAAO,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAE1D;;WAEG;QACa,aAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;IAKzD,CAAC;IAEJ;;;;;OAKG;IACI,gBAAgB;QACrB,OAAO,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;IAClF,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAO,CAClB,EAAkD,EAClD,MAAM,GAAG,0BAAkB;QAE3B,MAAM,OAAO,GACX,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,iCAAe,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,OAA4D,CAAC;QACjE,KAAK,IAAI,OAAO,GAAG,CAAC,GAAI,OAAO,EAAE,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC5E,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;gBACxB,OAAO,MAAM,CAAC,OAAO,CAAC;YACxB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC1D,MAAM,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;gBACzD,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClE,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACvC,MAAM,YAAY,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAChE,kEAAkE;gBAClE,wCAAwC;gBACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;gBACpF,MAAM,YAAY,CAAC;gBACnB,SAAS;YACX,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;gBACtB,MAAM,MAAM,CAAC,KAAK,CAAC;YACrB,CAAC;YAED,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;IACH,CAAC;CACF;AAlFD,kCAkFC", "sourcesContent": ["import { IBackoff, IBackoffFactory } from './backoff/Backoff';\nimport { ConstantBackoff } from './backoff/ConstantBackoff';\nimport { neverAbortedSignal } from './common/abort';\nimport { EventEmitter } from './common/Event';\nimport { ExecuteWrapper } from './common/Executor';\nimport { FailureReason, IDefaultPolicyContext, IPolicy } from './Policy';\n\nconst delay = (duration: number, unref: boolean) =>\n  new Promise(resolve => {\n    const timer = setTimeout(resolve, duration);\n    if (unref) {\n      timer.unref();\n    }\n  });\n\n/**\n * Context passed into the execute method of the builder.\n */\nexport interface IRetryContext extends IDefaultPolicyContext {\n  /**\n   * The retry attempt, starting at 1 for calls into backoffs.\n   */\n  attempt: number;\n}\n\n/**\n * Context passed into backoff delegate.\n */\nexport interface IRetryBackoffContext<R> extends IRetryContext {\n  /**\n   * The result of the last method call. Either a thrown error, or a value\n   * that we determined should be retried upon.\n   */\n  result: FailureReason<R>;\n}\n\nexport interface IRetryPolicyConfig {\n  backoff: IBackoffFactory<IRetryBackoffContext<unknown>>;\n  maxAttempts: number;\n\n  /**\n   * Whether to unreference the internal timer. This means the policy will not\n   * keep the Node.js even loop active. Defaults to `false`.\n   */\n  unref?: boolean;\n}\n\nexport class RetryPolicy implements IPolicy<IRetryContext> {\n  declare readonly _altReturn: never;\n\n  private readonly onGiveUpEmitter = new EventEmitter<FailureReason<unknown>>();\n  private readonly onRetryEmitter = new EventEmitter<\n    FailureReason<unknown> & { delay: number; attempt: number }\n  >();\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onSuccess = this.executor.onSuccess;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onFailure = this.executor.onFailure;\n\n  /**\n   * Emitter that fires when we retry a call, before any backoff.\n   *\n   */\n  public readonly onRetry = this.onRetryEmitter.addListener;\n\n  /**\n   * Emitter that fires when we're no longer retrying a call and are giving up.\n   */\n  public readonly onGiveUp = this.onGiveUpEmitter.addListener;\n\n  constructor(\n    private options: Readonly<IRetryPolicyConfig>,\n    private readonly executor: ExecuteWrapper,\n  ) {}\n\n  /**\n   * When retrying, a referenced timer is created. This means the Node.js event\n   * loop is kept active while we're delaying a retried call. Calling this\n   * method on the retry builder will unreference the timer, allowing the\n   * process to exit even if a retry might still be pending.\n   */\n  public dangerouslyUnref() {\n    return new RetryPolicy({ ...this.options, unref: true }, this.executor.clone());\n  }\n\n  /**\n   * Executes the given function with retries.\n   * @param fn Function to run\n   * @returns a Promise that resolves or rejects with the function results.\n   */\n  public async execute<T>(\n    fn: (context: IRetryContext) => PromiseLike<T> | T,\n    signal = neverAbortedSignal,\n  ): Promise<T> {\n    const factory: IBackoffFactory<IRetryBackoffContext<unknown>> =\n      this.options.backoff || new ConstantBackoff(0);\n    let backoff: IBackoff<IRetryBackoffContext<unknown>> | undefined;\n    for (let retries = 0; ; retries++) {\n      const result = await this.executor.invoke(fn, { attempt: retries, signal });\n      if ('success' in result) {\n        return result.success;\n      }\n\n      if (!signal.aborted && retries < this.options.maxAttempts) {\n        const context = { attempt: retries + 1, signal, result };\n        backoff = backoff ? backoff.next(context) : factory.next(context);\n        const delayDuration = backoff.duration;\n        const delayPromise = delay(delayDuration, !!this.options.unref);\n        // A little sneaky reordering here lets us use Sinon's fake timers\n        // when we get an emission in our tests.\n        this.onRetryEmitter.emit({ ...result, delay: delayDuration, attempt: retries + 1 });\n        await delayPromise;\n        continue;\n      }\n\n      this.onGiveUpEmitter.emit(result);\n      if ('error' in result) {\n        throw result.error;\n      }\n\n      return result.value;\n    }\n  }\n}\n"]}