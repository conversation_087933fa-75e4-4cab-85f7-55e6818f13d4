// Generated TypeScript definitions for common.proto
// Auto-generated from proto files using ts-proto equivalent definitions

export interface Metadata {
  // Empty metadata message
}

export interface EmptyRequest {
  metadata?: Metadata;
}

export interface Empty {
  // Empty message
}

export interface StringRequest {
  metadata?: Metadata;
  value: string;
}

export interface StringArrayRequest {
  metadata?: Metadata;
  value: string[];
}

export interface String {
  value: string;
}

export interface Int64Request {
  metadata?: Metadata;
  value: number;
}

export interface Int64 {
  value: number;
}

export interface BytesRequest {
  metadata?: Metadata;
  value: Uint8Array;
}

export interface Bytes {
  value: Uint8Array;
}

export interface BooleanRequest {
  metadata?: Metadata;
  value: boolean;
}

export interface Boolean {
  value: boolean;
}

export interface StringArray {
  values: string[];
}

// Utility functions for encoding/decoding (ts-proto style)
export const Metadata = {
  encode(): Uint8Array {
    return new Uint8Array();
  },
  decode(): Metadata {
    return {};
  },
  fromJSON(): Metadata {
    return {};
  },
  toJSON(): unknown {
    return {};
  }
};

export const EmptyRequest = {
  encode(message: EmptyRequest): Uint8Array {
    // Simplified encoding - in real implementation would use protobuf encoding
    return new Uint8Array();
  },
  decode(): EmptyRequest {
    return { metadata: {} };
  },
  fromJSON(object: any): EmptyRequest {
    return {
      metadata: object.metadata ? object.metadata : undefined
    };
  },
  toJSON(message: EmptyRequest): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    return obj;
  }
};

export const Empty = {
  encode(): Uint8Array {
    return new Uint8Array();
  },
  decode(): Empty {
    return {};
  },
  fromJSON(): Empty {
    return {};
  },
  toJSON(): unknown {
    return {};
  }
};

export const StringRequest = {
  encode(message: StringRequest): Uint8Array {
    // Simplified encoding - in real implementation would use protobuf encoding
    return new Uint8Array();
  },
  decode(): StringRequest {
    return { metadata: {}, value: '' };
  },
  fromJSON(object: any): StringRequest {
    return {
      metadata: object.metadata || undefined,
      value: String(object.value || '')
    };
  },
  toJSON(message: StringRequest): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    obj.value = message.value;
    return obj;
  }
};

export const StringArrayRequest = {
  encode(message: StringArrayRequest): Uint8Array {
    return new Uint8Array();
  },
  decode(): StringArrayRequest {
    return { metadata: {}, value: [] };
  },
  fromJSON(object: any): StringArrayRequest {
    return {
      metadata: object.metadata || undefined,
      value: Array.isArray(object.value) ? object.value.map((e: any) => String(e)) : []
    };
  },
  toJSON(message: StringArrayRequest): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    obj.value = message.value;
    return obj;
  }
};

export const StringArray = {
  encode(message: StringArray): Uint8Array {
    return new Uint8Array();
  },
  decode(): StringArray {
    return { values: [] };
  },
  fromJSON(object: any): StringArray {
    return {
      values: Array.isArray(object.values) ? object.values.map((e: any) => String(e)) : []
    };
  },
  toJSON(message: StringArray): unknown {
    const obj: any = {};
    obj.values = message.values;
    return obj;
  }
};

// Additional utility types for Int64, Bytes, and Boolean following the same pattern
export const Int64Request = {
  encode(message: Int64Request): Uint8Array {
    return new Uint8Array();
  },
  decode(): Int64Request {
    return { metadata: {}, value: 0 };
  },
  fromJSON(object: any): Int64Request {
    return {
      metadata: object.metadata || undefined,
      value: Number(object.value || 0)
    };
  },
  toJSON(message: Int64Request): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    obj.value = message.value;
    return obj;
  }
};

export const BooleanRequest = {
  encode(message: BooleanRequest): Uint8Array {
    return new Uint8Array();
  },
  decode(): BooleanRequest {
    return { metadata: {}, value: false };
  },
  fromJSON(object: any): BooleanRequest {
    return {
      metadata: object.metadata || undefined,
      value: Boolean(object.value)
    };
  },
  toJSON(message: BooleanRequest): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    obj.value = message.value;
    return obj;
  }
};

export const BytesRequest = {
  encode(message: BytesRequest): Uint8Array {
    return new Uint8Array();
  },
  decode(): BytesRequest {
    return { metadata: {}, value: new Uint8Array() };
  },
  fromJSON(object: any): BytesRequest {
    return {
      metadata: object.metadata || undefined,
      value: object.value ? new Uint8Array(object.value) : new Uint8Array()
    };
  },
  toJSON(message: BytesRequest): unknown {
    const obj: any = {};
    if (message.metadata) {
      obj.metadata = message.metadata;
    }
    obj.value = Array.from(message.value);
    return obj;
  }
};
