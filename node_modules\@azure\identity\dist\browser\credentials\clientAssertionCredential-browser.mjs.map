{"version": 3, "file": "clientAssertionCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/clientAssertionCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEnE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,4DAA4D,CAC7D,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAE7D;;GAEG;AACH,MAAM,OAAO,yBAAyB;IACpC;;OAEG;IACH;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\n\nconst BrowserNotSupportedError = new Error(\n  \"ClientAssertionCredential is not supported in the browser.\",\n);\nconst logger = credentialLogger(\"ClientAssertionCredential\");\n\n/**\n * Authenticates a service principal with a JWT assertion.\n */\nexport class ClientAssertionCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}