{"version": 3, "file": "regionalAuthority.js", "sourceRoot": "", "sources": ["../../src/regionalAuthority.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,CAAN,IAAY,iBA2GX;AA3GD,WAAY,iBAAiB;IAC3B,uDAAuD;IACvD,8DAAyC,CAAA;IACzC,wEAAwE;IACxE,sCAAiB,CAAA;IACjB,yEAAyE;IACzE,wCAAmB,CAAA;IACnB,2EAA2E;IAC3E,4CAAuB,CAAA;IACvB,wEAAwE;IACxE,sCAAiB,CAAA;IACjB,yEAAyE;IACzE,wCAAmB,CAAA;IACnB,gFAAgF;IAChF,sDAAiC,CAAA;IACjC,gFAAgF;IAChF,sDAAiC,CAAA;IACjC,+EAA+E;IAC/E,oDAA+B,CAAA;IAC/B,+EAA+E;IAC/E,oDAA+B,CAAA;IAC/B,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,6EAA6E;IAC7E,gDAA2B,CAAA;IAC3B,6EAA6E;IAC7E,gDAA2B,CAAA;IAC3B,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,yEAAyE;IACzE,wCAAmB,CAAA;IACnB,wEAAwE;IACxE,sCAAiB,CAAA;IACjB,+EAA+E;IAC/E,oDAA+B,CAAA;IAC/B,6EAA6E;IAC7E,gDAA2B,CAAA;IAC3B,kFAAkF;IAClF,0DAAqC,CAAA;IACrC,iFAAiF;IACjF,wDAAmC,CAAA;IACnC,8EAA8E;IAC9E,kDAA6B,CAAA;IAC7B,oFAAoF;IACpF,8DAAyC,CAAA;IACzC,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,0EAA0E;IAC1E,0CAAqB,CAAA;IACrB,+EAA+E;IAC/E,oDAA+B,CAAA;IAC/B,2EAA2E;IAC3E,4CAAuB,CAAA;IACvB,2EAA2E;IAC3E,4CAAuB,CAAA;IACvB,+EAA+E;IAC/E,oDAA+B,CAAA;IAC/B,oFAAoF;IACpF,8DAAyC,CAAA;IACzC,kFAAkF;IAClF,0DAAqC,CAAA;IACrC,mFAAmF;IACnF,4DAAuC,CAAA;IACvC,8EAA8E;IAC9E,kDAA6B,CAAA;IAC7B,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,2EAA2E;IAC3E,4CAAuB,CAAA;IACvB,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,8EAA8E;IAC9E,kDAA6B,CAAA;IAC7B,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,0EAA0E;IAC1E,0CAAqB,CAAA;IACrB,kFAAkF;IAClF,0DAAqC,CAAA;IACrC,iFAAiF;IACjF,wDAAmC,CAAA;IACnC,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,2EAA2E;IAC3E,4CAAuB,CAAA;IACvB,6EAA6E;IAC7E,gDAA2B,CAAA;IAC3B,4EAA4E;IAC5E,8CAAyB,CAAA;IACzB,gFAAgF;IAChF,sDAAiC,CAAA;IACjC,kFAAkF;IAClF,0DAAqC,CAAA;IACrC,+EAA+E;IAC/E,2DAAsC,CAAA;IACtC,2EAA2E;IAC3E,mDAA8B,CAAA;IAC9B,8EAA8E;IAC9E,yDAAoC,CAAA;IACpC,4EAA4E;IAC5E,qDAAgC,CAAA;IAChC,2EAA2E;IAC3E,sDAAiC,CAAA;IACjC,8EAA8E;IAC9E,4DAAuC,CAAA;AACzC,CAAC,EA3GW,iBAAiB,KAAjB,iBAAiB,QA2G5B;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,0BAA0B,CAAC,iBAA0B;IACnE,qFAAqF;IACrF,4EAA4E;IAC5E,oJAAoJ;;IAEpJ,uFAAuF;IACvF,IAAI,WAAW,GAAG,iBAAiB,CAAC;IAEpC,IACE,WAAW,KAAK,SAAS;QACzB,CAAA,MAAA,MAAA,UAAU,CAAC,OAAO,0CAAE,GAAG,0CAAE,6BAA6B,MAAK,SAAS,EACpE,CAAC;QACD,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;IAC1D,CAAC;IAED,IAAI,WAAW,KAAK,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;QACzD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Helps specify a regional authority, or \"AutoDiscoverRegion\" to auto-detect the region.\n */\nexport enum RegionalAuthority {\n  /** Instructs MSAL to attempt to discover the region */\n  AutoDiscoverRegion = \"AutoDiscoverRegion\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westus' region. */\n  USWest = \"westus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westus2' region. */\n  USWest2 = \"westus2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'centralus' region. */\n  USCentral = \"centralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastus' region. */\n  USEast = \"eastus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastus2' region. */\n  USEast2 = \"eastus2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'northcentralus' region. */\n  USNorthCentral = \"northcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southcentralus' region. */\n  USSouthCentral = \"southcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westcentralus' region. */\n  USWestCentral = \"westcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'canadacentral' region. */\n  CanadaCentral = \"canadacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'canadaeast' region. */\n  CanadaEast = \"canadaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'brazilsouth' region. */\n  BrazilSouth = \"brazilsouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'northeurope' region. */\n  EuropeNorth = \"northeurope\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westeurope' region. */\n  EuropeWest = \"westeurope\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uksouth' region. */\n  UKSouth = \"uksouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'ukwest' region. */\n  UKWest = \"ukwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'francecentral' region. */\n  FranceCentral = \"francecentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'francesouth' region. */\n  FranceSouth = \"francesouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandnorth' region. */\n  SwitzerlandNorth = \"switzerlandnorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandwest' region. */\n  SwitzerlandWest = \"switzerlandwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanynorth' region. */\n  GermanyNorth = \"germanynorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanywestcentral' region. */\n  GermanyWestCentral = \"germanywestcentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'norwaywest' region. */\n  NorwayWest = \"norwaywest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'norwayeast' region. */\n  NorwayEast = \"norwayeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastasia' region. */\n  AsiaEast = \"eastasia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southeastasia' region. */\n  AsiaSouthEast = \"southeastasia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'japaneast' region. */\n  JapanEast = \"japaneast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'japanwest' region. */\n  JapanWest = \"japanwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiaeast' region. */\n  AustraliaEast = \"australiaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiasoutheast' region. */\n  AustraliaSouthEast = \"australiasoutheast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral' region. */\n  AustraliaCentral = \"australiacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral2' region. */\n  AustraliaCentral2 = \"australiacentral2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'centralindia' region. */\n  IndiaCentral = \"centralindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southindia' region. */\n  IndiaSouth = \"southindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westindia' region. */\n  IndiaWest = \"westindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'koreasouth' region. */\n  KoreaSouth = \"koreasouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'koreacentral' region. */\n  KoreaCentral = \"koreacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uaecentral' region. */\n  UAECentral = \"uaecentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uaenorth' region. */\n  UAENorth = \"uaenorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southafricanorth' region. */\n  SouthAfricaNorth = \"southafricanorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southafricawest' region. */\n  SouthAfricaWest = \"southafricawest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth' region. */\n  ChinaNorth = \"chinanorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast' region. */\n  ChinaEast = \"chinaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth2' region. */\n  ChinaNorth2 = \"chinanorth2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast2' region. */\n  ChinaEast2 = \"chinaeast2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanycentral' region. */\n  GermanyCentral = \"germanycentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanynortheast' region. */\n  GermanyNorthEast = \"germanynortheast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovvirginia' region. */\n  GovernmentUSVirginia = \"usgovvirginia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgoviowa' region. */\n  GovernmentUSIowa = \"usgoviowa\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovarizona' region. */\n  GovernmentUSArizona = \"usgovarizona\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovtexas' region. */\n  GovernmentUSTexas = \"usgovtexas\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usdodeast' region. */\n  GovernmentUSDodEast = \"usdodeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usdodcentral' region. */\n  GovernmentUSDodCentral = \"usdodcentral\",\n}\n\n/**\n * Calculates the correct regional authority based on the supplied value\n * and the AZURE_REGIONAL_AUTHORITY_NAME environment variable.\n *\n * Values will be returned verbatim, except for {@link RegionalAuthority.AutoDiscoverRegion}\n * which is mapped to a value MSAL can understand.\n *\n * @internal\n */\nexport function calculateRegionalAuthority(regionalAuthority?: string): string | undefined {\n  // Note: as of today only 3 credentials support regional authority, and the parameter\n  // is not exposed via the public API. Regional Authority is _only_ supported\n  // via the AZURE_REGIONAL_AUTHORITY_NAME env var and _only_ for: ClientSecretCredential, ClientCertificateCredential, and ClientAssertionCredential.\n\n  // Accepting the regionalAuthority parameter will allow us to support it in the future.\n  let azureRegion = regionalAuthority;\n\n  if (\n    azureRegion === undefined &&\n    globalThis.process?.env?.AZURE_REGIONAL_AUTHORITY_NAME !== undefined\n  ) {\n    azureRegion = process.env.AZURE_REGIONAL_AUTHORITY_NAME;\n  }\n\n  if (azureRegion === RegionalAuthority.AutoDiscoverRegion) {\n    return \"AUTO_DISCOVER\";\n  }\n\n  return azureRegion;\n}\n"]}