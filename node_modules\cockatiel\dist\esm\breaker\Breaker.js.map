{"version": 3, "file": "Breaker.js", "sourceRoot": "", "sources": ["../../../src/breaker/Breaker.ts"], "names": [], "mappings": "AAuBA,cAAc,sBAAsB,CAAC;AACrC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,mBAAmB,CAAC", "sourcesContent": ["import { CircuitState } from '../CircuitBreakerPolicy';\n\n/**\n * The breaker determines when the circuit breaker should open.\n */\nexport interface IBreaker {\n  /**\n   * Gets or sets the internal state of the breaker. Used for serialization\n   * with {@link CircuitBreaker.toJSON}.\n   */\n  state: unknown;\n\n  /**\n   * Called when a call succeeds.\n   */\n  success(state: CircuitState): void;\n\n  /**\n   * Called when a call fails. Returns true if the circuit should open.\n   */\n  failure(state: CircuitState): boolean;\n}\n\nexport * from './ConsecutiveBreaker';\nexport * from './CountBreaker';\nexport * from './SamplingBreaker';\n\n"]}