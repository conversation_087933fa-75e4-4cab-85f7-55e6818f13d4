import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * This credential will use the currently logged-in user login information
 * via the Azure Developer CLI ('azd') commandline tool.
 */
export declare class AzureDeveloperCliCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=azureDeveloperCliCredential-browser.d.mts.map