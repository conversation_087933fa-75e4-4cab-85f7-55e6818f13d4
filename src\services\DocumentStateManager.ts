/**
 * Document State Manager - Refact-inspired document tracking for V1b3-Sama
 * Manages comprehensive document state and file change tracking
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface Document {
    doc_path: string;
    doc_text?: string;
    last_modified?: number;
    language_id?: string;
    is_dirty?: boolean;
    change_count?: number;
}

export interface DocumentState {
    workspace_folders: string[];
    workspace_files: string[];
    workspace_vcs_roots: string[];
    active_file_path?: string;
    memory_document_map: Map<string, Document>;
    cache_dirty: number;
    file_watchers: Map<string, vscode.FileSystemWatcher>;
}

export interface FileChangeEvent {
    type: 'created' | 'modified' | 'deleted';
    path: string;
    timestamp: number;
    content?: string;
}

export class DocumentStateManager {
    private documentState: DocumentState;
    private context: vscode.ExtensionContext;
    private changeListeners: ((event: FileChangeEvent) => void)[] = [];
    private disposables: vscode.Disposable[] = [];

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.documentState = {
            workspace_folders: [],
            workspace_files: [],
            workspace_vcs_roots: [],
            memory_document_map: new Map(),
            cache_dirty: Date.now(),
            file_watchers: new Map()
        };
    }

    /**
     * Initialize document state manager
     */
    public async initialize(): Promise<void> {
        try {
            // Initialize workspace folders
            await this.updateWorkspaceFolders();

            // Set up file system watchers
            this.setupFileWatchers();

            // Set up VS Code event listeners
            this.setupVSCodeEventListeners();

            // Index existing workspace files
            await this.indexWorkspaceFiles();

            console.log(`DocumentStateManager initialized with ${this.documentState.workspace_files.length} files`);
        } catch (error) {
            console.error('Failed to initialize DocumentStateManager:', error);
            throw error;
        }
    }

    /**
     * Handle file opened in VS Code
     */
    public async onDidOpen(filePath: string, text: string, languageId: string): Promise<void> {
        try {
            const normalizedPath = this.normalizePath(filePath);
            
            const document: Document = {
                doc_path: normalizedPath,
                doc_text: text,
                last_modified: Date.now(),
                language_id: languageId,
                is_dirty: false,
                change_count: 0
            };

            this.documentState.memory_document_map.set(normalizedPath, document);
            this.documentState.active_file_path = normalizedPath;
            this.markCacheDirty();

            console.log(`Document opened: ${this.getShortPath(normalizedPath)}`);

            // Emit change event
            this.emitChangeEvent({
                type: 'modified',
                path: normalizedPath,
                timestamp: Date.now(),
                content: text
            });
        } catch (error) {
            console.error('Failed to handle file open:', error);
        }
    }

    /**
     * Handle file changed in VS Code
     */
    public async onDidChange(filePath: string, text: string): Promise<void> {
        try {
            const normalizedPath = this.normalizePath(filePath);
            
            let document = this.documentState.memory_document_map.get(normalizedPath);
            if (!document) {
                document = {
                    doc_path: normalizedPath,
                    change_count: 0
                };
                this.documentState.memory_document_map.set(normalizedPath, document);
            }

            document.doc_text = text;
            document.last_modified = Date.now();
            document.is_dirty = true;
            document.change_count = (document.change_count || 0) + 1;

            this.markCacheDirty();

            // Emit change event
            this.emitChangeEvent({
                type: 'modified',
                path: normalizedPath,
                timestamp: Date.now(),
                content: text
            });
        } catch (error) {
            console.error('Failed to handle file change:', error);
        }
    }

    /**
     * Handle file saved in VS Code
     */
    public async onDidSave(filePath: string): Promise<void> {
        try {
            const normalizedPath = this.normalizePath(filePath);
            
            const document = this.documentState.memory_document_map.get(normalizedPath);
            if (document) {
                document.is_dirty = false;
                document.last_modified = Date.now();
            }

            this.markCacheDirty();
            console.log(`Document saved: ${this.getShortPath(normalizedPath)}`);
        } catch (error) {
            console.error('Failed to handle file save:', error);
        }
    }

    /**
     * Handle file deleted
     */
    public async onDidDelete(filePath: string): Promise<void> {
        try {
            const normalizedPath = this.normalizePath(filePath);
            
            // Remove from memory document map
            this.documentState.memory_document_map.delete(normalizedPath);
            
            // Remove from workspace files
            const index = this.documentState.workspace_files.indexOf(normalizedPath);
            if (index > -1) {
                this.documentState.workspace_files.splice(index, 1);
            }

            this.markCacheDirty();
            console.log(`Document deleted: ${this.getShortPath(normalizedPath)}`);

            // Emit change event
            this.emitChangeEvent({
                type: 'deleted',
                path: normalizedPath,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('Failed to handle file deletion:', error);
        }
    }

    /**
     * Get document by path
     */
    public getDocument(filePath: string): Document | undefined {
        const normalizedPath = this.normalizePath(filePath);
        return this.documentState.memory_document_map.get(normalizedPath);
    }

    /**
     * Get all documents
     */
    public getAllDocuments(): Document[] {
        return Array.from(this.documentState.memory_document_map.values());
    }

    /**
     * Get workspace files
     */
    public getWorkspaceFiles(): string[] {
        return [...this.documentState.workspace_files];
    }

    /**
     * Get workspace folders
     */
    public getWorkspaceFolders(): string[] {
        return [...this.documentState.workspace_folders];
    }

    /**
     * Get active file path
     */
    public getActiveFilePath(): string | undefined {
        return this.documentState.active_file_path;
    }

    /**
     * Check if cache is dirty
     */
    public isCacheDirty(): boolean {
        return this.documentState.cache_dirty > 0;
    }

    /**
     * Get cache dirty timestamp
     */
    public getCacheDirtyTime(): number {
        return this.documentState.cache_dirty;
    }

    /**
     * Add change listener
     */
    public addChangeListener(listener: (event: FileChangeEvent) => void): void {
        this.changeListeners.push(listener);
    }

    /**
     * Remove change listener
     */
    public removeChangeListener(listener: (event: FileChangeEvent) => void): void {
        const index = this.changeListeners.indexOf(listener);
        if (index > -1) {
            this.changeListeners.splice(index, 1);
        }
    }

    /**
     * Get document statistics
     */
    public getStats(): {
        totalDocuments: number;
        dirtyDocuments: number;
        workspaceFiles: number;
        memoryDocuments: number;
        averageChangeCount: number;
    } {
        const documents = this.getAllDocuments();
        const dirtyCount = documents.filter(doc => doc.is_dirty).length;
        const totalChanges = documents.reduce((sum, doc) => sum + (doc.change_count || 0), 0);

        return {
            totalDocuments: documents.length,
            dirtyDocuments: dirtyCount,
            workspaceFiles: this.documentState.workspace_files.length,
            memoryDocuments: this.documentState.memory_document_map.size,
            averageChangeCount: documents.length > 0 ? totalChanges / documents.length : 0
        };
    }

    /**
     * Dispose resources
     */
    public dispose(): void {
        // Dispose all VS Code event listeners
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Dispose file watchers
        for (const watcher of this.documentState.file_watchers.values()) {
            watcher.dispose();
        }

        this.disposables = [];
        this.documentState.file_watchers.clear();
        this.changeListeners = [];
    }

    /**
     * Update workspace folders
     */
    private async updateWorkspaceFolders(): Promise<void> {
        this.documentState.workspace_folders = [];

        if (vscode.workspace.workspaceFolders) {
            for (const folder of vscode.workspace.workspaceFolders) {
                this.documentState.workspace_folders.push(folder.uri.fsPath);
            }
        }

        // Find VCS roots
        await this.findVCSRoots();
    }

    /**
     * Find VCS roots in workspace
     */
    private async findVCSRoots(): Promise<void> {
        this.documentState.workspace_vcs_roots = [];

        for (const folder of this.documentState.workspace_folders) {
            // Check for .git directory
            const gitPath = path.join(folder, '.git');
            if (fs.existsSync(gitPath)) {
                this.documentState.workspace_vcs_roots.push(folder);
            }

            // Check for other VCS systems
            const vcsMarkers = ['.svn', '.hg', '.bzr'];
            for (const marker of vcsMarkers) {
                const markerPath = path.join(folder, marker);
                if (fs.existsSync(markerPath)) {
                    this.documentState.workspace_vcs_roots.push(folder);
                    break;
                }
            }
        }
    }

    /**
     * Setup file system watchers
     */
    private setupFileWatchers(): void {
        for (const folder of this.documentState.workspace_folders) {
            const pattern = new vscode.RelativePattern(folder, '**/*');
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);

            // Watch for file creation
            watcher.onDidCreate(async (uri) => {
                const filePath = uri.fsPath;
                if (this.shouldTrackFile(filePath)) {
                    this.documentState.workspace_files.push(this.normalizePath(filePath));
                    this.markCacheDirty();

                    this.emitChangeEvent({
                        type: 'created',
                        path: this.normalizePath(filePath),
                        timestamp: Date.now()
                    });
                }
            });

            // Watch for file changes
            watcher.onDidChange(async (uri) => {
                const filePath = this.normalizePath(uri.fsPath);

                // Update last modified time if we're tracking this file
                const document = this.documentState.memory_document_map.get(filePath);
                if (document) {
                    document.last_modified = Date.now();
                    this.markCacheDirty();
                }
            });

            // Watch for file deletion
            watcher.onDidDelete(async (uri) => {
                await this.onDidDelete(uri.fsPath);
            });

            this.documentState.file_watchers.set(folder, watcher);
        }
    }

    /**
     * Setup VS Code event listeners
     */
    private setupVSCodeEventListeners(): void {
        // Listen for text document open
        this.disposables.push(
            vscode.workspace.onDidOpenTextDocument(async (document) => {
                if (document.uri.scheme === 'file') {
                    await this.onDidOpen(
                        document.uri.fsPath,
                        document.getText(),
                        document.languageId
                    );
                }
            })
        );

        // Listen for text document changes
        this.disposables.push(
            vscode.workspace.onDidChangeTextDocument(async (event) => {
                if (event.document.uri.scheme === 'file') {
                    await this.onDidChange(
                        event.document.uri.fsPath,
                        event.document.getText()
                    );
                }
            })
        );

        // Listen for text document save
        this.disposables.push(
            vscode.workspace.onDidSaveTextDocument(async (document) => {
                if (document.uri.scheme === 'file') {
                    await this.onDidSave(document.uri.fsPath);
                }
            })
        );

        // Listen for active editor changes
        this.disposables.push(
            vscode.window.onDidChangeActiveTextEditor((editor) => {
                if (editor && editor.document.uri.scheme === 'file') {
                    this.documentState.active_file_path = this.normalizePath(editor.document.uri.fsPath);
                    this.markCacheDirty();
                }
            })
        );

        // Listen for workspace folder changes
        this.disposables.push(
            vscode.workspace.onDidChangeWorkspaceFolders(async () => {
                await this.updateWorkspaceFolders();
                await this.indexWorkspaceFiles();
            })
        );
    }

    /**
     * Index all workspace files
     */
    private async indexWorkspaceFiles(): Promise<void> {
        this.documentState.workspace_files = [];

        for (const folder of this.documentState.workspace_folders) {
            await this.indexDirectory(folder);
        }

        this.markCacheDirty();
        console.log(`Indexed ${this.documentState.workspace_files.length} workspace files`);
    }

    /**
     * Index files in a directory recursively
     */
    private async indexDirectory(dirPath: string): Promise<void> {
        try {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);

                // Skip hidden files and directories
                if (entry.name.startsWith('.')) {
                    continue;
                }

                // Skip node_modules and other common ignore patterns
                if (this.shouldIgnoreDirectory(entry.name)) {
                    continue;
                }

                if (entry.isDirectory()) {
                    await this.indexDirectory(fullPath);
                } else if (entry.isFile() && this.shouldTrackFile(fullPath)) {
                    this.documentState.workspace_files.push(this.normalizePath(fullPath));
                }
            }
        } catch (error) {
            console.warn(`Failed to index directory ${dirPath}:`, error);
        }
    }

    /**
     * Check if file should be tracked
     */
    private shouldTrackFile(filePath: string): boolean {
        const ext = path.extname(filePath).toLowerCase();
        const trackableExtensions = [
            '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.html', '.css', '.scss',
            '.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.sh', '.bat'
        ];

        return trackableExtensions.includes(ext);
    }

    /**
     * Check if directory should be ignored
     */
    private shouldIgnoreDirectory(dirName: string): boolean {
        const ignoreDirs = [
            'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'out',
            'target', 'bin', 'obj', '__pycache__', '.vscode', '.idea'
        ];

        return ignoreDirs.includes(dirName);
    }

    /**
     * Normalize file path
     */
    private normalizePath(filePath: string): string {
        return path.normalize(filePath).replace(/\\/g, '/');
    }

    /**
     * Get short path for display
     */
    private getShortPath(filePath: string): string {
        for (const folder of this.documentState.workspace_folders) {
            if (filePath.startsWith(folder)) {
                return path.relative(folder, filePath);
            }
        }
        return path.basename(filePath);
    }

    /**
     * Mark cache as dirty
     */
    private markCacheDirty(): void {
        this.documentState.cache_dirty = Date.now();
    }

    /**
     * Emit change event to listeners
     */
    private emitChangeEvent(event: FileChangeEvent): void {
        for (const listener of this.changeListeners) {
            try {
                listener(event);
            } catch (error) {
                console.error('Error in document change listener:', error);
            }
        }
    }
}
