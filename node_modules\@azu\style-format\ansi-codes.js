// LICENSE : MIT
module.exports     = {
    reset            : "\033[0m",
    bold             : "\033[1m",
    italic           : "\033[3m",
    blink            : "\033[5m",
    underline        : "\033[4m",
    underlineOff     : "\033[24m",
    inverse          : "\033[7m",
    inverseOff       : "\033[27m",
    strikethrough    : "\033[9m",
    strikethroughOff : "\033[29m",

    def              : "\033[39m",
    white            : "\033[37m",
    black            : "\033[30m",
    grey             : "\x1B[90m",
    red              : "\033[31m",
    green            : "\033[32m",
    blue             : "\033[34m",
    yellow           : "\033[33m",
    magenta          : "\033[35m",
    cyan             : "\033[36m",

    defBg            : "\033[49m",
    whiteBg          : "\033[47m",
    blackBg          : "\033[40m",
    redBg            : "\033[41m",
    greenBg          : "\033[42m",
    blueBg           : "\033[44m",
    yellowBg         : "\033[43m",
    magentaBg        : "\033[45m",
    cyanBg           : "\033[46m",

    brightBlack      : "\033[90m",
    brightRed        : "\033[91m",
    brightGreen      : "\033[92m",
    brightYellow     : "\033[93m",
    brightBlue       : "\033[94m",
    brightMagenta    : "\033[95m",
    brightCyan       : "\033[96m",
    brightWhite      : "\033[97m",

    brightBlackBg    : "\033[100m",
    brightRedBg      : "\033[101m",
    brightGreenBg    : "\033[102m",
    brightYellowBg   : "\033[103m",
    brightBlueBg     : "\033[104m",
    brightMagentaBg  : "\033[105m",
    brightCyanBg     : "\033[106m",
    brightWhiteBg    : "\033[107m"
}