{"version": 3, "file": "PlatformAuthInteractionClient.mjs", "sources": ["../../src/interaction_client/PlatformAuthInteractionClient.ts"], "sourcesContent": [null], "names": ["NativeAuthErrorCodes.userSwitch", "BrowserAuthErrorCodes.invalidPopTokenRequest", "BrowserAuthErrorCodes.nativePromptNotSupported"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAyEG,MAAO,6BAA8B,SAAQ,qBAAqB,CAAA;IAQpE,WACI,CAAA,MAA4B,EAC5B,cAAmC,EACnC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,KAAY,EACZ,iBAAqC,EACrC,QAA8B,EAC9B,SAAiB,EACjB,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,cAAc,EACd,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,QAAQ,EACR,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;AACrC,QAAA,IAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAC1C,MAAM,EACN,IAAI,CAAC,oBAAoB,EACzB,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,QAAQ,EACR,aAAa,CAChB,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;AAEnE,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,kBAAkB,CAAC;YAClD,WAAW,EAAE,gBAAgB,CAAC,QAAQ;AACtC,YAAA,cAAc,EAAE,OAAO;AACvB,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE;AACpE,SAAA,CAAC,CAAC;KACN;AAED;;;;AAIG;AACK,IAAA,cAAc,CAAC,OAA4B,EAAA;QAC/C,OAAO,CAAC,eAAe,GAAG;YACtB,GAAG,OAAO,CAAC,eAAe;AAC1B,YAAA,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI;SACrD,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,MAAM,YAAY,CACd,OAAwD,EACxD,iBAAqC,EAAA;AAErC,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,mCAAmC,EACrD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;;AAGpE,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAC/D,iBAAiB,CAAC,mCAAmC,EACrD,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAE5C,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,IAAI;;YAEA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;;YAGlE,IAAI;AACA,gBAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC5C,IAAI,CAAC,SAAS,EACd,aAAa,CAChB,CAAC;gBACF,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,cAAc,EAAE,KAAK;AACrB,oBAAA,SAAS,EAAE,IAAI;AAClB,iBAAA,CAAC,CAAC;AACH,gBAAA,OAAO,MAAM,CAAC;AACjB,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,iBAAiB,KAAK,iBAAiB,CAAC,WAAW,EAAE;AACrD,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,+EAA+E,CAClF,CAAC;AACF,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;;AAED,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,4EAA4E,CAC/E,CAAC;AACL,aAAA;YAED,MAAM,iBAAiB,GACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAE/D,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAClC,iBAAiB,EACjB,aAAa,EACb,YAAY,CACf;AACI,iBAAA,IAAI,CAAC,CAAC,MAA4B,KAAI;gBACnC,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,cAAc,EAAE,IAAI;oBACpB,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,iBAAA,CAAC,CAAC;gBACH,sBAAsB,CAAC,0BAA0B,EAAE,CAAC;AACpD,gBAAA,OAAO,MAAM,CAAC;AAClB,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,KAAgB,KAAI;gBACxB,mBAAmB,CAAC,GAAG,CAAC;AACpB,oBAAA,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oBAAA,cAAc,EAAE,IAAI;AACvB,iBAAA,CAAC,CAAC;AACH,gBAAA,MAAM,KAAK,CAAC;AAChB,aAAC,CAAC,CAAC;AACV,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,eAAe,EAAE;AAC9B,gBAAA,sBAAsB,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAChE,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;;AAKG;IACK,wBAAwB,CAC5B,OAA4B,EAC5B,aAA0B,EAAA;QAE1B,OAAO;YACH,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;AACpD,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,YAAY,EAAE,KAAK;SACtB,CAAC;KACL;AAED;;;;;AAKG;AACO,IAAA,MAAM,sBAAsB,CAClC,eAAuB,EACvB,OAA4B,EAAA;QAE5B,IAAI,CAAC,eAAe,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,8EAA8E,CACjF,CAAC;AACF,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;AACpE,SAAA;;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC;YACnD,eAAe;AAClB,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE;AACV,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;AACpE,SAAA;;QAGD,IAAI;YACA,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAC/C,OAAO,EACP,OAAO,CACV,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACpD,aAAa,CAChB,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG;AAChB,gBAAA,GAAG,OAAO;gBACV,aAAa,EAAE,MAAM,EAAE,aAA4B;gBACnD,OAAO,EAAE,MAAM,EAAE,OAAO;aAC3B,CAAC;YAEF,OAAO;AACH,gBAAA,GAAG,MAAM;AACT,gBAAA,OAAO,EAAE,WAAW;aACvB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,MAAM,oBAAoB,CACtB,OAAwB,EACxB,eAA2C,EAAA;AAE3C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wDAAwD,CAC3D,CAAC;AAEF,QAAA,MAAM,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QAC3C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC;QAE9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACpD,mBAAmB,CACtB,CAAC;QAEF,IAAI;YACA,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAC9D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;YAER,IAAI,CAAC,YAAY,eAAe,EAAE;gBAC9B,MAAM,sBAAsB,GACxB,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC7D,gBAAA,IAAI,sBAAsB,CAAC,CAAC,CAAC,EAAE;AAC3B,oBAAA,MAAM,CAAC,CAAC;AACX,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,cAAc,EACjC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAC7B,IAAI,CACP,CAAC;AAEF,QAAA,MAAM,iBAAiB,GAAsB;YACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;AACjC,YAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,YAAA,SAAS,EAAE,KAAK;SACnB,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB;AAC1D,cAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;cACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/C,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AACvC,QAAA,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,WAAW,EACX,iBAAiB,CACpB,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,MAAM,qBAAqB,CACvB,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,yDAAyD,CAC5D,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,uFAAuF,CAC1F,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACnE,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wGAAwG,CAC3G,CAAC;YACF,IAAI,iBAAiB,IAAI,aAAa,EAAE;gBACpC,iBAAiB,EAAE,SAAS,CACxB,EAAE,SAAS,EAAE,mBAAmB,EAAE,EAClC,aAAa,CAChB,CAAC;AACL,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,aAAa,CAAC;AAC7C,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sMAAsM,CACzM,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAChC,kBAAkB,CAAC,cAAc,CACpC,CACJ,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAE5C,IAAI;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mFAAmF,CACtF,CAAC;YACF,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACzD,YAAA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAC9C,QAAQ,EACR,OAAO,EACP,YAAY,CACf,CAAC;YAEF,MAAM,sBAAsB,GACxB,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,sBAAsB,CAAC,0BAA0B,EAAE,CAAC;AACpD,YAAA,OAAO,UAAU,CAAC;AACrB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC9D,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;KACvD;AAED;;;;;AAKG;AACO,IAAA,MAAM,oBAAoB,CAChC,QAA8B,EAC9B,OAA4B,EAC5B,YAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wDAAwD,CAC3D,CAAC;;AAGF,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAC9C,QAAQ,CAAC,QAAQ,EACjB,YAAY,CACf,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,CAAC,2BAA2B,CAC1D,QAAQ,EACR,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,mBAAmB,GACrB,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC;YACzC,eAAe,EAAE,OAAO,CAAC,SAAS;SACrC,CAAC,EAAE,aAAa,CAAC;;AAGtB,QAAA,IACI,OAAO,CAAC,eAAe,EAAE,eAAe;YACxC,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,EAC3C;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,sFAAsF,CACzF,CAAC;AACL,SAAA;aAAM,IACH,qBAAqB,KAAK,mBAAmB;YAC7C,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,EAC3C;;AAEE,YAAA,MAAM,qBAAqB,CAACA,UAA+B,CAAC,CAAC;AAChE,SAAA;;AAGD,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAChD,gBAAgB,EAAE,OAAO,CAAC,SAAS;AACtC,SAAA,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,mBAAmB,CACnC,IAAI,CAAC,cAAc,EACnB,SAAS,EACT,qBAAqB,EACrB,YAAY,EACZ,aAAa,EACb,QAAQ,CAAC,WAAW,EACpB,SAAS;AACT,QAAA,aAAa,CAAC,GAAG,EACjB,SAAS;QACT,QAAQ,CAAC,OAAO,CAAC,EAAE,EACnB,IAAI,CAAC,MAAM,CACd,CAAC;;QAGF,QAAQ,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;;QAGlD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAClD,QAAQ,EACR,OAAO,EACP,aAAa,EACb,WAAW,EACX,SAAS,CAAC,kBAAkB,EAC5B,YAAY,CACf,CAAC;;AAGF,QAAA,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,iBAAiB,CACxB,QAAQ,EACR,OAAO,EACP,qBAAqB,EACrB,aAAa,EACb,QAAQ,CAAC,YAAY,EACrB,MAAM,CAAC,QAAQ,EACf,YAAY,CACf,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;;AAKG;IACO,2BAA2B,CACjC,QAA8B,EAC9B,aAA0B,EAAA;;AAG1B,QAAA,MAAM,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAC7D,QAAQ,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,EAC9C,aAAa,CAAC,OAAO,EACrB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,EAClB,aAAa,CAChB,CAAC;AAEF,QAAA,OAAO,qBAAqB,CAAC;KAChC;AAED;;;;;AAKG;IACH,cAAc,CAAC,aAAqB,EAAE,cAAuB,EAAA;AACzD,QAAA,OAAO,cAAc;AACjB,cAAE,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC;AACrC,cAAE,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAC5C;AAED;;;;AAIG;AACH,IAAA,MAAM,sBAAsB,CACxB,QAA8B,EAC9B,OAA4B,EAAA;AAE5B,QAAA,IACI,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;YAC9C,OAAO,CAAC,YAAY,EACtB;AACE;;;AAGG;;YAGH,IAAI,QAAQ,CAAC,GAAG,EAAE;AACd,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,4DAA4D,CAC/D,CAAC;gBACF,OAAO,QAAQ,CAAC,GAAG,CAAC;AACvB,aAAA;;YAGD,MAAM,iBAAiB,GAAsB,IAAI,iBAAiB,CAC9D,IAAI,CAAC,aAAa,CACrB,CAAC;AACF,YAAA,MAAM,aAAa,GAAgC;gBAC/C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC7B,CAAC;AAEF;;;AAGG;AACH,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAChB,gBAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAClE,aAAA;AACD,YAAA,OAAO,iBAAiB,CAAC,YAAY,CACjC,QAAQ,CAAC,YAAY,EACrB,OAAO,CAAC,KAAK,EACb,aAAa,CAChB,CAAC;AACL,SAAA;AAAM,aAAA;YACH,OAAO,QAAQ,CAAC,YAAY,CAAC;AAChC,SAAA;KACJ;AAED;;;;;;;;;AASG;AACO,IAAA,MAAM,4BAA4B,CACxC,QAA8B,EAC9B,OAA4B,EAC5B,aAA0B,EAC1B,aAA4B,EAC5B,SAAiB,EACjB,YAAoB,EAAA;;AAGpB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,8BAA8B,CAC5C,QAAQ,CAAC,UAAU,CAAC,IAAI,CAC3B,CAAC;;AAGF,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CACtC,OAAO,CAAC,KAAK,EACb,QAAQ,CAAC,KAAK,CACjB,CAAC;QAEF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;AAC5D,QAAA,MAAM,GAAG,GACL,iBAAiB,CAAC,KAAK,CAAC;AACxB,YAAA,aAAa,CAAC,GAAG;AACjB,YAAA,aAAa,CAAC,GAAG;YACjB,SAAS,CAAC,YAAY,CAAC;AAC3B,QAAA,MAAM,GAAG,GACL,iBAAiB,CAAC,UAAU,CAAC;AAC7B,YAAA,aAAa,CAAC,GAAG;YACjB,SAAS,CAAC,YAAY,CAAC;QAE3B,MAAM,WAAW,GAAuB,8BAA8B,CAClE,aAAa,CAAC,cAAc,EAAE,EAC9B,SAAS;AACT,QAAA,aAAa,EACb,QAAQ,CAAC,QAAQ,CACpB,CAAC;AAEF;;;AAGG;QACH,IAAI,WAAW,CAAC,eAAe,KAAK,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE;YACrD,WAAW,CAAC,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;AACrD,SAAA;;QAGD,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACzD,QAAQ,EACR,OAAO,CACV,CAAC;QACF,MAAM,SAAS,GACX,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACxC,oBAAoB,CAAC,GAAG;AAC1B,cAAE,oBAAoB,CAAC,MAAM,CAAC;AAEtC,QAAA,MAAM,MAAM,GAAyB;AACjC,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,QAAQ,EAAE,GAAG;AACb,YAAA,MAAM,EAAE,cAAc,CAAC,OAAO,EAAE;AAChC,YAAA,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAC1B,YAAA,aAAa,EAAE,aAAa;AAC5B,YAAA,WAAW,EAAE,mBAAmB;AAChC,YAAA,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,KAAK;;YAExD,SAAS,EAAE,SAAS,CAAC,iBAAiB,CAClC,YAAY,GAAG,QAAQ,CAAC,UAAU,CACrC;AACD,YAAA,SAAS,EAAE,SAAS;YACpB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,KAAK,EAAE,QAAQ,CAAC,KAAK;AACrB,YAAA,gBAAgB,EAAE,IAAI;SACzB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;IACH,MAAM,YAAY,CAAC,aAA4B,EAAA;;AAE3C,QAAA,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;;AAGxE,QAAA,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAI;YAChE,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAuE,oEAAA,EAAA,CAAC,CAAE,CAAA,CAC7E,CAAC;AACN,SAAC,CAAC,CAAC;KACN;AAED;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CACb,QAA8B,EAC9B,OAA4B,EAC5B,qBAA6B,EAC7B,aAA0B,EAC1B,mBAA2B,EAC3B,QAAgB,EAChB,YAAoB,EAAA;AAEpB,QAAA,MAAM,aAAa,GACf,YAAY,CAAC,mBAAmB,CAC5B,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,QAAQ,CAAC,QAAQ,IAAI,EAAE,EACvB,OAAO,CAAC,QAAQ,EAChB,aAAa,CAAC,GAAG,IAAI,EAAE,CAC1B,CAAC;;QAGN,MAAM,SAAS,GACX,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACxC,SAAS,CAAC,kBAAkB;AAC9B,cAAE,CAAC,OAAO,QAAQ,CAAC,UAAU,KAAK,QAAQ;kBAClC,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC;AACnC,kBAAE,QAAQ,CAAC,UAAU,KAAK,CAAC,CAAC;AAC1C,QAAA,MAAM,sBAAsB,GAAG,YAAY,GAAG,SAAS,CAAC;AACxD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CACtC,QAAQ,CAAC,KAAK,EACd,OAAO,CAAC,KAAK,CAChB,CAAC;QAEF,MAAM,iBAAiB,GACnB,YAAY,CAAC,uBAAuB,CAChC,qBAAqB,EACrB,OAAO,CAAC,SAAS,EACjB,mBAAmB,EACnB,OAAO,CAAC,QAAQ,EAChB,aAAa,CAAC,GAAG,IAAI,QAAQ,EAC7B,cAAc,CAAC,WAAW,EAAE,EAC5B,sBAAsB,EACtB,CAAC,EACD,YAAY,EACZ,SAAS,EACT,OAAO,CAAC,SAAiC,EACzC,SAAS,EACT,OAAO,CAAC,KAAK,CAChB,CAAC;AAEN,QAAA,MAAM,iBAAiB,GAAG;AACtB,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,WAAW,EAAE,iBAAiB;SACjC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAC5C,iBAAiB,EACjB,IAAI,CAAC,aAAa,EAClB,OAAO,CAAC,YAAY,CACvB,CAAC;KACL;IAED,iBAAiB,CACb,SAAiB,EACjB,SAAsC,EAAA;AAEtC,QAAA,OAAO,SAAS,KAAK,oBAAoB,CAAC,GAAG;cACvC,SAAS,CAAC,kBAAkB;AAC9B,cAAE,CAAC,OAAO,SAAS,KAAK,QAAQ;AAC1B,kBAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC;AACzB,kBAAE,SAAS,KAAK,CAAC,CAAC;KAC/B;AAES,IAAA,8BAA8B,CACpC,YAAqB,EAAA;QAErB,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAEpD,IAAI,CAAC,IAAI,EAAE;AACP,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B;AACI,YAAA,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE;AACvD,YAAA,gBAAgB,EACZ,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE;YACnD,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB;YAClD,oBAAoB,EAAE,IAAI,CAAC,mBAAmB;YAC9C,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,eAAe;YACxC,gBAAgB,EAAE,IAAI,CAAC,cAAc;YACrC,aAAa,EAAE,IAAI,CAAC,UAAU;YAC9B,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;YAC5C,iBAAiB,EAAE,IAAI,CAAC,cAAc;YACtC,gBAAgB,EAAE,IAAI,CAAC,aAAa;YACpC,cAAc,EAAE,IAAI,CAAC,WAAW;YAChC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB;AAC5C,SAAA,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,YAAgC,EAAA;AACxD,QAAA,IAAI,YAAY,EAAE;YACd,IAAI;AACA,gBAAA,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACnC,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,gFAAgF,CACnF,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACO,IAAA,mBAAmB,CAAC,IAAU,EAAA;AACpC,QAAA,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gIAAgI,CACnI,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KAC3B;AAED;;;AAGG;IACO,MAAM,uBAAuB,CACnC,OAAwC,EAAA;AAExC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,0DAA0D,CAC7D,CAAC;QAEF,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;;QAGrE,MAAM,EAAE,MAAM,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAC5C,QAAA,QAAQ,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;AAE3C,QAAA,MAAM,gBAAgB,GAAwB;AAC1C,YAAA,GAAG,mBAAmB;YACtB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACnC,SAAS,EAAE,kBAAkB,CAAC,SAAS;AACvC,YAAA,KAAK,EAAE,QAAQ,CAAC,WAAW,EAAE;YAC7B,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC;YACrD,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,OAAO,CAAC,oBAAoB;YACvC,oBAAoB,EAAE,QAAQ,CAAC,KAAK;AACpC,YAAA,eAAe,EAAE;gBACb,GAAG,OAAO,CAAC,oBAAoB;gBAC/B,GAAG,OAAO,CAAC,oBAAoB;AAClC,aAAA;AACD,YAAA,mBAAmB,EAAE,KAAK;YAC1B,KAAK,EAAE,OAAO,CAAC,MAAM;SACxB,CAAC;;QAGF,IAAI,gBAAgB,CAAC,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;AACnD,YAAA,MAAM,sBAAsB,CACxBC,sBAA4C,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;AAC/C,QAAA,gBAAgB,CAAC,eAAe;AAC5B,YAAA,gBAAgB,CAAC,eAAe,IAAI,EAAE,CAAC;QAC3C,gBAAgB,CAAC,eAAe,CAAC,SAAS;YACtC,qBAAqB,CAAC,cAAc,CAAC;AAEzC,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;;AAE3D,YAAA,MAAM,aAAa,GAAgC;gBAC/C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC7B,CAAC;YAEF,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;AAGpE,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AACzB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AAC9C,gBAAA,gBAAgB,CAAC,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC;AACjD,gBAAA,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;AACxC,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAC3C,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAClD,CAAC;AACF,gBAAA,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;AACzC,aAAA;;AAGD,YAAA,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;AACxC,SAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;AAEtC,QAAA,OAAO,gBAAgB,CAAC;KAC3B;IAEO,MAAM,qBAAqB,CAC/B,OAAwC,EAAA;AAExC,QAAA,MAAM,gBAAgB,GAClB,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QAEpD,IAAI,OAAO,CAAC,OAAO,EAAE;;YAEjB,MAAM,IAAI,CAAC,sBAAsB,CAAC;gBAC9B,gBAAgB;gBAChB,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;gBACnD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,MAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3D,kBAAkB,CAAC,aAAa,EAAE,CAAC;AACnC,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAEO,IAAA,SAAS,CAAC,MAAe,EAAA;;QAE7B,QAAQ,IAAI,CAAC,KAAK;YACd,KAAK,KAAK,CAAC,SAAS,CAAC;YACrB,KAAK,KAAK,CAAC,6BAA6B;AACpC,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,6DAA6D,CAChE,CAAC;gBACF,OAAO,WAAW,CAAC,IAAI,CAAC;AAG/B,SAAA;;QAGD,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,kDAAkD,CACrD,CAAC;AACF,YAAA,OAAO,SAAS,CAAC;AACpB,SAAA;;AAGD,QAAA,QAAQ,MAAM;YACV,KAAK,WAAW,CAAC,IAAI,CAAC;YACtB,KAAK,WAAW,CAAC,OAAO,CAAC;YACzB,KAAK,WAAW,CAAC,KAAK;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,gEAAgE,CACnE,CAAC;AACF,gBAAA,OAAO,MAAM,CAAC;AAClB,YAAA;gBACI,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAqC,kCAAA,EAAA,MAAM,CAAqC,mCAAA,CAAA,CACnF,CAAC;AACF,gBAAA,MAAM,sBAAsB,CACxBC,wBAA8C,CACjD,CAAC;AACT,SAAA;KACJ;AAED;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,OAA4B,EAAA;AACxD,QAAA,MAAM,oBAAoB,GACtB,OAAO,CAAC,eAAe;YACvB,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,gBAAgB,CACtC;YACD,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,mBAAmB,CACzC;YACD,OAAO,CAAC,eAAe,CAAC,cAAc,CAClC,kBAAkB,CAAC,SAAS,CAC/B,CAAC;AAEN,QAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,oBAAoB,EAAE;YACpD,OAAO;AACV,SAAA;QAED,IAAI,eAAe,GAAW,EAAE,CAAC;AACjC,QAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,WAAW,CAAC;QAE/C,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AACnD,YAAA,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAC9C,SAAA;aAAM,IAAI,OAAO,CAAC,eAAe,EAAE;AAChC,YAAA,OAAO,CAAC,WAAW;AACf,gBAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;YACpE,eAAe;AACX,gBAAA,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC7D,SAAA;QAED,OAAO,CAAC,eAAe,GAAG;YACtB,eAAe;YACf,kBAAkB;SACrB,CAAC;AAEF,QAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;AACI,YAAA,gBAAgB,EAAE,eAAe;AACjC,YAAA,mBAAmB,EAAE,kBAAkB;AAC1C,SAAA,EACD,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;AACJ;;;;"}