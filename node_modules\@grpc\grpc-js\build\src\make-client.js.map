{"version": 3, "file": "make-client.js", "sourceRoot": "", "sources": ["../../src/make-client.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAwGH,sDA2DC;AAgCD,sDA2BC;AA1ND,qCAAkC;AAmDlC;;;;GAIG;AACH,MAAM,cAAc,GAAG;IACrB,KAAK,EAAE,eAAM,CAAC,SAAS,CAAC,gBAAgB;IACxC,aAAa,EAAE,eAAM,CAAC,SAAS,CAAC,uBAAuB;IACvD,aAAa,EAAE,eAAM,CAAC,SAAS,CAAC,uBAAuB;IACvD,IAAI,EAAE,eAAM,CAAC,SAAS,CAAC,qBAAqB;CAC7C,CAAC;AAgBF;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,GAAW;IACtC,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,qBAAqB,CACnC,OAA0B,EAC1B,WAAmB,EACnB,YAAiB;IAEjB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,YAAY,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,MAAM,iBAAkB,SAAQ,eAAM;KAIrC;IAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAClC,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,UAAuC,CAAC;QAC5C,6DAA6D;QAC7D,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACzB,UAAU,GAAG,MAAM,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,eAAe,CAAC;YAC/B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACzB,UAAU,GAAG,eAAe,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,OAAO,CAAC;YACvB,CAAC;QACH,CAAC;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,gBAAgB,CAAC;QACzC,MAAM,WAAW,GAAG,KAAK,CAAC,mBAAmB,CAAC;QAC9C,MAAM,UAAU,GAAG,OAAO,CACxB,cAAc,CAAC,UAAU,CAAC,EAC1B,KAAK,CAAC,IAAI,EACV,SAAS,EACT,WAAW,CACZ,CAAC;QACF,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;QAC/C,oDAAoD;QACpD,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC,YAAY,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACnE,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC;gBAC7C,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,iBAAiB,CAAC,OAAO,GAAG,OAAO,CAAC;IACpC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;IAE5C,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,OAAO,CACd,EAAY,EACZ,IAAY,EACZ,SAAmB,EACnB,WAAqB;IAErB,8DAA8D;IAC9D,OAAO,UAAqB,GAAG,IAAW;QACxC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9D,CAAC,CAAC;AACJ,CAAC;AASD,SAAS,wBAAwB,CAC/B,GAA+C;IAE/C,OAAO,QAAQ,IAAI,GAAG,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CACnC,UAA6B;IAE7B,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACrE,SAAS;YACX,CAAC;YACD,MAAM,WAAW,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC9D,IAAI,OAAO,GAAG,MAAM,CAAC;YACrB,KAAK,MAAM,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC1B,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;gBAC5B,CAAC;gBACD,OAAO,GAAG,OAAO,CAAC,WAAW,CAAe,CAAC;YAC/C,CAAC;YACD,IAAI,wBAAwB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,WAAW,CAAC,GAAG,qBAAqB,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}