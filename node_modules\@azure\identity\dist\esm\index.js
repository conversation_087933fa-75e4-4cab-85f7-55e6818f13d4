// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export * from "./plugins/consumer.js";
import { DefaultAzureCredential } from "./credentials/defaultAzureCredential.js";
export { AuthenticationError, AggregateAuthenticationError, AuthenticationErrorName, AggregateAuthenticationErrorName, CredentialUnavailableError, CredentialUnavailableErrorName, AuthenticationRequiredError, } from "./errors.js";
export { serializeAuthenticationRecord, deserializeAuthenticationRecord } from "./msal/utils.js";
export { ChainedTokenCredential } from "./credentials/chainedTokenCredential.js";
export { ClientSecretCredential } from "./credentials/clientSecretCredential.js";
export { DefaultAzureCredential } from "./credentials/defaultAzureCredential.js";
export { EnvironmentCredential } from "./credentials/environmentCredential.js";
export { ClientCertificateCredential } from "./credentials/clientCertificateCredential.js";
export { ClientAssertionCredential } from "./credentials/clientAssertionCredential.js";
export { AzureCliCredential } from "./credentials/azureCliCredential.js";
export { AzureDeveloperCliCredential } from "./credentials/azureDeveloperCliCredential.js";
export { InteractiveBrowserCredential } from "./credentials/interactiveBrowserCredential.js";
export { ManagedIdentityCredential } from "./credentials/managedIdentityCredential/index.js";
export { DeviceCodeCredential } from "./credentials/deviceCodeCredential.js";
export { AzurePipelinesCredential as AzurePipelinesCredential } from "./credentials/azurePipelinesCredential.js";
export { AuthorizationCodeCredential } from "./credentials/authorizationCodeCredential.js";
export { AzurePowerShellCredential } from "./credentials/azurePowerShellCredential.js";
export { UsernamePasswordCredential } from "./credentials/usernamePasswordCredential.js";
export { VisualStudioCodeCredential } from "./credentials/visualStudioCodeCredential.js";
export { OnBehalfOfCredential } from "./credentials/onBehalfOfCredential.js";
export { WorkloadIdentityCredential } from "./credentials/workloadIdentityCredential.js";
export { logger } from "./util/logging.js";
export { AzureAuthorityHosts } from "./constants.js";
/**
 * Returns a new instance of the {@link DefaultAzureCredential}.
 */
export function getDefaultAzureCredential() {
    return new DefaultAzureCredential();
}
export { getBearerTokenProvider } from "./tokenProvider.js";
//# sourceMappingURL=index.js.map