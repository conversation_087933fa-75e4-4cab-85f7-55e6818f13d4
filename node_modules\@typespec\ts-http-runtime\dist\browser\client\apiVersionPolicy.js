// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export const apiVersionPolicyName = "ApiVersionPolicy";
/**
 * Creates a policy that sets the apiVersion as a query parameter on every request
 * @param options - Client options
 * @returns Pipeline policy that sets the apiVersion as a query parameter on every request
 */
export function apiVersionPolicy(options) {
    return {
        name: apiVersionPolicyName,
        sendRequest: (req, next) => {
            // Use the apiVesion defined in request url directly
            // Append one if there is no apiVesion and we have one at client options
            const url = new URL(req.url);
            if (!url.searchParams.get("api-version") && options.apiVersion) {
                req.url = `${req.url}${Array.from(url.searchParams.keys()).length > 0 ? "&" : "?"}api-version=${options.apiVersion}`;
            }
            return next(req);
        },
    };
}
//# sourceMappingURL=apiVersionPolicy.js.map