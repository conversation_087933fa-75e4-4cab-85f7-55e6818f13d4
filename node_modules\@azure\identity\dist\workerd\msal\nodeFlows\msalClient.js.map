{"version": 3, "file": "msalClient.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AAKzC,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAExE,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EACL,qBAAqB,EACrB,oBAAoB,EACpB,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,YAAY,GACb,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAG9D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAGhE,OAAO,EAAE,0BAA0B,EAAE,MAAM,4BAA4B,CAAC;AACxE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D;;GAEG;AACH,MAAM,UAAU,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAiNlD;;;;;;;GAOG;AACH,MAAM,UAAU,yBAAyB,CACvC,QAAgB,EAChB,QAAgB,EAChB,oBAAuC,EAAE;;IAEzC,MAAM,cAAc,GAAG,eAAe,CACpC,MAAA,iBAAiB,CAAC,MAAM,mCAAI,UAAU,EACtC,QAAQ,EACR,QAAQ,CACT,CAAC;IAEF,sDAAsD;IACtD,MAAM,SAAS,GAAG,YAAY,CAAC,cAAc,EAAE,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAEpF,MAAM,UAAU,GAAG,IAAI,cAAc,iCAChC,iBAAiB,CAAC,sBAAsB,KAC3C,aAAa,EAAE,SAAS,EACxB,cAAc,EAAE,iBAAiB,CAAC,cAAc,IAChD,CAAC;IAEH,MAAM,UAAU,GAAuB;QACrC,IAAI,EAAE;YACJ,QAAQ;YACR,SAAS;YACT,gBAAgB,EAAE,mBAAmB,CACnC,cAAc,EACd,SAAS,EACT,iBAAiB,CAAC,wBAAwB,CAC3C;SACF;QACD,MAAM,EAAE;YACN,aAAa,EAAE,UAAU;YACzB,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB,CAAC,MAAA,iBAAiB,CAAC,MAAM,mCAAI,UAAU,CAAC;gBAC7E,QAAQ,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;gBACxC,iBAAiB,EAAE,MAAA,iBAAiB,CAAC,cAAc,0CAAE,0BAA0B;aAChF;SACF;KACF,CAAC;IACF,OAAO,UAAU,CAAC;AACpB,CAAC;AAyBD;;;;;;;;;GASG;AACH,MAAM,UAAU,gBAAgB,CAC9B,QAAgB,EAChB,QAAgB,EAChB,0BAA6C,EAAE;;IAE/C,MAAM,KAAK,GAAoB;QAC7B,UAAU,EAAE,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,EAAE,uBAAuB,CAAC;QAClF,aAAa,EAAE,uBAAuB,CAAC,oBAAoB;YACzD,CAAC,CAAC,YAAY,CAAC,uBAAuB,CAAC,oBAAoB,CAAC;YAC5D,CAAC,CAAC,IAAI;QACR,mBAAmB,EAAE,WAAW,CAAC,2BAA2B,CAAC,uBAAuB,CAAC;QACrF,MAAM,EAAE,MAAA,uBAAuB,CAAC,MAAM,mCAAI,UAAU;KACrD,CAAC;IAEF,MAAM,UAAU,GAA8C,IAAI,GAAG,EAAE,CAAC;IACxE,KAAK,UAAU,YAAY,CACzB,UAA2B,EAAE;QAE7B,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAErD,IAAI,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YAC7F,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,oCAAoC;QACpC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CACxB,iDAAiD,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,GAAG,CAC/F,CAAC;QAEF,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS;YACnC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,cAAc;YAChD,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,CAAC;QAEhD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEnF,eAAe,GAAG,IAAI,IAAI,CAAC,uBAAuB,iCAC7C,KAAK,CAAC,UAAU,KACnB,MAAM,EAAE,EAAE,kBAAkB,EAAE,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,EAAE,EACnF,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,WAAW,EAAE,IACzC,CAAC;QAEH,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAExC,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,MAAM,gBAAgB,GAAoD,IAAI,GAAG,EAAE,CAAC;IACpF,KAAK,UAAU,kBAAkB,CAC/B,UAA2B,EAAE;QAE7B,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAErD,IAAI,qBAAqB,GAAG,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,qBAAqB,EAAE,CAAC;YAC1B,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CACxB,sEAAsE,CACvE,CAAC;YACF,OAAO,qBAAqB,CAAC;QAC/B,CAAC;QAED,oCAAoC;QACpC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CACxB,uDACE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAClC,GAAG,CACJ,CAAC;QAEF,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS;YACnC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,cAAc;YAChD,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,CAAC;QAEhD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEnF,qBAAqB,GAAG,IAAI,IAAI,CAAC,6BAA6B,iCACzD,KAAK,CAAC,UAAU,KACnB,MAAM,EAAE,EAAE,kBAAkB,EAAE,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,EAAE,EACnF,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,WAAW,EAAE,IACzC,CAAC;QAEH,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAEpD,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAED,KAAK,UAAU,cAAc,CAC3B,GAAsE,EACtE,MAAgB,EAChB,UAA2B,EAAE;QAE7B,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YACjC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACtE,MAAM,IAAI,2BAA2B,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,gEAAgE;QAChE,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACtC,CAAC;QAED,MAAM,aAAa,GAA2B;YAC5C,OAAO,EAAE,KAAK,CAAC,aAAa;YAC5B,MAAM;YACN,MAAM,EAAE,KAAK,CAAC,YAAY;SAC3B,CAAC;QAEF,IAAI,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC/C,aAAa,CAAC,oBAAoB,KAAlC,aAAa,CAAC,oBAAoB,GAAK,EAAE,EAAC;YAC1C,IAAI,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC1D,aAAa,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,GAAG,sBAAsB,CAAC;YACnF,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACrC,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC;YAChE,aAAa,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAC3C,aAAa,CAAC,qBAAqB,GAAG,OAAO,CAAC,wBAAwB,CAAC,qBAAqB,CAAC;YAC7F,aAAa,CAAC,kBAAkB,GAAG,OAAO,CAAC,wBAAwB,CAAC,kBAAkB,CAAC;QACzF,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACnE,IAAI,CAAC;YACH,OAAO,MAAM,GAAG,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,SAAS,yBAAyB,CAAC,OAAyB;QAC1D,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE,CAAC;YACtB,OAAO,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC;QACnF,CAAC;QACD,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,UAAU,wBAAwB,CACrC,OAA0E,EAC1E,MAAqB,EACrB,OAAsC,EACtC,wBAAyE;;QAEzE,IAAI,QAAQ,GAAqC,IAAI,CAAC;QACtD,IAAI,CAAC;YACH,QAAQ,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,6BAA6B,EAAE,CAAC;gBAC7C,MAAM,CAAC,CAAC;YACV,CAAC;YACD,IAAI,OAAO,CAAC,8BAA8B,EAAE,CAAC;gBAC3C,MAAM,IAAI,2BAA2B,CAAC;oBACpC,MAAM;oBACN,eAAe,EAAE,OAAO;oBACxB,OAAO,EACL,uFAAuF;iBAC1F,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC;gBACH,QAAQ,GAAG,MAAM,wBAAwB,EAAE,CAAC;YAC9C,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC,aAAa,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,mCAAI,IAAI,CAAC;QAEhD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAClD,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,WAAW;YAC3B,kBAAkB,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;YAChD,qBAAqB,EAAE,MAAA,QAAQ,CAAC,SAAS,0CAAE,OAAO,EAAE;YACpD,SAAS,EAAE,QAAQ,CAAC,SAAS;SACf,CAAC;IACnB,CAAC;IAED,KAAK,UAAU,sBAAsB,CACnC,MAAgB,EAChB,YAAoB,EACpB,UAA2B,EAAE;;QAE7B,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAE9E,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAElD,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,8BAA8B,CAAC;gBAC5D,MAAM;gBACN,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,WAAW,EAAE,0BAA0B,EAAE;gBACzC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC,CAAC;YACH,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAChD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAClD,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,WAAW;gBAC3B,kBAAkB,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;gBAChD,qBAAqB,EAAE,MAAA,QAAQ,CAAC,SAAS,0CAAE,OAAO,EAAE;gBACpD,SAAS,EAAE,QAAQ,CAAC,SAAS;aACf,CAAC;QACnB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,UAAU,yBAAyB,CACtC,MAAgB,EAChB,eAAsC,EACtC,UAA2B,EAAE;;QAE7B,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAEjF,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QAExD,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,8BAA8B,CAAC;gBAC5D,MAAM;gBACN,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,WAAW,EAAE,0BAA0B,EAAE;gBACzC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;gBACvB,eAAe;aAChB,CAAC,CAAC;YACH,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEhD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAClD,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,WAAW;gBAC3B,kBAAkB,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;gBAChD,qBAAqB,EAAE,MAAA,QAAQ,CAAC,SAAS,0CAAE,OAAO,EAAE;gBACpD,SAAS,EAAE,QAAQ,CAAC,SAAS;aACf,CAAC;QACnB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,UAAU,2BAA2B,CACxC,MAAgB,EAChB,WAA6B,EAC7B,UAA2B,EAAE;;QAE7B,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAEnF,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAEtD,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,8BAA8B,CAAC;gBAC5D,MAAM;gBACN,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,WAAW,EAAE,0BAA0B,EAAE;gBACzC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC,CAAC;YACH,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEhD,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAClD,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,WAAW;gBAC3B,kBAAkB,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;gBAChD,qBAAqB,EAAE,MAAA,QAAQ,CAAC,SAAS,0CAAE,OAAO,EAAE;gBACpD,SAAS,EAAE,QAAQ,CAAC,SAAS;aACf,CAAC;QACnB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,UAAU,oBAAoB,CACjC,MAAgB,EAChB,kBAA4C,EAC5C,UAAyC,EAAE;QAE3C,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAE5E,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC;QAE5C,OAAO,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE;;YAC7D,MAAM,cAAc,GAA2B;gBAC7C,MAAM;gBACN,MAAM,EAAE,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,0CAAE,OAAO,mCAAI,KAAK;gBAC9C,kBAAkB;gBAClB,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC;YACF,MAAM,iBAAiB,GAAG,OAAO,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;YAC3E,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBACjD,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC/B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,iBAAiB,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,UAAU,0BAA0B,CACvC,MAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,UAA2B,EAAE;QAE7B,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAEtF,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC;QAE5C,OAAO,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE;YAC7D,MAAM,cAAc,GAAiC;gBACnD,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC;YAEF,OAAO,OAAO,CAAC,8BAA8B,CAAC,cAAc,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,gBAAgB;QACvB,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACzB,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,UAAU,2BAA2B,CACxC,MAAgB,EAChB,WAAmB,EACnB,iBAAyB,EACzB,YAAqB,EACrB,UAAyC,EAAE;QAE3C,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAEnF,IAAI,OAA0E,CAAC;QAC/E,IAAI,YAAY,EAAE,CAAC;YACjB,mFAAmF;YACnF,gIAAgI;YAChI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YAClD,OAAO,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE;YAC7D,OAAO,OAAO,CAAC,kBAAkB,CAAC;gBAChC,MAAM;gBACN,WAAW;gBACX,IAAI,EAAE,iBAAiB;gBACvB,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;aACxB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,UAAU,kBAAkB,CAC/B,MAAgB,EAChB,kBAA0B,EAC1B,iBAAsE,EACtE,UAA2B,EAAE;;QAE7B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAElF,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;YAC1C,gBAAgB;YAChB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACtE,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,iBAAiB,CAAC;QACzD,CAAC;aAAM,IAAI,OAAO,iBAAiB,KAAK,UAAU,EAAE,CAAC;YACnD,mBAAmB;YACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAClF,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,qBAAqB;YACrB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC3E,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC9D,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC;gBACpD,MAAM;gBACN,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,kBAAkB;aACjC,CAAC,CAAC;YACH,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEhD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,OAAO;gBACL,KAAK,EAAE,QAAQ,CAAC,WAAW;gBAC3B,kBAAkB,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;gBAChD,qBAAqB,EAAE,MAAA,QAAQ,CAAC,SAAS,0CAAE,OAAO,EAAE;gBACpD,SAAS,EAAE,QAAQ,CAAC,SAAS;aACf,CAAC;QACnB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,KAAK,UAAU,4BAA4B,CACzC,MAAgB,EAChB,UAAsC,EAAE;QAExC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAEtE,MAAM,GAAG,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC;QAExC;;;;;WAKG;QACH,KAAK,UAAU,gBAAgB,CAC7B,uBAAgC;;YAEhC,UAAU,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;YACpE,MAAM,kBAAkB,GAAG,4BAA4B,EAAE,CAAC;YAC1D,IAAI,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACxD,kBAAkB,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAC3C,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CACpD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,+EAA+E;gBAC/E,UAAU,CAAC,OAAO,CAChB,kIAAkI,CACnI,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC1D,OAAC,kBAAkB,CAAC,oBAAoB,oCAAvC,kBAAkB,CAAC,oBAAoB,GAAK,EAAE,EAAC,CAAC,mBAAmB,CAAC;oBACnE,sBAAsB,CAAC;YAC3B,CAAC;YACD,IAAI,uBAAuB,EAAE,CAAC;gBAC5B,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC;gBACnC,UAAU,CAAC,OAAO,CAAC,mEAAmE,CAAC,CAAC;YAC1F,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,OAAO,CAAC,qEAAqE,CAAC,CAAC;YAC5F,CAAC;YAED,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;gBACrC,kBAAkB,CAAC,QAAQ,GAAG,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC;gBACrE,kBAAkB,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBAChD,kBAAkB,CAAC,qBAAqB;oBACtC,OAAO,CAAC,wBAAwB,CAAC,qBAAqB,CAAC;gBACzD,kBAAkB,CAAC,kBAAkB,GAAG,OAAO,CAAC,wBAAwB,CAAC,kBAAkB,CAAC;YAC9F,CAAC;YACD,IAAI,CAAC;gBACH,OAAO,MAAM,GAAG,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAC/D,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,UAAU,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9E,oGAAoG;gBACpG,IAAI,uBAAuB,EAAE,CAAC;oBAC5B,OAAO,gBAAgB,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,4BAA4B;;YACnC,OAAO;gBACL,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;oBACzB,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;oBAClC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;gBACD,MAAM;gBACN,SAAS,EAAE,yBAAyB,CAAC,OAAO,CAAC;gBAC7C,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;gBACvB,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS;gBAC7B,aAAa,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,2BAA2B,0CAAE,YAAY;gBACjE,eAAe,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,2BAA2B,0CAAE,cAAc;gBACrE,MAAM,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB;aACxD,CAAC;QACJ,CAAC;QAED,OAAO,wBAAwB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;;YAC/D,MAAM,kBAAkB,GAAG,4BAA4B,EAAE,CAAC;YAE1D,IAAI,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC/C,OAAO,gBAAgB,CAAC,MAAA,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,uBAAuB,mCAAI,KAAK,CAAC,CAAC;YAC7F,CAAC;YACD,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;gBACrC,kBAAkB,CAAC,QAAQ,GAAG,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC;gBACrE,kBAAkB,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBAChD,kBAAkB,CAAC,qBAAqB;oBACtC,OAAO,CAAC,wBAAwB,CAAC,qBAAqB,CAAC;gBACzD,kBAAkB,CAAC,kBAAkB,GAAG,OAAO,CAAC,wBAAwB,CAAC,kBAAkB,CAAC;YAC9F,CAAC;YACD,OAAO,GAAG,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,gBAAgB;QAChB,sBAAsB;QACtB,yBAAyB;QACzB,2BAA2B;QAC3B,oBAAoB;QACpB,0BAA0B;QAC1B,2BAA2B;QAC3B,kBAAkB;QAClB,4BAA4B;KAC7B,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as msal from \"@azure/msal-node\";\n\nimport type { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport type { AuthenticationRecord, CertificateParts } from \"../types.js\";\nimport type { CredentialLogger } from \"../../util/logging.js\";\nimport { credentialLogger, formatSuccess } from \"../../util/logging.js\";\nimport type { PluginConfiguration } from \"./msalPlugins.js\";\nimport { msalPlugins } from \"./msalPlugins.js\";\nimport {\n  defaultLoggerCallback,\n  ensureValidMsalToken,\n  getAuthority,\n  getAuthorityHost,\n  getKnownAuthorities,\n  getMSALLogLevel,\n  handleMsalError,\n  msalToPublic,\n  publicToMsal,\n} from \"../utils.js\";\n\nimport { AuthenticationRequiredError } from \"../../errors.js\";\nimport type { BrokerOptions } from \"./brokerOptions.js\";\nimport type { DeviceCodePromptCallback } from \"../../credentials/deviceCodeCredentialOptions.js\";\nimport { IdentityClient } from \"../../client/identityClient.js\";\nimport type { InteractiveBrowserCredentialNodeOptions } from \"../../credentials/interactiveBrowserCredentialOptions.js\";\nimport type { TokenCachePersistenceOptions } from \"./tokenCachePersistenceOptions.js\";\nimport { calculateRegionalAuthority } from \"../../regionalAuthority.js\";\nimport { getLogLevel } from \"@azure/logger\";\nimport { resolveTenantId } from \"../../util/tenantIdUtils.js\";\n\n/**\n * The default logger used if no logger was passed in by the credential.\n */\nconst msalLogger = credentialLogger(\"MsalClient\");\n\n/**\n * Represents the options for acquiring a token using flows that support silent authentication.\n */\nexport interface GetTokenWithSilentAuthOptions extends GetTokenOptions {\n  /**\n   * Disables automatic authentication. If set to true, the method will throw an error if the user needs to authenticate.\n   *\n   * @remarks\n   *\n   * This option will be set to `false` when the user calls `authenticate` directly on a credential that supports it.\n   */\n  disableAutomaticAuthentication?: boolean;\n}\n\n/**\n * Represents the options for acquiring a token interactively.\n */\nexport interface GetTokenInteractiveOptions extends GetTokenWithSilentAuthOptions {\n  /**\n   * Window handle for parent window, required for WAM authentication.\n   */\n  parentWindowHandle?: Buffer;\n  /**\n   * Shared configuration options for browser customization\n   */\n  browserCustomizationOptions?: InteractiveBrowserCredentialNodeOptions[\"browserCustomizationOptions\"];\n  /**\n   * loginHint allows a user name to be pre-selected for interactive logins.\n   * Setting this option skips the account selection prompt and immediately attempts to login with the specified account.\n   */\n  loginHint?: string;\n}\n\n/**\n * Represents a client for interacting with the Microsoft Authentication Library (MSAL).\n */\nexport interface MsalClient {\n  /**\n   *\n   * Retrieves an access token by using the on-behalf-of flow and a client assertion callback of the calling service.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param userAssertionToken - The access token that was sent to the middle-tier API. This token must have an audience of the app making this OBO request.\n   * @param clientCredentials - The client secret OR client certificate OR client `getAssertion` callback.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenOnBehalfOf(\n    scopes: string[],\n    userAssertionToken: string,\n    clientCredentials: string | CertificateParts | (() => Promise<string>),\n    options?: GetTokenOptions,\n  ): Promise<AccessToken>;\n\n  /**\n   * Retrieves an access token by using an interactive prompt (InteractiveBrowserCredential).\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByInteractiveRequest(\n    scopes: string[],\n    options: GetTokenInteractiveOptions,\n  ): Promise<AccessToken>;\n  /**\n   * Retrieves an access token by using a user's username and password.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param username - The username provided by the developer.\n   * @param password - The user's password provided by the developer.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByUsernamePassword(\n    scopes: string[],\n    username: string,\n    password: string,\n    options?: GetTokenOptions,\n  ): Promise<AccessToken>;\n  /**\n   * Retrieves an access token by prompting the user to authenticate using a device code.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param userPromptCallback - The callback function that allows developers to customize the prompt message.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByDeviceCode(\n    scopes: string[],\n    userPromptCallback: DeviceCodePromptCallback,\n    options?: GetTokenWithSilentAuthOptions,\n  ): Promise<AccessToken>;\n  /**\n   * Retrieves an access token by using a client certificate.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param certificate - The client certificate used for authentication.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByClientCertificate(\n    scopes: string[],\n    certificate: CertificateParts,\n    options?: GetTokenOptions,\n  ): Promise<AccessToken>;\n\n  /**\n   * Retrieves an access token by using a client assertion.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param clientAssertion - The client `getAssertion` callback used for authentication.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByClientAssertion(\n    scopes: string[],\n    clientAssertion: () => Promise<string>,\n    options?: GetTokenOptions,\n  ): Promise<AccessToken>;\n\n  /**\n   * Retrieves an access token by using a client secret.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param clientSecret - The client secret of the application. This is a credential that the application can use to authenticate itself.\n   * @param options - Additional options that may be provided to the method.\n   * @returns An access token.\n   */\n  getTokenByClientSecret(\n    scopes: string[],\n    clientSecret: string,\n    options?: GetTokenOptions,\n  ): Promise<AccessToken>;\n\n  /**\n   * Retrieves an access token by using an authorization code flow.\n   *\n   * @param scopes - The scopes for which the access token is requested. These represent the resources that the application wants to access.\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param clientSecret - An optional client secret that was generated for the App Registration.\n   * @param options - Additional options that may be provided to the method.\n   */\n  getTokenByAuthorizationCode(\n    scopes: string[],\n    redirectUri: string,\n    authorizationCode: string,\n    clientSecret?: string,\n    options?: GetTokenWithSilentAuthOptions,\n  ): Promise<AccessToken>;\n\n  /**\n   * Retrieves the last authenticated account. This method expects an authentication record to have been previously loaded.\n   *\n   * An authentication record could be loaded by calling the `getToken` method, or by providing an `authenticationRecord` when creating a credential.\n   */\n  getActiveAccount(): AuthenticationRecord | undefined;\n}\n\n/**\n * Represents the options for configuring the MsalClient.\n */\nexport interface MsalClientOptions {\n  /**\n   * Parameters that enable WAM broker authentication in the InteractiveBrowserCredential.\n   */\n  brokerOptions?: BrokerOptions;\n\n  /**\n   * Parameters that enable token cache persistence in the Identity credentials.\n   */\n  tokenCachePersistenceOptions?: TokenCachePersistenceOptions;\n\n  /**\n   * A custom authority host.\n   */\n  authorityHost?: IdentityClient[\"tokenCredentialOptions\"][\"authorityHost\"];\n\n  /**\n   * Allows users to configure settings for logging policy options, allow logging account information and personally identifiable information for customer support.\n   */\n  loggingOptions?: IdentityClient[\"tokenCredentialOptions\"][\"loggingOptions\"];\n\n  /**\n   * The token credential options for the MsalClient.\n   */\n  tokenCredentialOptions?: IdentityClient[\"tokenCredentialOptions\"];\n\n  /**\n   * Determines whether instance discovery is disabled.\n   */\n  disableInstanceDiscovery?: boolean;\n\n  /**\n   * The logger for the MsalClient.\n   */\n  logger?: CredentialLogger;\n\n  /**\n   * The authentication record for the MsalClient.\n   */\n  authenticationRecord?: AuthenticationRecord;\n}\n\n/**\n * Generates the configuration for MSAL (Microsoft Authentication Library).\n *\n * @param clientId - The client ID of the application.\n * @param  tenantId - The tenant ID of the Azure Active Directory.\n * @param  msalClientOptions - Optional. Additional options for creating the MSAL client.\n * @returns  The MSAL configuration object.\n */\nexport function generateMsalConfiguration(\n  clientId: string,\n  tenantId: string,\n  msalClientOptions: MsalClientOptions = {},\n): msal.Configuration {\n  const resolvedTenant = resolveTenantId(\n    msalClientOptions.logger ?? msalLogger,\n    tenantId,\n    clientId,\n  );\n\n  // TODO: move and reuse getIdentityClientAuthorityHost\n  const authority = getAuthority(resolvedTenant, getAuthorityHost(msalClientOptions));\n\n  const httpClient = new IdentityClient({\n    ...msalClientOptions.tokenCredentialOptions,\n    authorityHost: authority,\n    loggingOptions: msalClientOptions.loggingOptions,\n  });\n\n  const msalConfig: msal.Configuration = {\n    auth: {\n      clientId,\n      authority,\n      knownAuthorities: getKnownAuthorities(\n        resolvedTenant,\n        authority,\n        msalClientOptions.disableInstanceDiscovery,\n      ),\n    },\n    system: {\n      networkClient: httpClient,\n      loggerOptions: {\n        loggerCallback: defaultLoggerCallback(msalClientOptions.logger ?? msalLogger),\n        logLevel: getMSALLogLevel(getLogLevel()),\n        piiLoggingEnabled: msalClientOptions.loggingOptions?.enableUnsafeSupportLogging,\n      },\n    },\n  };\n  return msalConfig;\n}\n\n/**\n * Represents the state necessary for the MSAL (Microsoft Authentication Library) client to operate.\n * This includes the MSAL configuration, cached account information, Azure region, and a flag to disable automatic authentication.\n *\n * @internal\n */\ninterface MsalClientState {\n  /** The configuration for the MSAL client. */\n  msalConfig: msal.Configuration;\n\n  /** The cached account information, or null if no account information is cached. */\n  cachedAccount: msal.AccountInfo | null;\n\n  /** Configured plugins */\n  pluginConfiguration: PluginConfiguration;\n\n  /** Claims received from challenges, cached for the next request */\n  cachedClaims?: string;\n\n  /** The logger instance */\n  logger: CredentialLogger;\n}\n\n/**\n * Creates an instance of the MSAL (Microsoft Authentication Library) client.\n *\n * @param clientId - The client ID of the application.\n * @param tenantId - The tenant ID of the Azure Active Directory.\n * @param createMsalClientOptions - Optional. Additional options for creating the MSAL client.\n * @returns An instance of the MSAL client.\n *\n * @public\n */\nexport function createMsalClient(\n  clientId: string,\n  tenantId: string,\n  createMsalClientOptions: MsalClientOptions = {},\n): MsalClient {\n  const state: MsalClientState = {\n    msalConfig: generateMsalConfiguration(clientId, tenantId, createMsalClientOptions),\n    cachedAccount: createMsalClientOptions.authenticationRecord\n      ? publicToMsal(createMsalClientOptions.authenticationRecord)\n      : null,\n    pluginConfiguration: msalPlugins.generatePluginConfiguration(createMsalClientOptions),\n    logger: createMsalClientOptions.logger ?? msalLogger,\n  };\n\n  const publicApps: Map<string, msal.PublicClientApplication> = new Map();\n  async function getPublicApp(\n    options: GetTokenOptions = {},\n  ): Promise<msal.PublicClientApplication> {\n    const appKey = options.enableCae ? \"CAE\" : \"default\";\n\n    let publicClientApp = publicApps.get(appKey);\n    if (publicClientApp) {\n      state.logger.getToken.info(\"Existing PublicClientApplication found in cache, returning it.\");\n      return publicClientApp;\n    }\n\n    // Initialize a new app and cache it\n    state.logger.getToken.info(\n      `Creating new PublicClientApplication with CAE ${options.enableCae ? \"enabled\" : \"disabled\"}.`,\n    );\n\n    const cachePlugin = options.enableCae\n      ? state.pluginConfiguration.cache.cachePluginCae\n      : state.pluginConfiguration.cache.cachePlugin;\n\n    state.msalConfig.auth.clientCapabilities = options.enableCae ? [\"cp1\"] : undefined;\n\n    publicClientApp = new msal.PublicClientApplication({\n      ...state.msalConfig,\n      broker: { nativeBrokerPlugin: state.pluginConfiguration.broker.nativeBrokerPlugin },\n      cache: { cachePlugin: await cachePlugin },\n    });\n\n    publicApps.set(appKey, publicClientApp);\n\n    return publicClientApp;\n  }\n\n  const confidentialApps: Map<string, msal.ConfidentialClientApplication> = new Map();\n  async function getConfidentialApp(\n    options: GetTokenOptions = {},\n  ): Promise<msal.ConfidentialClientApplication> {\n    const appKey = options.enableCae ? \"CAE\" : \"default\";\n\n    let confidentialClientApp = confidentialApps.get(appKey);\n    if (confidentialClientApp) {\n      state.logger.getToken.info(\n        \"Existing ConfidentialClientApplication found in cache, returning it.\",\n      );\n      return confidentialClientApp;\n    }\n\n    // Initialize a new app and cache it\n    state.logger.getToken.info(\n      `Creating new ConfidentialClientApplication with CAE ${\n        options.enableCae ? \"enabled\" : \"disabled\"\n      }.`,\n    );\n\n    const cachePlugin = options.enableCae\n      ? state.pluginConfiguration.cache.cachePluginCae\n      : state.pluginConfiguration.cache.cachePlugin;\n\n    state.msalConfig.auth.clientCapabilities = options.enableCae ? [\"cp1\"] : undefined;\n\n    confidentialClientApp = new msal.ConfidentialClientApplication({\n      ...state.msalConfig,\n      broker: { nativeBrokerPlugin: state.pluginConfiguration.broker.nativeBrokerPlugin },\n      cache: { cachePlugin: await cachePlugin },\n    });\n\n    confidentialApps.set(appKey, confidentialClientApp);\n\n    return confidentialClientApp;\n  }\n\n  async function getTokenSilent(\n    app: msal.ConfidentialClientApplication | msal.PublicClientApplication,\n    scopes: string[],\n    options: GetTokenOptions = {},\n  ): Promise<msal.AuthenticationResult> {\n    if (state.cachedAccount === null) {\n      state.logger.getToken.info(\"No cached account found in local state.\");\n      throw new AuthenticationRequiredError({ scopes });\n    }\n\n    // Keep track and reuse the claims we received across challenges\n    if (options.claims) {\n      state.cachedClaims = options.claims;\n    }\n\n    const silentRequest: msal.SilentFlowRequest = {\n      account: state.cachedAccount,\n      scopes,\n      claims: state.cachedClaims,\n    };\n\n    if (state.pluginConfiguration.broker.isEnabled) {\n      silentRequest.tokenQueryParameters ||= {};\n      if (state.pluginConfiguration.broker.enableMsaPassthrough) {\n        silentRequest.tokenQueryParameters[\"msal_request_type\"] = \"consumer_passthrough\";\n      }\n    }\n\n    if (options.proofOfPossessionOptions) {\n      silentRequest.shrNonce = options.proofOfPossessionOptions.nonce;\n      silentRequest.authenticationScheme = \"pop\";\n      silentRequest.resourceRequestMethod = options.proofOfPossessionOptions.resourceRequestMethod;\n      silentRequest.resourceRequestUri = options.proofOfPossessionOptions.resourceRequestUrl;\n    }\n    state.logger.getToken.info(\"Attempting to acquire token silently\");\n    try {\n      return await app.acquireTokenSilent(silentRequest);\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  /**\n   * Builds an authority URL for the given request. The authority may be different than the one used when creating the MSAL client\n   * if the user is creating cross-tenant requests\n   */\n  function calculateRequestAuthority(options?: GetTokenOptions): string | undefined {\n    if (options?.tenantId) {\n      return getAuthority(options.tenantId, getAuthorityHost(createMsalClientOptions));\n    }\n    return state.msalConfig.auth.authority;\n  }\n\n  /**\n   * Performs silent authentication using MSAL to acquire an access token.\n   * If silent authentication fails, falls back to interactive authentication.\n   *\n   * @param msalApp - The MSAL application instance.\n   * @param scopes - The scopes for which to acquire the access token.\n   * @param options - The options for acquiring the access token.\n   * @param onAuthenticationRequired - A callback function to handle interactive authentication when silent authentication fails.\n   * @returns A promise that resolves to an AccessToken object containing the access token and its expiration timestamp.\n   */\n  async function withSilentAuthentication(\n    msalApp: msal.ConfidentialClientApplication | msal.PublicClientApplication,\n    scopes: Array<string>,\n    options: GetTokenWithSilentAuthOptions,\n    onAuthenticationRequired: () => Promise<msal.AuthenticationResult | null>,\n  ): Promise<AccessToken> {\n    let response: msal.AuthenticationResult | null = null;\n    try {\n      response = await getTokenSilent(msalApp, scopes, options);\n    } catch (e: any) {\n      if (e.name !== \"AuthenticationRequiredError\") {\n        throw e;\n      }\n      if (options.disableAutomaticAuthentication) {\n        throw new AuthenticationRequiredError({\n          scopes,\n          getTokenOptions: options,\n          message:\n            \"Automatic authentication has been disabled. You may call the authentication() method.\",\n        });\n      }\n    }\n\n    // Silent authentication failed\n    if (response === null) {\n      try {\n        response = await onAuthenticationRequired();\n      } catch (err: any) {\n        throw handleMsalError(scopes, err, options);\n      }\n    }\n\n    // At this point we should have a token, process it\n    ensureValidMsalToken(scopes, response, options);\n    state.cachedAccount = response?.account ?? null;\n\n    state.logger.getToken.info(formatSuccess(scopes));\n    return {\n      token: response.accessToken,\n      expiresOnTimestamp: response.expiresOn.getTime(),\n      refreshAfterTimestamp: response.refreshOn?.getTime(),\n      tokenType: response.tokenType,\n    } as AccessToken;\n  }\n\n  async function getTokenByClientSecret(\n    scopes: string[],\n    clientSecret: string,\n    options: GetTokenOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using client secret`);\n\n    state.msalConfig.auth.clientSecret = clientSecret;\n\n    const msalApp = await getConfidentialApp(options);\n\n    try {\n      const response = await msalApp.acquireTokenByClientCredential({\n        scopes,\n        authority: calculateRequestAuthority(options),\n        azureRegion: calculateRegionalAuthority(),\n        claims: options?.claims,\n      });\n      ensureValidMsalToken(scopes, response, options);\n      state.logger.getToken.info(formatSuccess(scopes));\n      return {\n        token: response.accessToken,\n        expiresOnTimestamp: response.expiresOn.getTime(),\n        refreshAfterTimestamp: response.refreshOn?.getTime(),\n        tokenType: response.tokenType,\n      } as AccessToken;\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  async function getTokenByClientAssertion(\n    scopes: string[],\n    clientAssertion: () => Promise<string>,\n    options: GetTokenOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using client assertion`);\n\n    state.msalConfig.auth.clientAssertion = clientAssertion;\n\n    const msalApp = await getConfidentialApp(options);\n\n    try {\n      const response = await msalApp.acquireTokenByClientCredential({\n        scopes,\n        authority: calculateRequestAuthority(options),\n        azureRegion: calculateRegionalAuthority(),\n        claims: options?.claims,\n        clientAssertion,\n      });\n      ensureValidMsalToken(scopes, response, options);\n\n      state.logger.getToken.info(formatSuccess(scopes));\n      return {\n        token: response.accessToken,\n        expiresOnTimestamp: response.expiresOn.getTime(),\n        refreshAfterTimestamp: response.refreshOn?.getTime(),\n        tokenType: response.tokenType,\n      } as AccessToken;\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  async function getTokenByClientCertificate(\n    scopes: string[],\n    certificate: CertificateParts,\n    options: GetTokenOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using client certificate`);\n\n    state.msalConfig.auth.clientCertificate = certificate;\n\n    const msalApp = await getConfidentialApp(options);\n    try {\n      const response = await msalApp.acquireTokenByClientCredential({\n        scopes,\n        authority: calculateRequestAuthority(options),\n        azureRegion: calculateRegionalAuthority(),\n        claims: options?.claims,\n      });\n      ensureValidMsalToken(scopes, response, options);\n\n      state.logger.getToken.info(formatSuccess(scopes));\n      return {\n        token: response.accessToken,\n        expiresOnTimestamp: response.expiresOn.getTime(),\n        refreshAfterTimestamp: response.refreshOn?.getTime(),\n        tokenType: response.tokenType,\n      } as AccessToken;\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  async function getTokenByDeviceCode(\n    scopes: string[],\n    deviceCodeCallback: DeviceCodePromptCallback,\n    options: GetTokenWithSilentAuthOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using device code`);\n\n    const msalApp = await getPublicApp(options);\n\n    return withSilentAuthentication(msalApp, scopes, options, () => {\n      const requestOptions: msal.DeviceCodeRequest = {\n        scopes,\n        cancel: options?.abortSignal?.aborted ?? false,\n        deviceCodeCallback,\n        authority: calculateRequestAuthority(options),\n        claims: options?.claims,\n      };\n      const deviceCodeRequest = msalApp.acquireTokenByDeviceCode(requestOptions);\n      if (options.abortSignal) {\n        options.abortSignal.addEventListener(\"abort\", () => {\n          requestOptions.cancel = true;\n        });\n      }\n\n      return deviceCodeRequest;\n    });\n  }\n\n  async function getTokenByUsernamePassword(\n    scopes: string[],\n    username: string,\n    password: string,\n    options: GetTokenOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using username and password`);\n\n    const msalApp = await getPublicApp(options);\n\n    return withSilentAuthentication(msalApp, scopes, options, () => {\n      const requestOptions: msal.UsernamePasswordRequest = {\n        scopes,\n        username,\n        password,\n        authority: calculateRequestAuthority(options),\n        claims: options?.claims,\n      };\n\n      return msalApp.acquireTokenByUsernamePassword(requestOptions);\n    });\n  }\n\n  function getActiveAccount(): AuthenticationRecord | undefined {\n    if (!state.cachedAccount) {\n      return undefined;\n    }\n    return msalToPublic(clientId, state.cachedAccount);\n  }\n\n  async function getTokenByAuthorizationCode(\n    scopes: string[],\n    redirectUri: string,\n    authorizationCode: string,\n    clientSecret?: string,\n    options: GetTokenWithSilentAuthOptions = {},\n  ): Promise<AccessToken> {\n    state.logger.getToken.info(`Attempting to acquire token using authorization code`);\n\n    let msalApp: msal.ConfidentialClientApplication | msal.PublicClientApplication;\n    if (clientSecret) {\n      // If a client secret is provided, we need to use a confidential client application\n      // See https://learn.microsoft.com/entra/identity-platform/v2-oauth2-auth-code-flow#request-an-access-token-with-a-client_secret\n      state.msalConfig.auth.clientSecret = clientSecret;\n      msalApp = await getConfidentialApp(options);\n    } else {\n      msalApp = await getPublicApp(options);\n    }\n\n    return withSilentAuthentication(msalApp, scopes, options, () => {\n      return msalApp.acquireTokenByCode({\n        scopes,\n        redirectUri,\n        code: authorizationCode,\n        authority: calculateRequestAuthority(options),\n        claims: options?.claims,\n      });\n    });\n  }\n\n  async function getTokenOnBehalfOf(\n    scopes: string[],\n    userAssertionToken: string,\n    clientCredentials: string | CertificateParts | (() => Promise<string>),\n    options: GetTokenOptions = {},\n  ): Promise<AccessToken> {\n    msalLogger.getToken.info(`Attempting to acquire token on behalf of another user`);\n\n    if (typeof clientCredentials === \"string\") {\n      // Client secret\n      msalLogger.getToken.info(`Using client secret for on behalf of flow`);\n      state.msalConfig.auth.clientSecret = clientCredentials;\n    } else if (typeof clientCredentials === \"function\") {\n      // Client Assertion\n      msalLogger.getToken.info(`Using client assertion callback for on behalf of flow`);\n      state.msalConfig.auth.clientAssertion = clientCredentials;\n    } else {\n      // Client certificate\n      msalLogger.getToken.info(`Using client certificate for on behalf of flow`);\n      state.msalConfig.auth.clientCertificate = clientCredentials;\n    }\n\n    const msalApp = await getConfidentialApp(options);\n    try {\n      const response = await msalApp.acquireTokenOnBehalfOf({\n        scopes,\n        authority: calculateRequestAuthority(options),\n        claims: options.claims,\n        oboAssertion: userAssertionToken,\n      });\n      ensureValidMsalToken(scopes, response, options);\n\n      msalLogger.getToken.info(formatSuccess(scopes));\n      return {\n        token: response.accessToken,\n        expiresOnTimestamp: response.expiresOn.getTime(),\n        refreshAfterTimestamp: response.refreshOn?.getTime(),\n        tokenType: response.tokenType,\n      } as AccessToken;\n    } catch (err: any) {\n      throw handleMsalError(scopes, err, options);\n    }\n  }\n\n  async function getTokenByInteractiveRequest(\n    scopes: string[],\n    options: GetTokenInteractiveOptions = {},\n  ): Promise<AccessToken> {\n    msalLogger.getToken.info(`Attempting to acquire token interactively`);\n\n    const app = await getPublicApp(options);\n\n    /**\n     * A helper function that supports brokered authentication through the MSAL's public application.\n     *\n     * When options.useDefaultBrokerAccount is true, the method will attempt to authenticate using the default broker account.\n     * If the default broker account is not available, the method will fall back to interactive authentication.\n     */\n    async function getBrokeredToken(\n      useDefaultBrokerAccount: boolean,\n    ): Promise<msal.AuthenticationResult> {\n      msalLogger.verbose(\"Authentication will resume through the broker\");\n      const interactiveRequest = createBaseInteractiveRequest();\n      if (state.pluginConfiguration.broker.parentWindowHandle) {\n        interactiveRequest.windowHandle = Buffer.from(\n          state.pluginConfiguration.broker.parentWindowHandle,\n        );\n      } else {\n        // this is a bug, as the pluginConfiguration handler should validate this case.\n        msalLogger.warning(\n          \"Parent window handle is not specified for the broker. This may cause unexpected behavior. Please provide the parentWindowHandle.\",\n        );\n      }\n\n      if (state.pluginConfiguration.broker.enableMsaPassthrough) {\n        (interactiveRequest.tokenQueryParameters ??= {})[\"msal_request_type\"] =\n          \"consumer_passthrough\";\n      }\n      if (useDefaultBrokerAccount) {\n        interactiveRequest.prompt = \"none\";\n        msalLogger.verbose(\"Attempting broker authentication using the default broker account\");\n      } else {\n        msalLogger.verbose(\"Attempting broker authentication without the default broker account\");\n      }\n\n      if (options.proofOfPossessionOptions) {\n        interactiveRequest.shrNonce = options.proofOfPossessionOptions.nonce;\n        interactiveRequest.authenticationScheme = \"pop\";\n        interactiveRequest.resourceRequestMethod =\n          options.proofOfPossessionOptions.resourceRequestMethod;\n        interactiveRequest.resourceRequestUri = options.proofOfPossessionOptions.resourceRequestUrl;\n      }\n      try {\n        return await app.acquireTokenInteractive(interactiveRequest);\n      } catch (e: any) {\n        msalLogger.verbose(`Failed to authenticate through the broker: ${e.message}`);\n        // If we tried to use the default broker account and failed, fall back to interactive authentication\n        if (useDefaultBrokerAccount) {\n          return getBrokeredToken(/* useDefaultBrokerAccount: */ false);\n        } else {\n          throw e;\n        }\n      }\n    }\n\n    function createBaseInteractiveRequest(): msal.InteractiveRequest {\n      return {\n        openBrowser: async (url) => {\n          const open = await import(\"open\");\n          await open.default(url, { wait: true, newInstance: true });\n        },\n        scopes,\n        authority: calculateRequestAuthority(options),\n        claims: options?.claims,\n        loginHint: options?.loginHint,\n        errorTemplate: options?.browserCustomizationOptions?.errorMessage,\n        successTemplate: options?.browserCustomizationOptions?.successMessage,\n        prompt: options?.loginHint ? \"login\" : \"select_account\",\n      };\n    }\n\n    return withSilentAuthentication(app, scopes, options, async () => {\n      const interactiveRequest = createBaseInteractiveRequest();\n\n      if (state.pluginConfiguration.broker.isEnabled) {\n        return getBrokeredToken(state.pluginConfiguration.broker.useDefaultBrokerAccount ?? false);\n      }\n      if (options.proofOfPossessionOptions) {\n        interactiveRequest.shrNonce = options.proofOfPossessionOptions.nonce;\n        interactiveRequest.authenticationScheme = \"pop\";\n        interactiveRequest.resourceRequestMethod =\n          options.proofOfPossessionOptions.resourceRequestMethod;\n        interactiveRequest.resourceRequestUri = options.proofOfPossessionOptions.resourceRequestUrl;\n      }\n      return app.acquireTokenInteractive(interactiveRequest);\n    });\n  }\n\n  return {\n    getActiveAccount,\n    getTokenByClientSecret,\n    getTokenByClientAssertion,\n    getTokenByClientCertificate,\n    getTokenByDeviceCode,\n    getTokenByUsernamePassword,\n    getTokenByAuthorizationCode,\n    getTokenOnBehalfOf,\n    getTokenByInteractiveRequest,\n  };\n}\n"]}