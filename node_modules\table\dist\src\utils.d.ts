import type { SpanningCellConfig } from './types/api';
import type { BaseConfig, CellCoordinates, RangeCoordinate } from './types/internal';
export declare const sequence: (start: number, end: number) => number[];
export declare const sumArray: (array: number[]) => number;
export declare const extractTruncates: (config: BaseConfig) => number[];
export declare const flatten: <T>(array: T[][]) => T[];
export declare const calculateRangeCoordinate: (spanningCellConfig: SpanningCellConfig) => RangeCoordinate;
export declare const areCellEqual: (cell1: CellCoordinates, cell2: CellCoordinates) => boolean;
export declare const isCellInRange: (cell: CellCoordinates, { topLeft, bottomRight }: RangeCoordinate) => boolean;
