import type { TextlintResult } from "@textlint/types";
export interface FormatterConfig {
    formatterName: string;
    color?: boolean;
}
export declare function loadFormatter(formatterConfig: FormatterConfig): Promise<{
    format(results: TextlintResult[]): string;
}>;
/**
 * @deprecated use loadFormatter
 * @param formatterConfig
 */
export declare function createFormatter(formatterConfig: FormatterConfig): (results: TextlintResult[]) => string;
export interface FormatterDetail {
    name: string;
}
export declare function getFormatterList(): FormatterDetail[];
//# sourceMappingURL=index.d.ts.map