/**
 * Current version of the `@azure/identity` package.
 */
export declare const SDK_VERSION = "4.10.0";
/**
 * The default client ID for authentication
 * @internal
 */
export declare const DeveloperSignOnClientId = "04b07795-8ddb-461a-bbee-02f9e1bf7b46";
/**
 * The default tenant for authentication
 * @internal
 */
export declare const DefaultTenantId = "common";
/**
 * A list of known Azure authority hosts
 */
export declare enum AzureAuthorityHosts {
    /**
     * China-based Azure Authority Host
     */
    AzureChina = "https://login.chinacloudapi.cn",
    /**
     * Germany-based Azure Authority Host
     *
     * @deprecated Microsoft Cloud Germany was closed on October 29th, 2021.
     *
     * */
    AzureGermany = "https://login.microsoftonline.de",
    /**
     * US Government Azure Authority Host
     */
    AzureGovernment = "https://login.microsoftonline.us",
    /**
     * Public Cloud Azure Authority Host
     */
    AzurePublicCloud = "https://login.microsoftonline.com"
}
/**
 * @internal
 * The default authority host.
 */
export declare const DefaultAuthorityHost = AzureAuthorityHosts.AzurePublicCloud;
/**
 * @internal
 * The default environment host for Azure Public Cloud
 */
export declare const DefaultAuthority = "login.microsoftonline.com";
/**
 * @internal
 * Allow acquiring tokens for any tenant for multi-tentant auth.
 */
export declare const ALL_TENANTS: string[];
/**
 * @internal
 */
export declare const CACHE_CAE_SUFFIX = "cae";
/**
 * @internal
 */
export declare const CACHE_NON_CAE_SUFFIX = "nocae";
/**
 * @internal
 *
 * The default name for the cache persistence plugin.
 * Matches the constant defined in the cache persistence package.
 */
export declare const DEFAULT_TOKEN_CACHE_NAME = "msal.cache";
//# sourceMappingURL=constants.d.ts.map