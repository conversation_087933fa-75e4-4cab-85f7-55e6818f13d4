{"version": 3, "file": "state-cjs.cjs", "sourceRoot": "", "sources": ["../../src/state-cjs.cts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC;;;;GAIG;AACU,QAAA,KAAK,GAAG;IACnB,0BAA0B,EAAE,SAAS;CACtC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * @internal\n *\n * Holds the singleton instrumenter, to be shared across CJS and ESM imports.\n */\nexport const state = {\n  instrumenterImplementation: undefined,\n};\n"]}