{"version": 3, "file": "BrowserAuthError.mjs", "sources": ["../../src/error/BrowserAuthError.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.pkceNotCreated", "BrowserAuthErrorCodes.earJwkEmpty", "BrowserAuthErrorCodes.earJweEmpty", "BrowserAuthErrorCodes.cryptoNonExistent", "BrowserAuthErrorCodes.emptyNavigateUri", "BrowserAuthErrorCodes.hashEmptyError", "BrowserAuthErrorCodes.noStateInHash", "BrowserAuthErrorCodes.hashDoesNotContainKnownProperties", "BrowserAuthErrorCodes.unableToParseState", "BrowserAuthErrorCodes.stateInteractionTypeMismatch", "BrowserAuthErrorCodes.interactionInProgress", "BrowserAuthErrorCodes.popupWindowError", "BrowserAuthErrorCodes.emptyWindowError", "BrowserAuthErrorCodes.userCancelled", "BrowserAuthErrorCodes.monitorPopupTimeout", "BrowserAuthErrorCodes.monitorWindowTimeout", "BrowserAuthErrorCodes.redirectInIframe", "BrowserAuthErrorCodes.blockIframeReload", "BrowserAuthErrorCodes.blockNestedPopups", "BrowserAuthErrorCodes.iframeClosedPrematurely", "BrowserAuthErrorCodes.silentLogoutUnsupported", "BrowserAuthErrorCodes.noAccountError", "BrowserAuthErrorCodes.silentPromptValueError", "BrowserAuthErrorCodes.noTokenRequestCacheError", "BrowserAuthErrorCodes.unableToParseTokenRequestCacheError", "BrowserAuthErrorCodes.authRequestNotSetError", "BrowserAuthErrorCodes.invalidCacheType", "BrowserAuthErrorCodes.nonBrowserEnvironment", "BrowserAuthErrorCodes.databaseNotOpen", "BrowserAuthErrorCodes.noNetworkConnectivity", "BrowserAuthErrorCodes.postRequestFailed", "BrowserAuthErrorCodes.getRequestFailed", "BrowserAuthErrorCodes.failedToParseResponse", "BrowserAuthErrorCodes.unableToLoadToken", "BrowserAuthErrorCodes.cryptoKeyNotFound", "BrowserAuthErrorCodes.authCodeRequired", "BrowserAuthErrorCodes.authCodeOrNativeAccountIdRequired", "BrowserAuthErrorCodes.spaCodeAndNativeAccountIdPresent", "BrowserAuthErrorCodes.databaseUnavailable", "BrowserAuthErrorCodes.unableToAcquireTokenFromNativePlatform", "BrowserAuthErrorCodes.nativeHandshakeTimeout", "BrowserAuthErrorCodes.nativeExtensionNotInstalled", "BrowserAuthErrorCodes.nativeConnectionNotEstablished", "BrowserAuthErrorCodes.uninitializedPublicClientApplication", "BrowserAuthErrorCodes.nativePromptNotSupported", "BrowserAuthErrorCodes.invalidBase64String", "BrowserAuthErrorCodes.invalidPopTokenRequest", "BrowserAuthErrorCodes.failedToBuildHeaders", "BrowserAuthErrorCodes.failedToParseHeaders", "BrowserAuthErrorCodes.failedToDecryptEarResponse"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMH,MAAM,SAAS,GAAG,8CAA8C,CAAC;AAEjE;;AAEG;AACU,MAAA,wBAAwB,GAAG;AACpC,IAAA,CAACA,cAAoC,GACjC,8DAA8D;AAClE,IAAA,CAACC,WAAiC,GAC9B,qDAAqD;AACzD,IAAA,CAACC,WAAiC,GAC9B,wEAAwE;AAC5E,IAAA,CAACC,iBAAuC,GACpC,iDAAiD;AACrD,IAAA,CAACC,gBAAsC,GACnC,kEAAkE;AACtE,IAAA,CAACC,cAAoC,GAAG,CAAA,kHAAA,EAAqH,SAAS,CAAE,CAAA;AACxK,IAAA,CAACC,aAAmC,GAChC,mFAAmF;AACvF,IAAA,CAACC,iCAAuD,GAAG,CAAA,uGAAA,EAA0G,SAAS,CAAE,CAAA;AAChL,IAAA,CAACC,kBAAwC,GACrC,6EAA6E;AACjF,IAAA,CAACC,4BAAkD,GAC/C,yEAAyE;AAC7E,IAAA,CAACC,qBAA2C,GAAG,CAAA,kIAAA,EAAqI,SAAS,CAAE,CAAA;AAC/L,IAAA,CAACC,gBAAsC,GACnC,0GAA0G;AAC9G,IAAA,CAACC,gBAAsC,GACnC,uDAAuD;AAC3D,IAAA,CAACC,aAAmC,GAAG,0BAA0B;AACjE,IAAA,CAACC,mBAAyC,GAAG,CAAA,mDAAA,EAAsD,SAAS,CAAE,CAAA;AAC9G,IAAA,CAACC,oBAA0C,GAAG,CAAA,oDAAA,EAAuD,SAAS,CAAE,CAAA;AAChH,IAAA,CAACC,gBAAsC,GACnC,uLAAuL;AAC3L,IAAA,CAACC,iBAAuC,GAAG,CAAA,wFAAA,EAA2F,SAAS,CAAE,CAAA;AACjJ,IAAA,CAACC,iBAAuC,GACpC,qFAAqF;AACzF,IAAA,CAACC,uBAA6C,GAC1C,oDAAoD;AACxD,IAAA,CAACC,uBAA6C,GAC1C,iFAAiF;AACrF,IAAA,CAACC,cAAoC,GACjC,yJAAyJ;AAC7J,IAAA,CAACC,sBAA4C,GACzC,gHAAgH;AACpH,IAAA,CAACC,wBAA8C,GAC3C,kCAAkC;AACtC,IAAA,CAACC,mCAAyD,GACtD,+CAA+C;AACnD,IAAA,CAACC,sBAA4C,GACzC,gGAAgG;AACpG,IAAA,CAACC,gBAAsC,GAAG,oBAAoB;AAC9D,IAAA,CAACC,qBAA2C,GACxC,yEAAyE;AAC7E,IAAA,CAACC,eAAqC,GAAG,uBAAuB;AAChE,IAAA,CAACC,qBAA2C,GACxC,0DAA0D;AAC9D,IAAA,CAACC,iBAAuC,GACpC,2IAA2I;AAC/I,IAAA,CAACC,gBAAsC,GACnC,iFAAiF;AACrF,IAAA,CAACC,qBAA2C,GACxC,wDAAwD;AAC5D,IAAA,CAACC,iBAAuC,GAAG,+BAA+B;AAC1E,IAAA,CAACC,iBAAuC,GACpC,4DAA4D;AAChE,IAAA,CAACC,gBAAsC,GACnC,8FAA8F;AAClG,IAAA,CAACC,iCAAuD,GACpD,yEAAyE;AAC7E,IAAA,CAACC,gCAAsD,GACnD,6DAA6D;AACjE,IAAA,CAACC,mBAAyC,GACtC,+LAA+L;AACnM,IAAA,CAACC,sCAA4D,GAAG,CAAA,+CAAA,EAAkD,SAAS,CAAE,CAAA;AAC7H,IAAA,CAACC,sBAA4C,GACzC,yEAAyE;AAC7E,IAAA,CAACC,2BAAiD,GAC9C,iGAAiG;AACrG,IAAA,CAACC,8BAAoD,GAAG,CAAA,6HAAA,EAAgI,SAAS,CAAE,CAAA;AACnM,IAAA,CAACC,oCAA0D,GAAG,CAAA,+FAAA,EAAkG,SAAS,CAAE,CAAA;AAC3K,IAAA,CAACC,wBAA8C,GAC3C,mHAAmH;AACvH,IAAA,CAACC,mBAAyC,GACtC,gCAAgC;AACpC,IAAA,CAACC,sBAA4C,GACzC,0GAA0G;AAC9G,IAAA,CAACC,oBAA0C,GACvC,yCAAyC;AAC7C,IAAA,CAACC,oBAA0C,GACvC,kCAAkC;AACtC,IAAA,CAACC,0BAAgD,GAC7C,gCAAgC;EACtC;AAEF;;;;;;;AAOG;AACU,MAAA,uBAAuB,GAAG;AACnC,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEjD,cAAoC;AAC1C,QAAA,IAAI,EAAE,wBAAwB,CAACA,cAAoC,CAAC;AACvE,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEG,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,cAAc,EAAE;QACZ,IAAI,EAAEC,cAAoC;AAC1C,QAAA,IAAI,EAAE,wBAAwB,CAACA,cAAoC,CAAC;AACvE,KAAA;AACD,IAAA,4BAA4B,EAAE;QAC1B,IAAI,EAAEC,aAAmC;AACzC,QAAA,IAAI,EAAE,wBAAwB,CAACA,aAAmC,CAAC;AACtE,KAAA;AACD,IAAA,sCAAsC,EAAE;QACpC,IAAI,EAAEC,iCAAuD;AAC7D,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,iCAAuD,CAC1D;AACJ,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,kBAAwC;AAC9C,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,kBAAwC,CAC3C;AACJ,KAAA;AACD,IAAA,iCAAiC,EAAE;QAC/B,IAAI,EAAEC,4BAAkD;AACxD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,4BAAkD,CACrD;AACJ,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,qBAA2C;AACjD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,qBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,aAAmC;AACzC,QAAA,IAAI,EAAE,wBAAwB,CAACA,aAAmC,CAAC;AACtE,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,mBAAyC;AAC/C,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,mBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,yBAAyB,EAAE;QACvB,IAAI,EAAEC,oBAA0C;AAChD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,oBAA0C,CAC7C;AACJ,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,qCAAqC,EAAE;QACnC,IAAI,EAAEC,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,8BAA8B,EAAE;QAC5B,IAAI,EAAEC,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,4BAA4B,EAAE;QAC1B,IAAI,EAAEC,uBAA6C;AACnD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,uBAA6C,CAChD;AACJ,KAAA;AACD,IAAA,4BAA4B,EAAE;QAC1B,IAAI,EAAEC,uBAA6C;AACnD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,uBAA6C,CAChD;AACJ,KAAA;AACD,IAAA,cAAc,EAAE;QACZ,IAAI,EAAEC,cAAoC;AAC1C,QAAA,IAAI,EAAE,wBAAwB,CAACA,cAAoC,CAAC;AACvE,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,sBAA4C;AAClD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,sBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAA8C;AACpD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,wBAA8C,CACjD;AACJ,KAAA;AACD,IAAA,mCAAmC,EAAE;QACjC,IAAI,EAAEC,mCAAyD;AAC/D,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,mCAAyD,CAC5D;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,sBAA4C;AAClD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,sBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,qBAA2C;AACjD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,qBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,eAAe,EAAE;QACb,IAAI,EAAEC,eAAqC;AAC3C,QAAA,IAAI,EAAE,wBAAwB,CAACA,eAAqC,CAAC;AACxE,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,qBAA2C;AACjD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,qBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,4BAA4B,EAAE;QAC1B,IAAI,EAAEC,qBAA2C;AACjD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,qBAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,2BAA2B,EAAE;QACzB,IAAI,EAAEC,iBAAuC;AAC7C,QAAA,IAAI,EAAE,wBAAwB,CAACA,iBAAuC,CAAC;AAC1E,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,gBAAsC;AAC5C,QAAA,IAAI,EAAE,wBAAwB,CAACA,gBAAsC,CAAC;AACzE,KAAA;AACD,IAAA,+BAA+B,EAAE;QAC7B,IAAI,EAAEC,iCAAuD;AAC7D,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,iCAAuD,CAC1D;AACJ,KAAA;AACD,IAAA,8BAA8B,EAAE;QAC5B,IAAI,EAAEC,gCAAsD;AAC5D,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,gCAAsD,CACzD;AACJ,KAAA;AACD,IAAA,mBAAmB,EAAE;QACjB,IAAI,EAAEC,mBAAyC;AAC/C,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,mBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,sCAAsC,EAAE;QACpC,IAAI,EAAEC,sCAA4D;AAClE,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,sCAA4D,CAC/D;AACJ,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,sBAA4C;AAClD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,sBAA4C,CAC/C;AACJ,KAAA;AACD,IAAA,2BAA2B,EAAE;QACzB,IAAI,EAAEC,2BAAiD;AACvD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,2BAAiD,CACpD;AACJ,KAAA;AACD,IAAA,8BAA8B,EAAE;QAC5B,IAAI,EAAEC,8BAAoD;AAC1D,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,8BAAoD,CACvD;AACJ,KAAA;AACD,IAAA,oCAAoC,EAAE;QAClC,IAAI,EAAEC,oCAA0D;AAChE,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,oCAA0D,CAC7D;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAA8C;AACpD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,wBAA8C,CACjD;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,mBAAyC;AAC/C,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,mBAAyC,CAC5C;AACJ,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,sBAA4C;AAClD,QAAA,IAAI,EAAE,wBAAwB,CAC1BA,sBAA4C,CAC/C;AACJ,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,gBAAiB,SAAQ,SAAS,CAAA;IAC3C,WAAY,CAAA,SAAiB,EAAE,QAAiB,EAAA;QAC5C,KAAK,CAAC,SAAS,EAAE,wBAAwB,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEhE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;KAClC;AACJ,CAAA;AAEe,SAAA,sBAAsB,CAClC,SAAiB,EACjB,QAAiB,EAAA;AAEjB,IAAA,OAAO,IAAI,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD;;;;"}