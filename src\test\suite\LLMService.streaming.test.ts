import * as assert from 'assert';
import * as sinon from 'sinon';
import { LLMService } from '../../services/llmService';
import { LLMMessage, LLMStreamChunk } from '../../interfaces/ILLMService';

suite('LLMService Streaming Test Suite', () => {
    let llmService: LLMService;
    let sandbox: sinon.SinonSandbox;
    let fetchStub: sinon.SinonStub;

    setup(() => {
        sandbox = sinon.createSandbox();
        llmService = new LLMService();
        
        // Mock fetch globally
        fetchStub = sandbox.stub();
        (global as any).fetch = fetchStub;
    });

    teardown(() => {
        sandbox.restore();
    });

    test('should stream OpenAI responses correctly', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Hello, how are you?' }
        ];
        
        const chunks: LLMStreamChunk[] = [];
        const mockStreamData = [
            'data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":" there"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":"!"}}]}\n\n',
            'data: [DONE]\n\n'
        ];
        
        // Mock ReadableStream
        const mockReader = {
            read: sandbox.stub()
        };
        
        mockStreamData.forEach((data, index) => {
            mockReader.read.onCall(index).resolves({
                done: index === mockStreamData.length - 1,
                value: new TextEncoder().encode(data)
            });
        });
        
        mockReader.read.onCall(mockStreamData.length).resolves({ done: true });
        
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };
        
        fetchStub.resolves(mockResponse);
        
        const response = await llmService.sendMessageStream(
            messages,
            'openai',
            'gpt-3.5-turbo',
            'test-api-key',
            (chunk) => chunks.push(chunk)
        );
        
        assert.ok(fetchStub.calledOnce);
        assert.strictEqual(chunks.length, 4); // 3 content chunks + 1 completion
        assert.strictEqual(chunks[0].content, 'Hello');
        assert.strictEqual(chunks[1].content, ' there');
        assert.strictEqual(chunks[2].content, '!');
        assert.strictEqual(chunks[3].isComplete, true);
        assert.strictEqual(response.content, 'Hello there!');
    });

    test('should handle OpenAI streaming errors', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];
        
        const mockResponse = {
            ok: false,
            status: 401,
            statusText: 'Unauthorized'
        };
        
        fetchStub.resolves(mockResponse);
        
        try {
            await llmService.sendMessageStream(
                messages,
                'openai',
                'gpt-3.5-turbo',
                'invalid-key',
                () => {}
            );
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.ok(error instanceof Error);
            assert.ok(error.message.includes('OpenAI API error'));
        }
    });

    test('should stream Anthropic responses correctly', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Hello Claude' }
        ];
        
        const chunks: LLMStreamChunk[] = [];
        const mockStreamData = [
            'data: {"type":"content_block_delta","delta":{"text":"Hello"}}\n\n',
            'data: {"type":"content_block_delta","delta":{"text":" human"}}\n\n',
            'data: [DONE]\n\n'
        ];
        
        const mockReader = {
            read: sandbox.stub()
        };
        
        mockStreamData.forEach((data, index) => {
            mockReader.read.onCall(index).resolves({
                done: index === mockStreamData.length - 1,
                value: new TextEncoder().encode(data)
            });
        });
        
        mockReader.read.onCall(mockStreamData.length).resolves({ done: true });
        
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };
        
        fetchStub.resolves(mockResponse);
        
        const response = await llmService.sendMessageStream(
            messages,
            'anthropic',
            'claude-3-sonnet',
            'test-api-key',
            (chunk) => chunks.push(chunk)
        );
        
        assert.ok(fetchStub.calledOnce);
        assert.strictEqual(chunks.length, 3); // 2 content chunks + 1 completion
        assert.strictEqual(response.content, 'Hello human');
    });

    test('should handle stream cancellation with AbortController', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];
        
        const abortController = new AbortController();
        
        // Mock a long-running stream
        const mockReader = {
            read: sandbox.stub().callsFake(() => {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({
                            done: false,
                            value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":"test"}}]}\n\n')
                        });
                    }, 1000);
                });
            }),
            releaseLock: sandbox.stub()
        };
        
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };
        
        fetchStub.resolves(mockResponse);
        
        // Start streaming
        const streamPromise = llmService.sendMessageStream(
            messages,
            'openai',
            'gpt-3.5-turbo',
            'test-api-key',
            () => {},
            undefined,
            undefined,
            abortController
        );
        
        // Cancel after a short delay
        setTimeout(() => {
            abortController.abort();
        }, 100);
        
        try {
            await streamPromise;
            assert.fail('Should have been aborted');
        } catch (error) {
            // Should handle abort gracefully
            assert.ok(error instanceof Error);
        }
    });

    test('should normalize errors across providers', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];
        
        const testCases = [
            { status: 401, expectedMessage: 'Authentication failed' },
            { status: 429, expectedMessage: 'Rate limit exceeded' },
            { status: 403, expectedMessage: 'Access denied' },
            { status: 404, expectedMessage: 'Model or endpoint not found' }
        ];
        
        for (const testCase of testCases) {
            const mockResponse = {
                ok: false,
                status: testCase.status,
                statusText: 'Error',
                text: () => Promise.resolve('Error details')
            };
            
            fetchStub.resolves(mockResponse);
            
            try {
                await llmService.sendMessageStream(
                    messages,
                    'openai',
                    'gpt-3.5-turbo',
                    'test-key',
                    () => {}
                );
                assert.fail('Should have thrown an error');
            } catch (error) {
                assert.ok(error instanceof Error);
                assert.ok(error.message.includes(testCase.expectedMessage));
            }
        }
    });

    test('should fallback to non-streaming for unsupported providers', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];
        
        const chunks: LLMStreamChunk[] = [];
        
        // Mock non-streaming response
        const mockResponse = {
            ok: true,
            json: () => Promise.resolve({
                choices: [{ message: { content: 'Fallback response' } }],
                usage: { prompt_tokens: 10, completion_tokens: 5 }
            })
        };
        
        fetchStub.resolves(mockResponse);
        
        const response = await llmService.sendMessageStream(
            messages,
            'unsupported-provider',
            'test-model',
            'test-key',
            (chunk) => chunks.push(chunk)
        );
        
        assert.strictEqual(chunks.length, 1);
        assert.strictEqual(chunks[0].isComplete, true);
        assert.strictEqual(chunks[0].content, 'Fallback response');
        assert.strictEqual(response.content, 'Fallback response');
    });

    test('should handle malformed streaming data gracefully', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];
        
        const chunks: LLMStreamChunk[] = [];
        const mockStreamData = [
            'data: {"invalid": json}\n\n',  // Invalid JSON
            'data: {"choices":[{"delta":{"content":"valid"}}]}\n\n',  // Valid chunk
            'data: [DONE]\n\n'
        ];
        
        const mockReader = {
            read: sandbox.stub()
        };
        
        mockStreamData.forEach((data, index) => {
            mockReader.read.onCall(index).resolves({
                done: index === mockStreamData.length - 1,
                value: new TextEncoder().encode(data)
            });
        });
        
        mockReader.read.onCall(mockStreamData.length).resolves({ done: true });
        
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };
        
        fetchStub.resolves(mockResponse);
        
        const response = await llmService.sendMessageStream(
            messages,
            'openai',
            'gpt-3.5-turbo',
            'test-key',
            (chunk) => chunks.push(chunk)
        );
        
        // Should handle invalid JSON gracefully and continue with valid chunks
        assert.strictEqual(chunks.length, 2); // 1 valid content + 1 completion
        assert.strictEqual(chunks[0].content, 'valid');
        assert.strictEqual(response.content, 'valid');
    });

    test('should track conversation history for streaming', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Hello' }
        ];
        
        const mockReader = {
            read: sandbox.stub()
                .onCall(0).resolves({
                    done: false,
                    value: new TextEncoder().encode('data: {"choices":[{"delta":{"content":"Hi there"}}]}\n\n')
                })
                .onCall(1).resolves({
                    done: true,
                    value: new TextEncoder().encode('data: [DONE]\n\n')
                })
        };
        
        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };
        
        fetchStub.resolves(mockResponse);
        
        const conversationId = 'test-conversation';
        
        await llmService.sendMessageStream(
            messages,
            'openai',
            'gpt-3.5-turbo',
            'test-key',
            () => {},
            undefined,
            conversationId
        );
        
        const history = llmService.getConversationHistory(conversationId);
        assert.strictEqual(history.length, 2); // User message + assistant response
        assert.strictEqual(history[0].role, 'user');
        assert.strictEqual(history[1].role, 'assistant');
        assert.strictEqual(history[1].content, 'Hi there');
    });

    test('should cancel stream through service method', () => {
        const streamId = 'test-stream-123';

        // This should not throw an error
        llmService.cancelStream(streamId);

        // Verify the streaming controller is called
        // (In a real implementation, this would interact with the StreamingController)
        assert.ok(true); // Placeholder assertion
    });

    test('should validate DeepSeek API key format', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];

        const invalidApiKeys = [
            '',
            '   ',
            'invalid-key',
            'sk-',
            'sk-invalid@characters!',
            null,
            undefined
        ];

        for (const invalidKey of invalidApiKeys) {
            try {
                await llmService.sendMessageStream(
                    messages,
                    'deepseek',
                    'deepseek-coder',
                    invalidKey as any,
                    () => {}
                );
                assert.fail(`Should have thrown an error for invalid key: ${invalidKey}`);
            } catch (error) {
                assert.ok(error instanceof Error);
                assert.ok(
                    error.message.includes('API key') ||
                    error.message.includes('required') ||
                    error.message.includes('invalid')
                );
            }
        }
    });

    test('should stream DeepSeek responses correctly', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Write a simple function' }
        ];

        const chunks: LLMStreamChunk[] = [];
        const mockStreamData = [
            'data: {"choices":[{"delta":{"content":"def"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":" hello"}}]}\n\n',
            'data: {"choices":[{"delta":{"content":"():"}}]}\n\n',
            'data: [DONE]\n\n'
        ];

        const mockReader = {
            read: sandbox.stub(),
            releaseLock: sandbox.stub()
        };

        mockStreamData.forEach((data, index) => {
            mockReader.read.onCall(index).resolves({
                done: index === mockStreamData.length - 1,
                value: new TextEncoder().encode(data)
            });
        });

        mockReader.read.onCall(mockStreamData.length).resolves({ done: true });

        const mockResponse = {
            ok: true,
            body: {
                getReader: () => mockReader
            }
        };

        fetchStub.resolves(mockResponse);

        const response = await llmService.sendMessageStream(
            messages,
            'deepseek',
            'deepseek-coder',
            'sk-validapikey123456789',
            (chunk) => chunks.push(chunk)
        );

        assert.ok(fetchStub.calledOnce);

        // Verify the correct URL was called
        const fetchCall = fetchStub.getCall(0);
        assert.strictEqual(fetchCall.args[0], 'https://api.deepseek.com/v1/chat/completions');

        // Verify headers
        const headers = fetchCall.args[1].headers;
        assert.strictEqual(headers['Authorization'], 'Bearer sk-validapikey123456789');
        assert.strictEqual(headers['Content-Type'], 'application/json');
        assert.strictEqual(headers['User-Agent'], 'V1b3-Sama-Extension/5.0.2');

        // Verify streaming response
        assert.strictEqual(chunks.length, 4); // 3 content chunks + 1 completion
        assert.strictEqual(chunks[0].content, 'def');
        assert.strictEqual(chunks[1].content, ' hello');
        assert.strictEqual(chunks[2].content, '():');
        assert.strictEqual(chunks[3].isComplete, true);
        assert.strictEqual(response.content, 'def hello():');
    });

    test('should handle DeepSeek authentication errors', async () => {
        const messages: LLMMessage[] = [
            { role: 'user', content: 'Test message' }
        ];

        const mockResponse = {
            ok: false,
            status: 401,
            statusText: 'Unauthorized',
            text: () => Promise.resolve('{"error":{"message":"Invalid API key"}}')
        };

        fetchStub.resolves(mockResponse);

        try {
            await llmService.sendMessageStream(
                messages,
                'deepseek',
                'deepseek-coder',
                'sk-invalidkey123',
                () => {}
            );
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.ok(error instanceof Error);
            assert.ok(error.message.includes('DeepSeek authentication failed'));
        }
    });
});
