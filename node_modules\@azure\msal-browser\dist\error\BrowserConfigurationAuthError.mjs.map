{"version": 3, "file": "BrowserConfigurationAuthError.mjs", "sources": ["../../src/error/BrowserConfigurationAuthError.ts"], "sourcesContent": [null], "names": ["BrowserConfigurationAuthErrorCodes.storageNotSupported", "BrowserConfigurationAuthErrorCodes.stubbedPublicClientApplicationCalled", "BrowserConfigurationAuthErrorCodes.inMemRedirectUnavailable", "BrowserConfigurationAuthErrorCodes\r\n            .stubbedPublicClientApplicationCalled"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMU,MAAA,qCAAqC,GAAG;AACjD,IAAA,CAACA,mBAAsD,GACnD,uDAAuD;AAC3D,IAAA,CAACC,oCAAuE,GACpE,gLAAgL;AACpL,IAAA,CAACC,wBAA2D,GACxD,uRAAuR;EAC7R;AAEF;;;AAGG;AACU,MAAA,oCAAoC,GAAG;AAChD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEF,mBAAsD;AAC5D,QAAA,IAAI,EAAE,qCAAqC,CACvCA,mBAAsD,CACzD;AACJ,KAAA;AACD,IAAA,qBAAqB,EAAE;QACnB,IAAI,EAAEC,oCAAuE;QAC7E,IAAI,EAAE,qCAAqC,CACvCE,oCACyC,CAC5C;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAED,wBAA2D;AACjE,QAAA,IAAI,EAAE,qCAAqC,CACvCA,wBAA2D,CAC9D;AACJ,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,6BAA8B,SAAQ,SAAS,CAAA;IACxD,WAAY,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAChD,QAAA,KAAK,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;QAE5C,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,6BAA6B,CAAC,SAAS,CAAC,CAAC;KACxE;AACJ,CAAA;AAEK,SAAU,mCAAmC,CAC/C,SAAiB,EAAA;IAEjB,OAAO,IAAI,6BAA6B,CACpC,SAAS,EACT,qCAAqC,CAAC,SAAS,CAAC,CACnD,CAAC;AACN;;;;"}