import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * This credential will use the currently-logged-in user's login information via the Azure Power Shell command line tool.
 */
export declare class AzurePowerShellCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=azurePowerShellCredential-browser.d.mts.map