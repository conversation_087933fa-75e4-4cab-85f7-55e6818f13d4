// @ts-nocheck
// eslint-disable
// This file is generated by create-validator-ts
import Ajv from 'ajv';
export const SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "definitions": {
        "SecretLintConfigDescriptor_": {
            "$ref": "#/definitions/SecretLintConfigDescriptor"
        },
        "SecretLintConfigDescriptor": {
            "type": "object",
            "properties": {
                "sharedOptions": {
                    "$ref": "#/definitions/SecretlintCoreSharedOptions"
                },
                "rules": {
                    "type": "array",
                    "items": {
                        "anyOf": [
                            {
                                "$ref": "#/definitions/SecretLintConfigDescriptorRule"
                            },
                            {
                                "$ref": "#/definitions/SecretLintConfigDescriptorRulePreset"
                            }
                        ]
                    }
                }
            },
            "required": [
                "rules"
            ],
            "description": "An abstraction for config file"
        },
        "SecretlintCoreSharedOptions": {
            "type": "object",
            "description": "Share options for Rule Context This shared options can be accessed via `Context#sharedOptions`."
        },
        "SecretLintConfigDescriptorRule": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "description": "**Required** Rule id that is package name or shorten package name Examples\n- \"@scope/secretlint-rule-example\" or \"@scope/example\"(shorten)\n- \"secretlint-rule-example\" or \"example\"(shorten)"
                },
                "options": {
                    "$ref": "#/definitions/SecretLintRuleCreatorOptions",
                    "description": "Rule options. This value is defined by each rules. Default: {} (empty object)"
                },
                "disabled": {
                    "type": "boolean",
                    "description": "If true, Disable the rule. Default: false"
                },
                "allowMessageIds": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "An array of message id for suppress error report. message id is defined in each rule."
                },
                "severity": {
                    "$ref": "#/definitions/SecretLintRuleSeverityLevel",
                    "description": "Severity level for the rule. Default: \"error\""
                }
            },
            "required": [
                "id"
            ]
        },
        "SecretLintRuleCreatorOptions": {
            "type": "object"
        },
        "SecretLintRuleSeverityLevel": {
            "type": "string",
            "enum": [
                "info",
                "warning",
                "error"
            ],
            "description": "Rule Severity Level"
        },
        "SecretLintConfigDescriptorRulePreset": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "description": "**Required** Rule Preset id that is package name or shorten package name Examples\n- \"@scope/secretlint-rule-preset-example\" or \"@scope/preset-example\"(shorten)\n- \"secretlint-rule-preset-example\" or \"preset-example\"(shorten)"
                },
                "options": {
                    "$ref": "#/definitions/SecretLintRulePresetCreatorOptions",
                    "description": "Rule options See each rule documentation Default: {} (empty object)"
                },
                "disabled": {
                    "type": "boolean",
                    "description": "Disable the rule Default: false"
                },
                "rules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/SecretLintConfigDescriptorRule"
                    },
                    "description": "Preset's rule definitions rules is an array of rule definition Example {     \"id\": \"preset\"     \"rules\": [{ \"id\": \"example\", \"options\": {}]} }"
                }
            },
            "required": [
                "id"
            ]
        },
        "SecretLintRulePresetCreatorOptions": {
            "type": "object"
        },
        "SecretLintConfigDescriptorRule_": {
            "$ref": "#/definitions/SecretLintConfigDescriptorRule"
        },
        "SecretLintConfigDescriptorRulePreset_": {
            "$ref": "#/definitions/SecretLintConfigDescriptorRulePreset"
        }
    }
};
const ajv = new Ajv({ removeAdditional: true }).addSchema(SCHEMA, "SCHEMA");
export function validateSecretLintConfigDescriptor_(payload) {
    /** Schema is defined in {@link SCHEMA.definitions.SecretLintConfigDescriptor_ } **/
    const validator = ajv.getSchema("SCHEMA#/definitions/SecretLintConfigDescriptor_");
    const valid = validator(payload);
    if (!valid) {
        const error = new Error('Invalid SecretLintConfigDescriptor_: ' + ajv.errorsText(validator.errors, { dataVar: "SecretLintConfigDescriptor_" }));
        error.name = "ValidationError";
        throw error;
    }
    return payload;
}
export function isSecretLintConfigDescriptor_(payload) {
    try {
        validateSecretLintConfigDescriptor_(payload);
        return true;
    }
    catch (error) {
        return false;
    }
}
export function validateSecretLintConfigDescriptorRule_(payload) {
    /** Schema is defined in {@link SCHEMA.definitions.SecretLintConfigDescriptorRule_ } **/
    const validator = ajv.getSchema("SCHEMA#/definitions/SecretLintConfigDescriptorRule_");
    const valid = validator(payload);
    if (!valid) {
        const error = new Error('Invalid SecretLintConfigDescriptorRule_: ' + ajv.errorsText(validator.errors, { dataVar: "SecretLintConfigDescriptorRule_" }));
        error.name = "ValidationError";
        throw error;
    }
    return payload;
}
export function isSecretLintConfigDescriptorRule_(payload) {
    try {
        validateSecretLintConfigDescriptorRule_(payload);
        return true;
    }
    catch (error) {
        return false;
    }
}
export function validateSecretLintConfigDescriptorRulePreset_(payload) {
    /** Schema is defined in {@link SCHEMA.definitions.SecretLintConfigDescriptorRulePreset_ } **/
    const validator = ajv.getSchema("SCHEMA#/definitions/SecretLintConfigDescriptorRulePreset_");
    const valid = validator(payload);
    if (!valid) {
        const error = new Error('Invalid SecretLintConfigDescriptorRulePreset_: ' + ajv.errorsText(validator.errors, { dataVar: "SecretLintConfigDescriptorRulePreset_" }));
        error.name = "ValidationError";
        throw error;
    }
    return payload;
}
export function isSecretLintConfigDescriptorRulePreset_(payload) {
    try {
        validateSecretLintConfigDescriptorRulePreset_(payload);
        return true;
    }
    catch (error) {
        return false;
    }
}
//# sourceMappingURL=descriptor-types.validator.js.map