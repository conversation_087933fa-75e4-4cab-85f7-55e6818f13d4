{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAEhF,MAAM,OAAO,GAAG,mBAAmB,CAAC;IAClC,kBAAkB,EAAE,iBAAiB;IACrC,SAAS,EAAE,OAAO;CACnB,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GAAsB,OAAO,CAAC,MAAM,CAAC;AAE7D;;;;;;;;GAQG;AACH,MAAM,UAAU,WAAW,CAAC,KAAqB;IAC/C,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAC,SAAiB;IAClD,OAAO,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC/C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createLoggerContext } from \"@typespec/ts-http-runtime/internal/logger\";\n\nconst context = createLoggerContext({\n  logLevelEnvVarName: \"AZURE_LOG_LEVEL\",\n  namespace: \"azure\",\n});\n\n/**\n * The AzureLogger provides a mechanism for overriding where logs are output to.\n * By default, logs are sent to stderr.\n * Override the `log` method to redirect logs to another location.\n */\nexport const AzureLogger: AzureClientLogger = context.logger;\n\n/**\n * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n * @param level - The log level to enable for logging.\n * Options from most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport function setLogLevel(level?: AzureLogLevel): void {\n  context.setLogLevel(level);\n}\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function getLogLevel(): AzureLogLevel | undefined {\n  return context.getLogLevel();\n}\n\n/**\n * Creates a logger for use by the Azure SDKs that inherits from `AzureLogger`.\n * @param namespace - The name of the SDK package.\n * @hidden\n */\nexport function createClientLogger(namespace: string): AzureLogger {\n  return context.createClientLogger(namespace);\n}\n\n/**\n * A log function that can be dynamically enabled and redirected.\n */\nexport interface Debugger {\n  /**\n   * Logs the given arguments to the `log` method.\n   */\n  (...args: any[]): void;\n  /**\n   * True if this logger is active and logging.\n   */\n  enabled: boolean;\n  /**\n   * Used to cleanup/remove this logger.\n   */\n  destroy: () => boolean;\n  /**\n   * The current log method. Can be overridden to redirect output.\n   */\n  log: (...args: any[]) => void;\n  /**\n   * The namespace of this logger.\n   */\n  namespace: string;\n  /**\n   * Extends this logger with a child namespace.\n   * Namespaces are separated with a ':' character.\n   */\n  extend: (namespace: string) => Debugger;\n}\n\n/**\n * The log levels supported by the logger.\n * The log levels in order of most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport type AzureLogLevel = \"verbose\" | \"info\" | \"warning\" | \"error\";\n\n/**\n * An AzureClientLogger is a function that can log to an appropriate severity level.\n */\nexport type AzureClientLogger = Debugger;\n\n/**\n * Defines the methods available on the SDK-facing logger.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport interface AzureLogger {\n  /**\n   * Used for failures the program is unlikely to recover from,\n   * such as Out of Memory.\n   */\n  error: Debugger;\n  /**\n   * Used when a function fails to perform its intended task.\n   * Usually this means the function will throw an exception.\n   * Not used for self-healing events (e.g. automatic retry)\n   */\n  warning: Debugger;\n  /**\n   * Used when a function operates normally.\n   */\n  info: Debugger;\n  /**\n   * Used for detailed troubleshooting scenarios. This is\n   * intended for use by developers / system administrators\n   * for diagnosing specific failures.\n   */\n  verbose: Debugger;\n}\n"]}