{"version": 3, "file": "azurePowerShellCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azurePowerShellCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link AzurePowerShellCredential}\n */\nexport interface AzurePowerShellCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Allows specifying a tenant ID\n   */\n  tenantId?: string;\n  /**\n   * Process timeout configurable for making token requests, provided in milliseconds\n   */\n  processTimeoutInMs?: number;\n}\n"]}