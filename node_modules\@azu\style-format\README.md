## style-format

Library to easily style texts with string formatting.
It's based on [ansi-codes](http://github.com/azer/ansi-codes) and [format-text](http://github.com/azu/format-text)

## Install

```bash
$ npm install @azu/style-format
```

## Usage

```js
format('{blue} hello {green} world')
// => hello world

format('{bold}{red} span {yellow} eggs {reset}')
// => span eggs
```

## Reference

See [ansi-codes](http://github.com/azer/ansi-codes) for reference.

## License

MIT

Original is [style-format](http://github.com/azer/style-format).
