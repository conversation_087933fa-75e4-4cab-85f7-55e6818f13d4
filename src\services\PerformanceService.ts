// Performance monitoring and optimization service
// Tracks performance metrics and provides optimization insights

import * as vscode from 'vscode';

export interface PerformanceMetric {
    name: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    metadata?: any;
}

export interface PerformanceReport {
    totalOperations: number;
    averageResponseTime: number;
    slowestOperations: PerformanceMetric[];
    fastestOperations: PerformanceMetric[];
    workerUtilization: number;
    streamingMetrics: {
        totalStreams: number;
        averageFirstTokenTime: number;
        averageTokensPerSecond: number;
    };
    keyMetrics: {
        extensionActivationTime: number;
        timeToFirstToken: number;
        fileWorkerProcessingTime: number;
        bundleLoadTime: number;
        memoryUsage: number;
    };
    baselines: PerformanceBaselines;
    alerts: PerformanceAlert[];
}

export interface PerformanceBaselines {
    extensionActivation: { baseline: number; threshold: number };
    firstTokenResponse: { baseline: number; threshold: number };
    fileProcessing: { baseline: number; threshold: number };
    workerOperations: { baseline: number; threshold: number };
    memoryUsage: { baseline: number; threshold: number };
}

export interface PerformanceAlert {
    metric: string;
    current: number;
    baseline: number;
    threshold: number;
    severity: 'low' | 'medium' | 'high';
    timestamp: number;
    message: string;
}

export class PerformanceService {
    private metrics: Map<string, PerformanceMetric> = new Map();
    private completedMetrics: PerformanceMetric[] = [];
    private context: vscode.ExtensionContext;
    private maxMetricsHistory = 1000;
    private baselines: PerformanceBaselines = {
        extensionActivation: { baseline: 1200, threshold: 1380 },
        firstTokenResponse: { baseline: 500, threshold: 575 },
        fileProcessing: { baseline: 100, threshold: 115 },
        workerOperations: { baseline: 200, threshold: 230 },
        memoryUsage: { baseline: 50 * 1024 * 1024, threshold: 57.5 * 1024 * 1024 }
    };
    private alerts: PerformanceAlert[] = [];
    private isMonitoring = true;
    private historicalData: Map<string, number[]> = new Map();
    private sessionStartTime = Date.now();

    // Key performance metrics to track
    private readonly KEY_METRICS = {
        EXTENSION_ACTIVATION: 'extension_activation',
        TIME_TO_FIRST_TOKEN: 'time_to_first_token',
        FILE_WORKER_PROCESSING: 'file_worker_processing',
        BUNDLE_LOAD_TIME: 'bundle_load_time',
        MEMORY_USAGE: 'memory_usage'
    };

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadPersistedMetrics();
        this.loadHistoricalData();
        this.initializeBaselines();
        this.startPerformanceMonitoring();
    }

    /**
     * Start tracking a performance metric
     */
    public startMetric(name: string, metadata?: any): string {
        const metricId = `${name}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        
        const metric: PerformanceMetric = {
            name,
            startTime: performance.now(),
            metadata
        };

        this.metrics.set(metricId, metric);
        return metricId;
    }

    /**
     * End tracking a performance metric
     */
    public endMetric(metricId: string, additionalMetadata?: any): PerformanceMetric | null {
        const metric = this.metrics.get(metricId);
        if (!metric) {
            console.warn(`Performance metric ${metricId} not found`);
            return null;
        }

        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;
        
        if (additionalMetadata) {
            metric.metadata = { ...metric.metadata, ...additionalMetadata };
        }

        // Move to completed metrics
        this.metrics.delete(metricId);
        this.completedMetrics.push(metric);

        // Trim history if too large
        if (this.completedMetrics.length > this.maxMetricsHistory) {
            this.completedMetrics.splice(0, this.completedMetrics.length - this.maxMetricsHistory);
        }

        // Persist metrics periodically
        if (this.completedMetrics.length % 10 === 0) {
            this.persistMetrics();
        }

        return metric;
    }

    /**
     * Track a complete operation
     */
    public async trackOperation<T>(
        name: string,
        operation: () => Promise<T>,
        metadata?: any
    ): Promise<T> {
        const metricId = this.startMetric(name, metadata);
        
        try {
            const result = await operation();
            this.endMetric(metricId, { success: true });
            return result;
        } catch (error) {
            this.endMetric(metricId, { 
                success: false, 
                error: error instanceof Error ? error.message : 'Unknown error' 
            });
            throw error;
        }
    }

    /**
     * Track streaming operation metrics
     */
    public trackStreamingMetrics(streamId: string, event: 'start' | 'firstToken' | 'token' | 'complete', data?: any) {
        const metricName = `streaming_${event}`;
        const existingMetric = this.findMetricByStreamId(streamId);

        if (event === 'start') {
            this.startMetric(metricName, { streamId, ...data });
        } else if (existingMetric) {
            const updates: any = { [`${event}Time`]: performance.now() };
            if (data) {
                Object.assign(updates, data);
            }
            
            existingMetric.metadata = { ...existingMetric.metadata, ...updates };
            
            if (event === 'complete') {
                this.endMetric(existingMetric.name, updates);
            }
        }
    }

    /**
     * Initialize performance baselines
     */
    private initializeBaselines(): void {
        // Load existing baselines or set defaults
        const savedBaselines = this.context.globalState.get<PerformanceBaselines>('performanceBaselines');

        this.baselines = savedBaselines || {
            extensionActivation: { baseline: 1200, threshold: 1380 }, // 15% above baseline
            firstTokenResponse: { baseline: 500, threshold: 575 },
            fileProcessing: { baseline: 100, threshold: 115 },
            workerOperations: { baseline: 200, threshold: 230 },
            memoryUsage: { baseline: 50 * 1024 * 1024, threshold: 57.5 * 1024 * 1024 } // 50MB baseline
        };
    }

    /**
     * Start performance monitoring
     */
    private startPerformanceMonitoring(): void {
        // Monitor memory usage periodically
        setInterval(() => {
            if (this.isMonitoring) {
                this.trackMemoryUsage();
            }
        }, 30000); // Every 30 seconds

        // Track extension activation time
        this.trackExtensionActivation();
    }

    /**
     * Track extension activation time
     */
    private trackExtensionActivation(): void {
        const activationStart = performance.now();

        // This would be called when extension is fully activated
        setTimeout(() => {
            const activationTime = performance.now() - activationStart;
            this.recordKeyMetric(this.KEY_METRICS.EXTENSION_ACTIVATION, activationTime);
        }, 0);
    }

    /**
     * Track memory usage
     */
    private trackMemoryUsage(): void {
        if (typeof process !== 'undefined' && process.memoryUsage) {
            const memUsage = process.memoryUsage();
            this.recordKeyMetric(this.KEY_METRICS.MEMORY_USAGE, memUsage.heapUsed);
        }
    }

    /**
     * Record a key performance metric and check against baselines
     */
    public recordKeyMetric(metricType: string, value: number, metadata?: any): void {
        const metricId = this.startMetric(metricType, metadata);
        const metric = this.endMetric(metricId, { value });

        if (metric) {
            this.addToHistoricalData(metricType, value);
            this.checkPerformanceThreshold(metricType, value);
        }
    }

    /**
     * Load historical performance data
     */
    private loadHistoricalData(): void {
        try {
            const historicalData = this.context.globalState.get<Record<string, number[]>>('historicalPerformanceData', {});
            this.historicalData = new Map(Object.entries(historicalData));
        } catch (error) {
            console.error('Failed to load historical performance data:', error);
            this.historicalData = new Map();
        }
    }

    /**
     * Add value to historical data
     */
    private addToHistoricalData(metricType: string, value: number): void {
        if (!this.historicalData.has(metricType)) {
            this.historicalData.set(metricType, []);
        }

        const data = this.historicalData.get(metricType)!;
        data.push(value);

        // Keep only last 100 measurements per metric
        if (data.length > 100) {
            data.splice(0, data.length - 100);
        }

        // Persist periodically
        if (data.length % 10 === 0) {
            this.persistHistoricalData();
        }
    }

    /**
     * Persist historical data to storage
     */
    private persistHistoricalData(): void {
        try {
            const dataObject = Object.fromEntries(this.historicalData);
            this.context.globalState.update('historicalPerformanceData', dataObject);
        } catch (error) {
            console.error('Failed to persist historical performance data:', error);
        }
    }

    /**
     * Get performance trends for a metric
     */
    public getPerformanceTrend(metricType: string): any {
        const data = this.historicalData.get(metricType) || [];

        if (data.length < 5) {
            return {
                trend: 'insufficient_data',
                direction: 'unknown',
                change: 0,
                confidence: 0
            };
        }

        // Calculate trend using linear regression
        const n = data.length;
        const x = Array.from({ length: n }, (_, i) => i);
        const y = data;

        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
        const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        // Calculate R-squared for confidence
        const yMean = sumY / n;
        const ssRes = y.reduce((sum, yi, i) => {
            const predicted = slope * x[i] + intercept;
            return sum + Math.pow(yi - predicted, 2);
        }, 0);
        const ssTot = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
        const rSquared = 1 - (ssRes / ssTot);

        const direction = slope > 0 ? 'improving' : slope < 0 ? 'degrading' : 'stable';
        const change = Math.abs(slope) * n; // Total change over the period

        return {
            trend: direction,
            direction,
            change: Math.round(change),
            confidence: Math.round(rSquared * 100),
            slope: slope,
            dataPoints: n
        };
    }

    /**
     * Get session performance summary
     */
    public getSessionSummary(): any {
        const sessionDuration = Date.now() - this.sessionStartTime;
        const sessionMetrics = this.completedMetrics.filter(
            m => parseInt(m.name.split('_')[1]) >= this.sessionStartTime
        );

        return {
            sessionDuration: Math.round(sessionDuration / 1000), // seconds
            totalOperations: sessionMetrics.length,
            averageResponseTime: sessionMetrics.length > 0 ?
                sessionMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / sessionMetrics.length : 0,
            alertsGenerated: this.alerts.filter(a => a.timestamp >= this.sessionStartTime).length,
            performanceTrends: Object.values(this.KEY_METRICS).map(metric => ({
                metric,
                trend: this.getPerformanceTrend(metric)
            }))
        };
    }

    /**
     * Check if a metric exceeds performance thresholds
     */
    private checkPerformanceThreshold(metricType: string, value: number): void {
        let baseline: { baseline: number; threshold: number } | undefined;

        switch (metricType) {
            case this.KEY_METRICS.EXTENSION_ACTIVATION:
                baseline = this.baselines.extensionActivation;
                break;
            case this.KEY_METRICS.TIME_TO_FIRST_TOKEN:
                baseline = this.baselines.firstTokenResponse;
                break;
            case this.KEY_METRICS.FILE_WORKER_PROCESSING:
                baseline = this.baselines.fileProcessing;
                break;
            case this.KEY_METRICS.MEMORY_USAGE:
                baseline = this.baselines.memoryUsage;
                break;
        }

        if (baseline && value > baseline.threshold) {
            const degradation = ((value - baseline.baseline) / baseline.baseline) * 100;
            const severity = degradation > 50 ? 'high' : degradation > 25 ? 'medium' : 'low';

            const alert: PerformanceAlert = {
                metric: metricType,
                current: value,
                baseline: baseline.baseline,
                threshold: baseline.threshold,
                severity,
                timestamp: Date.now(),
                message: `Performance degradation detected: ${metricType} (${Math.round(degradation)}% above baseline)`
            };

            this.alerts.push(alert);
            this.logPerformanceAlert(alert);

            // Keep only recent alerts
            if (this.alerts.length > 100) {
                this.alerts = this.alerts.slice(-50);
            }
        }
    }

    /**
     * Log performance alert
     */
    private logPerformanceAlert(alert: PerformanceAlert): void {
        const logLevel = alert.severity === 'high' ? 'error' : alert.severity === 'medium' ? 'warn' : 'info';
        console[logLevel](`[Performance Alert] ${alert.message}`, {
            metric: alert.metric,
            current: alert.current,
            baseline: alert.baseline,
            threshold: alert.threshold
        });
    }

    /**
     * Update performance baselines based on recent measurements
     */
    public updateBaselines(): void {
        const recentMetrics = this.completedMetrics.filter(
            m => Date.now() - parseInt(m.name.split('_')[1]) < 24 * 60 * 60 * 1000 // Last 24 hours
        );

        // Update baselines based on 90th percentile of recent measurements
        Object.values(this.KEY_METRICS).forEach(metricType => {
            const metricValues = recentMetrics
                .filter(m => m.name.includes(metricType))
                .map(m => m.duration || 0)
                .sort((a, b) => a - b);

            if (metricValues.length >= 10) {
                const p90Index = Math.floor(metricValues.length * 0.9);
                const newBaseline = metricValues[p90Index];

                // Update baseline if it's reasonable (not too different from current)
                const currentBaseline = this.getBaselineForMetric(metricType);
                if (currentBaseline && Math.abs(newBaseline - currentBaseline.baseline) / currentBaseline.baseline < 0.5) {
                    this.updateBaselineForMetric(metricType, newBaseline);
                }
            }
        });

        // Persist updated baselines
        this.context.globalState.update('performanceBaselines', this.baselines);
    }

    private getBaselineForMetric(metricType: string): { baseline: number; threshold: number } | undefined {
        switch (metricType) {
            case this.KEY_METRICS.EXTENSION_ACTIVATION:
                return this.baselines.extensionActivation;
            case this.KEY_METRICS.TIME_TO_FIRST_TOKEN:
                return this.baselines.firstTokenResponse;
            case this.KEY_METRICS.FILE_WORKER_PROCESSING:
                return this.baselines.fileProcessing;
            case this.KEY_METRICS.MEMORY_USAGE:
                return this.baselines.memoryUsage;
            default:
                return undefined;
        }
    }

    private updateBaselineForMetric(metricType: string, newBaseline: number): void {
        const threshold = newBaseline * 1.15; // 15% above baseline

        switch (metricType) {
            case this.KEY_METRICS.EXTENSION_ACTIVATION:
                this.baselines.extensionActivation = { baseline: newBaseline, threshold };
                break;
            case this.KEY_METRICS.TIME_TO_FIRST_TOKEN:
                this.baselines.firstTokenResponse = { baseline: newBaseline, threshold };
                break;
            case this.KEY_METRICS.FILE_WORKER_PROCESSING:
                this.baselines.fileProcessing = { baseline: newBaseline, threshold };
                break;
            case this.KEY_METRICS.MEMORY_USAGE:
                this.baselines.memoryUsage = { baseline: newBaseline, threshold };
                break;
        }
    }

    /**
     * Get performance report
     */
    public getPerformanceReport(): PerformanceReport {
        const completedOps = this.completedMetrics.filter(m => m.duration !== undefined);
        
        if (completedOps.length === 0) {
            return {
                totalOperations: 0,
                averageResponseTime: 0,
                slowestOperations: [],
                fastestOperations: [],
                workerUtilization: 0,
                streamingMetrics: {
                    totalStreams: 0,
                    averageFirstTokenTime: 0,
                    averageTokensPerSecond: 0
                },
                keyMetrics: {
                    extensionActivationTime: 0,
                    timeToFirstToken: 0,
                    fileWorkerProcessingTime: 0,
                    bundleLoadTime: 0,
                    memoryUsage: 0
                },
                baselines: this.baselines,
                alerts: []
            };
        }

        // Calculate basic metrics
        const totalDuration = completedOps.reduce((sum, m) => sum + (m.duration || 0), 0);
        const averageResponseTime = totalDuration / completedOps.length;

        // Sort by duration
        const sortedByDuration = [...completedOps].sort((a, b) => (b.duration || 0) - (a.duration || 0));
        const slowestOperations = sortedByDuration.slice(0, 5);
        const fastestOperations = sortedByDuration.slice(-5).reverse();

        // Calculate worker utilization (simplified)
        const workerOps = completedOps.filter(m => m.name.includes('worker') || m.name.includes('diff') || m.name.includes('search'));
        const workerUtilization = workerOps.length / completedOps.length;

        // Calculate streaming metrics
        const streamingOps = completedOps.filter(m => m.name.includes('streaming'));
        const streamingMetrics = this.calculateStreamingMetrics(streamingOps);

        // Calculate key metrics
        const keyMetrics = this.calculateKeyMetrics(completedOps);

        return {
            totalOperations: completedOps.length,
            averageResponseTime,
            slowestOperations,
            fastestOperations,
            workerUtilization,
            streamingMetrics,
            keyMetrics,
            baselines: this.baselines,
            alerts: this.alerts.slice(-10) // Last 10 alerts
        };
    }

    /**
     * Calculate key performance metrics
     */
    private calculateKeyMetrics(completedOps: PerformanceMetric[]): any {
        const getAverageForMetric = (metricType: string) => {
            const metrics = completedOps.filter(m => m.name.includes(metricType));
            if (metrics.length === 0) return 0;
            return metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / metrics.length;
        };

        const getLatestForMetric = (metricType: string) => {
            const metrics = completedOps.filter(m => m.name.includes(metricType));
            if (metrics.length === 0) return 0;
            return metrics[metrics.length - 1]?.duration || 0;
        };

        return {
            extensionActivationTime: getLatestForMetric(this.KEY_METRICS.EXTENSION_ACTIVATION),
            timeToFirstToken: getAverageForMetric(this.KEY_METRICS.TIME_TO_FIRST_TOKEN),
            fileWorkerProcessingTime: getAverageForMetric(this.KEY_METRICS.FILE_WORKER_PROCESSING),
            bundleLoadTime: getLatestForMetric(this.KEY_METRICS.BUNDLE_LOAD_TIME),
            memoryUsage: getLatestForMetric(this.KEY_METRICS.MEMORY_USAGE)
        };
    }

    /**
     * Get metrics by category
     */
    public getMetricsByCategory(category: string): PerformanceMetric[] {
        return this.completedMetrics.filter(m => m.name.includes(category));
    }

    /**
     * Clear all metrics
     */
    public clearMetrics(): void {
        this.metrics.clear();
        this.completedMetrics.length = 0;
        this.persistMetrics();
    }

    /**
     * Export metrics for analysis
     */
    public exportMetrics(): string {
        const report = this.getPerformanceReport();
        const exportData = {
            report,
            rawMetrics: this.completedMetrics,
            timestamp: new Date().toISOString()
        };
        
        return JSON.stringify(exportData, null, 2);
    }

    private findMetricByStreamId(streamId: string): PerformanceMetric | undefined {
        for (const metric of this.metrics.values()) {
            if (metric.metadata?.streamId === streamId) {
                return metric;
            }
        }
        return undefined;
    }

    private calculateStreamingMetrics(streamingOps: PerformanceMetric[]) {
        if (streamingOps.length === 0) {
            return {
                totalStreams: 0,
                averageFirstTokenTime: 0,
                averageTokensPerSecond: 0
            };
        }

        const firstTokenTimes = streamingOps
            .map(m => m.metadata?.firstTokenTime - m.startTime)
            .filter(t => t > 0);

        const tokensPerSecond = streamingOps
            .map(m => {
                const duration = (m.duration || 0) / 1000; // Convert to seconds
                const tokens = m.metadata?.totalTokens || 0;
                return duration > 0 ? tokens / duration : 0;
            })
            .filter(tps => tps > 0);

        return {
            totalStreams: streamingOps.length,
            averageFirstTokenTime: firstTokenTimes.length > 0 
                ? firstTokenTimes.reduce((sum, t) => sum + t, 0) / firstTokenTimes.length 
                : 0,
            averageTokensPerSecond: tokensPerSecond.length > 0
                ? tokensPerSecond.reduce((sum, tps) => sum + tps, 0) / tokensPerSecond.length
                : 0
        };
    }

    private loadPersistedMetrics(): void {
        try {
            const persistedMetrics = this.context.globalState.get<PerformanceMetric[]>('performanceMetrics', []);
            this.completedMetrics = persistedMetrics.slice(-this.maxMetricsHistory);
        } catch (error) {
            console.error('Failed to load persisted performance metrics:', error);
            this.completedMetrics = [];
        }
    }

    private persistMetrics(): void {
        try {
            // Only persist the most recent metrics to avoid storage bloat
            const metricsToSave = this.completedMetrics.slice(-100);
            this.context.globalState.update('performanceMetrics', metricsToSave);
        } catch (error) {
            console.error('Failed to persist performance metrics:', error);
        }
    }

    /**
     * Show performance report in VS Code
     */
    public async showPerformanceReport(): Promise<void> {
        const report = this.getPerformanceReport();
        const reportText = this.formatReportForDisplay(report);

        const panel = vscode.window.createWebviewPanel(
            'v1b3-performance',
            'V1b3-Sama Performance Report',
            vscode.ViewColumn.One,
            { enableScripts: false }
        );

        panel.webview.html = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Performance Report</title>
                <style>
                    body { font-family: monospace; padding: 20px; }
                    .metric { margin: 10px 0; }
                    .slow { color: #ff6b6b; }
                    .fast { color: #51cf66; }
                    .average { color: #339af0; }
                </style>
            </head>
            <body>
                <h1>V1b3-Sama Performance Report</h1>
                <pre>${reportText}</pre>
            </body>
            </html>
        `;
    }

    private formatReportForDisplay(report: PerformanceReport): string {
        return `
Performance Summary:
==================
Total Operations: ${report.totalOperations}
Average Response Time: ${report.averageResponseTime.toFixed(2)}ms
Worker Utilization: ${(report.workerUtilization * 100).toFixed(1)}%

Streaming Metrics:
=================
Total Streams: ${report.streamingMetrics.totalStreams}
Average First Token Time: ${report.streamingMetrics.averageFirstTokenTime.toFixed(2)}ms
Average Tokens/Second: ${report.streamingMetrics.averageTokensPerSecond.toFixed(2)}

Slowest Operations:
==================
${report.slowestOperations.map(op => 
    `${op.name}: ${(op.duration || 0).toFixed(2)}ms`
).join('\n')}

Fastest Operations:
==================
${report.fastestOperations.map(op => 
    `${op.name}: ${(op.duration || 0).toFixed(2)}ms`
).join('\n')}
        `.trim();
    }
}
