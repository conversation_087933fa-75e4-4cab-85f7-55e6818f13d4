{"version": 3, "file": "credentialPersistenceOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/credentialPersistenceOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCachePersistenceOptions } from \"../msal/nodeFlows/tokenCachePersistenceOptions.js\";\n\n/**\n * Shared configuration options for credentials that support persistent token\n * caching.\n */\nexport interface CredentialPersistenceOptions {\n  /**\n   * Options to provide to the persistence layer (if one is available) when\n   * storing credentials.\n   *\n   * You must first register a persistence provider plugin. See the\n   * `@azure/identity-cache-persistence` package on NPM.\n   *\n   * Example:\n   *\n   * ```ts snippet:credential_persistence_options_example\n   * import { useIdentityPlugin, DeviceCodeCredential } from \"@azure/identity\";\n   *\n   * useIdentityPlugin(cachePersistencePlugin);\n   *\n   * const credential = new DeviceCodeCredential({\n   *   tokenCachePersistenceOptions: {\n   *     enabled: true,\n   *   },\n   * });\n   * ```\n   */\n\n  tokenCachePersistenceOptions?: TokenCachePersistenceOptions;\n}\n"]}