{"version": 3, "file": "uuidUtils.common.js", "sourceRoot": "", "sources": ["../../../src/util/uuidUtils.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,MAAM,UAAU,YAAY;IAC1B,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,4CAA4C;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QACpD,iDAAiD;QACjD,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YACb,IAAI,IAAI,GAAG,CAAC;QACd,CAAC;aAAM,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YACpB,oDAAoD;YACpD,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,oDAAoD;YACpD,IAAI,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACpC,CAAC;QACD,8DAA8D;QAC9D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YAChD,IAAI,IAAI,GAAG,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,YAAY,EAAE,CAAC;AACxB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function generateUUID(): string {\n  let uuid = \"\";\n  for (let i = 0; i < 32; i++) {\n    // Generate a random number between 0 and 15\n    const randomNumber = Math.floor(Math.random() * 16);\n    // Set the UUID version to 4 in the 13th position\n    if (i === 12) {\n      uuid += \"4\";\n    } else if (i === 16) {\n      // Set the UUID variant to \"10\" in the 17th position\n      uuid += (randomNumber & 0x3) | 0x8;\n    } else {\n      // Add a random hexadecimal digit to the UUID string\n      uuid += randomNumber.toString(16);\n    }\n    // Add hyphens to the UUID string at the appropriate positions\n    if (i === 7 || i === 11 || i === 15 || i === 19) {\n      uuid += \"-\";\n    }\n  }\n  return uuid;\n}\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return generateUUID();\n}\n"]}