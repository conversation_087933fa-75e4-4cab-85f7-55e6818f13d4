import { Sari<PERSON><PERSON><PERSON><PERSON>, SarifR<PERSON>ult<PERSON>uilder, SarifRuleBuilder, SarifRunBuilder } from "node-sarif-builder";
import path from "node:path";
import { pathToFileURL } from "node:url";
function buildSarifResult(lintResults) {
    // SARIF builder
    const sarifBuilder = new SarifBuilder();
    // SARIF Run builder
    const sarifRunBuilder = new SarifRunBuilder().initSimple({
        toolDriverName: "secretlint",
        toolDriverVersion: "1.0.0",
        url: "https://github.com/secretlint/secretlint"
    });
    // SARIF rules
    const addedRuleSet = new Set();
    lintResults.forEach((result) => {
        result.messages.forEach((message) => {
            const ruleId = message.ruleParentId ? `${message.ruleParentId} > ${message.ruleId}` : message.ruleId;
            if (addedRuleSet.has(ruleId)) {
                return;
            }
            addedRuleSet.add(ruleId);
            const sarifRuleBuiler = new SarifRuleBuilder().initSimple({
                ruleId: ruleId,
                shortDescriptionText: `secretlint rule(${ruleId}) error`,
                helpUri: message?.docsUrl
            });
            sarifRunBuilder.addRule(sarifRuleBuiler);
        });
    });
    // Add SARIF results (individual errors)
    lintResults.forEach((result) => {
        result.messages.forEach((message) => {
            const sarifResultBuilder = new SarifResultBuilder();
            const ruleId = message.ruleParentId ? `${message.ruleParentId} > ${message.ruleId}` : message.ruleId;
            const sarifResultInit = {
                level: message.severity === "info" ? "note" : message.severity,
                messageText: message.message,
                ruleId: ruleId,
                fileUri: process.env.SARIF_URI_ABSOLUTE
                    ? pathToFileURL(result.filePath).toString()
                    : path.relative(process.cwd(), result.filePath),
                startLine: fixLine(message.loc.start.line),
                startColumn: fixCol(message.loc.start.column),
                endLine: fixLine(message.loc.end.line),
                endColumn: fixCol(message.loc.end.column)
            };
            sarifResultBuilder.initSimple(sarifResultInit);
            sarifRunBuilder.addResult(sarifResultBuilder);
        });
    });
    sarifBuilder.addRun(sarifRunBuilder);
    return sarifBuilder.buildSarifJsonString({ indent: true });
}
function fixLine(val) {
    if (val === null) {
        return undefined;
    }
    return val === 0 ? 1 : val;
}
function fixCol(val) {
    if (val === null) {
        return undefined;
    }
    return val === 0 ? 1 : val + 1;
}
const formatter = (results) => {
    return buildSarifResult(results);
};
export default formatter;
//# sourceMappingURL=index.js.map