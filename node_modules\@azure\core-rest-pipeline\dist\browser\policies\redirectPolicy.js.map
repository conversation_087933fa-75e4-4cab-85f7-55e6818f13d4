{"version": 3, "file": "redirectPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/redirectPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,kBAAkB,IAAI,qBAAqB,EAC3C,cAAc,IAAI,iBAAiB,GACpC,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;AAaxD;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAAC,UAAiC,EAAE;IAChE,OAAO,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACpC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  redirectPolicyName as tspRedirectPolicyName,\n  redirectPolicy as tspRedirectPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the redirectPolicy.\n */\nexport const redirectPolicyName = tspRedirectPolicyName;\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectPolicyOptions {\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy to follow Location headers from the server in order\n * to support server-side redirection.\n * In the browser, this policy is not used.\n * @param options - Options to control policy behavior.\n */\nexport function redirectPolicy(options: RedirectPolicyOptions = {}): PipelinePolicy {\n  return tspRedirectPolicy(options);\n}\n"]}