{"version": 3, "file": "deviceCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/deviceCodeCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { InteractiveCredentialOptions } from \"./interactiveCredentialOptions.js\";\n\n/**\n * Provides the user code and verification URI where the code must be\n * entered.  Also provides a message to display to the user which\n * contains an instruction with these details.\n */\nexport interface DeviceCodeInfo {\n  /**\n   * The device code that the user must enter into the verification page.\n   */\n  userCode: string;\n\n  /**\n   * The verification URI to which the user must navigate to enter the device\n   * code.\n   */\n  verificationUri: string;\n\n  /**\n   * A message that may be shown to the user to instruct them on how to enter\n   * the device code in the page specified by the verification URI.\n   */\n  message: string;\n}\n\n/**\n * Defines the signature of a callback which will be passed to\n * DeviceCodeCredential for the purpose of displaying authentication\n * details to the user.\n */\nexport type DeviceCodePromptCallback = (deviceCodeInfo: DeviceCodeInfo) => void;\n\n/**\n * Defines options for the InteractiveBrowserCredential class for Node.js.\n */\nexport interface DeviceCodeCredentialOptions\n  extends InteractiveCredentialOptions,\n    CredentialPersistenceOptions {\n  /**\n   * The Microsoft Entra tenant (directory) ID.\n   */\n  tenantId?: string;\n  /**\n   * Client ID of the Microsoft Entra application that users will sign into.\n   * It is recommended that developers register their applications and assign appropriate roles.\n   * For more information, visit https://aka.ms/identity/AppRegistrationAndRoleAssignment.\n   * If not specified, users will authenticate to an Azure development application,\n   * which is not recommended for production scenarios.\n   */\n  clientId?: string;\n  /**\n   * A callback function that will be invoked to show {@link DeviceCodeInfo} to the user.\n   * If left unassigned, we will automatically log the device code information\n   * and the authentication instructions in the console.\n   */\n  userPromptCallback?: DeviceCodePromptCallback;\n}\n"]}