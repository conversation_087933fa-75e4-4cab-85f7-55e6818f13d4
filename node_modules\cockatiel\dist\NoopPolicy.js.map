{"version": 3, "file": "NoopPolicy.js", "sourceRoot": "", "sources": ["../src/NoopPolicy.ts"], "names": [], "mappings": ";;;AAAA,0CAAoD;AACpD,gDAAkE;AAGlE;;GAEG;AACH,MAAa,UAAU;IAAvB;QAEmB,aAAQ,GAAG,IAAI,yBAAc,EAAE,CAAC;QACjC,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACpC,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAQtD,CAAC;IANQ,KAAK,CAAC,OAAO,CAClB,EAA0D,EAC1D,SAAsB,0BAAkB;QAExC,OAAO,IAAA,wBAAa,EAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC;CACF;AAZD,gCAYC", "sourcesContent": ["import { neverAbortedSignal } from './common/abort';\nimport { ExecuteWrapper, returnOrThrow } from './common/Executor';\nimport { IDefaultPolicyContext, IPolicy } from './Policy';\n\n/**\n * A no-op policy, useful for unit tests and stubs.\n */\nexport class NoopPolicy implements IPolicy {\n  declare readonly _altReturn: never;\n  private readonly executor = new ExecuteWrapper();\n  public readonly onSuccess = this.executor.onSuccess;\n  public readonly onFailure = this.executor.onFailure;\n\n  public async execute<T>(\n    fn: (context: IDefaultPolicyContext) => PromiseLike<T> | T,\n    signal: AbortSignal = neverAbortedSignal,\n  ): Promise<T> {\n    return returnOrThrow(await this.executor.invoke(fn, { signal }));\n  }\n}\n"]}