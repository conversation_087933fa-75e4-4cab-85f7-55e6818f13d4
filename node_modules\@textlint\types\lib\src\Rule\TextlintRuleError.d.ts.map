{"version": 3, "file": "TextlintRuleError.d.ts", "sourceRoot": "", "sources": ["../../../src/Rule/TextlintRuleError.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF,MAAM,MAAM,mCAAmC,GAAG;IAC9C,KAAK,EAAE;QACH,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,GAAG,EAAE;QACD,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC;CACL,CAAC;AACF,MAAM,MAAM,qCAAqC,GAAG,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AACpG,MAAM,MAAM,gCAAgC,GACtC;IACI,IAAI,EAAE,kCAAkC,CAAC;IACzC,UAAU,EAAE,OAAO,CAAC;IACpB,KAAK,EAAE,qCAAqC,CAAC;CAChD,GACD;IACI,IAAI,EAAE,kCAAkC,CAAC;IACzC,UAAU,EAAE,OAAO,CAAC;IACpB,GAAG,EAAE,mCAAmC,CAAC;CAC5C,CAAC;AAER,MAAM,MAAM,wBAAwB,GAAG;IACnC;;;;;;;;;;;;;;;;OAgBG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,OAAO,CAAC,EAAE,gCAAgC,CAAC;IAC3C,GAAG,CAAC,EAAE,6BAA6B,CAAC;CACvC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,MAAM,0BAA0B,GAAG,wBAAwB,GAAG;IAChE,OAAO,EAAE,MAAM,CAAC;IAChB,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG,CAAC;CACxB,CAAC;AAEF,MAAM,WAAW,4BAA4B;IACzC,KAAK,OAAO,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,MAAM,GAAG,wBAAwB,GAAG,iBAAiB,CAAC;CACjG;AAED,MAAM,WAAW,iBAAiB;IAC9B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,OAAO,CAAC,EAAE,gCAAgC,CAAC;IACpD,QAAQ,CAAC,GAAG,CAAC,EAAE,6BAA6B,CAAC;CAChD"}