import { AuthError } from "@azure/msal-common/browser";
import * as BrowserConfigurationAuthErrorCodes from "./BrowserConfigurationAuthErrorCodes.js";
export { BrowserConfigurationAuthErrorCodes };
export declare const BrowserConfigurationAuthErrorMessages: {
    storage_not_supported: string;
    stubbed_public_client_application_called: string;
    in_mem_redirect_unavailable: string;
};
/**
 * BrowserAuthErrorMessage class containing string constants used by error codes and messages.
 * @deprecated Use BrowserAuthErrorCodes instead
 */
export declare const BrowserConfigurationAuthErrorMessage: {
    storageNotSupportedError: {
        code: string;
        desc: string;
    };
    stubPcaInstanceCalled: {
        code: string;
        desc: string;
    };
    inMemRedirectUnavailable: {
        code: string;
        desc: string;
    };
};
/**
 * Browser library error class thrown by the MSAL.js library for SPAs
 */
export declare class BrowserConfigurationAuthError extends AuthError {
    constructor(errorCode: string, errorMessage?: string);
}
export declare function createBrowserConfigurationAuthError(errorCode: string): BrowserConfigurationAuthError;
//# sourceMappingURL=BrowserConfigurationAuthError.d.ts.map