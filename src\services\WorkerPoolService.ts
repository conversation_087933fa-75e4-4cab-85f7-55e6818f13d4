// Worker Pool Service for managing Web Workers
// Provides efficient worker management and task distribution

import * as vscode from 'vscode';
import * as path from 'path';
import { WorkerMessage, WorkerResponse } from '../workers/FileSystemWorker';

// Define Worker interface for Node.js environment
interface WorkerLike {
    postMessage(message: any): void;
    terminate(): void;
    onmessage: ((event: { data: any }) => void) | null;
    onerror: ((event: { message: string }) => void) | null;
    onmessageerror: ((event: { data: any }) => void) | null;
}

// Polyfill Worker for Node.js environment
declare global {
    var WorkerConstructor: new (scriptPath: string) => WorkerLike;
    var NodeWorkerConstructor: new (scriptPath: string) => WorkerLike;
}

// Use worker_threads in Node.js environment if Worker is not available
if (typeof globalThis.Worker === 'undefined') {
    try {
        const { Worker: NodeWorker } = require('worker_threads');
        (global as any).WorkerConstructor = NodeWorker;
    } catch (error) {
        console.warn('Worker not available, WorkerPoolService will use fallback mode');
        // Create a mock Worker class for environments where workers are not available
        (global as any).WorkerConstructor = class MockWorker implements WorkerLike {
            constructor(scriptPath: string) {
                console.warn(`MockWorker created for ${scriptPath} - workers not available`);
            }
            postMessage(message: any) { /* no-op */ }
            terminate() { /* no-op */ }
            onmessage: ((event: { data: any }) => void) | null = null;
            onerror: ((event: { message: string }) => void) | null = null;
            onmessageerror: ((event: { data: any }) => void) | null = null;
        };
    }
} else {
    (global as any).WorkerConstructor = globalThis.Worker;
}

export interface WorkerTask {
    id: string;
    type: string;
    payload: any;
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout?: number;
}

export interface WorkerPoolOptions {
    maxWorkers: number;
    workerScript: string;
    taskTimeout: number;
}

export class WorkerPoolService {
    private workers: WorkerLike[] = [];
    private availableWorkers: WorkerLike[] = [];
    private busyWorkers: Set<WorkerLike> = new Set();
    private taskQueue: WorkerTask[] = [];
    private activeTasks: Map<string, WorkerTask> = new Map();
    private options: WorkerPoolOptions;
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext, options: Partial<WorkerPoolOptions> = {}) {
        this.context = context;

        // Determine optimal worker count based on system resources
        const cpuCount = require('os').cpus().length;
        const totalMemory = require('os').totalmem();
        const freeMemory = require('os').freemem();

        // Conservative worker allocation based on available resources
        let optimalWorkers = Math.max(2, Math.floor(cpuCount / 2));

        // Reduce workers if memory is constrained (less than 4GB free)
        if (freeMemory < 4 * 1024 * 1024 * 1024) {
            optimalWorkers = Math.max(1, Math.floor(optimalWorkers / 2));
        }

        // Cap at 8 workers to prevent resource exhaustion
        optimalWorkers = Math.min(8, optimalWorkers);

        this.options = {
            maxWorkers: options.maxWorkers || optimalWorkers,
            workerScript: options.workerScript || 'FileSystemWorker.js',
            taskTimeout: options.taskTimeout || 30000 // 30 seconds
        };

        console.log(`Initializing worker pool with ${this.options.maxWorkers} workers (CPU cores: ${cpuCount}, Free memory: ${Math.round(freeMemory / 1024 / 1024 / 1024)}GB)`);
        this.initializeWorkers();
    }

    private initializeWorkers(): void {
        const workerPath = path.join(this.context.extensionPath, 'dist', 'workers', this.options.workerScript);

        for (let i = 0; i < this.options.maxWorkers; i++) {
            try {
                const worker = new (global as any).WorkerConstructor(workerPath);
                this.setupWorkerEventHandlers(worker);
                this.workers.push(worker);
                this.availableWorkers.push(worker);
            } catch (error) {
                console.error(`Failed to create worker ${i}:`, error);
            }
        }

        console.log(`Initialized ${this.workers.length} workers`);
    }

    private setupWorkerEventHandlers(worker: WorkerLike): void {
        worker.onmessage = (event: { data: WorkerResponse }) => {
            this.handleWorkerMessage(worker, event.data);
        };

        worker.onerror = (error: { message: string }) => {
            console.error('Worker error:', error);
            this.handleWorkerError(worker, new Error(error.message));
        };

        worker.onmessageerror = (error: { data: any }) => {
            console.error('Worker message error:', error);
            this.handleWorkerError(worker, new Error('Worker message error'));
        };
    }

    private handleWorkerMessage(worker: WorkerLike, response: WorkerResponse): void {
        const { id, type, payload } = response;

        if (type === 'progress') {
            // Handle progress updates
            this.emitProgress(id, payload);
            return;
        }

        const task = this.activeTasks.get(id);
        if (!task) {
            console.warn(`Received response for unknown task: ${id}`);
            return;
        }

        // Remove task from active tasks
        this.activeTasks.delete(id);

        // Mark worker as available
        this.busyWorkers.delete(worker);
        this.availableWorkers.push(worker);

        // Handle response
        if (type === 'success') {
            task.resolve(payload);
        } else if (type === 'error') {
            task.reject(new Error(payload.message));
        }

        // Process next task in queue
        this.processNextTask();
    }

    private handleWorkerError(worker: WorkerLike, error: Error): void {
        console.error('Worker encountered an error:', error);

        // Find and reject all tasks assigned to this worker
        for (const [taskId, task] of this.activeTasks.entries()) {
            if (this.busyWorkers.has(worker)) {
                this.activeTasks.delete(taskId);
                task.reject(error);
            }
        }

        // Remove worker from busy set
        this.busyWorkers.delete(worker);

        // Try to restart the worker
        this.restartWorker(worker);
    }

    private restartWorker(failedWorker: WorkerLike): void {
        try {
            // Terminate the failed worker
            failedWorker.terminate();

            // Remove from workers array
            const index = this.workers.indexOf(failedWorker);
            if (index > -1) {
                this.workers.splice(index, 1);
            }

            // Remove from available workers
            const availableIndex = this.availableWorkers.indexOf(failedWorker);
            if (availableIndex > -1) {
                this.availableWorkers.splice(availableIndex, 1);
            }

            // Create new worker
            const workerPath = path.join(this.context.extensionPath, 'dist', 'workers', this.options.workerScript);
            const newWorker = new (global as any).WorkerConstructor(workerPath);
            this.setupWorkerEventHandlers(newWorker);
            
            this.workers.push(newWorker);
            this.availableWorkers.push(newWorker);

            console.log('Worker restarted successfully');

        } catch (error) {
            console.error('Failed to restart worker:', error);
        }
    }

    public async executeTask<T = any>(type: string, payload: any, timeout?: number): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const taskId = this.generateTaskId();
            const task: WorkerTask = {
                id: taskId,
                type,
                payload,
                resolve,
                reject,
                timeout: timeout || this.options.taskTimeout
            };

            // Set timeout
            const timeoutId = setTimeout(() => {
                if (this.activeTasks.has(taskId)) {
                    this.activeTasks.delete(taskId);
                    reject(new Error(`Task ${taskId} timed out after ${task.timeout}ms`));
                }
            }, task.timeout);

            // Override resolve to clear timeout
            const originalResolve = task.resolve;
            task.resolve = (value: any) => {
                clearTimeout(timeoutId);
                originalResolve(value);
            };

            // Override reject to clear timeout
            const originalReject = task.reject;
            task.reject = (error: Error) => {
                clearTimeout(timeoutId);
                originalReject(error);
            };

            // Try to execute immediately or queue
            if (this.availableWorkers.length > 0) {
                this.executeTaskOnWorker(task);
            } else {
                this.taskQueue.push(task);
            }
        });
    }

    private executeTaskOnWorker(task: WorkerTask): void {
        const worker = this.availableWorkers.pop();
        if (!worker) {
            this.taskQueue.push(task);
            return;
        }

        // Mark worker as busy
        this.busyWorkers.add(worker);
        this.activeTasks.set(task.id, task);

        // Send task to worker
        const message: WorkerMessage = {
            id: task.id,
            type: task.type as any,
            payload: task.payload
        };

        worker.postMessage(message);
    }

    private processNextTask(): void {
        if (this.taskQueue.length > 0 && this.availableWorkers.length > 0) {
            const nextTask = this.taskQueue.shift();
            if (nextTask) {
                this.executeTaskOnWorker(nextTask);
            }
        }
    }

    private generateTaskId(): string {
        return `task_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    }

    private emitProgress(taskId: string, progressData: any): void {
        // Emit progress event (could be extended to use EventEmitter)
        console.log(`Task ${taskId} progress:`, progressData);
    }

    public getPoolStats(): any {
        const now = Date.now();
        const recentTasks = Array.from(this.activeTasks.values()).filter(
            task => now - parseInt(task.id.split('_')[1]) < 60000 // Last minute
        );

        return {
            totalWorkers: this.workers.length,
            availableWorkers: this.availableWorkers.length,
            busyWorkers: this.busyWorkers.size,
            queuedTasks: this.taskQueue.length,
            activeTasks: this.activeTasks.size,
            utilization: this.busyWorkers.size / this.workers.length,
            recentTasksPerMinute: recentTasks.length,
            averageQueueTime: this.calculateAverageQueueTime(),
            workerEfficiency: this.calculateWorkerEfficiency()
        };
    }

    private calculateAverageQueueTime(): number {
        if (this.taskQueue.length === 0) return 0;

        const now = Date.now();
        const queueTimes = this.taskQueue.map(task => {
            const taskCreated = parseInt(task.id.split('_')[1]);
            return now - taskCreated;
        });

        return queueTimes.reduce((sum, time) => sum + time, 0) / queueTimes.length;
    }

    private calculateWorkerEfficiency(): number {
        if (this.workers.length === 0) return 0;

        const busyRatio = this.busyWorkers.size / this.workers.length;
        const queuePressure = Math.min(1, this.taskQueue.length / this.workers.length);

        // Efficiency is high when workers are busy but queue isn't overwhelmed
        return busyRatio * (1 - queuePressure * 0.5);
    }

    public async terminate(): Promise<void> {
        // Reject all pending tasks
        for (const task of this.activeTasks.values()) {
            task.reject(new Error('Worker pool is terminating'));
        }

        for (const task of this.taskQueue) {
            task.reject(new Error('Worker pool is terminating'));
        }

        // Terminate all workers
        const terminationPromises = this.workers.map(worker => {
            return new Promise<void>((resolve) => {
                worker.terminate();
                resolve();
            });
        });

        await Promise.all(terminationPromises);

        // Clear all collections
        this.workers.length = 0;
        this.availableWorkers.length = 0;
        this.busyWorkers.clear();
        this.taskQueue.length = 0;
        this.activeTasks.clear();

        console.log('Worker pool terminated');
    }

    // Convenience methods for common operations
    // Note: generateDiff() moved to UnifiedDiffService for consolidation

    public async searchFiles(searchPath: string, options: any): Promise<string[]> {
        return this.executeTask('search', { searchPath, ...options });
    }

    public async indexDirectory(rootPath: string, excludePatterns: string[], includePatterns: string[]): Promise<any> {
        return this.executeTask('index', { rootPath, excludePatterns, includePatterns });
    }

    public async analyzeFile(filePath: string, analysisType: string): Promise<any> {
        return this.executeTask('analyze', { filePath, analysisType });
    }

    // Internal method for UnifiedDiffService to use worker pool
    public async generateDiff(original: string, modified: string, filename?: string): Promise<any> {
        return this.executeTask('diff', { original, modified, filename });
    }
}
