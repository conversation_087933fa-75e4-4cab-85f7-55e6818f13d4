{"version": 3, "file": "azureCliCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureCliCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link AzureCliCredential}\n */\nexport interface AzureCliCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Allows specifying a tenant ID\n   */\n  tenantId?: string;\n  /**\n   * Process timeout configurable for making token requests, provided in milliseconds\n   */\n  processTimeoutInMs?: number;\n  /**\n   * Subscription is the name or ID of a subscription. Set this to acquire tokens for an account other\n   * than the Azure CLI's current account.\n   */\n  subscription?: string;\n}\n"]}