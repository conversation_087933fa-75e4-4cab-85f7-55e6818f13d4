{"version": 3, "file": "calculateRowHeights.js", "sourceRoot": "", "sources": ["../../src/calculateRowHeights.ts"], "names": [], "mappings": ";;;AAAA,+DAE+B;AAK/B,mCAGiB;AAEjB;;GAEG;AACI,MAAM,mBAAmB,GAAG,CAAC,IAAW,EAAE,MAAkB,EAAY,EAAE;IAC/E,MAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,KAAK,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;QAC5C,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;;YAC9B,MAAM,eAAe,GAAG,MAAA,MAAM,CAAC,mBAAmB,0CAAE,kBAAkB,CAAC,EAAC,GAAG,EAAE,SAAS;gBACpF,GAAG,EAAE,QAAQ,EAAC,CAAC,CAAC;YAElB,IAAI,CAAC,eAAe,EAAE;gBACpB,MAAM,UAAU,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAClH,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAE5C,OAAO;aACR;YACD,MAAM,EAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAC,GAAG,eAAe,CAAC;YAEvD,kFAAkF;YAClF,IAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,EAAE;gBAChC,MAAM,+BAA+B,GAAG,IAAA,gBAAQ,EAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChF,MAAM,2BAA2B,GAAG,WAAW,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;gBAClE,MAAM,iCAAiC,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,qBAAqB,EAAE,EAAE;;oBACpH,0BAA0B;oBAC1B,OAAO,CAAC,CAAA,MAAA,MAAM,CAAC,kBAAkB,+CAAzB,MAAM,EAAsB,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA,CAAC;gBAC1E,CAAC,CAAC,CAAC,MAAM,CAAC;gBAEV,MAAM,UAAU,GAAG,MAAM,GAAG,+BAA+B,GAAG,2BAA2B,GAAG,iCAAiC,CAAC;gBAC9H,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;aAC7C;YAED,kEAAkE;QACpE,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC5B;IAED,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAtCW,QAAA,mBAAmB,uBAsC9B"}