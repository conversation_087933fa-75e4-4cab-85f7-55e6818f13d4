{"name": "@vscode/vsce", "version": "3.5.0", "description": "VS Code Extensions Manager", "repository": {"type": "git", "url": "https://github.com/Microsoft/vsce"}, "homepage": "https://code.visualstudio.com", "bugs": "https://github.com/Microsoft/vsce/issues", "keywords": ["vscode", "vsce", "extension"], "publishConfig": {"access": "public"}, "contributors": ["Microsoft Corporation"], "author": "Microsoft Corporation", "license": "MIT", "main": "out/api.js", "typings": "dist/vsce.d.ts", "bin": {"vsce": "vsce"}, "scripts": {"compile": "tsc", "api": "api-extractor run --local --verbose", "build": "npm run compile && npm run api", "watch:build": "npm run compile -- --watch", "test": "mocha", "watch:test": "npm run test -- --watch"}, "engines": {"node": ">= 20"}, "dependencies": {"@azure/identity": "^4.1.0", "@secretlint/node": "^9.3.4", "@secretlint/secretlint-formatter-sarif": "^9.3.4", "@secretlint/secretlint-rule-no-dotenv": "^9.3.4", "@secretlint/secretlint-rule-preset-recommend": "^9.3.4", "@vscode/vsce-sign": "^2.0.0", "azure-devops-node-api": "^12.5.0", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.9", "cockatiel": "^3.1.2", "commander": "^12.1.0", "form-data": "^4.0.0", "glob": "^11.0.0", "hosted-git-info": "^4.0.2", "jsonc-parser": "^3.2.0", "leven": "^3.1.0", "markdown-it": "^14.1.0", "mime": "^1.3.4", "minimatch": "^3.0.3", "parse-semver": "^1.1.1", "read": "^1.0.7", "secretlint": "^9.3.4", "semver": "^7.5.2", "tmp": "^0.2.3", "typed-rest-client": "^1.8.4", "url-join": "^4.0.1", "xml2js": "^0.5.0", "yauzl": "^2.3.1", "yazl": "^2.2.2"}, "devDependencies": {"@microsoft/api-extractor": "^7.33.7", "@types/cheerio": "^0.22.29", "@types/glob": "^8.1.0", "@types/hosted-git-info": "^3.0.2", "@types/markdown-it": "^0.0.2", "@types/mime": "^1", "@types/minimatch": "^3.0.3", "@types/mocha": "^7.0.2", "@types/node": "^16.11.7", "@types/read": "^0.0.28", "@types/semver": "^6.0.0", "@types/tmp": "^0.2.2", "@types/url-join": "^4.0.1", "@types/xml2js": "^0.4.4", "@types/yauzl": "^2.9.2", "@types/yazl": "^2.4.2", "mocha": "^11.1.0", "source-map-support": "^0.4.2", "ts-node": "^10.9.1", "typescript": "^4.3.2"}, "optionalDependencies": {"keytar": "^7.7.0"}, "mocha": {"require": ["ts-node/register"], "watch-files": "src/**", "spec": "src/test/**/*.ts"}}