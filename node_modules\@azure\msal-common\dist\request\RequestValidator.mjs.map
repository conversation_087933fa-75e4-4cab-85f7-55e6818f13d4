{"version": 3, "file": "RequestValidator.mjs", "sources": ["../../src/request/RequestValidator.ts"], "sourcesContent": [null], "names": ["ClientConfigurationErrorCodes.redirectUriEmpty", "ClientConfigurationErrorCodes.invalidPromptValue", "ClientConfigurationErrorCodes.invalidClaims", "ClientConfigurationErrorCodes.pkceParamsMissing", "ClientConfigurationErrorCodes.invalidCodeChallengeMethod"], "mappings": ";;;;;;AAAA;;;AAGG;AAQH;;AAEG;MACU,gBAAgB,CAAA;AACzB;;;AAGG;IACH,OAAO,mBAAmB,CAAC,WAAmB,EAAA;QAC1C,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,8BAA8B,CAChCA,gBAA8C,CACjD,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;IACH,OAAO,cAAc,CAAC,MAAc,EAAA;QAChC,MAAM,YAAY,GAAG,EAAE,CAAC;AAExB,QAAA,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,SAAA;QAED,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAClC,YAAA,MAAM,8BAA8B,CAChCC,kBAAgD,CACnD,CAAC;AACL,SAAA;KACJ;IAED,OAAO,cAAc,CAAC,MAAc,EAAA;QAChC,IAAI;AACA,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACtB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,OAAO,2BAA2B,CAC9B,aAAqB,EACrB,mBAA2B,EAAA;AAE3B,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,mBAAmB,EAAE;AACxC,YAAA,MAAM,8BAA8B,CAChCC,iBAA+C,CAClD,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,CAAC;AACzD,SAAA;KACJ;AAED;;;AAGG;IACH,OAAO,2BAA2B,CAAC,mBAA2B,EAAA;QAC1D,IACI;AACI,YAAA,yBAAyB,CAAC,KAAK;AAC/B,YAAA,yBAAyB,CAAC,IAAI;AACjC,SAAA,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,EACpC;AACE,YAAA,MAAM,8BAA8B,CAChCC,0BAAwD,CAC3D,CAAC;AACL,SAAA;KACJ;AACJ;;;;"}