import type { PipelinePolicy } from "../pipeline.js";
/**
 * The programmatic identifier of the decompressResponsePolicy.
 */
export declare const decompressResponsePolicyName = "decompressResponsePolicy";
/**
 * A policy to enable response decompression according to Accept-Encoding header
 * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding
 */
export declare function decompressResponsePolicy(): PipelinePolicy;
//# sourceMappingURL=decompressResponsePolicy.d.ts.map