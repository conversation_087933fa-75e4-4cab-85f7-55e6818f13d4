{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAoBlC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport {\n  AddEventOptions,\n  Instrumenter,\n  InstrumenterSpanOptions,\n  OperationTracingOptions,\n  OptionsWithTracingContext,\n  Resolved,\n  SpanStatus,\n  SpanStatusError,\n  SpanStatusSuccess,\n  TracingClient,\n  TracingClientOptions,\n  TracingContext,\n  TracingSpan,\n  TracingSpanKind,\n  TracingSpanLink,\n  TracingSpanOptions,\n} from \"./interfaces.js\";\nexport { useInstrumenter } from \"./instrumenter.js\";\nexport { createTracingClient } from \"./tracingClient.js\";\n"]}