{"version": 3, "file": "authHostEnv-browser.mjs", "sourceRoot": "", "sources": ["../../../src/util/authHostEnv-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,8DAA8D,CAC/D,CAAC;AAEF,MAAM,UAAU,2BAA2B;IACzC,MAAM,wBAAwB,CAAC;AACjC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nconst BrowserNotSupportedError = new Error(\n  \"getAuthorityHostEnvironment is not supported in the browser.\",\n);\n\nexport function getAuthorityHostEnvironment(): { authorityHost: string } | undefined {\n  throw BrowserNotSupportedError;\n}\n"]}