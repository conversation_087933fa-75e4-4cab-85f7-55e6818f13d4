{"version": 3, "file": "TaskCancelledError.js", "sourceRoot": "", "sources": ["../../src/errors/TaskCancelledError.ts"], "names": [], "mappings": ";;;AAAA,MAAa,kBAAmB,SAAQ,KAAK;IAG3C;;OAEG;IACH,YAA4B,UAAU,qBAAqB;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QADW,YAAO,GAAP,OAAO,CAAwB;QAL3C,yBAAoB,GAAG,IAAI,CAAC;IAO5C,CAAC;CACF;AATD,gDASC", "sourcesContent": ["export class TaskCancelledError extends Error {\n  public readonly isTaskCancelledError = true;\n\n  /**\n   * Error thrown when a task is cancelled.\n   */\n  constructor(public readonly message = 'Operation cancelled') {\n    super(message);\n  }\n}\n"]}