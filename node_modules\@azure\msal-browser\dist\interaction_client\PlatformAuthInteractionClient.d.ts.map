{"version": 3, "file": "PlatformAuthInteractionClient.d.ts", "sourceRoot": "", "sources": ["../../src/interaction_client/PlatformAuthInteractionClient.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,MAAM,EACN,OAAO,EAIP,aAAa,EAEb,QAAQ,EAOR,kBAAkB,EAQlB,WAAW,EAOX,0BAA0B,EAE7B,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EACH,KAAK,EAIL,iBAAiB,EACpB,MAAM,8BAA8B,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,+CAA+C,CAAC;AACpF,OAAO,EACH,IAAI,EACJ,oBAAoB,EACvB,MAAM,gDAAgD,CAAC;AAOxD,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AAKvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAG3E,OAAO,EAAE,oBAAoB,EAAE,MAAM,gDAAgD,CAAC;AAEtF,qBAAa,6BAA8B,SAAQ,qBAAqB;IACpE,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;IACvB,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IACrD,SAAS,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC/C,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;IACpD,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;gBAGnB,MAAM,EAAE,oBAAoB,EAC5B,cAAc,EAAE,mBAAmB,EACnC,aAAa,EAAE,OAAO,EACtB,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,YAAY,EAC1B,gBAAgB,EAAE,iBAAiB,EACnC,KAAK,EAAE,KAAK,EACZ,iBAAiB,EAAE,kBAAkB,EACrC,QAAQ,EAAE,oBAAoB,EAC9B,SAAS,EAAE,MAAM,EACjB,iBAAiB,EAAE,mBAAmB,EACtC,aAAa,CAAC,EAAE,MAAM;IAuC1B;;;;OAIG;IACH,OAAO,CAAC,cAAc;IAOtB;;;OAGG;IACG,YAAY,CACd,OAAO,EAAE,YAAY,GAAG,aAAa,GAAG,gBAAgB,EACxD,iBAAiB,CAAC,EAAE,iBAAiB,GACtC,OAAO,CAAC,oBAAoB,CAAC;IAiFhC;;;;;OAKG;IACH,OAAO,CAAC,wBAAwB;IAahC;;;;;OAKG;cACa,sBAAsB,CAClC,eAAe,EAAE,MAAM,EACvB,OAAO,EAAE,mBAAmB,GAC7B,OAAO,CAAC,oBAAoB,CAAC;IAyChC;;;;OAIG;IACG,oBAAoB,CACtB,OAAO,EAAE,eAAe,EACxB,eAAe,EAAE,0BAA0B,GAC5C,OAAO,CAAC,IAAI,CAAC;IA8ChB;;;;OAIG;IACG,qBAAqB,CACvB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,aAAa,CAAC,EAAE,MAAM,GACvB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IA8DvC;;;OAGG;IACH,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAKvB;;;;;OAKG;cACa,oBAAoB,CAChC,QAAQ,EAAE,oBAAoB,EAC9B,OAAO,EAAE,mBAAmB,EAC5B,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,oBAAoB,CAAC;IAoFhC;;;;;OAKG;IACH,SAAS,CAAC,2BAA2B,CACjC,QAAQ,EAAE,oBAAoB,EAC9B,aAAa,EAAE,WAAW,GAC3B,MAAM;IAaT;;;;;OAKG;IACH,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,QAAQ;IAMxE;;;;OAIG;IACG,sBAAsB,CACxB,QAAQ,EAAE,oBAAoB,EAC9B,OAAO,EAAE,mBAAmB,GAC7B,OAAO,CAAC,MAAM,CAAC;IA8ClB;;;;;;;;;OASG;cACa,4BAA4B,CACxC,QAAQ,EAAE,oBAAoB,EAC9B,OAAO,EAAE,mBAAmB,EAC5B,aAAa,EAAE,WAAW,EAC1B,aAAa,EAAE,aAAa,EAC5B,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,oBAAoB,CAAC;IAuEhC;;;OAGG;IACG,YAAY,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAY/D;;;;;;;;;OASG;IACH,iBAAiB,CACb,QAAQ,EAAE,oBAAoB,EAC9B,OAAO,EAAE,mBAAmB,EAC5B,qBAAqB,EAAE,MAAM,EAC7B,aAAa,EAAE,WAAW,EAC1B,mBAAmB,EAAE,MAAM,EAC3B,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,MAAM,GACrB,OAAO,CAAC,IAAI,CAAC;IAoDhB,iBAAiB,CACb,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACvC,MAAM;IAQT,SAAS,CAAC,8BAA8B,CACpC,YAAY,CAAC,EAAE,MAAM,GACtB,IAAI,GAAG,IAAI;IAgCd;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAc3B;;;;OAIG;IACH,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAWlD;;;OAGG;cACa,uBAAuB,CACnC,OAAO,EAAE,YAAY,GAAG,gBAAgB,GACzC,OAAO,CAAC,mBAAmB,CAAC;YAmFjB,qBAAqB;IAoBnC,OAAO,CAAC,SAAS;IAwCjB;;;;OAIG;IACH,OAAO,CAAC,uBAAuB;CA2ClC"}