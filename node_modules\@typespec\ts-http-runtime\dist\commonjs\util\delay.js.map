{"version": 3, "file": "delay.js", "sourceRoot": "", "sources": ["../../../src/util/delay.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAUlC,kDAkBC;AA1BD,2CAAwD;AAExD;;;;;GAKG;AACH,SAAgB,mBAAmB,CACjC,YAAoB,EACpB,MAGC;IAED,6CAA6C;IAC7C,MAAM,gBAAgB,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAE3E,yCAAyC;IACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAE1E,gFAAgF;IAChF,mEAAmE;IACnE,MAAM,cAAc,GAAG,YAAY,GAAG,CAAC,GAAG,IAAA,qCAAyB,EAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IAEzF,OAAO,EAAE,cAAc,EAAE,CAAC;AAC5B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { getRandomIntegerInclusive } from \"./random.js\";\n\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n * @param retryAttempt - The current retry attempt number.\n * @param config - The exponential retry configuration.\n * @returns An object containing the calculated retry delay.\n */\nexport function calculateRetryDelay(\n  retryAttempt: number,\n  config: {\n    retryDelayInMs: number;\n    maxRetryDelayInMs: number;\n  },\n): { retryAfterInMs: number } {\n  // Exponentially increase the delay each time\n  const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);\n\n  // Don't let the delay exceed the maximum\n  const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);\n\n  // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n  // that retries across multiple clients don't occur simultaneously.\n  const retryAfterInMs = clampedDelay / 2 + getRandomIntegerInclusive(0, clampedDelay / 2);\n\n  return { retryAfterInMs };\n}\n"]}