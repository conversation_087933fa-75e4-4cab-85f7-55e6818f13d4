{"version": 3, "file": "deserializationPolicy.js", "sourceRoot": "", "sources": ["../../src/deserializationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAWlC,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAO9C,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAClD,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAEhE,MAAM,uBAAuB,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAClE,MAAM,sBAAsB,GAAG,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;AAE3E;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAyCjE;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,UAAwC,EAAE;;IAC9E,MAAM,gBAAgB,GAAG,MAAA,MAAA,OAAO,CAAC,oBAAoB,0CAAE,IAAI,mCAAI,uBAAuB,CAAC;IACvF,MAAM,eAAe,GAAG,MAAA,MAAA,OAAO,CAAC,oBAAoB,0CAAE,GAAG,mCAAI,sBAAsB,CAAC;IACpF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IACpD,MAAM,cAAc,GAA8B;QAChD,GAAG,EAAE;YACH,QAAQ,EAAE,MAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,GAAG,CAAC,QAAQ,mCAAI,EAAE;YAC/C,WAAW,EAAE,MAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,GAAG,CAAC,WAAW,mCAAI,KAAK;YACxD,UAAU,EAAE,MAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,GAAG,CAAC,UAAU,mCAAI,WAAW;SAC7D;KACF,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,uBAAuB,CAC5B,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,cAAc,EACd,QAAQ,CACT,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC9B,cAAgC;IAEhC,IAAI,MAAwC,CAAC;IAC7C,MAAM,OAAO,GAAqB,cAAc,CAAC,OAAO,CAAC;IACzD,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,aAAa,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,aAAa,CAAC;IACnD,IAAI,aAAa,EAAE,CAAC;QAClB,IAAI,CAAC,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,uBAAuB,CAAA,EAAE,CAAC;YAC5C,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,uBAAuB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,yBAAyB,CAAC,cAAgC;IACjE,MAAM,OAAO,GAAqB,cAAc,CAAC,OAAO,CAAC;IACzD,MAAM,aAAa,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,iBAAiB,CAAC;IAC3D,IAAI,MAAe,CAAC;IACpB,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,GAAG,IAAI,CAAC;IAChB,CAAC;SAAM,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE,CAAC;QAClD,MAAM,GAAG,iBAAiB,CAAC;IAC7B,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,uBAAuB,CACpC,gBAA0B,EAC1B,eAAyB,EACzB,QAA0B,EAC1B,OAAkC,EAClC,QAA2D;IAE3D,MAAM,cAAc,GAAG,MAAM,KAAK,CAChC,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,OAAO,EACP,QAAQ,CACT,CAAC;IACF,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE,CAAC;QAC/C,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,MAAM,aAAa,GAAG,uBAAuB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtE,MAAM,aAAa,GAAG,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,aAAa,CAAC;IACnD,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC/C,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,MAAM,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;IAC7D,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,mBAAmB,CACzD,cAAc,EACd,aAAa,EACb,YAAY,EACZ,OAAO,CACR,CAAC;IACF,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,KAAK,CAAC;IACd,CAAC;SAAM,IAAI,oBAAoB,EAAE,CAAC;QAChC,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,oEAAoE;IACpE,sCAAsC;IACtC,IAAI,YAAY,EAAE,CAAC;QACjB,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;YAC5B,IAAI,kBAAkB,GAAQ,cAAc,CAAC,UAAU,CAAC;YACxD,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;gBAC1F,kBAAkB;oBAChB,OAAO,kBAAkB,KAAK,QAAQ;wBACpC,CAAC,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,cAAe,CAAC;wBAC7D,CAAC,CAAC,EAAE,CAAC;YACX,CAAC;YACD,IAAI,CAAC;gBACH,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CAC9D,YAAY,CAAC,UAAU,EACvB,kBAAkB,EAClB,yBAAyB,EACzB,OAAO,CACR,CAAC;YACJ,CAAC;YAAC,OAAO,gBAAqB,EAAE,CAAC;gBAC/B,MAAM,SAAS,GAAG,IAAI,SAAS,CAC7B,SAAS,gBAAgB,iDAAiD,cAAc,CAAC,UAAU,EAAE,EACrG;oBACE,UAAU,EAAE,cAAc,CAAC,MAAM;oBACjC,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,QAAQ,EAAE,cAAc;iBACzB,CACF,CAAC;gBACF,MAAM,SAAS,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC/C,uGAAuG;YACvG,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;QAC9E,CAAC;QAED,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;YAC/B,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACjE,YAAY,CAAC,aAAa,EAC1B,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,EAC5B,EAAE,GAAG,EAAE,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAC3C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,oBAAoB,CAAC,aAA4B;IACxD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACjE,OAAO,CACL,mBAAmB,CAAC,MAAM,KAAK,CAAC;QAChC,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAC3E,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,cAAqC,EACrC,aAA4B,EAC5B,YAA8C,EAC9C,OAAkC;;IAElC,MAAM,iBAAiB,GAAG,GAAG,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;IACtF,MAAM,oBAAoB,GAAY,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC,CAAC,iBAAiB;QACnB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAEnB,IAAI,oBAAoB,EAAE,CAAC;QACzB,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC1B,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;YACtD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;QACtD,CAAC;IACH,CAAC;IAED,MAAM,iBAAiB,GAAG,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;IAE1E,MAAM,mBAAmB,GAAG,CAAA,MAAA,cAAc,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAC/E,cAAc,CAAC,MAAM,CACtB;QACC,CAAC,CAAC,2BAA2B,cAAc,CAAC,MAAM,EAAE;QACpD,CAAC,CAAE,cAAc,CAAC,UAAqB,CAAC;IAE1C,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,mBAAmB,EAAE;QAC/C,UAAU,EAAE,cAAc,CAAC,MAAM;QACjC,OAAO,EAAE,cAAc,CAAC,OAAO;QAC/B,QAAQ,EAAE,cAAc;KACzB,CAAC,CAAC;IAEH,yFAAyF;IACzF,yDAAyD;IACzD,sDAAsD;IACtD,IACE,CAAC,iBAAiB;QAClB,CAAC,CAAC,CAAA,MAAA,MAAA,cAAc,CAAC,UAAU,0CAAE,KAAK,0CAAE,IAAI,MAAI,MAAA,MAAA,cAAc,CAAC,UAAU,0CAAE,KAAK,0CAAE,OAAO,CAAA,CAAC,EACtF,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;IAED,MAAM,iBAAiB,GAAG,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,UAAU,CAAC;IACxD,MAAM,oBAAoB,GAAG,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,aAAa,CAAC;IAE9D,IAAI,CAAC;QACH,iFAAiF;QACjF,mDAAmD;QACnD,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;YAC7C,IAAI,iBAAiB,CAAC;YAEtB,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,kBAAkB,GAAQ,UAAU,CAAC;gBACzC,IAAI,aAAa,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;oBACpF,kBAAkB,GAAG,EAAE,CAAC;oBACxB,MAAM,WAAW,GAAG,iBAAiB,CAAC,cAAc,CAAC;oBACrD,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,WAAW,EAAE,CAAC;wBAClD,kBAAkB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;oBAC/C,CAAC;gBACH,CAAC;gBACD,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,WAAW,CACtD,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,EAC3B,OAAO,CACR,CAAC;YACJ,CAAC;YAED,MAAM,aAAa,GAAQ,UAAU,CAAC,KAAK,IAAI,iBAAiB,IAAI,UAAU,CAAC;YAC/E,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YAChC,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC1B,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;YACxC,CAAC;YAED,IAAI,iBAAiB,EAAE,CAAC;gBACrB,KAAK,CAAC,QAAmC,CAAC,UAAU,GAAG,iBAAiB,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,mFAAmF;QACnF,IAAI,cAAc,CAAC,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAClD,KAAK,CAAC,QAAmC,CAAC,aAAa;gBACtD,aAAa,CAAC,UAAU,CAAC,WAAW,CAClC,oBAAoB,EACpB,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,EAC/B,4BAA4B,CAC7B,CAAC;QACN,CAAC;IACH,CAAC;IAAC,OAAO,YAAiB,EAAE,CAAC;QAC3B,KAAK,CAAC,OAAO,GAAG,UAAU,YAAY,CAAC,OAAO,mDAAmD,cAAc,CAAC,UAAU,6BAA6B,CAAC;IAC1J,CAAC;IAED,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChD,CAAC;AAED,KAAK,UAAU,KAAK,CAClB,gBAA0B,EAC1B,eAAyB,EACzB,iBAAwC,EACxC,IAA+B,EAC/B,QAA2D;;IAE3D,IACE,CAAC,CAAA,MAAA,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACnF,iBAAiB,CAAC,UAAU,EAC5B,CAAC;QACD,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;QAC1C,MAAM,WAAW,GAAW,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAChF,MAAM,iBAAiB,GAAa,CAAC,WAAW;YAC9C,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,IACE,iBAAiB,CAAC,MAAM,KAAK,CAAC;gBAC9B,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EACjF,CAAC;gBACD,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAChD,OAAO,iBAAiB,CAAC;YAC3B,CAAC;iBAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5F,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAChD,CAAC;gBACD,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC5C,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;gBACpC,OAAO,iBAAiB,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,GAAG,GAAG,UAAU,GAAG,gDAAgD,iBAAiB,CAAC,UAAU,GAAG,CAAC;YACzG,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC;YAClD,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG,EAAE;gBAC3B,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,iBAAiB,CAAC,MAAM;gBACpC,OAAO,EAAE,iBAAiB,CAAC,OAAO;gBAClC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;YACH,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  FullOperationResponse,\n  OperationRequest,\n  OperationResponseMap,\n  OperationSpec,\n  RequiredSerializerOptions,\n  SerializerOptions,\n  XmlOptions,\n} from \"./interfaces.js\";\nimport { XML_CHARKEY } from \"./interfaces.js\";\nimport type {\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { RestError } from \"@azure/core-rest-pipeline\";\nimport { MapperTypeNames } from \"./serializer.js\";\nimport { getOperationRequestInfo } from \"./operationHelpers.js\";\n\nconst defaultJsonContentTypes = [\"application/json\", \"text/json\"];\nconst defaultXmlContentTypes = [\"application/xml\", \"application/atom+xml\"];\n\n/**\n * The programmatic identifier of the deserializationPolicy.\n */\nexport const deserializationPolicyName = \"deserializationPolicy\";\n\n/**\n * Options to configure API response deserialization.\n */\nexport interface DeserializationPolicyOptions {\n  /**\n   * Configures the expected content types for the deserialization of\n   * JSON and XML response bodies.\n   */\n  expectedContentTypes?: DeserializationContentTypes;\n\n  /**\n   * A function that is able to parse XML. Required for XML support.\n   */\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>;\n\n  /**\n   * Configures behavior of xml parser and builder.\n   */\n  serializerOptions?: SerializerOptions;\n}\n\n/**\n * The content-types that will indicate that an operation response should be deserialized in a\n * particular way.\n */\nexport interface DeserializationContentTypes {\n  /**\n   * The content-types that indicate that an operation response should be deserialized as JSON.\n   * Defaults to [ \"application/json\", \"text/json\" ].\n   */\n  json?: string[];\n\n  /**\n   * The content-types that indicate that an operation response should be deserialized as XML.\n   * Defaults to [ \"application/xml\", \"application/atom+xml\" ].\n   */\n  xml?: string[];\n}\n\n/**\n * This policy handles parsing out responses according to OperationSpecs on the request.\n */\nexport function deserializationPolicy(options: DeserializationPolicyOptions = {}): PipelinePolicy {\n  const jsonContentTypes = options.expectedContentTypes?.json ?? defaultJsonContentTypes;\n  const xmlContentTypes = options.expectedContentTypes?.xml ?? defaultXmlContentTypes;\n  const parseXML = options.parseXML;\n  const serializerOptions = options.serializerOptions;\n  const updatedOptions: RequiredSerializerOptions = {\n    xml: {\n      rootName: serializerOptions?.xml.rootName ?? \"\",\n      includeRoot: serializerOptions?.xml.includeRoot ?? false,\n      xmlCharKey: serializerOptions?.xml.xmlCharKey ?? XML_CHARKEY,\n    },\n  };\n\n  return {\n    name: deserializationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      const response = await next(request);\n      return deserializeResponseBody(\n        jsonContentTypes,\n        xmlContentTypes,\n        response,\n        updatedOptions,\n        parseXML,\n      );\n    },\n  };\n}\n\nfunction getOperationResponseMap(\n  parsedResponse: PipelineResponse,\n): undefined | OperationResponseMap {\n  let result: OperationResponseMap | undefined;\n  const request: OperationRequest = parsedResponse.request;\n  const operationInfo = getOperationRequestInfo(request);\n  const operationSpec = operationInfo?.operationSpec;\n  if (operationSpec) {\n    if (!operationInfo?.operationResponseGetter) {\n      result = operationSpec.responses[parsedResponse.status];\n    } else {\n      result = operationInfo?.operationResponseGetter(operationSpec, parsedResponse);\n    }\n  }\n  return result;\n}\n\nfunction shouldDeserializeResponse(parsedResponse: PipelineResponse): boolean {\n  const request: OperationRequest = parsedResponse.request;\n  const operationInfo = getOperationRequestInfo(request);\n  const shouldDeserialize = operationInfo?.shouldDeserialize;\n  let result: boolean;\n  if (shouldDeserialize === undefined) {\n    result = true;\n  } else if (typeof shouldDeserialize === \"boolean\") {\n    result = shouldDeserialize;\n  } else {\n    result = shouldDeserialize(parsedResponse);\n  }\n  return result;\n}\n\nasync function deserializeResponseBody(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  response: PipelineResponse,\n  options: RequiredSerializerOptions,\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>,\n): Promise<PipelineResponse> {\n  const parsedResponse = await parse(\n    jsonContentTypes,\n    xmlContentTypes,\n    response,\n    options,\n    parseXML,\n  );\n  if (!shouldDeserializeResponse(parsedResponse)) {\n    return parsedResponse;\n  }\n\n  const operationInfo = getOperationRequestInfo(parsedResponse.request);\n  const operationSpec = operationInfo?.operationSpec;\n  if (!operationSpec || !operationSpec.responses) {\n    return parsedResponse;\n  }\n\n  const responseSpec = getOperationResponseMap(parsedResponse);\n  const { error, shouldReturnResponse } = handleErrorResponse(\n    parsedResponse,\n    operationSpec,\n    responseSpec,\n    options,\n  );\n  if (error) {\n    throw error;\n  } else if (shouldReturnResponse) {\n    return parsedResponse;\n  }\n\n  // An operation response spec does exist for current status code, so\n  // use it to deserialize the response.\n  if (responseSpec) {\n    if (responseSpec.bodyMapper) {\n      let valueToDeserialize: any = parsedResponse.parsedBody;\n      if (operationSpec.isXML && responseSpec.bodyMapper.type.name === MapperTypeNames.Sequence) {\n        valueToDeserialize =\n          typeof valueToDeserialize === \"object\"\n            ? valueToDeserialize[responseSpec.bodyMapper.xmlElementName!]\n            : [];\n      }\n      try {\n        parsedResponse.parsedBody = operationSpec.serializer.deserialize(\n          responseSpec.bodyMapper,\n          valueToDeserialize,\n          \"operationRes.parsedBody\",\n          options,\n        );\n      } catch (deserializeError: any) {\n        const restError = new RestError(\n          `Error ${deserializeError} occurred in deserializing the responseBody - ${parsedResponse.bodyAsText}`,\n          {\n            statusCode: parsedResponse.status,\n            request: parsedResponse.request,\n            response: parsedResponse,\n          },\n        );\n        throw restError;\n      }\n    } else if (operationSpec.httpMethod === \"HEAD\") {\n      // head methods never have a body, but we return a boolean to indicate presence/absence of the resource\n      parsedResponse.parsedBody = response.status >= 200 && response.status < 300;\n    }\n\n    if (responseSpec.headersMapper) {\n      parsedResponse.parsedHeaders = operationSpec.serializer.deserialize(\n        responseSpec.headersMapper,\n        parsedResponse.headers.toJSON(),\n        \"operationRes.parsedHeaders\",\n        { xml: {}, ignoreUnknownProperties: true },\n      );\n    }\n  }\n\n  return parsedResponse;\n}\n\nfunction isOperationSpecEmpty(operationSpec: OperationSpec): boolean {\n  const expectedStatusCodes = Object.keys(operationSpec.responses);\n  return (\n    expectedStatusCodes.length === 0 ||\n    (expectedStatusCodes.length === 1 && expectedStatusCodes[0] === \"default\")\n  );\n}\n\nfunction handleErrorResponse(\n  parsedResponse: FullOperationResponse,\n  operationSpec: OperationSpec,\n  responseSpec: OperationResponseMap | undefined,\n  options: RequiredSerializerOptions,\n): { error: RestError | null; shouldReturnResponse: boolean } {\n  const isSuccessByStatus = 200 <= parsedResponse.status && parsedResponse.status < 300;\n  const isExpectedStatusCode: boolean = isOperationSpecEmpty(operationSpec)\n    ? isSuccessByStatus\n    : !!responseSpec;\n\n  if (isExpectedStatusCode) {\n    if (responseSpec) {\n      if (!responseSpec.isError) {\n        return { error: null, shouldReturnResponse: false };\n      }\n    } else {\n      return { error: null, shouldReturnResponse: false };\n    }\n  }\n\n  const errorResponseSpec = responseSpec ?? operationSpec.responses.default;\n\n  const initialErrorMessage = parsedResponse.request.streamResponseStatusCodes?.has(\n    parsedResponse.status,\n  )\n    ? `Unexpected status code: ${parsedResponse.status}`\n    : (parsedResponse.bodyAsText as string);\n\n  const error = new RestError(initialErrorMessage, {\n    statusCode: parsedResponse.status,\n    request: parsedResponse.request,\n    response: parsedResponse,\n  });\n\n  // If the item failed but there's no error spec or default spec to deserialize the error,\n  // and the parsed body doesn't look like an error object,\n  // we should fail so we just throw the parsed response\n  if (\n    !errorResponseSpec &&\n    !(parsedResponse.parsedBody?.error?.code && parsedResponse.parsedBody?.error?.message)\n  ) {\n    throw error;\n  }\n\n  const defaultBodyMapper = errorResponseSpec?.bodyMapper;\n  const defaultHeadersMapper = errorResponseSpec?.headersMapper;\n\n  try {\n    // If error response has a body, try to deserialize it using default body mapper.\n    // Then try to extract error code & message from it\n    if (parsedResponse.parsedBody) {\n      const parsedBody = parsedResponse.parsedBody;\n      let deserializedError;\n\n      if (defaultBodyMapper) {\n        let valueToDeserialize: any = parsedBody;\n        if (operationSpec.isXML && defaultBodyMapper.type.name === MapperTypeNames.Sequence) {\n          valueToDeserialize = [];\n          const elementName = defaultBodyMapper.xmlElementName;\n          if (typeof parsedBody === \"object\" && elementName) {\n            valueToDeserialize = parsedBody[elementName];\n          }\n        }\n        deserializedError = operationSpec.serializer.deserialize(\n          defaultBodyMapper,\n          valueToDeserialize,\n          \"error.response.parsedBody\",\n          options,\n        );\n      }\n\n      const internalError: any = parsedBody.error || deserializedError || parsedBody;\n      error.code = internalError.code;\n      if (internalError.message) {\n        error.message = internalError.message;\n      }\n\n      if (defaultBodyMapper) {\n        (error.response! as FullOperationResponse).parsedBody = deserializedError;\n      }\n    }\n\n    // If error response has headers, try to deserialize it using default header mapper\n    if (parsedResponse.headers && defaultHeadersMapper) {\n      (error.response! as FullOperationResponse).parsedHeaders =\n        operationSpec.serializer.deserialize(\n          defaultHeadersMapper,\n          parsedResponse.headers.toJSON(),\n          \"operationRes.parsedHeaders\",\n        );\n    }\n  } catch (defaultError: any) {\n    error.message = `Error \"${defaultError.message}\" occurred in deserializing the responseBody - \"${parsedResponse.bodyAsText}\" for the default response.`;\n  }\n\n  return { error, shouldReturnResponse: false };\n}\n\nasync function parse(\n  jsonContentTypes: string[],\n  xmlContentTypes: string[],\n  operationResponse: FullOperationResponse,\n  opts: RequiredSerializerOptions,\n  parseXML?: (str: string, opts?: XmlOptions) => Promise<any>,\n): Promise<FullOperationResponse> {\n  if (\n    !operationResponse.request.streamResponseStatusCodes?.has(operationResponse.status) &&\n    operationResponse.bodyAsText\n  ) {\n    const text = operationResponse.bodyAsText;\n    const contentType: string = operationResponse.headers.get(\"Content-Type\") || \"\";\n    const contentComponents: string[] = !contentType\n      ? []\n      : contentType.split(\";\").map((component) => component.toLowerCase());\n\n    try {\n      if (\n        contentComponents.length === 0 ||\n        contentComponents.some((component) => jsonContentTypes.indexOf(component) !== -1)\n      ) {\n        operationResponse.parsedBody = JSON.parse(text);\n        return operationResponse;\n      } else if (contentComponents.some((component) => xmlContentTypes.indexOf(component) !== -1)) {\n        if (!parseXML) {\n          throw new Error(\"Parsing XML not supported.\");\n        }\n        const body = await parseXML(text, opts.xml);\n        operationResponse.parsedBody = body;\n        return operationResponse;\n      }\n    } catch (err: any) {\n      const msg = `Error \"${err}\" occurred while parsing the response body - ${operationResponse.bodyAsText}.`;\n      const errCode = err.code || RestError.PARSE_ERROR;\n      const e = new RestError(msg, {\n        code: errCode,\n        statusCode: operationResponse.status,\n        request: operationResponse.request,\n        response: operationResponse,\n      });\n      throw e;\n    }\n  }\n\n  return operationResponse;\n}\n"]}