{"version": 3, "file": "clientCertificateCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientCertificateCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { CredentialPersistenceOptions } from \"./credentialPersistenceOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Optional parameters for the {@link ClientCertificateCredential} class.\n */\nexport interface ClientCertificateCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n  // TODO: Export again once we're ready to release this feature.\n  // /**\n  //  * Specifies a regional authority. Please refer to the {@link RegionalAuthority} type for the accepted values.\n  //  * If {@link RegionalAuthority.AutoDiscoverRegion} is specified, we will try to discover the regional authority endpoint.\n  //  * If the property is not specified, the credential uses the global authority endpoint.\n  //  */\n  // regionalAuthority?: string;\n}\n"]}