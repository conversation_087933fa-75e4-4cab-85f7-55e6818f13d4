{"name": "nise", "version": "5.1.9", "description": "Fake XHR and server", "keywords": ["test", "testing", "fake", "mock", "xhr", "server"], "repository": {"type": "git", "url": "http://github.com/sinonjs/nise.git"}, "main": "lib/index.js", "module": "nise.js", "scripts": {"bundle": "browserify --no-detect-globals -s nise -o nise.js lib/index.js", "lint": "eslint --max-warnings 35 .", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "prepare": "npm run bundle", "prepublishOnly": "mkdocs gh-deploy -r upstream || mkdocs gh-deploy -r origin", "test": "mocha lib/**/*.test.js", "test:coverage": "nyc --reporter=lcov --reporter=text --all npm test -- --reporter dot", "test:headless": "mochify --https-server --plugin [ proxyquire-universal ] --no-detect-globals test/global-hack.js lib/**/*.test.js"}, "browser": {"jsdom": false, "jsdom-global": false}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "nyc": {"exclude": ["nise.js", "coverage/**", "**/*.test.js"]}, "files": ["nise.js", "lib/**/*.js", "!lib/**/*.test.js"], "devDependencies": {"@sinonjs/eslint-config": "^5.0.2", "@sinonjs/referee": "^11.0.0", "browserify": "^16.5.2", "husky": "^4.3.8", "jsdom": "^23", "jsdom-global": "3.0.2", "lint-staged": "^15.2.0", "mocha": "^10.2.0", "mochify": "^9.2.0", "nyc": "^15.1.0", "prettier": "^3.1.1", "proxyquire": "^2.1.3", "proxyquire-universal": "^3.0.1", "proxyquireify": "^3.2.1", "sinon": ">=17"}, "dependencies": {"@sinonjs/commons": "^3.0.0", "@sinonjs/fake-timers": "^11.2.2", "@sinonjs/text-encoding": "^0.7.2", "just-extend": "^6.2.0", "path-to-regexp": "^6.2.1"}, "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint --quiet"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}