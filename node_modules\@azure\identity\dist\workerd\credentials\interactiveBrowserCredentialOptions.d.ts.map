{"version": 3, "file": "interactiveBrowserCredentialOptions.d.ts", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveBrowserCredentialOptions.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,kCAAkC,CAAC;AACpF,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,mCAAmC,CAAC;AACtF,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,mCAAmC,CAAC;AAEtF;;;;;;;;GAQG;AACH,MAAM,MAAM,iBAAiB,GAAG,UAAU,GAAG,OAAO,CAAC;AAErD;;GAEG;AACH,MAAM,WAAW,uCACf,SAAQ,4BAA4B,EAClC,4BAA4B,EAC5B,2BAA2B,EAC3B,iBAAiB;IACnB;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,4CAA6C,SAAQ,4BAA4B;IAChG;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC,CAAC;IAEtC;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;OAKG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,iBAAiB,CAAC;IAE/B;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB"}