// V1b3-Sama File Operations Test
// Tests file system access, editing, and auto-approval integration

const vscode = require('vscode');
const path = require('path');
const fs = require('fs').promises;

async function testFileOperations() {
    console.log('🧪 Starting V1b3-Sama File Operations Test...');
    
    try {
        // Get workspace folder
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            throw new Error('No workspace folder found. Please open a folder in VS Code.');
        }
        
        const workspaceRoot = workspaceFolders[0].uri.fsPath;
        console.log(`📁 Workspace root: ${workspaceRoot}`);
        
        // Test 1: Read existing files in workspace
        console.log('\n📖 Test 1: Reading workspace files...');
        await testReadWorkspaceFiles(workspaceRoot);
        
        // Test 2: Create new file using vscode.WorkspaceEdit
        console.log('\n📝 Test 2: Creating new file...');
        await testCreateFile(workspaceRoot);
        
        // Test 3: Modify existing file using vscode.WorkspaceEdit
        console.log('\n✏️ Test 3: Modifying existing file...');
        await testModifyFile(workspaceRoot);
        
        // Test 4: Test auto-approval integration
        console.log('\n🤖 Test 4: Testing auto-approval integration...');
        await testAutoApprovalIntegration();
        
        console.log('\n✅ All file operation tests completed successfully!');
        
    } catch (error) {
        console.error('❌ File operations test failed:', error);
        vscode.window.showErrorMessage(`File operations test failed: ${error.message}`);
    }
}

async function testReadWorkspaceFiles(workspaceRoot) {
    try {
        // Read package.json if it exists
        const packageJsonPath = path.join(workspaceRoot, 'package.json');
        try {
            const packageContent = await fs.readFile(packageJsonPath, 'utf8');
            const packageData = JSON.parse(packageContent);
            console.log(`✅ Successfully read package.json: ${packageData.name || 'Unknown'} v${packageData.version || 'Unknown'}`);
        } catch (error) {
            console.log('ℹ️ No package.json found (this is normal for non-Node.js projects)');
        }
        
        // List all files in workspace root
        const files = await fs.readdir(workspaceRoot);
        console.log(`✅ Found ${files.length} items in workspace root`);
        console.log(`📋 First 10 items: ${files.slice(0, 10).join(', ')}`);
        
    } catch (error) {
        throw new Error(`Failed to read workspace files: ${error.message}`);
    }
}

async function testCreateFile(workspaceRoot) {
    try {
        const testFileName = 'v1b3-test-file.txt';
        const testFilePath = path.join(workspaceRoot, testFileName);
        const testContent = `# V1b3-Sama Test File
Created at: ${new Date().toISOString()}
This file was created by the V1b3-Sama file operations test.

Test content:
- Line 1
- Line 2
- Line 3
`;
        
        // Create file using vscode.WorkspaceEdit
        const uri = vscode.Uri.file(testFilePath);
        const edit = new vscode.WorkspaceEdit();
        
        // Create the file
        edit.createFile(uri, { overwrite: true });
        
        // Apply the creation
        const createSuccess = await vscode.workspace.applyEdit(edit);
        if (!createSuccess) {
            throw new Error('Failed to create file - workspace edit rejected');
        }
        
        // Add content
        const contentEdit = new vscode.WorkspaceEdit();
        contentEdit.insert(uri, new vscode.Position(0, 0), testContent);
        
        const contentSuccess = await vscode.workspace.applyEdit(contentEdit);
        if (!contentSuccess) {
            throw new Error('Failed to add content to file - workspace edit rejected');
        }
        
        // Verify file was created
        const fileExists = await fs.access(testFilePath).then(() => true).catch(() => false);
        if (!fileExists) {
            throw new Error('File was not created on disk');
        }
        
        // Read back the content to verify
        const readContent = await fs.readFile(testFilePath, 'utf8');
        if (readContent !== testContent) {
            throw new Error('File content does not match expected content');
        }
        
        console.log(`✅ Successfully created file: ${testFileName} (${testContent.length} characters)`);
        
        // Open the file in editor
        const document = await vscode.workspace.openTextDocument(uri);
        await vscode.window.showTextDocument(document, { preview: false });
        console.log('✅ File opened in VS Code editor');
        
    } catch (error) {
        throw new Error(`Failed to create file: ${error.message}`);
    }
}

async function testModifyFile(workspaceRoot) {
    try {
        const testFileName = 'v1b3-test-file.txt';
        const testFilePath = path.join(workspaceRoot, testFileName);
        
        // Check if test file exists
        const fileExists = await fs.access(testFilePath).then(() => true).catch(() => false);
        if (!fileExists) {
            throw new Error('Test file does not exist. Create file test may have failed.');
        }
        
        const modifiedContent = `# V1b3-Sama Test File (MODIFIED)
Modified at: ${new Date().toISOString()}
This file was modified by the V1b3-Sama file operations test.

Modified content:
- Modified Line 1
- Modified Line 2
- Modified Line 3
- New Line 4
- New Line 5

Status: SUCCESSFULLY MODIFIED
`;
        
        // Modify file using vscode.WorkspaceEdit
        const uri = vscode.Uri.file(testFilePath);
        const document = await vscode.workspace.openTextDocument(uri);
        
        const edit = new vscode.WorkspaceEdit();
        const fullRange = new vscode.Range(
            document.positionAt(0),
            document.positionAt(document.getText().length)
        );
        
        edit.replace(uri, fullRange, modifiedContent);
        
        const success = await vscode.workspace.applyEdit(edit);
        if (!success) {
            throw new Error('Workspace edit was rejected by VS Code');
        }
        
        // Verify modification
        const readContent = await fs.readFile(testFilePath, 'utf8');
        if (readContent !== modifiedContent) {
            throw new Error('File content was not modified correctly');
        }
        
        console.log(`✅ Successfully modified file: ${testFileName} (${modifiedContent.length} characters)`);
        
        // Show the modified file
        await vscode.window.showTextDocument(document, { preview: false });
        console.log('✅ Modified file displayed in VS Code editor');
        
    } catch (error) {
        throw new Error(`Failed to modify file: ${error.message}`);
    }
}

async function testAutoApprovalIntegration() {
    try {
        // Test that auto-approval is working by checking the extension's auto-approval manager
        const extension = vscode.extensions.getExtension('v1b3-sama.v1b3-sama');
        if (!extension) {
            throw new Error('V1b3-Sama extension not found');
        }
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        console.log('✅ V1b3-Sama extension is active');
        console.log('✅ Auto-approval is hardcoded to always approve file operations');
        console.log('✅ File operations should execute immediately without user prompts');
        
    } catch (error) {
        throw new Error(`Failed to test auto-approval integration: ${error.message}`);
    }
}

// Export the test function
module.exports = { testFileOperations };

// If running directly, execute the test
if (require.main === module) {
    testFileOperations();
}
