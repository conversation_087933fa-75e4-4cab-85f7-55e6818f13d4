"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.RestError = void 0;
exports.isRestError = isRestError;
const ts_http_runtime_1 = require("@typespec/ts-http-runtime");
/**
 * A custom error type for failed pipeline requests.
 */
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.RestError = ts_http_runtime_1.RestError;
/**
 * Typeguard for RestError
 * @param e - Something caught by a catch clause.
 */
function isRestError(e) {
    return (0, ts_http_runtime_1.isRestError)(e);
}
//# sourceMappingURL=restError.js.map