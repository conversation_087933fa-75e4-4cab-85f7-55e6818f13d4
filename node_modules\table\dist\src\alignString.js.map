{"version": 3, "file": "alignString.js", "sourceRoot": "", "sources": ["../../src/alignString.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAuC;AAIvC,mCAEiB;AAEjB,MAAM,SAAS,GAAG,CAAC,OAAe,EAAE,KAAa,EAAU,EAAE;IAC3D,OAAO,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,KAAa,EAAU,EAAE;IAC5D,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AACrC,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,OAAe,EAAE,KAAa,EAAU,EAAE;IAC7D,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,OAAe,EAAE,KAAa,EAAU,EAAE;IAC9D,MAAM,kBAAkB,GAAG,IAAA,0BAAkB,EAAC,OAAO,CAAC,CAAC;IAEvD,IAAI,kBAAkB,KAAK,CAAC,EAAE;QAC5B,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAClC;IAED,MAAM,YAAY,GAAG,IAAA,0BAAkB,EAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;IAEnE,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,EAAE;QACjC,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAClC;IAED,IAAI,kBAAkB,GAAG,CAAC,CAAC;IAE3B,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,EAAE;QAC5C,OAAO,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,WAAW,GAAG,CAAC,OAAe,EAAE,cAAsB,EAAE,SAAoB,EAAU,EAAE;IACnG,MAAM,YAAY,GAAG,IAAA,sBAAW,EAAC,OAAO,CAAC,CAAC;IAE1C,IAAI,YAAY,KAAK,cAAc,EAAE;QACnC,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,YAAY,GAAG,cAAc,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;KAC9F;IAED,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,OAAO,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;KACnC;IAED,MAAM,cAAc,GAAG,cAAc,GAAG,YAAY,CAAC;IAErD,IAAI,SAAS,KAAK,MAAM,EAAE;QACxB,OAAO,SAAS,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAC3C;IAED,IAAI,SAAS,KAAK,OAAO,EAAE;QACzB,OAAO,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAC5C;IAED,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,OAAO,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAC9C;IAED,OAAO,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;AAC9C,CAAC,CAAC;AA9BW,QAAA,WAAW,eA8BtB"}