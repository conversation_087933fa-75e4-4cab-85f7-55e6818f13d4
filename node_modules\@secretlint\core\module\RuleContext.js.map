{"version": 3, "file": "RuleContext.js", "sourceRoot": "", "sources": ["../src/RuleContext.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAcjE,OAAO,EAAE,gBAAgB,EAAE,MAAM,6CAA6C,CAAC;AAS/E,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAAkB,EAAE;IACnD,MAAM,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;IACzC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,OAAO;QACH,MAAM,CAAC,UAAuC;YAC1C,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAClD,CAAC;QACD,QAAQ,CAAC,OAA6C;YAClD,MAAM,QAAQ,GAAG,CAAC,UAAuC,EAAE,EAAE;gBACzD,OAAO,CAAC,UAAU,CAAC,CAAC;YACxB,CAAC,CAAC;YACF,aAAa,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC1C,OAAO,GAAG,EAAE;gBACR,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;QACN,CAAC;QACD,MAAM,CAAC,UAAuC;YAC1C,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAClD,CAAC;QACD,QAAQ,CAAC,OAA6C;YAClD,MAAM,QAAQ,GAAG,CAAC,UAAuC,EAAE,EAAE;gBACzD,OAAO,CAAC,UAAU,CAAC,CAAC;YACxB,CAAC,CAAC;YACF,aAAa,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC1C,OAAO,GAAG,EAAE;gBACR,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC,CAAC;QACN,CAAC;KACJ,CAAC;AACN,CAAC,CAAC;AAeF,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,EAC9B,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,aAAa,EACb,aAAa,EACb,MAAM,GACiB,EAAyB,EAAE;IAClD,OAAO;QACH,aAAa,EAAE,aAAa;QAC5B,gBAAgB,EAAE,CAA2C,QAAW,EAAE,EAAE;YACxE,OAAO,gBAAgB,CAAC,QAAQ,EAAE;gBAC9B,aAAa,EAAE,MAAM;aACxB,CAAC,CAAC;QACP,CAAC;QACD,MAAM,CAAC,UAA0C;YAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;YACvC,aAAa,CAAC,MAAM,CAAC;gBACjB,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,MAAM;gBACd,YAAY;gBACZ,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC;gBACjD,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC;QACP,CAAC;QACD,MAAM,CAAC,UAA0C;YAC7C,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;YACxD,oCAAoC;YACpC,MAAM,aAAa,GAAG,QAAQ,IAAI,OAAO,CAAC;YAC1C,IAAI,YAAY,EAAE;gBACd,aAAa,CAAC,MAAM,CAAC;oBACjB,GAAG,UAAU;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,MAAM;oBACd,YAAY;oBACZ,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC;oBACjD,QAAQ,EAAE,aAAa;oBACvB,OAAO;oBACP,SAAS;oBACT,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS;oBACrE,IAAI;iBACP,CAAC,CAAC;aACN;iBAAM;gBACH,aAAa,CAAC,MAAM,CAAC;oBACjB,GAAG,UAAU;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,MAAM;oBACd,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC;oBACjD,QAAQ,EAAE,aAAa;oBACvB,OAAO;oBACP,SAAS;oBACT,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS;oBACrE,IAAI;iBACP,CAAC,CAAC;aACN;QACL,CAAC;KACJ,CAAC;AACN,CAAC,CAAC"}