{"version": 3, "file": "sanitizer.js", "sourceRoot": "", "sources": ["../../../src/util/sanitizer.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAsB,QAAQ,EAAE,MAAM,aAAa,CAAC;AAqB3D,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC,sFAAsF;AACtF,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;CACnB,CAAC;AAEF,MAAM,6BAA6B,GAAa,CAAC,aAAa,CAAC,CAAC;AAEhE;;GAEG;AACH,MAAM,OAAO,SAAS;IAIpB,YAAY,EACV,4BAA4B,EAAE,kBAAkB,GAAG,EAAE,EACrD,gCAAgC,EAAE,sBAAsB,GAAG,EAAE,MACzC,EAAE;QACtB,kBAAkB,GAAG,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC1E,sBAAsB,GAAG,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAEtF,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,GAAY;QAC1B,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE;YAC9B,iEAAiE;YACjE,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,uCACK,KAAK,KACR,IAAI,EAAE,KAAK,CAAC,IAAI,EAChB,OAAO,EAAE,KAAK,CAAC,OAAO,IACtB;YACJ,CAAC;YAED,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;YACtD,CAAC;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;gBAC1B,6BAA6B;gBAC7B,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,IAAI,GAAG,KAAK,UAAU,EAAE,CAAC;gBAC9B,2BAA2B;gBAC3B,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBACnC,iEAAiE;gBACjE,mDAAmD;gBACnD,OAAO,SAAS,CAAC;YACnB,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpB,OAAO,YAAY,CAAC;gBACtB,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,EACD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACxD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAEO,eAAe,CAAC,GAAkB;QACxC,MAAM,SAAS,GAAkB,EAAE,CAAC;QACpC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACnD,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;YAClC,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa,CAAC,KAAoB;QACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAkB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACrD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { type UnknownObject, isObject } from \"./object.js\";\n\n/**\n * Sanitizer options\n */\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\n// Make sure this list is up-to-date with the one under core/logger/Readme#Keyconcepts\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"WWW-Authenticate\",\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\n/**\n * A utility class to sanitize objects for logging.\n */\nexport class Sanitizer {\n  private allowedHeaderNames: Set<string>;\n  private allowedQueryParameters: Set<string>;\n\n  constructor({\n    additionalAllowedHeaderNames: allowedHeaderNames = [],\n    additionalAllowedQueryParameters: allowedQueryParameters = [],\n  }: SanitizerOptions = {}) {\n    allowedHeaderNames = defaultAllowedHeaderNames.concat(allowedHeaderNames);\n    allowedQueryParameters = defaultAllowedQueryParameters.concat(allowedQueryParameters);\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  /**\n   * Sanitizes an object for logging.\n   * @param obj - The object to sanitize\n   * @returns - The sanitized object as a string\n   */\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message,\n          };\n        }\n\n        if (key === \"headers\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2,\n    );\n  }\n\n  /**\n   * Sanitizes a URL for logging.\n   * @param value - The URL to sanitize\n   * @returns - The sanitized URL as a string\n   */\n  public sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null || value === \"\") {\n      return value;\n    }\n\n    const url = new URL(value);\n\n    if (!url.search) {\n      return value;\n    }\n\n    for (const [key] of url.searchParams) {\n      if (!this.allowedQueryParameters.has(key.toLowerCase())) {\n        url.searchParams.set(key, RedactedString);\n      }\n    }\n\n    return url.toString();\n  }\n\n  private sanitizeHeaders(obj: UnknownObject): UnknownObject {\n    const sanitized: UnknownObject = {};\n    for (const key of Object.keys(obj)) {\n      if (this.allowedHeaderNames.has(key.toLowerCase())) {\n        sanitized[key] = obj[key];\n      } else {\n        sanitized[key] = RedactedString;\n      }\n    }\n    return sanitized;\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (this.allowedQueryParameters.has(k.toLowerCase())) {\n        sanitized[k] = value[k];\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n}\n"]}