export declare const redirectUriEmpty = "redirect_uri_empty";
export declare const claimsRequestParsingError = "claims_request_parsing_error";
export declare const authorityUriInsecure = "authority_uri_insecure";
export declare const urlParseError = "url_parse_error";
export declare const urlEmptyError = "empty_url_error";
export declare const emptyInputScopesError = "empty_input_scopes_error";
export declare const invalidPromptValue = "invalid_prompt_value";
export declare const invalidClaims = "invalid_claims";
export declare const tokenRequestEmpty = "token_request_empty";
export declare const logoutRequestEmpty = "logout_request_empty";
export declare const invalidCodeChallengeMethod = "invalid_code_challenge_method";
export declare const pkceParamsMissing = "pkce_params_missing";
export declare const invalidCloudDiscoveryMetadata = "invalid_cloud_discovery_metadata";
export declare const invalidAuthorityMetadata = "invalid_authority_metadata";
export declare const untrustedAuthority = "untrusted_authority";
export declare const missingSshJwk = "missing_ssh_jwk";
export declare const missingSshKid = "missing_ssh_kid";
export declare const missingNonceAuthenticationHeader = "missing_nonce_authentication_header";
export declare const invalidAuthenticationHeader = "invalid_authentication_header";
export declare const cannotSetOIDCOptions = "cannot_set_OIDCOptions";
export declare const cannotAllowPlatformBroker = "cannot_allow_platform_broker";
export declare const authorityMismatch = "authority_mismatch";
//# sourceMappingURL=ClientConfigurationErrorCodes.d.ts.map