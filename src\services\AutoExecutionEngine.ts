import * as vscode from 'vscode';
import * as path from 'path';
import { FileOperationsService } from './FileOperationsService';
import { AutoApprovalManager } from './AutoApprovalManager';
import { ActionHistoryManager } from '../actionHistoryManager';
import { FileOperation } from './LLMResponseParser';
import { UnifiedExecutionService, ExecutionResult as UnifiedExecutionResult } from './UnifiedExecutionService';
import { workspaceValidation } from './WorkspaceValidationService';

export interface ProcessedFileOperation extends FileOperation {
    id: string;
    approvalStatus: 'auto_approved' | 'requires_approval' | 'denied';
    approvalRule?: any;
    approvalReason?: string;
    executionStatus: 'pending' | 'executing' | 'completed' | 'failed';
    executionError?: string;
    backupPath?: string;
    timestamp: number;
}

export interface ExecutionResult {
    successful: ProcessedFileOperation[];
    failed: ProcessedFileOperation[];
    skipped: ProcessedFileOperation[];
    totalExecuted: number;
    totalFailed: number;
    executionTime: number;
}

/**
 * Engine for automatically executing approved file operations
 * Handles validation, backup, execution, and error recovery
 */
export class AutoExecutionEngine {
    private fileOperationsService: FileOperationsService;
    private autoApprovalManager: AutoApprovalManager;
    private actionHistoryManager: ActionHistoryManager;
    private outputChannel: vscode.OutputChannel;
    private unifiedExecutionService: UnifiedExecutionService;

    constructor(
        fileOperationsService: FileOperationsService,
        autoApprovalManager: AutoApprovalManager,
        actionHistoryManager: ActionHistoryManager
    ) {
        this.fileOperationsService = fileOperationsService;
        this.autoApprovalManager = autoApprovalManager;
        this.actionHistoryManager = actionHistoryManager;
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama Auto-Execution');

        // Initialize unified execution service (without terminal service for file-only operations)
        this.unifiedExecutionService = new UnifiedExecutionService(
            fileOperationsService,
            undefined as any, // Terminal service not needed for file operations
            actionHistoryManager
        );
    }

    /**
     * Execute all auto-approved operations using UnifiedExecutionService
     */
    public async executeApprovedOperations(
        operations: ProcessedFileOperation[]
    ): Promise<ExecutionResult> {
        // Use unified execution service for file operations
        const result = await this.unifiedExecutionService.executeFileOperations(operations);

        // Convert result format to match AutoExecutionEngine interface
        return {
            successful: result.successful,
            failed: result.failed,
            skipped: result.skipped,
            totalExecuted: result.totalExecuted,
            totalFailed: result.totalFailed,
            executionTime: result.executionTime
        };
    }

    // Note: executeOperation() method moved to UnifiedExecutionService for consolidation

    // Note: executeCreateOperation(), executeModifyOperation(), executeCreateDirectoryOperation()
    // methods moved to UnifiedExecutionService for consolidation

    /**
     * Create backup of existing file if needed
     */
    private async createBackupIfNeeded(operation: ProcessedFileOperation): Promise<string | undefined> {
        if (operation.type !== 'modify') {
            return undefined;
        }

        try {
            const config = await this.autoApprovalManager.getConfig();
            if (!config.safetyLimits.requireConfirmationForDeletion) {
                return undefined; // Backups disabled
            }

            const fileExists = await this.fileOperationsService.fileExists(operation.path);
            if (!fileExists) {
                return undefined; // No file to backup
            }

            // Read current content
            const currentContent = await this.fileOperationsService.readFile(operation.path);
            
            // Create backup filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = `${operation.path}.backup.${timestamp}`;
            
            // Save backup
            await this.fileOperationsService.createFile(backupPath, currentContent);
            
            this.outputChannel.appendLine(`📋 Backup created: ${backupPath}`);
            return backupPath;

        } catch (error) {
            console.warn('Failed to create backup:', error);
            return undefined; // Continue without backup
        }
    }

    /**
     * Validate operation before execution
     */
    private async validateOperation(operation: ProcessedFileOperation): Promise<void> {
        // Use centralized workspace validation
        const validation = workspaceValidation.validateFilePath(operation.path);
        if (!validation.valid) {
            throw new Error(validation.error || 'Invalid file path');
        }

        // Check content for non-directory operations
        if (operation.type !== 'create_directory' && !operation.content) {
            throw new Error('File content cannot be empty for create/modify operations');
        }
    }

    /**
     * Get the current workspace folder (using centralized validation service)
     */
    private getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
        return workspaceValidation.getWorkspaceFolder();
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        this.outputChannel.dispose();
    }
}
