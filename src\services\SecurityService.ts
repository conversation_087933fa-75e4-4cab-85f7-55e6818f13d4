// Security Service for V1b3-Sama Extension
// Provides sandboxing and permission management for high-risk operations

import * as vscode from 'vscode';
import { McpToolCall } from '../interfaces/IMcpClientService';

export interface SecurityPolicy {
    allowFileSystemAccess: boolean;
    allowNetworkAccess: boolean;
    allowTerminalAccess: boolean;
    allowedFileExtensions: string[];
    blockedPaths: string[];
    maxFileSize: number; // in bytes
    requireApprovalForCommands: string[];
}

export interface PermissionRequest {
    operation: string;
    resource: string;
    details: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityContext {
    userId: string;
    sessionId: string;
    timestamp: number;
    permissions: string[];
}

export class SecurityService {
    private context: vscode.ExtensionContext;
    private defaultPolicy: SecurityPolicy;
    private activePermissions: Map<string, boolean> = new Map();

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.defaultPolicy = this.getDefaultSecurityPolicy();
    }

    /**
     * Get the default security policy
     */
    private getDefaultSecurityPolicy(): SecurityPolicy {
        return {
            allowFileSystemAccess: false,
            allowNetworkAccess: false,
            allowTerminalAccess: false,
            allowedFileExtensions: ['.txt', '.md', '.json', '.js', '.ts', '.py', '.html', '.css'],
            blockedPaths: [
                '/etc',
                '/usr/bin',
                '/System',
                'C:\\Windows',
                'C:\\Program Files',
                '~/.ssh',
                '~/.aws',
                '~/.config'
            ],
            maxFileSize: 10 * 1024 * 1024, // 10MB
            requireApprovalForCommands: [
                'rm', 'del', 'format', 'sudo', 'chmod', 'chown',
                'curl', 'wget', 'ssh', 'scp', 'git push', 'npm publish'
            ]
        };
    }

    /**
     * Get current security policy from settings
     */
    public getSecurityPolicy(): SecurityPolicy {
        const userPolicy = this.context.globalState.get<Partial<SecurityPolicy>>('securityPolicy', {});
        return { ...this.defaultPolicy, ...userPolicy };
    }

    /**
     * Update security policy
     */
    public async updateSecurityPolicy(policy: Partial<SecurityPolicy>): Promise<void> {
        const currentPolicy = this.getSecurityPolicy();
        const newPolicy = { ...currentPolicy, ...policy };
        await this.context.globalState.update('securityPolicy', newPolicy);
    }

    /**
     * Validate if an MCP tool call is safe to execute
     */
    public async validateMcpToolCall(toolCall: McpToolCall): Promise<{ allowed: boolean; reason?: string }> {
        const policy = this.getSecurityPolicy();
        
        // Check if tool involves file system operations
        if (this.isFileSystemOperation(toolCall)) {
            if (!policy.allowFileSystemAccess) {
                return { allowed: false, reason: 'File system access is disabled' };
            }
            
            const fileValidation = this.validateFileAccess(toolCall, policy);
            if (!fileValidation.allowed) {
                return fileValidation;
            }
        }

        // Check if tool involves network operations
        if (this.isNetworkOperation(toolCall)) {
            if (!policy.allowNetworkAccess) {
                return { allowed: false, reason: 'Network access is disabled' };
            }
        }

        // Check if tool involves terminal operations
        if (this.isTerminalOperation(toolCall)) {
            if (!policy.allowTerminalAccess) {
                return { allowed: false, reason: 'Terminal access is disabled' };
            }
            
            const commandValidation = this.validateCommand(toolCall, policy);
            if (!commandValidation.allowed) {
                return commandValidation;
            }
        }

        return { allowed: true };
    }

    /**
     * Request permission for a high-risk operation
     */
    public async requestPermission(request: PermissionRequest): Promise<boolean> {
        const permissionKey = `${request.operation}:${request.resource}`;
        
        // Check if permission was already granted for this session
        if (this.activePermissions.has(permissionKey)) {
            return this.activePermissions.get(permissionKey)!;
        }

        // Show permission dialog based on risk level
        let message = `V1b3-Sama wants to perform: ${request.operation}\n`;
        message += `Resource: ${request.resource}\n`;
        message += `Details: ${request.details}\n`;
        message += `Risk Level: ${request.riskLevel.toUpperCase()}`;

        const options = request.riskLevel === 'critical' 
            ? ['Deny', 'Allow Once'] 
            : ['Deny', 'Allow Once', 'Allow for Session'];

        const choice = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            ...options
        );

        const allowed = choice !== 'Deny' && choice !== undefined;
        
        if (allowed && choice === 'Allow for Session') {
            this.activePermissions.set(permissionKey, true);
        }

        return allowed;
    }

    /**
     * Check if tool call involves file system operations
     */
    private isFileSystemOperation(toolCall: McpToolCall): boolean {
        const fsKeywords = ['file', 'read', 'write', 'create', 'delete', 'move', 'copy', 'directory'];
        const toolName = toolCall.toolName.toLowerCase();
        return fsKeywords.some(keyword => toolName.includes(keyword));
    }

    /**
     * Check if tool call involves network operations
     */
    private isNetworkOperation(toolCall: McpToolCall): boolean {
        const networkKeywords = ['http', 'fetch', 'download', 'upload', 'api', 'request', 'curl', 'wget'];
        const toolName = toolCall.toolName.toLowerCase();
        return networkKeywords.some(keyword => toolName.includes(keyword));
    }

    /**
     * Check if tool call involves terminal operations
     */
    private isTerminalOperation(toolCall: McpToolCall): boolean {
        const terminalKeywords = ['exec', 'run', 'command', 'shell', 'terminal', 'process'];
        const toolName = toolCall.toolName.toLowerCase();
        return terminalKeywords.some(keyword => toolName.includes(keyword));
    }

    /**
     * Validate file access against security policy
     */
    private validateFileAccess(toolCall: McpToolCall, policy: SecurityPolicy): { allowed: boolean; reason?: string } {
        const filePath = this.extractFilePath(toolCall);
        
        if (filePath) {
            // Check blocked paths
            for (const blockedPath of policy.blockedPaths) {
                if (filePath.startsWith(blockedPath)) {
                    return { allowed: false, reason: `Access to ${blockedPath} is blocked` };
                }
            }

            // Check file extension
            const extension = filePath.substring(filePath.lastIndexOf('.'));
            if (extension && !policy.allowedFileExtensions.includes(extension)) {
                return { allowed: false, reason: `File extension ${extension} is not allowed` };
            }
        }

        return { allowed: true };
    }

    /**
     * Validate command against security policy
     */
    private validateCommand(toolCall: McpToolCall, policy: SecurityPolicy): { allowed: boolean; reason?: string } {
        const command = this.extractCommand(toolCall);
        
        if (command) {
            for (const blockedCommand of policy.requireApprovalForCommands) {
                if (command.toLowerCase().includes(blockedCommand.toLowerCase())) {
                    return { allowed: false, reason: `Command '${blockedCommand}' requires explicit approval` };
                }
            }
        }

        return { allowed: true };
    }

    /**
     * Extract file path from tool call arguments
     */
    private extractFilePath(toolCall: McpToolCall): string | null {
        const args = toolCall.arguments;
        
        // Common argument names for file paths
        const pathKeys = ['path', 'file', 'filename', 'filepath', 'src', 'dest', 'target'];
        
        for (const key of pathKeys) {
            if (args[key] && typeof args[key] === 'string') {
                return args[key];
            }
        }

        return null;
    }

    /**
     * Extract command from tool call arguments
     */
    private extractCommand(toolCall: McpToolCall): string | null {
        const args = toolCall.arguments;
        
        // Common argument names for commands
        const commandKeys = ['command', 'cmd', 'exec', 'run', 'script'];
        
        for (const key of commandKeys) {
            if (args[key] && typeof args[key] === 'string') {
                return args[key];
            }
        }

        return null;
    }

    /**
     * Clear session permissions
     */
    public clearSessionPermissions(): void {
        this.activePermissions.clear();
    }

    /**
     * Get security audit log
     */
    public getSecurityAuditLog(): any[] {
        return this.context.globalState.get('securityAuditLog', []);
    }

    /**
     * Log security event
     */
    public async logSecurityEvent(event: any): Promise<void> {
        const auditLog = this.getSecurityAuditLog();
        auditLog.push({
            ...event,
            timestamp: Date.now()
        });
        
        // Keep only last 1000 events
        if (auditLog.length > 1000) {
            auditLog.splice(0, auditLog.length - 1000);
        }
        
        await this.context.globalState.update('securityAuditLog', auditLog);
    }
}
