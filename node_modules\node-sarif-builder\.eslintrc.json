{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "env": {"es6": true}, "ignorePatterns": ["node_modules", "dist", "coverage"], "plugins": ["import", "eslint-comments"], "extends": ["eslint:recommended", "plugin:eslint-comments/recommended", "plugin:@typescript-eslint/recommended", "plugin:import/typescript", "prettier"], "globals": {"BigInt": true, "console": true, "WebAssembly": true}, "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "eslint-comments/disable-enable-pair": ["error", {"allowWholeFile": true}], "eslint-comments/no-unused-disable": "error", "import/order": ["error", {"newlines-between": "always", "alphabetize": {"order": "asc"}}], "sort-imports": ["error", {"ignoreDeclarationSort": true, "ignoreCase": true}]}}