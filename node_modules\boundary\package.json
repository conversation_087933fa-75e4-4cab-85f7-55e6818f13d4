{"name": "boundary", "version": "2.0.0", "description": "Provides boundary functions, (upper-bound and lower-bound).", "keywords": ["algorithm"], "homepage": "https://github.com/textlint/boundary", "bugs": {"url": "https://github.com/textlint/boundary/issues"}, "repository": {"type": "git", "url": "https://github.com/textlint/boundary.git"}, "license": "BSD-2-<PERSON><PERSON>", "author": "<PERSON><PERSON>", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "tsc -p .", "prepublishOnly": "npm run build", "test": "mocha \"test/**/*.{js,ts}\"", "watch": "tsc -p . --watch"}, "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^18.11.18", "mocha": "^10.2.0", "ts-node": "^10.9.1", "ts-node-test-register": "^10.0.0", "typescript": "^4.9.4"}, "packageManager": "npm@8.19.2"}