{"version": 3, "file": "BulkheadPolicy.js", "sourceRoot": "", "sources": ["../src/BulkheadPolicy.ts"], "names": [], "mappings": ";;;AAAA,0CAAoD;AACpD,0CAAuC;AACvC,0CAA8C;AAC9C,gDAAmD;AACnD,0EAAuE;AACvE,4CAAqD;AAUrD,MAAa,cAAc;IAuBzB;;OAEG;IACH,IAAW,cAAc;QACvB,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,YACmB,QAAgB,EAChB,aAAqB;QADrB,aAAQ,GAAR,QAAQ,CAAQ;QAChB,kBAAa,GAAb,aAAa,CAAQ;QAvChC,WAAM,GAAG,CAAC,CAAC;QACF,UAAK,GAA+B,EAAE,CAAC;QACvC,oBAAe,GAAG,IAAI,oBAAY,EAAQ,CAAC;QAC3C,aAAQ,GAAG,IAAI,yBAAc,EAAE,CAAC;QAEjD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,aAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;IAsBzD,CAAC;IAEJ;;;;OAIG;IACI,KAAK,CAAC,OAAO,CAClB,EAA0D,EAC1D,MAAM,GAAG,0BAAkB;QAE3B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,IAAI,2BAAkB,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC;gBACH,OAAO,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAC9B,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,aAAK,GAAK,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACjD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,6CAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACrE,CAAC;IAEO,OAAO;QACb,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,OAAO,CAAC,OAAO,EAAE;aACd,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;aAClB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxB,CAAC;CACF;AAzFD,wCAyFC", "sourcesContent": ["import { neverAbortedSignal } from './common/abort';\nimport { defer } from './common/defer';\nimport { EventEmitter } from './common/Event';\nimport { ExecuteWrapper } from './common/Executor';\nimport { BulkheadRejectedError } from './errors/BulkheadRejectedError';\nimport { TaskCancelledError } from './errors/Errors';\nimport { IDefaultPolicyContext, IPolicy } from './Policy';\n\ninterface IQueueItem<T> {\n  signal: AbortSignal;\n  fn(context: IDefaultPolicyContext): Promise<T> | T;\n  resolve(value: T): void;\n  reject(error: Error): void;\n}\n\nexport class BulkheadPolicy implements IPolicy {\n  public declare readonly _altReturn: never;\n\n  private active = 0;\n  private readonly queue: Array<IQueueItem<unknown>> = [];\n  private readonly onRejectEmitter = new EventEmitter<void>();\n  private readonly executor = new ExecuteWrapper();\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onSuccess = this.executor.onSuccess;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onFailure = this.executor.onFailure;\n\n  /**\n   * Emitter that fires when an item is rejected from the bulkhead.\n   */\n  public readonly onReject = this.onRejectEmitter.addListener;\n\n  /**\n   * Returns the number of available execution slots at this point in time.\n   */\n  public get executionSlots() {\n    return this.capacity - this.active;\n  }\n\n  /**\n   * Returns the number of queue slots at this point in time.\n   */\n  public get queueSlots() {\n    return this.queueCapacity - this.queue.length;\n  }\n\n  /**\n   * Bulkhead limits concurrent requests made.\n   */\n  constructor(\n    private readonly capacity: number,\n    private readonly queueCapacity: number,\n  ) {}\n\n  /**\n   * Executes the given function.\n   * @param fn Function to execute\n   * @throws a {@link BulkheadRejectedException} if the bulkhead limits are exceeeded\n   */\n  public async execute<T>(\n    fn: (context: IDefaultPolicyContext) => PromiseLike<T> | T,\n    signal = neverAbortedSignal,\n  ): Promise<T> {\n    if (signal.aborted) {\n      throw new TaskCancelledError();\n    }\n\n    if (this.active < this.capacity) {\n      this.active++;\n      try {\n        return await fn({ signal });\n      } finally {\n        this.active--;\n        this.dequeue();\n      }\n    }\n\n    if (this.queue.length < this.queueCapacity) {\n      const { resolve, reject, promise } = defer<T>();\n      this.queue.push({ signal, fn, resolve, reject });\n      return promise;\n    }\n\n    this.onRejectEmitter.emit();\n    throw new BulkheadRejectedError(this.capacity, this.queueCapacity);\n  }\n\n  private dequeue() {\n    const item = this.queue.shift();\n    if (!item) {\n      return;\n    }\n\n    Promise.resolve()\n      .then(() => this.execute(item.fn, item.signal))\n      .then(item.resolve)\n      .catch(item.reject);\n  }\n}\n"]}