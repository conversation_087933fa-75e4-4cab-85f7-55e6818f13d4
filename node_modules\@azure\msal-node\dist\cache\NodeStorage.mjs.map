{"version": 3, "file": "NodeStorage.mjs", "sources": ["../../src/cache/NodeStorage.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AA6BH;;;AAGG;AACG,MAAO,WAAY,SAAQ,YAAY,CAAA;AAMzC,IAAA,WAAA,CACI,MAAc,EACd,QAAgB,EAChB,UAAmB,EACnB,sBAA+C,EAAA;QAE/C,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,sBAAsB,CAAC,CAAC;QATxD,IAAK,CAAA,KAAA,GAAiB,EAAE,CAAC;QACzB,IAAc,CAAA,cAAA,GAAoB,EAAE,CAAC;AASzC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,IAAgB,EAAA;AAClC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAClC;AAED;;AAEG;IACH,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1D;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,KAAmB,EAAA;AACpC,QAAA,MAAM,aAAa,GAAkB;AACjC,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,YAAY,EAAE,EAAE;AAChB,YAAA,aAAa,EAAE,EAAE;AACjB,YAAA,WAAW,EAAE,EAAE;SAClB,CAAC;AAEF,QAAA,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AACrB,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,SAAS;AACZ,aAAA;YACD,IAAI,KAAK,YAAY,aAAa,EAAE;AAChC,gBAAA,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAsB,CAAC;AACxD,aAAA;AAAM,iBAAA,IAAI,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;AAC5C,gBAAA,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAsB,CAAC;AACxD,aAAA;AAAM,iBAAA,IAAI,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAChD,gBAAA,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,KAA0B,CAAC;AAChE,aAAA;AAAM,iBAAA,IAAI,YAAY,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;AACjD,gBAAA,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAA2B,CAAC;AAClE,aAAA;iBAAM,IAAI,YAAY,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;AACrD,gBAAA,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,KAA0B,CAAC;AAC/D,aAAA;AAAM,iBAAA;gBACH,SAAS;AACZ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,aAA4B,EAAA;;AAE7C,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAE5B,QAAA,KAAK,GAAG;AACJ,YAAA,GAAG,KAAK;YACR,GAAG,aAAa,CAAC,QAAQ;YACzB,GAAG,aAAa,CAAC,QAAQ;YACzB,GAAG,aAAa,CAAC,YAAY;YAC7B,GAAG,aAAa,CAAC,aAAa;YAC9B,GAAG,aAAa,CAAC,WAAW;SAC/B,CAAC;;AAGF,QAAA,OAAO,KAAK,CAAC;KAChB;AAED;;AAEG;IACH,gBAAgB,GAAA;AACZ,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;;QAG7C,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjE,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,aAA4B,EAAA;AACzC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;;QAG7C,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAErB,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AAED;;AAEG;IACH,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,KAAmB,EAAA;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;QAGnB,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,OAAO,CAAC,GAAW,EAAA;QACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAa,UAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;;AAGzC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;KACrB;AAED;;;;AAIG;IACH,OAAO,CAAC,GAAW,EAAE,KAAqB,EAAA;QACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAa,UAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;;AAGzC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;;AAGnB,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACxB;IAED,cAAc,GAAA;AACV,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAExD,QAAA,OAAO,WAAW,CAAC;KACtB;IAED,YAAY,GAAA;AACR,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC9C,QAAA,MAAM,SAAS,GAAG;YACd,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;YACpD,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;SACzD,CAAC;AAEF,QAAA,OAAO,SAAS,CAAC;KACpB;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,UAAkB,EAAA;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/C,QAAA,OAAO,aAAa;AAChB,cAAE,MAAM,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;cAC5D,IAAI,CAAC;KACd;AAED;;;AAGG;IACH,MAAM,UAAU,CAAC,OAAsB,EAAA;AACnC,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,UAAkB,EAAA;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAkB,CAAC;AAC1D,QAAA,IAAI,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;AACvC,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;IACH,MAAM,oBAAoB,CAAC,OAAsB,EAAA;QAC7C,MAAM,UAAU,GAAG,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,wBAAwB,CAAC,cAAsB,EAAA;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAsB,CAAC;AACtE,QAAA,IAAI,YAAY,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE;AAC/C,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;IACH,MAAM,wBAAwB,CAC1B,WAA8B,EAAA;QAE9B,MAAM,cAAc,GAAG,YAAY,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KAC7C;AAED;;;AAGG;AACH,IAAA,yBAAyB,CACrB,eAAuB,EAAA;QAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAC7B,eAAe,CACI,CAAC;AACxB,QAAA,IAAI,YAAY,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAE;AACjD,YAAA,OAAO,YAAkC,CAAC;AAC7C,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;IACH,MAAM,yBAAyB,CAC3B,YAAgC,EAAA;QAEhC,MAAM,eAAe,GACjB,YAAY,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;KAC/C;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,cAAsB,EAAA;QACjC,MAAM,WAAW,GAAsB,IAAI,CAAC,OAAO,CAC/C,cAAc,CACI,CAAC;QACvB,IAAI,YAAY,CAAC,mBAAmB,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE;AAC/D,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;AAGG;AACH,IAAA,cAAc,CAAC,WAA8B,EAAA;QACzC,MAAM,cAAc,GAAG,YAAY,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;KAC7C;AAED;;;AAGG;AACH,IAAA,kBAAkB,CACd,kBAA0B,EAAA;QAE1B,MAAM,qBAAqB,GAA0B,IAAI,CAAC,OAAO,CAC7D,kBAAkB,CACI,CAAC;AAC3B,QAAA,IACI,qBAAqB;AACrB,YAAA,YAAY,CAAC,uBAAuB,CAChC,kBAAkB,EAClB,qBAAqB,CACxB,EACH;AACE,YAAA,OAAO,qBAAqB,CAAC;AAChC,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;IACH,kBAAkB,CACd,kBAA0B,EAC1B,eAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;KACrD;AAED;;;AAGG;AACH,IAAA,oBAAoB,CAAC,GAAW,EAAA;QAC5B,MAAM,uBAAuB,GAA4B,IAAI,CAAC,OAAO,CACjE,GAAG,CACqB,CAAC;AAC7B,QAAA,IACI,uBAAuB;AACvB,YAAA,YAAY,CAAC,yBAAyB,CAAC,GAAG,EAAE,uBAAuB,CAAC,EACtE;AACE,YAAA,OAAO,uBAAuB,CAAC;AAClC,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;AAEG;IACH,wBAAwB,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,KAAI;AACjC,YAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;AACzC,SAAC,CAAC,CAAC;KACN;AAED;;;;AAIG;IACH,oBAAoB,CAAC,GAAW,EAAE,QAAiC,EAAA;AAC/D,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;KAC/B;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,kBAA0B,EAAA;QACzC,MAAM,eAAe,GAAqB,IAAI,CAAC,OAAO,CAClD,kBAAkB,CACD,CAAC;AACtB,QAAA,IACI,eAAe;AACf,YAAA,YAAY,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,eAAe,CAAC,EACtE;AACE,YAAA,OAAO,eAAe,CAAC;AAC1B,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;IACH,kBAAkB,CACd,kBAA0B,EAC1B,eAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;KACrD;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,GAAW,EAAA;QAClB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAa,UAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;;QAGzC,IAAI,MAAM,GAAY,KAAK,CAAC;AAC5B,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAE9B,QAAA,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACd,YAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,MAAM,GAAG,IAAI,CAAC;AACjB,SAAA;;AAGD,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;AACH,IAAA,qBAAqB,CAAC,UAAkB,EAAA;AACpC,QAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;KAC/B;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,GAAW,EAAA;QACnB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACvC;AAED;;AAEG;IACH,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;AAG/C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KAClC;AAED;;AAEG;IACH,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;;AAG5D,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;;AAGjC,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACtB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACzB,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AAED;;;AAGG;IACH,OAAO,qBAAqB,CAAC,KAAa,EAAA;QACtC,OAAO,YAAY,CAAC,mBAAmB,CACnC,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAC1C,CAAC;KACL;AAED;;;AAGG;IACH,OAAO,iBAAiB,CAAC,aAA4B,EAAA;AACjD,QAAA,OAAO,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;KACtD;AAED;;AAEG;IACH,wBAAwB,CACpB,eAAuB,EACvB,UAA+B,EAAA;QAE/B,MAAM,eAAe,GAAG,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAEvE,IAAI,eAAe,KAAK,eAAe,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAChD,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAuB,oBAAA,EAAA,UAAU,CAAC,cAAc,CAAY,UAAA,CAAA,CAC/D,CAAC;AACF,gBAAA,OAAO,eAAe,CAAC;AAC1B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAmC,gCAAA,EAAA,UAAU,CAAC,cAAc,CAAuE,qEAAA,CAAA,CACtI,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,eAAe,CAAC;KAC1B;AACJ;;;;"}