{"version": 3, "file": "visualStudioCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/visualStudioCodeCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Provides options to configure the Visual Studio Code credential.\n *\n * @deprecated This credential is deprecated because the VS Code Azure Account extension on which this credential\n * relies has been deprecated. Users should use other dev-time credentials, such as {@link AzureCliCredential},\n * {@link AzureDeveloperCliCredential}, or {@link AzurePowerShellCredential} for their\n * local development needs. See Azure Account extension deprecation notice [here](https://github.com/microsoft/vscode-azure-account/issues/964).\n */\nexport interface VisualStudioCodeCredentialOptions extends MultiTenantTokenCredentialOptions {\n  /**\n   * Optionally pass in a Tenant ID to be used as part of the credential\n   */\n  tenantId?: string;\n}\n"]}