{"version": 3, "file": "multiTenantTokenCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/multiTenantTokenCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredentialOptions } from \"../tokenCredentialOptions.js\";\n\n/**\n * Options for multi-tenant applications which allows for additionally allowed tenants.\n */\nexport interface MultiTenantTokenCredentialOptions extends TokenCredentialOptions {\n  /**\n   * For multi-tenant applications, specifies additional tenants for which the credential may acquire tokens.\n   * Add the wildcard value \"*\" to allow the credential to acquire tokens for any tenant the application is installed.\n   */\n  additionallyAllowedTenants?: string[];\n}\n"]}