{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACzG,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAEzC,SAAS,gBAAgB,CAAC,WAAmC;IACzD,gBAAgB;IAChB,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;IACxC,oBAAoB;IACpB,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC;QACrD,cAAc,EAAE,YAAY;QAC5B,iBAAiB,EAAE,OAAO;QAC1B,GAAG,EAAE,0CAA0C;KAClD,CAAC,CAAC;IACH,cAAc;IACd,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;IACvC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QAC3B,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACrG,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC1B,OAAO;aACV;YACD,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,eAAe,GAAG,IAAI,gBAAgB,EAAE,CAAC,UAAU,CAAC;gBACtD,MAAM,EAAE,MAAM;gBACd,oBAAoB,EAAE,mBAAmB,MAAM,SAAS;gBACxD,OAAO,EAAE,OAAO,EAAE,OAAO;aAC5B,CAAC,CAAC;YACH,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,wCAAwC;IACxC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QAC3B,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAChC,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACrG,MAAM,eAAe,GAAG;gBACpB,KAAK,EAAE,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ;gBAC9D,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;oBACnC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE;oBAC3C,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC;gBACnD,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC1C,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;gBAC7C,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;gBACtC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;aACnC,CAAC;YACX,kBAAkB,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC/C,eAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,YAAY,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACrC,OAAO,YAAY,CAAC,oBAAoB,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,OAAO,CAAC,GAAW;IACxB,IAAI,GAAG,KAAK,IAAI,EAAE;QACd,OAAO,SAAS,CAAC;KACpB;IACD,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/B,CAAC;AAED,SAAS,MAAM,CAAC,GAAW;IACvB,IAAI,GAAG,KAAK,IAAI,EAAE;QACd,OAAO,SAAS,CAAC;KACpB;IACD,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,SAAS,GAAwB,CAAC,OAAO,EAAE,EAAE;IAC/C,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC,CAAC;AACF,eAAe,SAAS,CAAC"}