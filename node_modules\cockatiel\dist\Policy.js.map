{"version": 3, "file": "Policy.js", "sourceRoot": "", "sources": ["../src/Policy.ts"], "names": [], "mappings": ";;;AAwOA,gCAEC;AAKD,gCAEC;AAID,4CAEC;AAKD,4CAEC;AAKD,4BAEC;AA4BD,8BAYC;AAYD,0BAQC;AA0DD,oBAgBC;AAqBD,sBAWC;AAyBD,wCAKC;AAoBD,4BAQC;AAreD,+CAAqE;AACrE,qDAAkD;AAClD,iEAAsF;AAEtF,gDAAmD;AACnD,qDAAkD;AAClD,6CAA0C;AAC1C,+CAAkE;AAClE,mDAAiE;AAIjE,MAAM,UAAU,GAAG,CAAI,GAAmB,EAAE,SAAiC,EAAE,EAAE,CAC/E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAU,EAAE,EAAE,CAAC,CAAC,YAAY,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAU,EAAE,EAAE,CAAC,CAAC,YAAY,GAAG,CAAC;AAElG,MAAM,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;AAC1B,MAAM,KAAK,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;AAuG1B,MAAa,MAAM;IACjB;;;OAGG;IACH,YAA4B,OAAqC;QAArC,YAAO,GAAP,OAAO,CAA8B;IAAG,CAAC;IAErE;;;;;;;;;;;;;;;;OAgBG;IACI,MAAM,CAAI,GAAmB,EAAE,SAAiC;QACrE,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC1C,OAAO,IAAI,MAAM,CAAC;YAChB,GAAG,IAAI,CAAC,OAAO;YACf,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;SAC3D,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,MAAM,CAAC,SAAoC;QAChD,OAAO,IAAI,MAAM,CAAC;YAChB,GAAG,IAAI,CAAC,OAAO;YACf,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,YAAY,CAAC,SAAkC;QACpD,OAAO,IAAI,MAAM,CAAC;YAChB,GAAG,IAAI,CAAC,OAAO;YACf,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC;SAChE,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,YAAY,CAAI,GAAmB,EAAE,SAAiC;QAC3E,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC1C,OAAO,IAAI,MAAM,CAAC;YAChB,GAAG,IAAI,CAAC,OAAO;YACf,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC;SAC7D,CAAC,CAAC;IACL,CAAC;CACF;AArGD,wBAqGC;AAEY,QAAA,IAAI,GAAG,IAAI,uBAAU,EAAE,CAAC;AAErC;;GAEG;AACU,QAAA,SAAS,GAAG,IAAI,MAAM,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;AAElF;;GAEG;AACH,SAAgB,UAAU,CAAI,GAAmB,EAAE,SAAiC;IAClF,OAAO,IAAI,MAAM,CAAC,EAAE,WAAW,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;AACtF,CAAC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,SAAoC;IAC7D,OAAO,IAAI,MAAM,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,CAAC;AACD;;GAEG;AACH,SAAgB,gBAAgB,CAAI,GAAmB,EAAE,SAAiC;IACxF,OAAO,IAAI,MAAM,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;AACtF,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,SAAsC;IACrE,OAAO,IAAI,MAAM,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC,CAAC;AACrE,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAa,EAAE,QAAgB,CAAC;IACvD,OAAO,IAAI,+BAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,SAAgB,SAAS,CAAC,MAA6C;IACrE,OAAO,CAAC,OAAgB,EAAE,IAAY,EAAE,UAA8B,EAAE,EAAE;QACxE,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,oDAAoD,OAAO,KAAK,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,UAAU,CAAC,KAAK,GAAG,UAAyB,GAAG,IAAW;YACxD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YACrF,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAClF,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;GASG;AACH,SAAgB,OAAO,CACrB,QAAgB,EAChB,cAAuF;IAEvF,OAAO,IAAI,6BAAa,CACtB,QAAQ,EACR,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,cAAc,CACnF,CAAC;AACJ,CAAC;AA0DD,SAAgB,IAAI,CAClB,GAAG,CAAuB;IAE1B,OAAO;QACL,UAAU,EAAE,SAAgB;QAC5B,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QACzB,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QACzB,OAAO,EAAE,CAAC;QACV,OAAO,CAAI,EAAsC,EAAE,MAAmB;YACpE,MAAM,GAAG,GAAG,CAAC,OAAU,EAAE,CAAS,EAA8B,EAAE,CAChE,CAAC,KAAK,CAAC,CAAC,MAAM;gBACZ,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAChF,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,EAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,KAAK,CACnB,MAAc,EACd,IAGC;IAED,OAAO,IAAI,yBAAW,CACpB,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,yBAAe,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,QAAQ,EAAE,EAC9F,IAAI,yBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAC5E,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAgB,cAAc,CAAC,MAAc,EAAE,IAA4B;IACzE,OAAO,IAAI,2CAAoB,CAC7B,IAAI,EACJ,IAAI,yBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAC5E,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,SAAgB,QAAQ,CAAI,MAAc,EAAE,cAA0C;IACpF,OAAO,IAAI,+BAAc,CACvB,IAAI,yBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;IAC3E,qEAAqE;IACrE,oEAAoE;IACpE,4CAA4C;IAC5C,CAAC,OAAO,cAAc,KAAK,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,cAAc,CAAY,CAC1F,CAAC;AACJ,CAAC", "sourcesContent": ["import { ConstantBackoff, IBackoffFactory } from './backoff/Backoff';\nimport { BulkheadPolicy } from './BulkheadPolicy';\nimport { CircuitBreakerPolicy, ICircuitBreakerOptions } from './CircuitBreakerPolicy';\nimport { Event } from './common/Event';\nimport { ExecuteWrapper } from './common/Executor';\nimport { FallbackPolicy } from './FallbackPolicy';\nimport { NoopPolicy } from './NoopPolicy';\nimport { IRetryBackoffContext, RetryPolicy } from './RetryPolicy';\nimport { TimeoutPolicy, TimeoutStrategy } from './TimeoutPolicy';\n\ntype Constructor<T> = new (...args: any) => T;\n\nconst typeFilter = <T>(cls: Constructor<T>, predicate?: (error: T) => boolean) =>\n  predicate ? (v: unknown) => v instanceof cls && predicate(v) : (v: unknown) => v instanceof cls;\n\nconst always = () => true;\nconst never = () => false;\n\nexport interface IBasePolicyOptions {\n  errorFilter: (error: Error) => boolean;\n  resultFilter: (result: unknown) => boolean;\n}\n\n/**\n * The reason for a call failure. Either an error, or the a value that was\n * marked as a failure (when using result filtering).\n */\nexport type FailureReason<ReturnType> = { error: Error } | { value: ReturnType };\n\n/**\n * Event emitted on the `onFailure` calls.\n */\nexport interface IFailureEvent {\n  /**\n   * Call duration, in milliseconds (with nanosecond precision, as the OS allows).\n   */\n  duration: number;\n\n  /**\n   * Whether the error was handled by the policy.\n   */\n  handled: boolean;\n\n  /**\n   * The reason for the error.\n   */\n  reason: FailureReason<unknown>;\n}\n\n/**\n * Event emitted on the `onSuccess` calls.\n */\nexport interface ISuccessEvent {\n  /**\n   * Call duration, in milliseconds (with nanosecond precision, as the OS allows).\n   */\n  duration: number;\n}\n\nexport interface IDefaultPolicyContext {\n  /**\n   * Abort signal for the operation. This is propagated through multiple\n   * retry policies.\n   */\n  signal: AbortSignal;\n}\n\n/**\n * IPolicy is the type of all policies that Cockatiel provides. It describes\n * an execute() function which takes a generic argument.\n */\nexport interface IPolicy<\n  ContextType extends IDefaultPolicyContext = IDefaultPolicyContext,\n  AltReturn = never,\n> {\n  /**\n   * Virtual property only used for TypeScript--will not actually be defined.\n   * @deprecated This property does not exist\n   */\n  readonly _altReturn: AltReturn;\n\n  /**\n   * Fires on the policy when a request successfully completes and some\n   * successful value will be returned. In a retry policy, this is fired once\n   * even if the request took multiple retries to succeed.\n   */\n  readonly onSuccess: Event<ISuccessEvent>;\n\n  /**\n   * Fires on the policy when a request fails *due to a handled reason* fails\n   * and will give rejection to the called.\n   */\n  readonly onFailure: Event<IFailureEvent>;\n\n  /**\n   * Runs the function through behavior specified by the policy.\n   */\n  execute<T>(\n    fn: (context: ContextType) => PromiseLike<T> | T,\n    signal?: AbortSignal,\n  ): Promise<T | AltReturn>;\n}\n\nexport interface IMergedPolicy<A extends IDefaultPolicyContext, B, W extends IPolicy<any, any>[]>\n  extends IPolicy<A, B> {\n  readonly wrapped: W;\n}\n\ntype MergePolicies<A, B> =\n  A extends IPolicy<infer A1, any>\n    ? B extends IPolicy<infer B1, any>\n      ? IMergedPolicy<\n          A1 & B1,\n          A['_altReturn'] | B['_altReturn'],\n          B extends IMergedPolicy<any, any, infer W> ? [A, ...W] : [A, B]\n        >\n      : never\n    : never;\n\nexport class Policy {\n  /**\n   * Factory that builds a base set of filters that can be used in circuit\n   * breakers, retries, etc.\n   */\n  constructor(public readonly options: Readonly<IBasePolicyOptions>) {}\n\n  /**\n   * Allows the policy to additionally handles errors of the given type.\n   *\n   * @param cls Class constructor to check that the error is an instance of.\n   * @param predicate If provided, a function to be called with the error\n   * which should return \"true\" if we want to handle this error.\n   * @example\n   * ```js\n   * // retry both network errors and response errors with a 503 status code\n   * new Policy()\n   *  .orType(NetworkError)\n   *  .orType(ResponseError, err => err.statusCode === 503)\n   *  .retry()\n   *  .attempts(3)\n   *  .execute(() => getJsonFrom('https://example.com'));\n   * ```\n   */\n  public orType<T>(cls: Constructor<T>, predicate?: (error: T) => boolean) {\n    const filter = typeFilter(cls, predicate);\n    return new Policy({\n      ...this.options,\n      errorFilter: e => this.options.errorFilter(e) || filter(e),\n    });\n  }\n\n  /**\n   * Allows the policy to additionally handles errors that pass the given\n   * predicate function.\n   *\n   * @param predicate Takes any thrown error, and returns true if it should\n   * be retried by this policy.\n   * @example\n   * ```js\n   * // only retry if the error has a \"shouldBeRetried\" property set\n   * new Policy()\n   *  .orWhen(err => err.shouldBeRetried === true)\n   *  .retry()\n   *  .attempts(3)\n   *  .execute(() => getJsonFrom('https://example.com'));\n   * ```\n   */\n  public orWhen(predicate: (error: Error) => boolean) {\n    return new Policy({\n      ...this.options,\n      errorFilter: e => this.options.errorFilter(e) || predicate(e),\n    });\n  }\n\n  /**\n   * Adds handling for return values. The predicate will be called with\n   * the return value of the executed function,\n   *\n   * @param predicate Takes the returned value, and returns true if it\n   * should be retried by this policy.\n   * @example\n   * ```js\n   * // retry when the response status code is a 5xx\n   * new Policy()\n   *  .orResultWhen(res => res.statusCode >= 500)\n   *  .retry()\n   *  .attempts(3)\n   *  .execute(() => getJsonFrom('https://example.com'));\n   * ```\n   */\n  public orWhenResult(predicate: (r: unknown) => boolean) {\n    return new Policy({\n      ...this.options,\n      resultFilter: r => this.options.resultFilter(r) || predicate(r),\n    });\n  }\n\n  /**\n   * Adds handling for return values. The predicate will be called with\n   * the return value of the executed function,\n   *\n   * @param predicate Takes the returned value, and returns true if it\n   * should be retried by this policy.\n   * @example\n   * ```js\n   * // retry when the response status code is a 5xx\n   * new Policy()\n   *  .orResultType(res => res.statusCode >= 500)\n   *  .retry()\n   *  .attempts(3)\n   *  .execute(() => getJsonFrom('https://example.com'));\n   * ```\n   */\n  public orResultType<T>(cls: Constructor<T>, predicate?: (error: T) => boolean) {\n    const filter = typeFilter(cls, predicate);\n    return new Policy({\n      ...this.options,\n      resultFilter: r => this.options.resultFilter(r) || filter(r),\n    });\n  }\n}\n\nexport const noop = new NoopPolicy();\n\n/**\n * A policy that handles all errors.\n */\nexport const handleAll = new Policy({ errorFilter: always, resultFilter: never });\n\n/**\n * See {@link Policy.orType} for usage.\n */\nexport function handleType<T>(cls: Constructor<T>, predicate?: (error: T) => boolean) {\n  return new Policy({ errorFilter: typeFilter(cls, predicate), resultFilter: never });\n}\n\n/**\n * See {@link Policy.orWhen} for usage.\n */\nexport function handleWhen(predicate: (error: Error) => boolean) {\n  return new Policy({ errorFilter: predicate, resultFilter: never });\n}\n/**\n * See {@link Policy.orResultType} for usage.\n */\nexport function handleResultType<T>(cls: Constructor<T>, predicate?: (error: T) => boolean) {\n  return new Policy({ errorFilter: never, resultFilter: typeFilter(cls, predicate) });\n}\n\n/**\n * See {@link Policy.orWhenResult} for usage.\n */\nexport function handleWhenResult(predicate: (error: unknown) => boolean) {\n  return new Policy({ errorFilter: never, resultFilter: predicate });\n}\n\n/**\n * Creates a bulkhead--a policy that limits the number of concurrent calls.\n */\nexport function bulkhead(limit: number, queue: number = 0) {\n  return new BulkheadPolicy(limit, queue);\n}\n\n/**\n * A decorator that can be used to wrap class methods and apply the given\n * policy to them. It also adds the last argument normally given in\n * {@link Policy.execute} as the last argument in the function call.\n * For example:\n *\n * ```ts\n * import { usePolicy, retry, handleAll } from 'cockatiel';\n *\n * const retry = retry(handleAll, { maxAttempts: 3 });\n *\n * class Database {\n *   @usePolicy(retry)\n *   public getUserInfo(userId, context, cancellationToken) {\n *     console.log('Retry attempt number', context.attempt);\n *     // implementation here\n *   }\n * }\n *\n * const db = new Database();\n * db.getUserInfo(3).then(info => console.log('User 3 info:', info))\n * ```\n *\n * Note that it will force the return type to be a Promise, since that's\n * what policies return.\n */\nexport function usePolicy(policy: IPolicy<IDefaultPolicyContext, never>) {\n  return (_target: unknown, _key: string, descriptor: PropertyDescriptor) => {\n    const inner = descriptor.value;\n    if (typeof inner !== 'function') {\n      throw new Error(`Can only decorate functions with @cockatiel, got ${typeof inner}`);\n    }\n\n    descriptor.value = function (this: unknown, ...args: any[]) {\n      const signal = args[args.length - 1] instanceof AbortSignal ? args.pop() : undefined;\n      return policy.execute(context => inner.apply(this, [...args, context]), signal);\n    };\n  };\n}\n\n/**\n * Creates a timeout policy.\n * @param duration - How long to wait before timing out execute()'d functions\n * @param strategy - Strategy for timeouts, \"Cooperative\" or \"Aggressive\".\n * A {@link CancellationToken} will be pass to any executed function, and in\n * cooperative timeouts we'll simply wait for that function to return or\n * throw. In aggressive timeouts, we'll immediately throw a\n * {@link TaskCancelledError} when the timeout is reached, in addition to\n * marking the passed token as failed.\n */\nexport function timeout(\n  duration: number,\n  strategyOrOpts: TimeoutStrategy | { strategy: TimeoutStrategy; abortOnReturn: boolean },\n) {\n  return new TimeoutPolicy(\n    duration,\n    typeof strategyOrOpts === 'string' ? { strategy: strategyOrOpts } : strategyOrOpts,\n  );\n}\n\n/**\n * Wraps the given set of policies into a single policy. For instance, this:\n *\n * ```js\n * retry.execute(() =>\n *  breaker.execute(() =>\n *    timeout.execute(({ cancellationToken }) => getData(cancellationToken))))\n * ```\n *\n * Is the equivalent to:\n *\n * ```js\n * Policy\n *  .wrap(retry, breaker, timeout)\n *  .execute(({ cancellationToken }) => getData(cancellationToken)));\n * ```\n *\n * The `context` argument passed to the executed function is the merged object\n * of all previous policies.\n *\n */\n// The types here a certain unattrative. Ideally we could do\n// `wrap<A, B>(p: IPolicy<A, B>): IPolicy<A, B>`, but TS doesn't narrow the\n// types well in that scenario (unless p is explicitly typed as an IPolicy\n// and not some implementation) and returns `IPolicy<void, unknown>` and\n// the like. This is the best solution I've found for it.\nexport function wrap<A extends IPolicy<IDefaultPolicyContext, unknown>>(p1: A): A;\nexport function wrap<\n  A extends IPolicy<IDefaultPolicyContext, unknown>,\n  B extends IPolicy<IDefaultPolicyContext, unknown>,\n>(p1: A, p2: B): MergePolicies<A, B>;\nexport function wrap<\n  A extends IPolicy<IDefaultPolicyContext, unknown>,\n  B extends IPolicy<IDefaultPolicyContext, unknown>,\n  C extends IPolicy<IDefaultPolicyContext, unknown>,\n>(p1: A, p2: B, p3: C): MergePolicies<C, MergePolicies<A, B>>;\nexport function wrap<\n  A extends IPolicy<IDefaultPolicyContext, unknown>,\n  B extends IPolicy<IDefaultPolicyContext, unknown>,\n  C extends IPolicy<IDefaultPolicyContext, unknown>,\n  D extends IPolicy<IDefaultPolicyContext, unknown>,\n>(p1: A, p2: B, p3: C, p4: D): MergePolicies<D, MergePolicies<C, MergePolicies<A, B>>>;\nexport function wrap<\n  A extends IPolicy<IDefaultPolicyContext, unknown>,\n  B extends IPolicy<IDefaultPolicyContext, unknown>,\n  C extends IPolicy<IDefaultPolicyContext, unknown>,\n  D extends IPolicy<IDefaultPolicyContext, unknown>,\n  E extends IPolicy<IDefaultPolicyContext, unknown>,\n>(\n  p1: A,\n  p2: B,\n  p3: C,\n  p4: D,\n  p5: E,\n): MergePolicies<E, MergePolicies<D, MergePolicies<C, MergePolicies<A, B>>>>;\nexport function wrap<C extends IDefaultPolicyContext, A>(...p: Array<IPolicy<C, A>>): IPolicy<C, A>;\nexport function wrap<C extends IDefaultPolicyContext, A>(\n  ...p: Array<IPolicy<C, A>>\n): IMergedPolicy<C, A, IPolicy<C, A>[]> {\n  return {\n    _altReturn: undefined as any,\n    onFailure: p[0].onFailure,\n    onSuccess: p[0].onSuccess,\n    wrapped: p,\n    execute<T>(fn: (context: C) => PromiseLike<T> | T, signal: AbortSignal): Promise<T | A> {\n      const run = (context: C, i: number): PromiseLike<T | A> | T | A =>\n        i === p.length\n          ? fn(context)\n          : p[i].execute(next => run({ ...context, ...next }, i + 1), context.signal);\n      return Promise.resolve(run({ signal } as C, 0));\n    },\n  };\n}\n\n/**\n * Creates a retry policy. The options should contain the backoff strategy to\n * use. Included strategies are:\n *  - {@link ConstantBackoff}\n *  - {@link ExponentialBackoff}\n *  - {@link IterableBackoff}\n *  - {@link DelegateBackoff} (advanced)\n *\n * For example:\n *\n * ```\n * import { handleAll, retry } from 'cockatiel';\n *\n * const policy = retry(handleAll, { backoff: new ExponentialBackoff() });\n * ```\n *\n * You can optionally pass in the `attempts` to limit the maximum number of\n * retry attempts per call.\n */\nexport function retry(\n  policy: Policy,\n  opts: {\n    maxAttempts?: number;\n    backoff?: IBackoffFactory<IRetryBackoffContext<unknown>>;\n  },\n) {\n  return new RetryPolicy(\n    { backoff: opts.backoff || new ConstantBackoff(0), maxAttempts: opts.maxAttempts ?? Infinity },\n    new ExecuteWrapper(policy.options.errorFilter, policy.options.resultFilter),\n  );\n}\n\n/**\n * Returns a circuit breaker for the policy. **Important**: you should share\n * your circuit breaker between executions of whatever function you're\n * wrapping for it to function!\n *\n * ```ts\n * import { SamplingBreaker, Policy } from 'cockatiel';\n *\n * // Break if more than 20% of requests fail in a 30 second time window:\n * const breaker = Policy\n *  .handleAll()\n *  .circuitBreaker(10_000, new SamplingBreaker(0.2, 30 * 1000));\n *\n * export function handleRequest() {\n *   return breaker.execute(() => getInfoFromDatabase());\n * }\n * ```\n *\n * @param halfOpenAfter Time after failures to try to open the circuit\n * breaker again.\n * @param breaker The circuit breaker to use. This package exports\n * ConsecutiveBreaker and SamplingBreakers for you to use.\n */\nexport function circuitBreaker(policy: Policy, opts: ICircuitBreakerOptions) {\n  return new CircuitBreakerPolicy(\n    opts,\n    new ExecuteWrapper(policy.options.errorFilter, policy.options.resultFilter),\n  );\n}\n\n/**\n * Falls back to the given value in the event of an error.\n *\n * ```ts\n * import { Policy } from 'cockatiel';\n *\n * const fallback = Policy\n *  .handleType(DatabaseError)\n *  .fallback(() => getStaleData());\n *\n * export function handleRequest() {\n *   return fallback.execute(() => getInfoFromDatabase());\n * }\n * ```\n *\n * @param toValue Value to fall back to, or a function that creates the\n * value to return (any may return a promise)\n */\nexport function fallback<R>(policy: Policy, valueOrFactory: (() => Promise<R> | R) | R) {\n  return new FallbackPolicy(\n    new ExecuteWrapper(policy.options.errorFilter, policy.options.resultFilter),\n    // not technically type-safe, since if they actually want to _return_\n    // a function, that gets lost here. We'll just advice in the docs to\n    // use a higher-order function if necessary.\n    (typeof valueOrFactory === 'function' ? valueOrFactory : () => valueOrFactory) as () => R,\n  );\n}\n"]}