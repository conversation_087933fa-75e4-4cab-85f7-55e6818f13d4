// LICENSE : MIT
"use strict";
import { moduleInterop } from "@textlint/module-interop";
import fs from "fs";
import path from "path";
import { dynamicImport, tryResolve } from "@textlint/resolver";
import debug0 from "debug";
// formatter
import checkstyleFormatter from "./formatters/checkstyle";
import compactFormatter from "./formatters/compact";
import jslintXMLFormatter from "./formatters/jslint-xml";
import jsonFormatter from "./formatters/json";
import junitFormatter from "./formatters/junit";
import prettyErrorFormatter from "./formatters/pretty-error";
import stylishFormatter from "./formatters/stylish";
import tableFormatter from "./formatters/table";
import tapFormatter from "./formatters/tap";
import unixFormatter from "./formatters/unix";
const builtinFormatterList = {
    checkstyle: checkstyleFormatter,
    compact: compactFormatter,
    "jslint-xml": jslintXMLFormatter,
    json: jsonFormatter,
    junit: junitFormatter,
    "pretty-error": prettyErrorFormatter,
    stylish: stylishFormatter,
    table: tableFormatter,
    tap: tapFormatter,
    unix: unixFormatter
};
const builtinFormatterNames = Object.keys(builtinFormatterList);
const debug = debug0("textlint:@textlint/linter-formatter");
const isFormatterFunction = (formatter) => {
    return typeof formatter === "function";
};
export async function loadFormatter(formatterConfig) {
    var _a;
    const formatterName = formatterConfig.formatterName;
    debug(`formatterName: ${formatterName}`);
    if (builtinFormatterNames.includes(formatterName)) {
        return {
            format(results) {
                return builtinFormatterList[formatterName](results, formatterConfig);
            }
        };
    }
    let formatter;
    let formatterPath;
    if (fs.existsSync(formatterName)) {
        formatterPath = formatterName;
    }
    else if (fs.existsSync(path.resolve(process.cwd(), formatterName))) {
        formatterPath = path.resolve(process.cwd(), formatterName);
    }
    else {
        const pkgPath = tryResolve(`textlint-formatter-${formatterName}`, {
            parentModule: "linter-formatter"
        }) ||
            tryResolve(formatterName, {
                parentModule: "linter-formatter"
            });
        if (pkgPath) {
            formatterPath = pkgPath;
        }
    }
    if (!formatterPath) {
        throw new Error(`Could not find formatter ${formatterName}`);
    }
    try {
        const mod = (_a = moduleInterop((await dynamicImport(formatterPath, {
            parentModule: "linter-formatter"
        })).exports)) === null || _a === void 0 ? void 0 : _a.default;
        if (!isFormatterFunction(mod)) {
            throw new Error(`formatter should export function, but ${formatterPath} exports ${typeof mod}`);
        }
        formatter = mod;
    }
    catch (ex) {
        throw new Error(`Could not find formatter ${formatterName}
${ex}`);
    }
    return {
        format(results) {
            return formatter(results, formatterConfig);
        }
    };
}
/**
 * @deprecated use loadFormatter
 * @param formatterConfig
 */
export function createFormatter(formatterConfig) {
    const formatterName = formatterConfig.formatterName;
    debug(`formatterName: ${formatterName}`);
    if (builtinFormatterNames.includes(formatterName)) {
        return function (results) {
            return builtinFormatterList[formatterName](results, formatterConfig);
        };
    }
    let formatter;
    let formatterPath;
    if (fs.existsSync(formatterName)) {
        formatterPath = formatterName;
    }
    else if (fs.existsSync(path.resolve(process.cwd(), formatterName))) {
        formatterPath = path.resolve(process.cwd(), formatterName);
    }
    else {
        const pkgPath = tryResolve(`textlint-formatter-${formatterName}`, {
            parentModule: "linter-formatter"
        }) ||
            tryResolve(formatterName, {
                parentModule: "linter-formatter"
            });
        if (pkgPath) {
            formatterPath = pkgPath;
        }
    }
    if (!formatterPath) {
        throw new Error(`Could not find formatter ${formatterName}`);
    }
    try {
        formatter = moduleInterop(require(formatterPath));
    }
    catch (ex) {
        throw new Error(`Could not find formatter ${formatterName}
${ex}`);
    }
    return function (results) {
        return formatter(results, formatterConfig);
    };
}
export function getFormatterList() {
    return builtinFormatterNames.map((name) => {
        return {
            name
        };
    });
}
//# sourceMappingURL=index.js.map