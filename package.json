{"name": "v1b3-sama", "displayName": "V1b3-Sama", "version": "6.0.3", "description": "An AI assistant extension for VS Code with advanced agentic capabilities", "publisher": "jerry<PERSON><PERSON>", "license": "MIT", "icon": "assets/icons/v1b3logo.jpeg", "repository": {"type": "git", "url": "https://github.com/jerryjoo/v1b3-sama.git"}, "engines": {"vscode": "^1.80.0"}, "categories": ["AI", "Programming Languages", "Machine Learning", "Other"], "keywords": ["ai", "assistant", "agent", "llm", "deepseek", "openai", "anthropic", "groq", "openrouter", "code generation", "workspace automation", "file operations", "chat", "productivity"], "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}, "homepage": "https://github.com/jerryjoo/v1b3-sama", "bugs": {"url": "https://github.com/jerryjoo/v1b3-sama/issues"}, "activationEvents": ["onStartupFinished", "onCommand:v1b3-sama.start", "onView:v1b3-sama-chat", "onWebviewPanel:v1b3-sama", "workspaceContains:**/*", "onFileSystem:file", "onLanguage:javascript", "onLanguage:typescript", "onLanguage:python", "onLanguage:html", "onLanguage:css", "onLanguage:json", "onLanguage:markdown", "onLanguage:yaml", "onLanguage:xml", "onLanguage:java", "onLanguage:csharp", "onLanguage:cpp", "onLanguage:c", "onLanguage:go", "onLanguage:rust", "onLanguage:php", "onLanguage:ruby", "onLanguage:swift", "onLanguage:kotlin", "onLanguage:scala", "onLanguage:r", "onLanguage:sql", "onLanguage:shell", "onLanguage:powershell", "onLanguage:dockerfile", "onLanguage:plaintext"], "capabilities": {"virtualWorkspaces": {"supported": "limited", "description": "V1b3-Sama supports virtual workspaces with limited file system operations"}, "untrustedWorkspaces": {"supported": "limited", "description": "V1b3-Sama requires workspace trust for file operations and AI features"}}, "main": "./dist/extension.js", "contributes": {"commands": [{"command": "v1b3-sama.start", "title": "V1b3-Sama: Start"}, {"command": "v1b3-sama.stop", "title": "V1b3-Sama: Stop"}, {"command": "v1b3-sama.clearChat", "title": "V1b3-Sama: <PERSON>"}, {"command": "v1b3-sama.clearAllConversations", "title": "V1b3-Sama: Clear All Conversations"}, {"command": "v1b3-sama.saveCheckpoint", "title": "V1b3-Sama: Save Checkpoint"}, {"command": "v1b3-sama.restoreCheckpoint", "title": "V1b3-Sama: Restore Checkpoint"}, {"command": "v1b3-sama.newChat", "title": "V1b3-Sama: New Chat"}, {"command": "v1b3-sama.manageMcpServers", "title": "V1b3-Sam<PERSON>: Manage MCP Servers"}, {"command": "v1b3-sama.viewActionHistory", "title": "V1b3-Sama: View Action History"}, {"command": "v1b3-sama.configureApiProviders", "title": "V1b3-Sama: Configure API Providers"}, {"command": "v1b3-sama.openSettings", "title": "V1b3-Sam<PERSON>: Open Settings"}, {"command": "v1b3-sama.refreshProviders", "title": "V1b3-Sama: Refresh Providers"}, {"command": "v1b3-sama.setupApi<PERSON>ey", "title": "V1b3-Sama: Setup API Key"}, {"command": "v1b3-sama.testProviderHealth", "title": "V1b3-Sama: Test Provider Health"}, {"command": "v1b3-sama.switchProvider", "title": "V1b3-Sama: Switch Provider"}, {"command": "v1b3-sama.showProviderStatus", "title": "V1b3-Sama: Show Provider Status"}, {"command": "v1b3-sama.showProviderDashboard", "title": "V1b3-Sama: Show Provider Dashboard"}, {"command": "v1b3-sama.refreshModels", "title": "V1b3-Sama: Refresh Models"}, {"command": "v1b3-sama.searchModels", "title": "V1b3-Sama: Search Models"}, {"command": "v1b3-sama.toggleMonitoring", "title": "V1b3-Sama: Toggle Monitoring"}, {"command": "v1b3-sama.showMonitoringStats", "title": "V1b3-Sama: Show Monitoring Stats"}, {"command": "v1b3-sama.createFile", "title": "V1b3-Sama: Create File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.createDirectory", "title": "V1b3-Sama: Create Directory", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.deleteFile", "title": "V1b3-Sama: Delete File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.renameFile", "title": "V1b3-Sama: Rename File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.copyFile", "title": "V1b3-Sama: Copy File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.moveFile", "title": "V1b3-Sama: Move File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.readFile", "title": "V1b3-Sama: Read File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.writeFile", "title": "V1b3-Sama: Write File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.appendToFile", "title": "V1b3-Sama: Append to File", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.searchFiles", "title": "V1b3-Sama: Search Files", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.listFiles", "title": "V1b3-Sama: List Files", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.watchFiles", "title": "V1b3-Sama: Watch Files", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.validateWorkspace", "title": "V1b3-Sama: Validate Workspace", "enablement": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.refreshWorkspace", "title": "V1b3-Sama: Refresh Workspace", "enablement": "workspaceFolderCount > 0"}], "viewsContainers": {"activitybar": [{"id": "v1b3-sama", "title": "V1b3-Sama", "icon": "assets/icons/sidebar-icon.svg"}]}, "views": {"v1b3-sama": [{"type": "webview", "id": "v1b3-sama.chatView", "name": ""}]}, "configuration": {"title": "V1b3-Sama", "properties": {"v1b3-sama.apiKey": {"type": "string", "default": "", "description": "API key for LLM provider"}, "v1b3-sama.provider": {"type": "string", "enum": ["deepseek", "groq", "openai", "anthropic", "google", "openrouter", "azure", "local"], "default": "deepseek", "description": "LLM provider to use for chat and code generation", "enumDescriptions": ["DeepSeek - Fast and efficient coding models", "Groq - Ultra-fast inference with Llama models", "OpenAI - GPT-4 and GPT-3.5 models", "Anthropic - Claude models for advanced reasoning", "Google AI - Gemini models with multimodal capabilities", "OpenRouter - Access to multiple model providers", "Azure OpenAI - Enterprise-grade OpenAI models", "Local Server - Self-hosted or local model server"]}, "v1b3-sama.model": {"type": "string", "default": "deepseek-coder", "description": "Specific model to use for chat and code generation. Available models depend on the selected provider."}, "v1b3-sama.ui.theme": {"type": "string", "enum": ["system", "light", "dark", "high-contrast"], "default": "system", "description": "Theme for the V1b3-Sama chat interface", "enumDescriptions": ["Follow VS Code theme", "Light theme", "Dark theme", "High contrast theme"]}, "v1b3-sama.ui.fontSize": {"type": "number", "default": 14, "minimum": 10, "maximum": 24, "description": "Font size for the chat interface in pixels"}, "v1b3-sama.ui.showLineNumbers": {"type": "boolean", "default": true, "description": "Show line numbers in code blocks within the chat interface"}, "v1b3-sama.ui.enableSyntaxHighlighting": {"type": "boolean", "default": true, "description": "Enable syntax highlighting for code blocks in the chat interface"}, "v1b3-sama.ui.compactMode": {"type": "boolean", "default": false, "description": "Use compact mode for the chat interface to save space"}, "v1b3-sama.enableWebSearch": {"type": "boolean", "default": true, "description": "Enable web search functionality for grounding AI responses with current information"}, "v1b3-sama.mcpServers": {"type": "array", "default": [], "description": "List of configured MCP servers"}, "v1b3-sama.performance.workerPoolSize": {"type": "number", "default": 0, "minimum": 0, "maximum": 16, "description": "Number of workers in the worker pool (0 = auto-detect based on CPU cores)"}, "v1b3-sama.performance.enableStreaming": {"type": "boolean", "default": true, "description": "Enable real-time streaming for LLM responses"}, "v1b3-sama.performance.streamingTimeout": {"type": "number", "default": 30000, "minimum": 5000, "maximum": 120000, "description": "Timeout for streaming connections in milliseconds"}, "v1b3-sama.performance.enablePerformanceMonitoring": {"type": "boolean", "default": true, "description": "Enable performance monitoring and alerting"}, "v1b3-sama.performance.alertThreshold": {"type": "number", "default": 15, "minimum": 5, "maximum": 100, "description": "Performance degradation threshold percentage for alerts"}, "v1b3-sama.files.maxFileSize": {"type": "number", "default": 104857600, "minimum": 1048576, "description": "Maximum file size in bytes for processing (default: 100MB)"}, "v1b3-sama.files.workerThreshold": {"type": "number", "default": 102400, "minimum": 10240, "description": "File size threshold in bytes for using worker pool (default: 100KB)"}, "v1b3-sama.cache.enableResponseCache": {"type": "boolean", "default": true, "description": "Enable caching of LLM responses"}, "v1b3-sama.cache.cacheTTL": {"type": "number", "default": 900000, "minimum": 60000, "maximum": 3600000, "description": "Cache time-to-live in milliseconds (default: 15 minutes)"}, "v1b3-sama.debug.enableVerboseLogging": {"type": "boolean", "default": false, "description": "Enable verbose logging for debugging"}, "v1b3-sama.debug.logPerformanceMetrics": {"type": "boolean", "default": false, "description": "Log performance metrics to console for debugging purposes"}, "v1b3-sama.security.validateApiKeys": {"type": "boolean", "default": true, "description": "Validate API key format before storing (recommended for security)"}, "v1b3-sama.security.requireConfirmationForDeletion": {"type": "boolean", "default": true, "description": "Require confirmation before deleting files or folders"}, "v1b3-sama.security.sandboxMcpOperations": {"type": "boolean", "default": true, "description": "Run MCP server operations in a sandboxed environment"}, "v1b3-sama.contextEngine.enabled": {"type": "boolean", "default": true, "description": "Enable the context engine for intelligent code understanding"}, "v1b3-sama.contextEngine.maxContextFiles": {"type": "number", "default": 50, "minimum": 10, "maximum": 200, "description": "Maximum number of files to include in context analysis"}, "v1b3-sama.contextEngine.autoTrigger": {"type": "boolean", "default": true, "description": "Automatically trigger context analysis based on user queries"}, "v1b3-sama.contextEngine.includeTests": {"type": "boolean", "default": false, "description": "Include test files in context analysis"}, "v1b3-sama.execution.confirmBeforeRun": {"type": "boolean", "default": true, "description": "Confirm before executing generated code or terminal commands"}, "v1b3-sama.execution.timeoutSeconds": {"type": "number", "default": 30, "minimum": 5, "maximum": 300, "description": "Timeout for code execution in seconds"}, "v1b3-sama.execution.preserveWorkingDirectory": {"type": "boolean", "default": true, "description": "Preserve the working directory between command executions"}, "v1b3-sama.monitoring.enabled": {"type": "boolean", "default": true, "description": "Enable provider health monitoring"}, "v1b3-sama.monitoring.checkInterval": {"type": "number", "default": 5, "minimum": 1, "maximum": 60, "description": "Health check interval in minutes"}, "v1b3-sama.monitoring.healthCheckTimeout": {"type": "number", "default": 10, "minimum": 5, "maximum": 30, "description": "Health check timeout in seconds"}, "v1b3-sama.monitoring.maxErrorsBeforeDegraded": {"type": "number", "default": 3, "minimum": 1, "maximum": 10, "description": "Maximum errors before marking provider as degraded"}, "v1b3-sama.monitoring.autoFailover": {"type": "boolean", "default": true, "description": "Enable automatic failover to healthy providers"}, "v1b3-sama.monitoring.preferredProviders": {"type": "array", "default": ["deepseek", "groq", "openrouter", "local"], "description": "Preferred provider order for failover"}, "v1b3-sama.workspace.autoCreateFiles": {"type": "boolean", "default": true, "description": "Automatically create files when AI generates code"}, "v1b3-sama.workspace.autoCreateDirectories": {"type": "boolean", "default": true, "description": "Automatically create directories when needed for file operations"}, "v1b3-sama.workspace.enableFileWatcher": {"type": "boolean", "default": true, "description": "Enable file system watcher for real-time workspace updates"}, "v1b3-sama.workspace.maxFileOperationsPerRequest": {"type": "number", "default": 50, "minimum": 1, "maximum": 200, "description": "Maximum number of file operations allowed per AI request"}}}, "resourceLabelFormatters": [{"scheme": "file", "authority": "", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "V1b3-Sama"}}], "fileSystemProviders": [{"scheme": "v1b3-workspace", "capabilities": ["FileReadWrite", "FileOpenReadWriteClose", "FileDelete", "FileFolderCopy", "FileRename", "FileCreate", "FileWatch"]}], "workspaceTrust": {"request": "onDemand", "description": "V1b3-Sama requires workspace trust to perform file operations and execute AI-generated code safely."}, "menus": {"explorer/context": [{"command": "v1b3-sama.readFile", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "v1b3-sama@1"}, {"command": "v1b3-sama.writeFile", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "v1b3-sama@2"}, {"command": "v1b3-sama.deleteFile", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "v1b3-sama@3"}, {"command": "v1b3-sama.copyFile", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "v1b3-sama@4"}, {"command": "v1b3-sama.createDirectory", "when": "resourceScheme == file && explorerResourceIsFolder", "group": "v1b3-sama@5"}], "editor/context": [{"command": "v1b3-sama.writeFile", "when": "editorTextFocus && resourceScheme == file", "group": "v1b3-sama@1"}, {"command": "v1b3-sama.appendToFile", "when": "editorTextFocus && resourceScheme == file", "group": "v1b3-sama@2"}], "commandPalette": [{"command": "v1b3-sama.createFile", "when": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.createDirectory", "when": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.validateWorkspace", "when": "workspaceFolderCount > 0"}, {"command": "v1b3-sama.refreshWorkspace", "when": "workspaceFolderCount > 0"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run compile:main && npm run compile:workers", "compile:main": "webpack", "compile:workers": "webpack --config webpack.workers.config.js", "watch": "npm run watch:main & npm run watch:workers", "watch:main": "webpack --watch", "watch:workers": "webpack --config webpack.workers.config.js --watch", "package": "npm run package:main && npm run package:workers", "package:main": "webpack --mode production --devtool hidden-source-map", "package:workers": "webpack --config webpack.workers.config.js --mode production --devtool hidden-source-map", "package:analyze": "ANALYZE=true npm run package:main", "build:dev": "npm run compile", "build:prod": "npm run package", "analyze:bundle": "node scripts/analyze-bundle.js", "measure:baselines": "node scripts/measure-baselines.js", "validate:production": "node scripts/validate-production.js", "optimize": "npm run analyze:bundle && npm run measure:baselines", "release:prepare": "npm run validate:production && npm run test:all", "test:unit": "npm run compile-tests && mocha out/test/suite/*.test.js", "test:integration": "npm run test", "test:coverage": "npm run compile-tests && nyc --reporter=html --reporter=text mocha out/test/suite/*.test.js", "test:all": "npm run test:unit && npm run test:integration", "validate:final": "node scripts/final-validation.js", "release:final": "npm run validate:final && npm run package && npm run package:vsix", "package:vsix": "npx vsce package --out v1b3-sama-6.0.3.vsix --no-dependencies", "package:vsix-force": "npx vsce package --out v1b3-sama-6.0.3.vsix --no-dependencies --allow-star-activation", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "test:watch": "npm run compile-tests && mocha out/test/suite/*.test.js --watch"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/sinon": "^10.0.15", "@types/vscode": "^1.80.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vscode/test-electron": "^2.3.2", "@vscode/vsce": "^3.5.0", "c8": "^8.0.1", "copy-webpack-plugin": "^13.0.0", "eslint": "^8.41.0", "glob": "^8.1.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "sinon": "^15.2.0", "ts-loader": "^9.4.3", "typescript": "^5.1.3", "webpack": "^5.85.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.1"}, "dependencies": {"@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@types/diff": "^7.0.2", "@types/google-protobuf": "^3.15.12", "axios": "^1.4.0", "chalk": "^5.4.1", "diff": "^8.0.2", "globby": "^14.1.0", "grpc-tools": "^1.13.0", "http-server": "^14.1.1", "ts-proto": "^2.7.3"}}