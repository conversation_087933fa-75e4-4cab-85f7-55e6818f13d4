{"version": 3, "file": "RulePresetContext.js", "sourceRoot": "", "sources": ["../src/RulePresetContext.ts"], "names": [], "mappings": "AAWA,OAAO,EAAiB,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAEpE;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,EACpC,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,aAAa,EACb,aAAa,EACb,MAAM,GAQT,EAA+B,EAAE;IAC9B,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,IAAI,EAAE,CAAC;IACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,EAAE,gCAAgC,EAAE,WAAW,CAAC,CAAC;QACnF,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;KAC5E;IACD,OAAO;QACH,aAAa;QACb,YAAY,CACR,IAAoC,EACpC,YAAqE;YAErE,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;gBACvD,OAAO,cAAc,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,8BAA8B;YAC9B,8DAA8D;YAC9D,MAAM,qBAAqB,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;YAClF,MAAM,sBAAsB,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;YACpF,MAAM,sBAAsB,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;YACpF,MAAM,yBAAyB,GAAG,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC;YAC9F,MAAM,OAAO,GAAG,iBAAiB,CAAC;gBAC9B,uCAAuC;gBACvC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;gBACpB,mCAAmC;gBACnC,YAAY,EAAE,gBAAgB,CAAC,EAAE;gBACjC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,sBAAsB;gBAChC,UAAU;gBACV,aAAa,EAAE,aAAa;gBAC5B,aAAa,EAAE,aAAa;gBAC5B,MAAM;aACT,CAAC,CAAC;YACH,MAAM,oBAAoB,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9D,aAAa,CAAC,YAAY,CAAC;gBACvB,cAAc,EAAE;oBACZ,2BAA2B;oBAC3B,GAAG,oBAAoB;oBACvB,6BAA6B;oBAC7B,mCAAmC;oBACnC,GAAG,cAAc;oBACjB,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChB,OAAO,EAAE,qBAAqB;oBAC9B,IAAI;oBACJ,QAAQ,EAAE,sBAAsB;oBAChC,eAAe,EAAE,yBAAyB;oBAC1C,QAAQ,EAAE,sBAAsB;iBACnC;gBACD,OAAO;aACV,CAAC,CAAC;QACP,CAAC;KACJ,CAAC;AACN,CAAC,CAAC"}