{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/msal/types.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * @internal\n */\nexport type AppType = \"public\" | \"confidential\" | \"publicFirst\" | \"confidentialFirst\";\n\n/**\n * The shape we use return the token (and the expiration date).\n * @internal\n */\nexport interface MsalToken {\n  accessToken?: string;\n  expiresOn: Date | null;\n}\n\n/**\n * Represents a valid (i.e. complete) MSAL token.\n */\nexport type ValidMsalToken = { [P in keyof MsalToken]-?: NonNullable<MsalToken[P]> };\n\n/**\n * Internal representation of MSAL's Account information.\n * Helps us to disambiguate the MSAL classes accross environments.\n * @internal\n */\nexport interface MsalAccountInfo {\n  homeAccountId: string;\n  environment?: string;\n  tenantId: string;\n  username: string;\n  localAccountId: string;\n  name?: string;\n  // Leaving idTokenClaims as object since that's how MSAL has this assigned.\n  idTokenClaims?: object;\n}\n\n/**\n * Represents the common properties of any of the MSAL responses.\n * @internal\n */\nexport interface MsalResult {\n  authority?: string;\n  account: MsalAccountInfo | null;\n  accessToken: string;\n  expiresOn: Date | null;\n  refreshOn?: Date | null;\n}\n\n/**\n * The record to use to find the cached tokens in the cache.\n */\nexport interface AuthenticationRecord {\n  /**\n   * Entity which issued the token represented by the domain of the issuer (e.g. login.microsoftonline.com)\n   */\n  authority: string;\n  /**\n   * The home account Id.\n   */\n  homeAccountId: string;\n  /**\n   * The associated client ID.\n   */\n  clientId: string;\n  /**\n   * The associated tenant ID.\n   */\n  tenantId: string;\n  /**\n   * The username of the logged in account.\n   */\n  username: string;\n}\n\n/**\n * Represents a parsed certificate\n * @internal\n */\nexport interface CertificateParts {\n  /**\n   * Hex encoded X.509 SHA-256 thumbprint of the certificate.\n   */\n  thumbprintSha256: string;\n  /**\n   * Hex encoded X.509 SHA-1 thumbprint of the certificate.\n   * @deprecated Use thumbprintSha256 property instead. Thumbprint needs to be computed with SHA-256 algorithm.\n   * SHA-1 is only needed for backwards compatibility with older versions of ADFS.\n   */\n  thumbprint: string;\n  /**\n   * The PEM encoded private key.\n   */\n  privateKey: string;\n  /**\n   * x5c header.\n   */\n  x5c?: string;\n}\n"]}