{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../../src/logger/log.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAMlC,kBAEC;;AAND,qCAA8B;AAC9B,kEAA6B;AAC7B,8DAAwC;AAExC,SAAgB,GAAG,CAAC,OAAgB,EAAE,GAAG,IAAW;IAClD,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,mBAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,aAAG,EAAE,CAAC,CAAC;AACjE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { EOL } from \"node:os\";\nimport util from \"node:util\";\nimport * as process from \"node:process\";\n\nexport function log(message: unknown, ...args: any[]): void {\n  process.stderr.write(`${util.format(message, ...args)}${EOL}`);\n}\n"]}