## 4.0.0 - 2018-02-02

 - Ignore empty string arguments and throw an exception for non-string. Closes #36, #18 ([da05242f381bfe1ae09d00b708cfdbdb93c1a85d](https://github.com/jfromaniello/url-join/commit/da05242f381bfe1ae09d00b708cfdbdb93c1a85d)), closes [#36](https://github.com/jfromaniello/url-join/issues/36) [#18](https://github.com/jfromaniello/url-join/issues/18)



## 3.0.0 - 2018-01-12

 - add new test ([d65d7c1696cb53b53ceabadf1a77917196967b4c](https://github.com/jfromaniello/url-join/commit/d65d7c1696cb53b53ceabadf1a77917196967b4c))
 - Fixed to handle the colon in non-protocol separation role in the first part. ([9212db75f805031a9cc06120b5dd08a6cdd805e4](https://github.com/jfromaniello/url-join/commit/9212db75f805031a9cc06120b5dd08a6cdd805e4))



## 2.0.5 - 2018-01-10

 - revert to previous behavior #30 ([b6943343af7bd723cbca266388e84e036543577d](https://github.com/jfromaniello/url-join/commit/b6943343af7bd723cbca266388e84e036543577d)), closes [#30](https://github.com/jfromaniello/url-join/issues/30)



## 2.0.4 - 2018-01-10

 - fix bower.json ([9677895a4afe51d8a1d670980bc6fede71252e9a](https://github.com/jfromaniello/url-join/commit/9677895a4afe51d8a1d670980bc6fede71252e9a))



## 2.0.3 - 2018-01-09

 - 2.0.3 ([7b7806b21cf81a3476e39ddb8a6f51272a276186](https://github.com/jfromaniello/url-join/commit/7b7806b21cf81a3476e39ddb8a6f51272a276186))
 - Added a test for simple paths for issue #21 ([be99b10a707b4d22aac015d19eb087fff46d4270](https://github.com/jfromaniello/url-join/commit/be99b10a707b4d22aac015d19eb087fff46d4270)), closes [#21](https://github.com/jfromaniello/url-join/issues/21)
 - Added some new tests for cases that fail. ([f1afbd62c3149476a9ef099ba523e85fb4839732](https://github.com/jfromaniello/url-join/commit/f1afbd62c3149476a9ef099ba523e85fb4839732))
 - Passes all the tests with these changes. ([8cde667f400fa83efc7ed5c2437c7cb25c7d7600](https://github.com/jfromaniello/url-join/commit/8cde667f400fa83efc7ed5c2437c7cb25c7d7600))
 - The protocol slashes should be normalized also when the protocol is not alone in the first argument. ([0ce1239c60f7bbb625d4ccbf1fcf044f37488bd8](https://github.com/jfromaniello/url-join/commit/0ce1239c60f7bbb625d4ccbf1fcf044f37488bd8))



## 2.0.2 - 2017-05-18

 - fix: remove consecutives slashes ([33639364ef186e257b8424620017b9d1ba225539](https://github.com/jfromaniello/url-join/commit/33639364ef186e257b8424620017b9d1ba225539))



## 2.0.1 - 2017-04-12

 - update mocha and bower.json ([ebd3665028b2408d405f9a31f8479e91c4ef52c1](https://github.com/jfromaniello/url-join/commit/ebd3665028b2408d405f9a31f8479e91c4ef52c1))
 - feat: add test ([46d3387141e5d2f751da699e02d57fc36bfe37a8](https://github.com/jfromaniello/url-join/commit/46d3387141e5d2f751da699e02d57fc36bfe37a8))
 - fix: ignore encoded url when removing consecusive slashes ([711add4e8af8fc97390adef14b9a4722cac5e70a](https://github.com/jfromaniello/url-join/commit/711add4e8af8fc97390adef14b9a4722cac5e70a))



## 2.0.0 - 2017-04-11

 - Add a LICENSE file ([ffd3b2253470cee648152c55dd51c1bf4e688a60](https://github.com/jfromaniello/url-join/commit/ffd3b2253470cee648152c55dd51c1bf4e688a60))
 - change copyright year ([9f67671dd8ab23b4d2da6ae775efdf66d594eac3](https://github.com/jfromaniello/url-join/commit/9f67671dd8ab23b4d2da6ae775efdf66d594eac3))
 - refactor: use local startsWith function ([a1e1214644cd187f2584b79b4241ac3b8c9b9f1b](https://github.com/jfromaniello/url-join/commit/a1e1214644cd187f2584b79b4241ac3b8c9b9f1b))
 - fix: split logic for files ([d7053a99aa40b0c2f4802819f7e0643be8889ac4](https://github.com/jfromaniello/url-join/commit/d7053a99aa40b0c2f4802819f7e0643be8889ac4))
 - feat: add file protocol support ([48ebe0d84e8e2eca3a02fe5e3259cdd294e519dc](https://github.com/jfromaniello/url-join/commit/48ebe0d84e8e2eca3a02fe5e3259cdd294e519dc))



## 1.1.0 - 2016-04-05

 - add .travis.yml ([c75e7507f72fd4be101b64bb44539fd249842cc0](https://github.com/jfromaniello/url-join/commit/c75e7507f72fd4be101b64bb44539fd249842cc0))
 - added new syntax to allow options, fixed #! urls ([b8e5d8372c55187cdd9c6fa5e02830f76858347e](https://github.com/jfromaniello/url-join/commit/b8e5d8372c55187cdd9c6fa5e02830f76858347e))
 - added travis, updated version in bower.json ([5a58405d89298e693e8f97a74b14324d83a8a87a](https://github.com/jfromaniello/url-join/commit/5a58405d89298e693e8f97a74b14324d83a8a87a))
 - fixed query string handling, closes #9, closes #4 ([e190fe28282287204dbe7877979f18b4570042f9](https://github.com/jfromaniello/url-join/commit/e190fe28282287204dbe7877979f18b4570042f9)), closes [#9](https://github.com/jfromaniello/url-join/issues/9) [#4](https://github.com/jfromaniello/url-join/issues/4)



## 1.0.0 - 2016-03-23




## 0.1.0 - 2016-03-23

 - 0.1.0 ([2db128d268dfd531f1af6c9bd0543458387e94cd](https://github.com/jfromaniello/url-join/commit/2db128d268dfd531f1af6c9bd0543458387e94cd))
 - add support for AMD and windows['url-join'] ([b02169596877a1e6cd518f1b0d711f38c721fb02](https://github.com/jfromaniello/url-join/commit/b02169596877a1e6cd518f1b0d711f38c721fb02))
 - added comments, fixed leading // ([3f72b6ea6fa84c4b254d0c656815a5df6b89a10a](https://github.com/jfromaniello/url-join/commit/3f72b6ea6fa84c4b254d0c656815a5df6b89a10a))
 - added test for leading // ([baac627b2052e1d9b5c05e48c8dc6a05a80e08fa](https://github.com/jfromaniello/url-join/commit/baac627b2052e1d9b5c05e48c8dc6a05a80e08fa))
 - bower init ([650dcfe72eee854108dd0832963553eae5ede7c5](https://github.com/jfromaniello/url-join/commit/650dcfe72eee854108dd0832963553eae5ede7c5))
 - initial ([af68a208966de3d4be757c9d0f4a918c6dfa360e](https://github.com/jfromaniello/url-join/commit/af68a208966de3d4be757c9d0f4a918c6dfa360e))
 - minor ([dde2dc6815f9a0476d7aade1d6848cbc5f3a14a4](https://github.com/jfromaniello/url-join/commit/dde2dc6815f9a0476d7aade1d6848cbc5f3a14a4))
 - minor ([4d9d8ee16591da2092739a172145f968f71598dc](https://github.com/jfromaniello/url-join/commit/4d9d8ee16591da2092739a172145f968f71598dc))
 - minor ([9ed0161497ee7d7d1b4b04d1735483a6216fe2c6](https://github.com/jfromaniello/url-join/commit/9ed0161497ee7d7d1b4b04d1735483a6216fe2c6))
 - simplify normalize function ([d6886a362828eacc028c6167b9ae0efd8b2fbfc8](https://github.com/jfromaniello/url-join/commit/d6886a362828eacc028c6167b9ae0efd8b2fbfc8))



