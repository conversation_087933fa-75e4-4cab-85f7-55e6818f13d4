{"version": 3, "file": "SilentAuthCodeClient.mjs", "sources": ["../../src/interaction_client/SilentAuthCodeClient.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.authCodeRequired", "BrowserAuthErrorCodes.silentLogoutUnsupported"], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AA4BG,MAAO,oBAAqB,SAAQ,yBAAyB,CAAA;AAG/D,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,KAAY,EACZ,iBAAqC,EACrC,oBAA2C,EAC3C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AAED;;;AAGG;IACH,MAAM,YAAY,CACd,OAAiC,EAAA;;AAGjC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACf,YAAA,MAAM,sBAAsB,CACxBA,gBAAsC,CACzC,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,aAAa,GAAkC,MAAM,WAAW,CAClE,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAEnC,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,IAAI,CAAC,KAAK,CACb,CAAC;QAEF,IAAI;;AAEA,YAAA,MAAM,eAAe,GAAmC;AACpD,gBAAA,GAAG,aAAa;gBAChB,IAAI,EAAE,OAAO,CAAC,IAAI;aACrB,CAAC;;AAGF,YAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,aAAa,CAAC,SAAS;gBACzC,wBAAwB,EAAE,aAAa,CAAC,iBAAiB;gBACzD,2BAA2B,EAAE,aAAa,CAAC,oBAAoB;gBAC/D,OAAO,EAAE,aAAa,CAAC,OAAO;AACjC,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,UAAU,GACZ,IAAI,gCAAgC,CAAC,YAAY,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;;YAGhD,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAC7C,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,eAAe,EACf,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,YAAA,OAAO,MAAM,WAAW,CACpB,kBAAkB,CAAC,4BAA4B,CAAC,IAAI,CAChD,kBAAkB,CACrB,EACD,iBAAiB,CAAC,4BAA4B,EAC9C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG;gBACI,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,YAAY,EAAE,OAAO,CAAC,WAAW;gBACjC,qBAAqB,EAAE,OAAO,CAAC,kBAAkB;gBACjD,wBAAwB,EAAE,OAAO,CAAC,qBAAqB;AAC1D,aAAA,EACD,aAAa,EACb,KAAK,CACR,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;AAEG;IACH,MAAM,GAAA;;QAEF,OAAO,OAAO,CAAC,MAAM,CACjB,sBAAsB,CAClBC,uBAA6C,CAChD,CACJ,CAAC;KACL;AACJ;;;;"}