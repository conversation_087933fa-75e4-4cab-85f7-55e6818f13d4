import type { AuthorityValidationOptions } from "./authorityValidationOptions.js";
import type { CredentialPersistenceOptions } from "./credentialPersistenceOptions.js";
import type { MultiTenantTokenCredentialOptions } from "./multiTenantTokenCredentialOptions.js";
/**
 * Optional parameters for the {@link ClientCertificateCredential} class.
 */
export interface ClientCertificateCredentialOptions extends MultiTenantTokenCredentialOptions, CredentialPersistenceOptions, AuthorityValidationOptions {
    /**
     * Option to include x5c header for SubjectName and Issuer name authorization.
     * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim
     */
    sendCertificateChain?: boolean;
}
//# sourceMappingURL=clientCertificateCredentialOptions.d.ts.map