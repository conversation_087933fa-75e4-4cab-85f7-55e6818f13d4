{"version": 3, "file": "msalPlugins.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalPlugins.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,gBAAgB,EAChB,oBAAoB,EACpB,wBAAwB,GACzB,MAAM,oBAAoB,CAAC;AAoD5B;;;GAGG;AACH,MAAM,CAAC,IAAI,mBAAmB,GAEd,SAAS,CAAC;AAE1B;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,cAAc,CAAC,cAA8D;QAC3E,mBAAmB,GAAG,cAAc,CAAC;IACvC,CAAC;CACF,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,IAAI,gBAAgB,GAIX,SAAS,CAAC;AAE1B,MAAM,UAAU,eAAe;IAC7B,OAAO,gBAAgB,KAAK,SAAS,CAAC;AACxC,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAA8B;IACxE,eAAe,CAAC,MAAM;QACpB,gBAAgB,GAAG;YACjB,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;;;;;;GAOG;AACH,SAAS,2BAA2B,CAAC,OAA0B;;IAC7D,MAAM,MAAM,GAAwB;QAClC,KAAK,EAAE,EAAE;QACT,MAAM,EAAE;YACN,SAAS,EAAE,MAAA,MAAA,OAAO,CAAC,aAAa,0CAAE,OAAO,mCAAI,KAAK;YAClD,oBAAoB,EAAE,MAAA,MAAA,OAAO,CAAC,aAAa,0CAAE,0BAA0B,mCAAI,KAAK;YAChF,kBAAkB,EAAE,MAAA,OAAO,CAAC,aAAa,0CAAE,kBAAkB;SAC9D;KACF,CAAC;IAEF,IAAI,MAAA,OAAO,CAAC,4BAA4B,0CAAE,OAAO,EAAE,CAAC;QAClD,IAAI,mBAAmB,KAAK,SAAS,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CACb;gBACE,qFAAqF;gBACrF,yHAAyH;gBACzH,mFAAmF;gBACnF,0FAA0F;aAC3F,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,4BAA4B,CAAC,IAAI,IAAI,wBAAwB,CAAC;QAC5F,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,mBAAmB,iBAC5C,IAAI,EAAE,GAAG,aAAa,IAAI,oBAAoB,EAAE,IAC7C,OAAO,CAAC,4BAA4B,EACvC,CAAC;QACH,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,mBAAmB,iBAC/C,IAAI,EAAE,GAAG,aAAa,IAAI,gBAAgB,EAAE,IACzC,OAAO,CAAC,4BAA4B,EACvC,CAAC;IACL,CAAC;IAED,IAAI,MAAA,OAAO,CAAC,aAAa,0CAAE,OAAO,EAAE,CAAC;QACnC,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CACb;gBACE,kFAAkF;gBAClF,mGAAmG;gBACnG,mFAAmF;gBACnF,8EAA8E;aAC/E,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,kBAAkB,GAAG,gBAAiB,CAAC,MAAM,CAAC;IAC9D,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,2BAA2B;CAC5B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type * as msalNode from \"@azure/msal-node\";\n\nimport {\n  CACHE_CAE_SUFFIX,\n  CACHE_NON_CAE_SUFFIX,\n  DEFAULT_TOKEN_CACHE_NAME,\n} from \"../../constants.js\";\n\nimport type { MsalClientOptions } from \"./msalClient.js\";\nimport type { NativeBrokerPluginControl } from \"../../plugins/provider.js\";\nimport type { TokenCachePersistenceOptions } from \"./tokenCachePersistenceOptions.js\";\n\n/**\n * Configuration for the plugins used by the MSAL node client.\n */\nexport interface PluginConfiguration {\n  /**\n   * Configuration for the cache plugin.\n   */\n  cache: {\n    /**\n     * The non-CAE cache plugin handler.\n     */\n    cachePlugin?: Promise<msalNode.ICachePlugin>;\n    /**\n     * The CAE cache plugin handler - persisted to a different file.\n     */\n    cachePluginCae?: Promise<msalNode.ICachePlugin>;\n  };\n  /**\n   * Configuration for the broker plugin.\n   */\n  broker: {\n    /**\n     * True if the broker plugin is enabled and available. False otherwise.\n     *\n     * It is a bug if this is true and the broker plugin is not available.\n     */\n    isEnabled: boolean;\n    /**\n     * If true, MSA account will be passed through, required for WAM authentication.\n     */\n    enableMsaPassthrough: boolean;\n    /**\n     * The parent window handle for the broker.\n     */\n    parentWindowHandle?: Uint8Array;\n    /**\n     * The native broker plugin handler.\n     */\n    nativeBrokerPlugin?: msalNode.INativeBrokerPlugin;\n    /**\n     * If set to true, the credential will attempt to use the default broker account for authentication before falling back to interactive authentication. Default is set to false.\n     */\n    useDefaultBrokerAccount?: boolean;\n  };\n}\n\n/**\n * The current persistence provider, undefined by default.\n * @internal\n */\nexport let persistenceProvider:\n  | ((options?: TokenCachePersistenceOptions) => Promise<msalNode.ICachePlugin>)\n  | undefined = undefined;\n\n/**\n * An object that allows setting the persistence provider.\n * @internal\n */\nexport const msalNodeFlowCacheControl = {\n  setPersistence(pluginProvider: Exclude<typeof persistenceProvider, undefined>): void {\n    persistenceProvider = pluginProvider;\n  },\n};\n\n/**\n * The current native broker provider, undefined by default.\n * @internal\n */\nexport let nativeBrokerInfo:\n  | {\n      broker: msalNode.INativeBrokerPlugin;\n    }\n  | undefined = undefined;\n\nexport function hasNativeBroker(): boolean {\n  return nativeBrokerInfo !== undefined;\n}\n\n/**\n * An object that allows setting the native broker provider.\n * @internal\n */\nexport const msalNodeFlowNativeBrokerControl: NativeBrokerPluginControl = {\n  setNativeBroker(broker): void {\n    nativeBrokerInfo = {\n      broker,\n    };\n  },\n};\n\n/**\n * Configures plugins, validating that required plugins are available and enabled.\n *\n * Does not create the plugins themselves, but rather returns the configuration that will be used to create them.\n *\n * @param options - options for creating the MSAL client\n * @returns plugin configuration\n */\nfunction generatePluginConfiguration(options: MsalClientOptions): PluginConfiguration {\n  const config: PluginConfiguration = {\n    cache: {},\n    broker: {\n      isEnabled: options.brokerOptions?.enabled ?? false,\n      enableMsaPassthrough: options.brokerOptions?.legacyEnableMsaPassthrough ?? false,\n      parentWindowHandle: options.brokerOptions?.parentWindowHandle,\n    },\n  };\n\n  if (options.tokenCachePersistenceOptions?.enabled) {\n    if (persistenceProvider === undefined) {\n      throw new Error(\n        [\n          \"Persistent token caching was requested, but no persistence provider was configured.\",\n          \"You must install the identity-cache-persistence plugin package (`npm install --save @azure/identity-cache-persistence`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(cachePersistencePlugin)` before using `tokenCachePersistenceOptions`.\",\n        ].join(\" \"),\n      );\n    }\n\n    const cacheBaseName = options.tokenCachePersistenceOptions.name || DEFAULT_TOKEN_CACHE_NAME;\n    config.cache.cachePlugin = persistenceProvider({\n      name: `${cacheBaseName}.${CACHE_NON_CAE_SUFFIX}`,\n      ...options.tokenCachePersistenceOptions,\n    });\n    config.cache.cachePluginCae = persistenceProvider({\n      name: `${cacheBaseName}.${CACHE_CAE_SUFFIX}`,\n      ...options.tokenCachePersistenceOptions,\n    });\n  }\n\n  if (options.brokerOptions?.enabled) {\n    if (nativeBrokerInfo === undefined) {\n      throw new Error(\n        [\n          \"Broker for WAM was requested to be enabled, but no native broker was configured.\",\n          \"You must install the identity-broker plugin package (`npm install --save @azure/identity-broker`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(createNativeBrokerPlugin())` before using `enableBroker`.\",\n        ].join(\" \"),\n      );\n    }\n    config.broker.nativeBrokerPlugin = nativeBrokerInfo!.broker;\n  }\n\n  return config;\n}\n\n/**\n * Wraps generatePluginConfiguration as a writeable property for test stubbing purposes.\n */\nexport const msalPlugins = {\n  generatePluginConfiguration,\n};\n"]}