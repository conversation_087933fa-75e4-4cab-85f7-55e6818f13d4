{"version": 3, "file": "mask-result.js", "sourceRoot": "", "sources": ["../../src/formatters/mask-result.ts"], "names": [], "mappings": "AAEA,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,KAAgC,EAAE,OAAe,EAAE,EAAE;IACpF,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF,MAAM,SAAS,GAAwB,CAAC,OAAO,EAAE,EAAE;IAC/C,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAC3C,MAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;QACnD,IAAI,iBAAiB,KAAK,QAAQ,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACvE;QACD,IAAI,GAAG,GAAG,aAAa,CAAC;QACxB,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,YAAY,IAAI,GAAG,CAAC;KACvB;IACD,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEF,eAAe,SAAS,CAAC"}