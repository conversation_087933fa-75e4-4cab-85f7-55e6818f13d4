/**
 * User Preference Service
 * Learns and manages user preferences across sessions
 */

import * as vscode from 'vscode';
import { 
    IUserPreferenceService, 
    UserPreference, 
    ThreadedMessage 
} from '../interfaces/IEnhancedMemorySystem';

export class UserPreferenceService implements IUserPreferenceService {
    private static readonly PREFERENCES_KEY = 'v1b3sama.userPreferences';
    private preferences: Map<string, UserPreference> = new Map();
    private context: vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadPreferences();
    }

    public async getPreferences(category?: string): Promise<UserPreference[]> {
        const allPreferences = Array.from(this.preferences.values());
        
        if (category) {
            return allPreferences.filter(p => p.category === category);
        }
        
        return allPreferences.sort((a, b) => b.confidence - a.confidence);
    }

    public async setPreference(
        category: string, 
        key: string, 
        value: any, 
        confidence: number = 0.8
    ): Promise<void> {
        const id = `${category}:${key}`;
        const existing = this.preferences.get(id);
        
        if (existing) {
            // Update existing preference
            existing.value = value;
            existing.confidence = Math.min(1.0, existing.confidence + 0.1); // Increase confidence
            existing.frequency += 1;
            existing.lastUpdated = Date.now();
        } else {
            // Create new preference
            const preference: UserPreference = {
                id,
                category: category as any,
                key,
                value,
                confidence,
                learnedFrom: [],
                lastUpdated: Date.now(),
                frequency: 1
            };
            this.preferences.set(id, preference);
        }
        
        await this.savePreferences();
    }

    public async updatePreferenceConfidence(id: string, confidence: number): Promise<void> {
        const preference = this.preferences.get(id);
        if (preference) {
            preference.confidence = Math.max(0, Math.min(1, confidence));
            preference.lastUpdated = Date.now();
            await this.savePreferences();
        }
    }

    public async learnFromConversation(
        conversationId: string, 
        messages: ThreadedMessage[]
    ): Promise<UserPreference[]> {
        const learnedPreferences: UserPreference[] = [];
        
        for (const message of messages) {
            if (message.role === 'user') {
                // Analyze user message for preferences
                const preferences = await this.extractPreferencesFromMessage(message, conversationId);
                learnedPreferences.push(...preferences);
            }
        }
        
        return learnedPreferences;
    }

    public async getPreferencesByRelevance(context: string): Promise<UserPreference[]> {
        const allPreferences = Array.from(this.preferences.values());
        const contextLower = context.toLowerCase();
        
        // Score preferences by relevance to context
        const scoredPreferences = allPreferences.map(pref => {
            let relevanceScore = pref.confidence * 0.5; // Base score from confidence
            
            // Check if preference key/value relates to context
            if (pref.key.toLowerCase().includes(contextLower) || 
                String(pref.value).toLowerCase().includes(contextLower)) {
                relevanceScore += 0.3;
            }
            
            // Boost recent preferences
            const daysSinceUpdate = (Date.now() - pref.lastUpdated) / (1000 * 60 * 60 * 24);
            if (daysSinceUpdate < 7) {
                relevanceScore += 0.2;
            }
            
            // Boost frequently used preferences
            if (pref.frequency > 5) {
                relevanceScore += 0.1;
            }
            
            return { preference: pref, score: relevanceScore };
        });
        
        return scoredPreferences
            .filter(sp => sp.score > 0.3) // Only return relevant preferences
            .sort((a, b) => b.score - a.score)
            .map(sp => sp.preference);
    }

    private async extractPreferencesFromMessage(
        message: ThreadedMessage, 
        conversationId: string
    ): Promise<UserPreference[]> {
        const preferences: UserPreference[] = [];
        const content = message.content.toLowerCase();
        
        // Language preferences
        const languages = ['typescript', 'javascript', 'python', 'java', 'rust', 'go', 'c++', 'c#'];
        for (const lang of languages) {
            if (content.includes(lang) || content.includes(lang.replace('script', ''))) {
                await this.recordPreference('language_preference', 'preferred_language', lang, conversationId);
                preferences.push(this.preferences.get(`language_preference:preferred_language`)!);
            }
        }
        
        // Framework preferences
        const frameworks = ['react', 'vue', 'angular', 'express', 'fastapi', 'django', 'spring'];
        for (const framework of frameworks) {
            if (content.includes(framework)) {
                await this.recordPreference('framework_preference', 'preferred_framework', framework, conversationId);
                preferences.push(this.preferences.get(`framework_preference:preferred_framework`)!);
            }
        }
        
        // Coding style preferences
        if (content.includes('camelcase') || content.includes('camel case')) {
            await this.recordPreference('naming_convention', 'variable_naming', 'camelCase', conversationId);
        } else if (content.includes('snake_case') || content.includes('snake case')) {
            await this.recordPreference('naming_convention', 'variable_naming', 'snake_case', conversationId);
        }
        
        // Architecture preferences
        if (content.includes('microservice') || content.includes('micro service')) {
            await this.recordPreference('architecture_pattern', 'preferred_architecture', 'microservices', conversationId);
        } else if (content.includes('monolith')) {
            await this.recordPreference('architecture_pattern', 'preferred_architecture', 'monolithic', conversationId);
        }
        
        return preferences;
    }

    private async recordPreference(
        category: string, 
        key: string, 
        value: any, 
        conversationId: string
    ): Promise<void> {
        const id = `${category}:${key}`;
        const existing = this.preferences.get(id);
        
        if (existing) {
            existing.frequency += 1;
            existing.confidence = Math.min(1.0, existing.confidence + 0.05);
            existing.lastUpdated = Date.now();
            if (!existing.learnedFrom.includes(conversationId)) {
                existing.learnedFrom.push(conversationId);
            }
        } else {
            const preference: UserPreference = {
                id,
                category: category as any,
                key,
                value,
                confidence: 0.6, // Start with moderate confidence
                learnedFrom: [conversationId],
                lastUpdated: Date.now(),
                frequency: 1
            };
            this.preferences.set(id, preference);
        }
        
        await this.savePreferences();
    }

    private async loadPreferences(): Promise<void> {
        try {
            const stored = this.context.globalState.get<UserPreference[]>(
                UserPreferenceService.PREFERENCES_KEY, 
                []
            );
            
            this.preferences.clear();
            for (const pref of stored) {
                this.preferences.set(pref.id, pref);
            }
            
            console.log(`User Preferences: Loaded ${this.preferences.size} preferences`);
        } catch (error) {
            console.error('Failed to load user preferences:', error);
        }
    }

    private async savePreferences(): Promise<void> {
        try {
            const preferencesArray = Array.from(this.preferences.values());
            await this.context.globalState.update(
                UserPreferenceService.PREFERENCES_KEY, 
                preferencesArray
            );
        } catch (error) {
            console.error('Failed to save user preferences:', error);
        }
    }

    /**
     * Get preferences as context string for LLM prompts
     */
    public async getPreferencesAsContext(): Promise<string> {
        const preferences = await this.getPreferences();
        
        if (preferences.length === 0) {
            return '';
        }
        
        let context = '\n\n## User Preferences (Learned from Previous Interactions)\n\n';
        
        const groupedPrefs = preferences.reduce((groups, pref) => {
            if (!groups[pref.category]) {
                groups[pref.category] = [];
            }
            groups[pref.category].push(pref);
            return groups;
        }, {} as Record<string, UserPreference[]>);
        
        for (const [category, prefs] of Object.entries(groupedPrefs)) {
            context += `**${category.replace('_', ' ').toUpperCase()}:**\n`;
            for (const pref of prefs.slice(0, 3)) { // Limit to top 3 per category
                context += `- ${pref.key}: ${pref.value} (confidence: ${(pref.confidence * 100).toFixed(0)}%)\n`;
            }
            context += '\n';
        }
        
        return context;
    }

    /**
     * Clear all preferences (for privacy/reset)
     */
    public async clearAllPreferences(): Promise<void> {
        this.preferences.clear();
        await this.context.globalState.update(UserPreferenceService.PREFERENCES_KEY, []);
    }
}
