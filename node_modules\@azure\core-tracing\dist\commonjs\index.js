"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTracingClient = exports.useInstrumenter = void 0;
var instrumenter_js_1 = require("./instrumenter.js");
Object.defineProperty(exports, "useInstrumenter", { enumerable: true, get: function () { return instrumenter_js_1.useInstrumenter; } });
var tracingClient_js_1 = require("./tracingClient.js");
Object.defineProperty(exports, "createTracingClient", { enumerable: true, get: function () { return tracingClient_js_1.createTracingClient; } });
//# sourceMappingURL=index.js.map