{"version": 3, "file": "Authorize.mjs", "sources": ["../../src/protocol/Authorize.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.earJwkEmpty", "BrowserAuthErrorCodes.nativeConnectionNotEstablished", "BrowserAuthErrorCodes.earJweEmpty"], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;AA4CH;;;;;;;;AAQG;AACH,eAAe,qBAAqB,CAChC,MAA4B,EAC5B,SAAoB,EACpB,OAAsC,EACtC,MAAc,EACd,iBAAqC,EAAA;IAErC,MAAM,UAAU,GAAG,iBAAiB,CAAC,qCAAqC,CACtE,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,EACxC,OAAO,EACP,MAAM,EACN,iBAAiB,CACpB,CAAC;AACF,IAAA,uBAAuB,CAAC,cAAc,CAAC,UAAU,EAAE;QAC/C,GAAG,EAAE,gBAAgB,CAAC,QAAQ;AAC9B,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,EAAE,EAAE,EAAE;AACN,QAAA,GAAG,EAAE,EAAE;AACV,KAAA,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EAAE;QAChD,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,MAAM,CAAC,SAAS,CAAC,WAAW,CAC/B,CAAC;AACL,KAAA;IAED,IAAI,OAAO,CAAC,cAAc,EAAE;;AAExB,QAAA,uBAAuB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;;AAGpD,QAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;YAC3D,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC3D,YAAA,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC;;AAG3D,YAAA,IAAI,UAAU,CAAC;AACf,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACrD,iBAAiB,CAAC,mBAAmB,EACrC,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnB,gBAAA,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC;AACjD,aAAA;AAAM,iBAAA;gBACH,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACpD,aAAA;AACD,YAAA,uBAAuB,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC/D,SAAA;AACJ,KAAA;IAED,uBAAuB,CAAC,sBAAsB,CAC1C,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,iBAAiB,CACpB,CAAC;AAEF,IAAA,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;AAQG;AACI,eAAe,qBAAqB,CACvC,MAA4B,EAC5B,SAAoB,EACpB,OAAsC,EACtC,MAAc,EACd,iBAAqC,EAAA;AAErC,IAAA,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AACxB,QAAA,MAAM,8BAA8B,CAChC,6BAA6B,CAAC,iBAAiB,CAClD,CAAC;AACL,KAAA;AAED,IAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,qBAAqB,EACrB,iBAAiB,CAAC,iBAAiB,EACnC,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;IACzD,uBAAuB,CAAC,eAAe,CAAC,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAE5E,IAAA,uBAAuB,CAAC,sBAAsB,CAC1C,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,SAAS,CAAC,0BAA0B,CACvC,CAAC;IAEF,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,OAAO,CAAC,oBAAoB,IAAI,EAAE,CACrC,CAAC;AAEF,IAAA,OAAO,iBAAiB,CAAC,eAAe,CACpC,SAAS,EACT,UAAU,EACV,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAClC,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACN,CAAC;AAED;;AAEG;AACI,eAAe,UAAU,CAC5B,KAAe,EACf,MAA4B,EAC5B,SAAoB,EACpB,OAAsC,EACtC,MAAc,EACd,iBAAqC,EAAA;AAErC,IAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,QAAA,MAAM,sBAAsB,CAACA,WAAiC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,UAAU,GAAG,MAAM,qBAAqB,CAC1C,MAAM,EACN,SAAS,EACT,OAAO,EACP,MAAM,EACN,iBAAiB,CACpB,CAAC;IAEF,uBAAuB,CAAC,eAAe,CACnC,UAAU,EACV,iBAAiB,CAAC,0BAA0B,CAC/C,CAAC;IACF,uBAAuB,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAErE,IAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC9C,uBAAuB,CAAC,uBAAuB,CAC3C,WAAW,EACX,OAAO,CAAC,oBAAoB,IAAI,EAAE,CACrC,CAAC;IACF,MAAM,GAAG,GAAG,iBAAiB,CAAC,eAAe,CACzC,SAAS,EACT,WAAW,EACX,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAClC,OAAO,CAAC,oBAAoB,CAC/B,CAAC;IAEF,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;AAMG;AACH,SAAS,UAAU,CACf,KAAe,EACf,YAAoB,EACpB,UAA+B,EAAA;IAE/B,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,IAAA,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;IAE3B,UAAU,CAAC,OAAO,CAAC,CAAC,KAAa,EAAE,GAAW,KAAI;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3C,QAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,QAAA,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;AACjB,QAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAEpB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5B,KAAC,CAAC,CAAC;AAEH,IAAA,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;AAaG;AACI,eAAe,4BAA4B,CAC9C,OAAsC,EACtC,SAAiB,EACjB,KAAY,EACZ,MAA4B,EAC5B,cAAmC,EACnC,aAAkC,EAClC,YAA0B,EAC1B,MAAc,EACd,iBAAqC,EACrC,oBAA2C,EAAA;AAE3C,IAAA,MAAM,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;IAE1D,IAAI,CAAC,oBAAoB,EAAE;AACvB,QAAA,MAAM,sBAAsB,CACxBC,8BAAoD,CACvD,CAAC;AACL,KAAA;IACD,MAAM,aAAa,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC/D,IAAA,MAAM,uBAAuB,GAAG,IAAI,6BAA6B,CAC7D,MAAM,EACN,cAAc,EACd,aAAa,EACb,MAAM,EACN,YAAY,EACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAC9B,KAAK,EACL,iBAAiB,EACjB,oBAAoB,EACpB,SAAS,EACT,aAAa,EACb,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,IAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,aAAa,CAAC,iBAAiB,CACxD,aAAa,EACb,OAAO,CAAC,KAAK,CAChB,CAAC;IACF,OAAO,WAAW,CACd,uBAAuB,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAClE,iBAAiB,CAAC,mCAAmC,EACrD,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CAAC;AACE,QAAA,GAAG,OAAO;AACV,QAAA,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,SAAS;AACpB,KAAA,CAAC,CAAC;AACP,CAAC;AAED;;;;;;;;;;AAUG;AACI,eAAe,kBAAkB,CACpC,OAAsC,EACtC,QAA2B,EAC3B,YAAoB,EACpB,KAAY,EACZ,MAA4B,EAC5B,UAAmC,EACnC,cAAmC,EACnC,aAAkC,EAClC,YAA0B,EAC1B,MAAc,EACd,iBAAqC,EACrC,oBAA2C,EAAA;;AAG3C,IAAA,eAAe,CAAC,cAAc,CAC1B,cAAc,EACd,MAAM,CAAC,IAAI,CAAC,QAAQ,EACpB,OAAO,CACV,CAAC;IACF,IAAI,QAAQ,CAAC,SAAS,EAAE;AACpB,QAAA,OAAO,WAAW,CACd,4BAA4B,EAC5B,iBAAiB,CAAC,4BAA4B,EAC9C,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,EACP,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,MAAM,EACN,cAAc,EACd,aAAa,EACb,YAAY,EACZ,MAAM,EACN,iBAAiB,EACjB,oBAAoB,CACvB,CAAC;AACL,KAAA;AACD,IAAA,MAAM,eAAe,GAAmC;AACpD,QAAA,GAAG,OAAO;AACV,QAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;AACzB,QAAA,YAAY,EAAE,YAAY;KAC7B,CAAC;;AAEF,IAAA,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAC7C,UAAU,EACV,cAAc,EACd,eAAe,EACf,MAAM,EACN,iBAAiB,CACpB,CAAC;;AAEF,IAAA,MAAM,MAAM,GAAG,MAAM,WAAW,CAC5B,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAC9D,iBAAiB,CAAC,kBAAkB,EACpC,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAErB,IAAA,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACI,eAAe,iBAAiB,CACnC,OAAsC,EACtC,QAA2B,EAC3B,KAAY,EACZ,MAA4B,EAC5B,SAAoB,EACpB,cAAmC,EACnC,aAAkC,EAClC,YAA0B,EAC1B,MAAc,EACd,iBAAqC,EACrC,oBAA2C,EAAA;;AAG3C,IAAA,eAAe,CAAC,cAAc,CAC1B,cAAc,EACd,MAAM,CAAC,IAAI,CAAC,QAAQ,EACpB,OAAO,CACV,CAAC;;IAGF,iBAAiB,CAAC,6BAA6B,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAEzE,IAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACnB,QAAA,MAAM,sBAAsB,CAACC,WAAiC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACjB,QAAA,MAAM,sBAAsB,CAACF,WAAiC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAC5B,MAAM,WAAW,CACb,kBAAkB,EAClB,iBAAiB,CAAC,kBAAkB,EACpC,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CACkB,CAAC;IAE1D,IAAI,aAAa,CAAC,SAAS,EAAE;AACzB,QAAA,OAAO,WAAW,CACd,4BAA4B,EAC5B,iBAAiB,CAAC,4BAA4B,EAC9C,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,EACP,aAAa,CAAC,SAAS,EACvB,KAAK,EACL,MAAM,EACN,cAAc,EACd,aAAa,EACb,YAAY,EACZ,MAAM,EACN,iBAAiB,EACjB,oBAAoB,CACvB,CAAC;AACL,KAAA;AAED,IAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACpB,cAAc,EACd,IAAI,SAAS,CAAC,MAAM,EAAE,iBAAiB,CAAC,EACxC,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,iBAAiB,CACpB,CAAC;;AAGF,IAAA,eAAe,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;;AAGrD,IAAA,MAAM,cAAc,GAA6B;AAC7C,QAAA,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,WAAW,EAAE,aAAa,CAAC,WAAW;QACtC,qBAAqB,EAAE,aAAa,CAAC,qBAAqB;QAC1D,wBAAwB,EAAE,aAAa,CAAC,wBAAwB;QAChE,mBAAmB,EAAE,aAAa,CAAC,mBAAmB;QACtD,YAAY,EAAE,aAAa,CAAC,YAAY;KAC3C,CAAC;IAEF,QAAQ,MAAM,WAAW,CACrB,eAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,EAC/D,iBAAiB,CAAC,yBAAyB,EAC3C,MAAM,EACN,iBAAiB,EACjB,OAAO,CAAC,aAAa,CACxB,CACG,aAAa,EACb,SAAS,EACT,SAAS,CAAC,UAAU,EAAE,EACtB,OAAO,EACP,cAAc,EACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACZ,EAA0B;AAC/B;;;;"}