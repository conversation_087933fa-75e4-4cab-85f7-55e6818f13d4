{"version": 3, "file": "defaultAzureCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredential.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AA+BlC,wFAmDC;AAqFD,kEAIC;AAhKD,mEAAiF;AAEjF,mEAA6D;AAC7D,qFAA+E;AAC/E,iFAA2E;AAC3E,2EAAqE;AACrE,yEAAmE;AAEnE,mFAA6E;AAE7E,mDAAsD;AAEtD,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,wBAAwB,CAAC,CAAC;AAE1D;;;;;GAKG;AACH,SAAgB,sCAAsC,CACpD,UAG4C,EAAE;;IAE9C,MAAA,OAAO,CAAC,YAAY,oCAApB,OAAO,CAAC,YAAY,GAAK;QACvB,UAAU,EAAE,CAAC;QACb,cAAc,EAAE,GAAG;KACpB,EAAC;IACF,MAAM,uBAAuB,GAC3B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAC9B,MAAM,wBAAwB,GAC5B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,wBAAwB,mCAC5E,uBAAuB,CAAC;IAC1B,MAAM,iBAAiB,GAAI,OAAmD,aAAnD,OAAO,uBAAP,OAAO,CAC9B,yBAAyB,CAAC;IAC9B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAC5D,MAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAClE,IAAI,iBAAiB,EAAE,CAAC;QACtB,MAAM,gCAAgC,mCACjC,OAAO,KACV,UAAU,EAAE,iBAAiB,GAC9B,CAAC;QACF,OAAO,IAAI,oCAAyB,CAAC,gCAAgC,CAAC,CAAC;IACzE,CAAC;IAED,IAAI,YAAY,IAAI,wBAAwB,EAAE,CAAC;QAC7C,MAAM,iCAAiC,mCAClC,OAAO,KACV,QAAQ,EAAE,QAAQ,GACnB,CAAC;QAEF,OAAO,IAAI,oCAAyB,CAClC,wBAAwB,EACxB,iCAAiC,CAClC,CAAC;IACJ,CAAC;IAED,IAAI,uBAAuB,EAAE,CAAC;QAC5B,MAAM,4BAA4B,mCAC7B,OAAO,KACV,QAAQ,EAAE,uBAAuB,GAClC,CAAC;QAEF,OAAO,IAAI,oCAAyB,CAAC,4BAA4B,CAAC,CAAC;IACrE,CAAC;IAED,2FAA2F;IAC3F,OAAO,IAAI,oCAAyB,CAAC,OAAO,CAAC,CAAC;AAChD,CAAC;AAED;;;;;GAKG;AACH,SAAS,uCAAuC,CAC9C,OAA+E;;IAE/E,MAAM,uBAAuB,GAC3B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAC9B,MAAM,wBAAwB,GAC5B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,wBAAwB,mCAC5E,uBAAuB,CAAC;IAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;IAC5D,MAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IAClE,IAAI,YAAY,IAAI,wBAAwB,EAAE,CAAC;QAC7C,MAAM,iCAAiC,mCAClC,OAAO,KACV,QAAQ,EACR,QAAQ,EAAE,wBAAwB,EAClC,aAAa,EAAE,YAAY,GAC5B,CAAC;QACF,OAAO,IAAI,0DAA0B,CAAC,iCAAiC,CAAC,CAAC;IAC3E,CAAC;IACD,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,mCAAmC,mCACpC,OAAO,KACV,QAAQ,GACT,CAAC;QACF,OAAO,IAAI,0DAA0B,CAAC,mCAAmC,CAAC,CAAC;IAC7E,CAAC;IAED,2FAA2F;IAC3F,OAAO,IAAI,0DAA0B,CAAC,OAAO,CAAC,CAAC;AACjD,CAAC;AAED;;;;;GAKG;AACH,SAAS,wCAAwC,CAC/C,UAAyC,EAAE;IAE3C,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO,IAAI,4DAA2B,iBAAG,kBAAkB,IAAK,OAAO,EAAG,CAAC;AAC7E,CAAC;AAED;;;;;GAKG;AACH,SAAS,+BAA+B,CACtC,UAAyC,EAAE;IAE3C,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO,IAAI,0CAAkB,iBAAG,kBAAkB,IAAK,OAAO,EAAG,CAAC;AACpE,CAAC;AAED;;;;;GAKG;AACH,SAAS,sCAAsC,CAC7C,UAAyC,EAAE;IAE3C,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACtD,OAAO,IAAI,wDAAyB,iBAAG,kBAAkB,IAAK,OAAO,EAAG,CAAC;AAC3E,CAAC;AAED;;;;;GAKG;AACH,SAAgB,2BAA2B,CACzC,UAAyC,EAAE;IAE3C,OAAO,IAAI,gDAAqB,CAAC,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED;;;GAGG;AACH,MAAa,4BAA4B;IAIvC,YAAY,cAAsB,EAAE,OAAe;QACjD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,iCAAiC,GAAG,OAAO,CAAC;IACnD,CAAC;IAED,QAAQ;QACN,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,YAAY,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC,iCAAiC,EAAE,CACrF,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF;AAfD,oEAeC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAa,sBAAuB,SAAQ,kDAAsB;IAsBhE,YAAY,OAAuC;QACjD,2EAA2E;QAC3E,MAAM,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB;YAC/D,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE;YAC1D,CAAC,CAAC,SAAS,CAAC;QACd,MAAM,sBAAsB,GAAG;YAC7B,+BAA+B;YAC/B,sCAAsC;YACtC,wCAAwC;SACzC,CAAC;QACF,MAAM,uBAAuB,GAAG;YAC9B,2BAA2B;YAC3B,uCAAuC;YACvC,sCAAsC;SACvC,CAAC;QACF,IAAI,mBAAmB,GAAG,EAAE,CAAC;QAC7B,mFAAmF;QACnF,yEAAyE;QACzE,IAAI,qBAAqB,EAAE,CAAC;YAC1B,QAAQ,qBAAqB,EAAE,CAAC;gBAC9B,KAAK,KAAK;oBACR,6FAA6F;oBAC7F,mBAAmB,GAAG,sBAAsB,CAAC;oBAC7C,MAAM;gBACR,KAAK,MAAM;oBACT,oFAAoF;oBACpF,mBAAmB,GAAG,uBAAuB,CAAC;oBAC9C,MAAM;gBACR,OAAO,CAAC,CAAC,CAAC;oBACR,6EAA6E;oBAC7E,qFAAqF;oBACrF,MAAM,YAAY,GAAG,+CAA+C,OAAO,CAAC,GAAG,CAAC,uBAAuB,qCAAqC,CAAC;oBAC7I,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,2EAA2E;YAC3E,mBAAmB,GAAG,CAAC,GAAG,uBAAuB,EAAE,GAAG,sBAAsB,CAAC,CAAC;QAChF,CAAC;QAED,gLAAgL;QAChL,8DAA8D;QAC9D,6DAA6D;QAC7D,gEAAgE;QAChE,sHAAsH;QACtH,MAAM,WAAW,GAAsB,mBAAmB,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE;YACpF,IAAI,CAAC;gBACH,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,CACZ,WAAW,kBAAkB,CAAC,IAAI,iDAAiD,GAAG,EAAE,CACzF,CAAC;gBACF,OAAO,IAAI,4BAA4B,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YAChF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC;IACxB,CAAC;CACF;AAjFD,wDAiFC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./defaultAzureCredentialOptions.js\";\nimport type {\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n} from \"./managedIdentityCredential/options.js\";\nimport { ManagedIdentityCredential } from \"./managedIdentityCredential/index.js\";\n\nimport { AzureCliCredential } from \"./azureCliCredential.js\";\nimport { AzureDeveloperCliCredential } from \"./azureDeveloperCliCredential.js\";\nimport { AzurePowerShellCredential } from \"./azurePowerShellCredential.js\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential.js\";\nimport { EnvironmentCredential } from \"./environmentCredential.js\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { WorkloadIdentityCredential } from \"./workloadIdentityCredential.js\";\nimport type { WorkloadIdentityCredentialOptions } from \"./workloadIdentityCredentialOptions.js\";\nimport { credentialLogger } from \"../util/logging.js\";\n\nconst logger = credentialLogger(\"DefaultAzureCredential\");\n\n/**\n * Creates a {@link ManagedIdentityCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nexport function createDefaultManagedIdentityCredential(\n  options:\n    | DefaultAzureCredentialOptions\n    | DefaultAzureCredentialResourceIdOptions\n    | DefaultAzureCredentialClientIdOptions = {},\n): TokenCredential {\n  options.retryOptions ??= {\n    maxRetries: 5,\n    retryDelayInMs: 800,\n  };\n  const managedIdentityClientId =\n    (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n    process.env.AZURE_CLIENT_ID;\n  const workloadIdentityClientId =\n    (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n    managedIdentityClientId;\n  const managedResourceId = (options as DefaultAzureCredentialResourceIdOptions)\n    ?.managedIdentityResourceId;\n  const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n  const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n  if (managedResourceId) {\n    const managedIdentityResourceIdOptions: ManagedIdentityCredentialResourceIdOptions = {\n      ...options,\n      resourceId: managedResourceId,\n    };\n    return new ManagedIdentityCredential(managedIdentityResourceIdOptions);\n  }\n\n  if (workloadFile && workloadIdentityClientId) {\n    const workloadIdentityCredentialOptions: DefaultAzureCredentialOptions = {\n      ...options,\n      tenantId: tenantId,\n    };\n\n    return new ManagedIdentityCredential(\n      workloadIdentityClientId,\n      workloadIdentityCredentialOptions,\n    );\n  }\n\n  if (managedIdentityClientId) {\n    const managedIdentityClientOptions: ManagedIdentityCredentialClientIdOptions = {\n      ...options,\n      clientId: managedIdentityClientId,\n    };\n\n    return new ManagedIdentityCredential(managedIdentityClientOptions);\n  }\n\n  // We may be able to return a UnavailableCredential here, but that may be a breaking change\n  return new ManagedIdentityCredential(options);\n}\n\n/**\n * Creates a {@link WorkloadIdentityCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nfunction createDefaultWorkloadIdentityCredential(\n  options?: DefaultAzureCredentialOptions | DefaultAzureCredentialClientIdOptions,\n): TokenCredential {\n  const managedIdentityClientId =\n    (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n    process.env.AZURE_CLIENT_ID;\n  const workloadIdentityClientId =\n    (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n    managedIdentityClientId;\n  const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n  const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n  if (workloadFile && workloadIdentityClientId) {\n    const workloadIdentityCredentialOptions: WorkloadIdentityCredentialOptions = {\n      ...options,\n      tenantId,\n      clientId: workloadIdentityClientId,\n      tokenFilePath: workloadFile,\n    };\n    return new WorkloadIdentityCredential(workloadIdentityCredentialOptions);\n  }\n  if (tenantId) {\n    const workloadIdentityClientTenantOptions: WorkloadIdentityCredentialOptions = {\n      ...options,\n      tenantId,\n    };\n    return new WorkloadIdentityCredential(workloadIdentityClientTenantOptions);\n  }\n\n  // We may be able to return a UnavailableCredential here, but that may be a breaking change\n  return new WorkloadIdentityCredential(options);\n}\n\n/**\n * Creates a {@link AzureDeveloperCliCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nfunction createDefaultAzureDeveloperCliCredential(\n  options: DefaultAzureCredentialOptions = {},\n): TokenCredential {\n  const processTimeoutInMs = options.processTimeoutInMs;\n  return new AzureDeveloperCliCredential({ processTimeoutInMs, ...options });\n}\n\n/**\n * Creates a {@link AzureCliCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nfunction createDefaultAzureCliCredential(\n  options: DefaultAzureCredentialOptions = {},\n): TokenCredential {\n  const processTimeoutInMs = options.processTimeoutInMs;\n  return new AzureCliCredential({ processTimeoutInMs, ...options });\n}\n\n/**\n * Creates a {@link AzurePowerShellCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nfunction createDefaultAzurePowershellCredential(\n  options: DefaultAzureCredentialOptions = {},\n): TokenCredential {\n  const processTimeoutInMs = options.processTimeoutInMs;\n  return new AzurePowerShellCredential({ processTimeoutInMs, ...options });\n}\n\n/**\n * Creates an {@link EnvironmentCredential} from the provided options.\n * @param options - Options to configure the credential.\n *\n * @internal\n */\nexport function createEnvironmentCredential(\n  options: DefaultAzureCredentialOptions = {},\n): TokenCredential {\n  return new EnvironmentCredential(options);\n}\n\n/**\n * A no-op credential that logs the reason it was skipped if getToken is called.\n * @internal\n */\nexport class UnavailableDefaultCredential implements TokenCredential {\n  credentialUnavailableErrorMessage: string;\n  credentialName: string;\n\n  constructor(credentialName: string, message: string) {\n    this.credentialName = credentialName;\n    this.credentialUnavailableErrorMessage = message;\n  }\n\n  getToken(): Promise<null> {\n    logger.getToken.info(\n      `Skipping ${this.credentialName}, reason: ${this.credentialUnavailableErrorMessage}`,\n    );\n    return Promise.resolve(null);\n  }\n}\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration that works for most\n * applications that use Azure SDK client libraries. For more information, see\n * [DefaultAzureCredential overview](https://aka.ms/azsdk/js/identity/credential-chains#use-defaultazurecredential-for-flexibility).\n *\n * The following credential types will be tried, in order:\n *\n * - {@link EnvironmentCredential}\n * - {@link WorkloadIdentityCredential}\n * - {@link ManagedIdentityCredential}\n * - {@link AzureCliCredential}\n * - {@link AzurePowerShellCredential}\n * - {@link AzureDeveloperCliCredential}\n *\n * Consult the documentation of these credential types for more information\n * on how they attempt authentication.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialClientIdOptions}.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialClientIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialResourceIdOptions}.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialResourceIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialOptions}.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialOptions);\n\n  constructor(options?: DefaultAzureCredentialOptions) {\n    // If AZURE_TOKEN_CREDENTIALS is not set, use the default credential chain.\n    const azureTokenCredentials = process.env.AZURE_TOKEN_CREDENTIALS\n      ? process.env.AZURE_TOKEN_CREDENTIALS.trim().toLowerCase()\n      : undefined;\n    const devCredentialFunctions = [\n      createDefaultAzureCliCredential,\n      createDefaultAzurePowershellCredential,\n      createDefaultAzureDeveloperCliCredential,\n    ];\n    const prodCredentialFunctions = [\n      createEnvironmentCredential,\n      createDefaultWorkloadIdentityCredential,\n      createDefaultManagedIdentityCredential,\n    ];\n    let credentialFunctions = [];\n    // If AZURE_TOKEN_CREDENTIALS is set, use it to determine which credentials to use.\n    // The value of AZURE_TOKEN_CREDENTIALS should be either \"dev\" or \"prod\".\n    if (azureTokenCredentials) {\n      switch (azureTokenCredentials) {\n        case \"dev\":\n          // If AZURE_TOKEN_CREDENTIALS is set to \"dev\", use the developer tool-based credential chain.\n          credentialFunctions = devCredentialFunctions;\n          break;\n        case \"prod\":\n          // If AZURE_TOKEN_CREDENTIALS is set to \"prod\", use the production credential chain.\n          credentialFunctions = prodCredentialFunctions;\n          break;\n        default: {\n          // If AZURE_TOKEN_CREDENTIALS is set to an unsupported value, throw an error.\n          // We will throw an error here to prevent the creation of the DefaultAzureCredential.\n          const errorMessage = `Invalid value for AZURE_TOKEN_CREDENTIALS = ${process.env.AZURE_TOKEN_CREDENTIALS}. Valid values are 'prod' or 'dev'.`;\n          logger.warning(errorMessage);\n          throw new Error(errorMessage);\n        }\n      }\n    } else {\n      // If AZURE_TOKEN_CREDENTIALS is not set, use the default credential chain.\n      credentialFunctions = [...prodCredentialFunctions, ...devCredentialFunctions];\n    }\n\n    // Errors from individual credentials should not be thrown in the DefaultAzureCredential constructor, instead throwing on getToken() which is handled by ChainedTokenCredential.\n    // When adding new credentials to the default chain, consider:\n    // 1. Making the constructor parameters required and explicit\n    // 2. Validating any required parameters in the factory function\n    // 3. Returning a UnavailableDefaultCredential from the factory function if a credential is unavailable for any reason\n    const credentials: TokenCredential[] = credentialFunctions.map((createCredentialFn) => {\n      try {\n        return createCredentialFn(options);\n      } catch (err: any) {\n        logger.warning(\n          `Skipped ${createCredentialFn.name} because of an error creating the credential: ${err}`,\n        );\n        return new UnavailableDefaultCredential(createCredentialFn.name, err.message);\n      }\n    });\n\n    super(...credentials);\n  }\n}\n"]}