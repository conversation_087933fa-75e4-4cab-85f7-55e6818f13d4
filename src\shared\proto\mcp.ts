// Generated TypeScript definitions for mcp.proto
// Auto-generated from proto files using ts-proto equivalent definitions

export interface McpServer {
  name: string;
  command: string;
  args: string[];
  env?: Record<string, string>;
  cwd?: string;
  status: McpServerStatus;
  lastError?: string;
  timeout?: number;
  config?: string;
  tools?: McpTool[];
  resources?: McpResource[];
  resourceTemplates?: McpResourceTemplate[];
  error?: string;
  disabled?: boolean;
}

export enum McpServerStatus {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  ERROR = 'ERROR'
}

export interface McpTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface McpResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface McpResourceTemplate {
  uriTemplate: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface McpConnectionConfig {
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface McpContextData {
  workspaceRoot?: string;
  activeFile?: string;
  selectedText?: string;
  openFiles: string[];
}

export interface McpToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface McpToolResult {
  success: boolean;
  result?: any;
  error?: string;
}

export interface McpResourceContent {
  uri: string;
  mimeType?: string;
  text?: string;
  blob?: Uint8Array;
}

// Request/Response message types
export interface ToggleMcpServerRequest {
  serverName: string;
  enabled: boolean;
}

export interface UpdateMcpTimeoutRequest {
  serverName: string;
  timeout: number;
}

export interface AddRemoteMcpServerRequest {
  name: string;
  url: string;
  apiKey?: string;
}

export interface McpServersResponse {
  mcpServers: McpServer[];
}

// Alias for backward compatibility
export type McpServers = McpServersResponse;

// Utility functions for encoding/decoding (ts-proto style)
export const McpServer = {
  encode(message: McpServer): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpServer {
    return {
      name: '',
      command: '',
      args: [],
      status: McpServerStatus.DISCONNECTED
    };
  },
  fromJSON(object: any): McpServer {
    return {
      name: String(object.name || ''),
      command: String(object.command || ''),
      args: Array.isArray(object.args) ? object.args.map((e: any) => String(e)) : [],
      env: object.env || {},
      cwd: object.cwd ? String(object.cwd) : undefined,
      status: object.status || McpServerStatus.DISCONNECTED,
      lastError: object.lastError ? String(object.lastError) : undefined,
      timeout: object.timeout ? Number(object.timeout) : undefined,
      config: object.config ? String(object.config) : undefined,
      tools: Array.isArray(object.tools) ? object.tools.map((e: any) => McpTool.fromJSON(e)) : undefined,
      resources: Array.isArray(object.resources) ? object.resources.map((e: any) => McpResource.fromJSON(e)) : undefined,
      resourceTemplates: Array.isArray(object.resourceTemplates) ? object.resourceTemplates.map((e: any) => e) : undefined,
      error: object.error ? String(object.error) : undefined,
      disabled: object.disabled ? Boolean(object.disabled) : undefined
    };
  },
  toJSON(message: McpServer): unknown {
    const obj: any = {};
    obj.name = message.name;
    obj.command = message.command;
    obj.args = message.args;
    if (message.env) {
      obj.env = message.env;
    }
    if (message.cwd) {
      obj.cwd = message.cwd;
    }
    obj.status = message.status;
    if (message.lastError) {
      obj.lastError = message.lastError;
    }
    if (message.timeout) {
      obj.timeout = message.timeout;
    }
    if (message.config) {
      obj.config = message.config;
    }
    if (message.tools) {
      obj.tools = message.tools.map(e => McpTool.toJSON(e));
    }
    if (message.resources) {
      obj.resources = message.resources.map(e => McpResource.toJSON(e));
    }
    if (message.resourceTemplates) {
      obj.resourceTemplates = message.resourceTemplates;
    }
    if (message.error) {
      obj.error = message.error;
    }
    if (message.disabled !== undefined) {
      obj.disabled = message.disabled;
    }
    return obj;
  }
};

export const McpTool = {
  encode(message: McpTool): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpTool {
    return {
      name: '',
      description: '',
      inputSchema: {}
    };
  },
  fromJSON(object: any): McpTool {
    return {
      name: String(object.name || ''),
      description: String(object.description || ''),
      inputSchema: object.inputSchema || {}
    };
  },
  toJSON(message: McpTool): unknown {
    const obj: any = {};
    obj.name = message.name;
    obj.description = message.description;
    obj.inputSchema = message.inputSchema;
    return obj;
  }
};

export const McpResource = {
  encode(message: McpResource): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpResource {
    return {
      uri: '',
      name: ''
    };
  },
  fromJSON(object: any): McpResource {
    return {
      uri: String(object.uri || ''),
      name: String(object.name || ''),
      description: object.description ? String(object.description) : undefined,
      mimeType: object.mimeType ? String(object.mimeType) : undefined
    };
  },
  toJSON(message: McpResource): unknown {
    const obj: any = {};
    obj.uri = message.uri;
    obj.name = message.name;
    if (message.description) {
      obj.description = message.description;
    }
    if (message.mimeType) {
      obj.mimeType = message.mimeType;
    }
    return obj;
  }
};

export const McpToolCall = {
  encode(message: McpToolCall): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpToolCall {
    return {
      name: '',
      arguments: {}
    };
  },
  fromJSON(object: any): McpToolCall {
    return {
      name: String(object.name || ''),
      arguments: object.arguments || {}
    };
  },
  toJSON(message: McpToolCall): unknown {
    const obj: any = {};
    obj.name = message.name;
    obj.arguments = message.arguments;
    return obj;
  }
};

export const McpToolResult = {
  encode(message: McpToolResult): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpToolResult {
    return {
      success: false
    };
  },
  fromJSON(object: any): McpToolResult {
    return {
      success: Boolean(object.success),
      result: object.result,
      error: object.error ? String(object.error) : undefined
    };
  },
  toJSON(message: McpToolResult): unknown {
    const obj: any = {};
    obj.success = message.success;
    if (message.result !== undefined) {
      obj.result = message.result;
    }
    if (message.error) {
      obj.error = message.error;
    }
    return obj;
  }
};

export const McpServersResponse = {
  encode(message: McpServersResponse): Uint8Array {
    return new Uint8Array();
  },
  decode(): McpServersResponse {
    return {
      mcpServers: []
    };
  },
  fromJSON(object: any): McpServersResponse {
    return {
      mcpServers: Array.isArray(object.mcpServers) 
        ? object.mcpServers.map((e: any) => McpServer.fromJSON(e))
        : []
    };
  },
  toJSON(message: McpServersResponse): unknown {
    const obj: any = {};
    obj.mcpServers = message.mcpServers.map(e => McpServer.toJSON(e));
    return obj;
  }
};

// Service definition for gRPC client integration
export interface McpServiceDefinition {
  toggleServer: (request: ToggleMcpServerRequest) => Promise<McpServersResponse>;
  updateTimeout: (request: UpdateMcpTimeoutRequest) => Promise<McpServersResponse>;
  addRemoteServer: (request: AddRemoteMcpServerRequest) => Promise<McpServersResponse>;
  getServers: () => Promise<McpServersResponse>;
}
