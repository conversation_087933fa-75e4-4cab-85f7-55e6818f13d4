import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import * as crypto from 'crypto';
import { workspaceValidation } from './WorkspaceValidationService';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);
const access = promisify(fs.access);
const copyFile = promisify(fs.copyFile);

export interface FileOperation {
    type: 'create' | 'modify' | 'delete' | 'rename' | 'move';
    path: string;
    content?: string;
    newPath?: string;
    encoding?: BufferEncoding;
    id?: string; // Unique identifier for tracking
    priority?: number; // For operation ordering
}

export interface FileOperationResult {
    success: boolean;
    operation: FileOperation;
    error?: string;
    uri?: vscode.Uri;
    timestamp?: Date;
    retryCount?: number;
}

// Enhanced operation queue types
export type FileOperationTask = () => Promise<FileOperationResult>;

export interface QueuedOperation {
    id: string;
    operation: FileOperation;
    task: FileOperationTask;
    priority: number;
    retries: number;
    maxRetries: number;
    timestamp: Date;
}

export interface DirectoryStructure {
    name: string;
    path: string;
    type: 'file' | 'directory';
    children?: DirectoryStructure[];
    size?: number;
    lastModified?: Date;
}

/**
 * Comprehensive file operations service for VS Code workspace
 * Enhanced with reliable workspace edits, operation queuing, and error handling
 * Based on patterns from Cline, Blackbox, and Augment extensions
 */
export class FileOperationsService {
    private context: vscode.ExtensionContext;
    private outputChannel: vscode.OutputChannel;
    private operationQueue: QueuedOperation[] = [];
    private isProcessingQueue: boolean = false;
    private maxConcurrentOperations: number = 3;
    private defaultMaxRetries: number = 3;
    private operationTimeout: number = 30000; // 30 seconds

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.outputChannel = vscode.window.createOutputChannel('V1b3-Sama File Operations');

        // Start queue processor
        this.startQueueProcessor();
    }

    /**
     * Enhanced operation queue processor
     */
    private async startQueueProcessor(): Promise<void> {
        setInterval(async () => {
            if (!this.isProcessingQueue && this.operationQueue.length > 0) {
                await this.processQueue();
            }
        }, 100); // Check every 100ms
    }

    /**
     * Process queued operations with concurrency control
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessingQueue) return;

        this.isProcessingQueue = true;

        try {
            // Sort by priority (higher priority first)
            this.operationQueue.sort((a, b) => b.priority - a.priority);

            // Process up to maxConcurrentOperations
            const batch = this.operationQueue.splice(0, this.maxConcurrentOperations);

            if (batch.length > 0) {
                this.outputChannel.appendLine(`Processing ${batch.length} file operations...`);

                const promises = batch.map(async (queuedOp) => {
                    try {
                        const result = await this.executeWithTimeout(queuedOp.task, this.operationTimeout);

                        if (!result.success && queuedOp.retries < queuedOp.maxRetries) {
                            // Retry failed operation
                            queuedOp.retries++;
                            queuedOp.priority = Math.max(0, queuedOp.priority - 1); // Lower priority for retries
                            this.operationQueue.push(queuedOp);
                            this.outputChannel.appendLine(`Retrying operation ${queuedOp.id} (attempt ${queuedOp.retries}/${queuedOp.maxRetries})`);
                        }

                        return result;
                    } catch (error) {
                        this.outputChannel.appendLine(`Operation ${queuedOp.id} failed: ${error}`);
                        return {
                            success: false,
                            operation: queuedOp.operation,
                            error: error instanceof Error ? error.message : String(error),
                            timestamp: new Date(),
                            retryCount: queuedOp.retries
                        };
                    }
                });

                await Promise.allSettled(promises);
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * Execute operation with timeout
     */
    private async executeWithTimeout<T>(operation: () => Promise<T>, timeout: number): Promise<T> {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Operation timed out after ${timeout}ms`));
            }, timeout);

            operation()
                .then(resolve)
                .catch(reject)
                .finally(() => clearTimeout(timer));
        });
    }

    /**
     * Add operation to queue
     */
    private queueOperation(operation: FileOperation, task: FileOperationTask, priority: number = 5): string {
        const id = crypto.randomBytes(8).toString('hex');

        const queuedOp: QueuedOperation = {
            id,
            operation: { ...operation, id },
            task,
            priority,
            retries: 0,
            maxRetries: this.defaultMaxRetries,
            timestamp: new Date()
        };

        this.operationQueue.push(queuedOp);
        this.outputChannel.appendLine(`Queued operation ${id}: ${operation.type} ${operation.path}`);

        return id;
    }

    /**
     * Create a new file with content (Enhanced with reliable workspace edits)
     */
    public async createFile(filePath: string, content: string = '', encoding: BufferEncoding = 'utf8'): Promise<FileOperationResult> {
        return this.createFileReliable(filePath, content, encoding);
    }

    /**
     * Reliable file creation with enhanced error handling and workspace validation
     */
    private async createFileReliable(filePath: string, content: string = '', encoding: BufferEncoding = 'utf8'): Promise<FileOperationResult> {
        try {
            // Use centralized workspace validation
            const validation = workspaceValidation.validateFilePath(filePath);

            if (!validation.valid) {
                throw new Error(validation.error || 'Invalid file path');
            }

            const workspaceFolder = workspaceValidation.requireWorkspace();
            const fullPath = validation.resolvedPath!;
            const uri = vscode.Uri.file(fullPath);

            // Check if file already exists
            const fileExists = await this.fileExists(filePath);
            if (fileExists) {
                throw new Error(`File already exists: ${filePath}`);
            }

            // Ensure directory exists
            const dir = path.dirname(fullPath);
            try {
                await mkdir(dir, { recursive: true });
            } catch (dirError) {
                throw new Error(`Failed to create directory ${dir}: ${dirError}`);
            }

            // Use enhanced WorkspaceEdit with proper error handling
            const edit = new vscode.WorkspaceEdit();

            // Create file first
            edit.createFile(uri, {
                overwrite: false,
                ignoreIfExists: false
            });

            // Apply file creation
            const createSuccess = await vscode.workspace.applyEdit(edit);

            if (!createSuccess) {
                throw new Error('Failed to create file - workspace edit rejected');
            }

            // Add content if provided
            if (content && content.trim()) {
                const contentEdit = new vscode.WorkspaceEdit();
                contentEdit.insert(uri, new vscode.Position(0, 0), content);

                const contentSuccess = await vscode.workspace.applyEdit(contentEdit);

                if (!contentSuccess) {
                    // Try to clean up the empty file
                    try {
                        const cleanupEdit = new vscode.WorkspaceEdit();
                        cleanupEdit.deleteFile(uri, { ignoreIfNotExists: true });
                        await vscode.workspace.applyEdit(cleanupEdit);
                    } catch {
                        // Ignore cleanup errors
                    }
                    throw new Error('Failed to add content to file - workspace edit rejected');
                }
            }

            this.outputChannel.appendLine(`✓ Created file: ${filePath} (${content.length} characters)`);

            // Open the file in editor
            try {
                const document = await vscode.workspace.openTextDocument(uri);
                await vscode.window.showTextDocument(document, { preview: false });
            } catch (openError) {
                this.outputChannel.appendLine(`⚠️ File created but failed to open in editor: ${openError}`);
            }

            return {
                success: true,
                operation: { type: 'create', path: filePath, content, encoding },
                uri,
                timestamp: new Date(),
                retryCount: 0
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`✗ Failed to create file ${filePath}: ${errorMsg}`);

            return {
                success: false,
                operation: { type: 'create', path: filePath, content, encoding },
                error: errorMsg,
                timestamp: new Date()
            };
        }
    }

    /**
     * Modify existing file content (Enhanced with reliable workspace edits)
     */
    public async modifyFile(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<FileOperationResult> {
        return this.modifyFileReliable(filePath, content, encoding);
    }

    /**
     * Reliable file modification with enhanced error handling and backup
     */
    private async modifyFileReliable(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<FileOperationResult> {
        let originalContent: string | undefined;

        try {
            // Use centralized workspace validation
            const validation = workspaceValidation.validateFilePath(filePath);
            if (!validation.valid) {
                throw new Error(validation.error || 'Invalid file path');
            }

            const workspaceFolder = workspaceValidation.requireWorkspace();
            const fullPath = validation.resolvedPath!;
            const uri = vscode.Uri.file(fullPath);

            // Check if file exists and get original content for backup
            const fileExists = await this.fileExists(filePath);
            if (!fileExists) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            // Create backup of original content
            try {
                originalContent = await this.readFile(filePath, encoding);
            } catch (readError) {
                throw new Error(`Failed to read original file content: ${readError}`);
            }

            // Validate content change is necessary
            if (originalContent === content) {
                this.outputChannel.appendLine(`⚠️ File content unchanged: ${filePath}`);
                return {
                    success: true,
                    operation: { type: 'modify', path: filePath, content, encoding },
                    uri,
                    timestamp: new Date(),
                    retryCount: 0
                };
            }

            // Use enhanced WorkspaceEdit with proper error handling
            const edit = new vscode.WorkspaceEdit();

            try {
                const document = await vscode.workspace.openTextDocument(uri);
                const fullRange = new vscode.Range(
                    document.positionAt(0),
                    document.positionAt(document.getText().length)
                );

                edit.replace(uri, fullRange, content);

                const success = await vscode.workspace.applyEdit(edit);

                if (!success) {
                    throw new Error('Workspace edit was rejected by VS Code');
                }

                this.outputChannel.appendLine(`✓ Modified file: ${filePath} (${content.length} characters)`);

                // Show the modified file
                try {
                    await vscode.window.showTextDocument(document, { preview: false });
                } catch (showError) {
                    this.outputChannel.appendLine(`⚠️ File modified but failed to show in editor: ${showError}`);
                }

                return {
                    success: true,
                    operation: { type: 'modify', path: filePath, content, encoding },
                    uri,
                    timestamp: new Date(),
                    retryCount: 0
                };

            } catch (editError) {
                // Attempt to restore original content if modification failed
                if (originalContent !== undefined) {
                    try {
                        const restoreEdit = new vscode.WorkspaceEdit();
                        const document = await vscode.workspace.openTextDocument(uri);
                        const fullRange = new vscode.Range(
                            document.positionAt(0),
                            document.positionAt(document.getText().length)
                        );
                        restoreEdit.replace(uri, fullRange, originalContent);
                        await vscode.workspace.applyEdit(restoreEdit);
                        this.outputChannel.appendLine(`🔄 Restored original content after failed modification`);
                    } catch (restoreError) {
                        this.outputChannel.appendLine(`❌ Failed to restore original content: ${restoreError}`);
                    }
                }
                throw editError;
            }

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`✗ Failed to modify file ${filePath}: ${errorMsg}`);

            return {
                success: false,
                operation: { type: 'modify', path: filePath, content, encoding },
                error: errorMsg,
                timestamp: new Date()
            };
        }
    }

    /**
     * Delete a file (Enhanced with reliable workspace edits)
     */
    public async deleteFile(filePath: string): Promise<FileOperationResult> {
        return this.deleteFileReliable(filePath);
    }

    /**
     * Reliable file deletion with enhanced error handling
     */
    private async deleteFileReliable(filePath: string): Promise<FileOperationResult> {
        try {
            // Use centralized workspace validation
            const validation = workspaceValidation.validateFilePath(filePath);
            if (!validation.valid) {
                throw new Error(validation.error || 'Invalid file path');
            }

            const workspaceFolder = workspaceValidation.requireWorkspace();
            const fullPath = validation.resolvedPath!;
            const uri = vscode.Uri.file(fullPath);

            // Check if file exists
            const fileExists = await this.fileExists(filePath);
            if (!fileExists) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            // Check if file is currently open in editor
            const openEditors = vscode.window.visibleTextEditors;
            const isOpen = openEditors.some(editor =>
                editor.document.uri.fsPath === fullPath
            );

            if (isOpen) {
                // Close the file first
                const document = await vscode.workspace.openTextDocument(uri);
                await vscode.window.showTextDocument(document);
                await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
            }

            // Use enhanced WorkspaceEdit with proper error handling
            const edit = new vscode.WorkspaceEdit();
            edit.deleteFile(uri, {
                recursive: false,
                ignoreIfNotExists: false
            });

            const success = await vscode.workspace.applyEdit(edit);

            if (!success) {
                throw new Error('Workspace edit was rejected by VS Code');
            }

            this.outputChannel.appendLine(`✓ Deleted file: ${filePath}`);

            return {
                success: true,
                operation: { type: 'delete', path: filePath },
                uri,
                timestamp: new Date(),
                retryCount: 0
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`✗ Failed to delete file ${filePath}: ${errorMsg}`);

            return {
                success: false,
                operation: { type: 'delete', path: filePath },
                error: errorMsg,
                timestamp: new Date()
            };
        }
    }

    /**
     * Rename a file (Enhanced with reliable workspace edits)
     */
    public async renameFile(oldPath: string, newPath: string): Promise<FileOperationResult> {
        return this.renameFileReliable(oldPath, newPath);
    }

    /**
     * Reliable file renaming with enhanced error handling
     */
    private async renameFileReliable(oldPath: string, newPath: string): Promise<FileOperationResult> {
        try {
            // Use centralized workspace validation for both paths
            const oldValidation = workspaceValidation.validateFilePath(oldPath);
            const newValidation = workspaceValidation.validateFilePath(newPath);

            if (!oldValidation.valid) {
                throw new Error(`Source path invalid: ${oldValidation.error}`);
            }
            if (!newValidation.valid) {
                throw new Error(`Destination path invalid: ${newValidation.error}`);
            }

            const workspaceFolder = workspaceValidation.requireWorkspace();
            const oldFullPath = oldValidation.resolvedPath!;
            const newFullPath = newValidation.resolvedPath!;
            const oldUri = vscode.Uri.file(oldFullPath);
            const newUri = vscode.Uri.file(newFullPath);

            // Check if source file exists
            const sourceExists = await this.fileExists(oldPath);
            if (!sourceExists) {
                throw new Error(`Source file does not exist: ${oldPath}`);
            }

            // Check if target file already exists
            const targetExists = await this.fileExists(newPath);
            if (targetExists) {
                throw new Error(`Target file already exists: ${newPath}`);
            }

            // Ensure target directory exists
            const newDir = path.dirname(newFullPath);
            try {
                await mkdir(newDir, { recursive: true });
            } catch (dirError) {
                throw new Error(`Failed to create target directory ${newDir}: ${dirError}`);
            }

            // Use enhanced WorkspaceEdit with proper error handling
            const edit = new vscode.WorkspaceEdit();
            edit.renameFile(oldUri, newUri, {
                overwrite: false,
                ignoreIfExists: false
            });

            const success = await vscode.workspace.applyEdit(edit);

            if (!success) {
                throw new Error('Workspace edit was rejected by VS Code');
            }

            this.outputChannel.appendLine(`✓ Renamed file: ${oldPath} → ${newPath}`);

            return {
                success: true,
                operation: { type: 'rename', path: oldPath, newPath },
                uri: newUri,
                timestamp: new Date(),
                retryCount: 0
            };

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`✗ Failed to rename file ${oldPath}: ${errorMsg}`);

            return {
                success: false,
                operation: { type: 'rename', path: oldPath, newPath },
                error: errorMsg,
                timestamp: new Date()
            };
        }
    }

    /**
     * Read file content
     */
    public async readFile(filePath: string, encoding: BufferEncoding = 'utf8'): Promise<string> {
        // Use centralized workspace validation
        const validation = workspaceValidation.validateFilePath(filePath);
        if (!validation.valid) {
            throw new Error(validation.error || 'Invalid file path');
        }

        const fullPath = validation.resolvedPath!;
        const content = await readFile(fullPath, encoding);
        return content;
    }

    /**
     * Check if file exists
     */
    public async fileExists(filePath: string): Promise<boolean> {
        try {
            // Use centralized workspace validation
            const validation = workspaceValidation.validateFilePath(filePath);
            if (!validation.valid) {
                return false; // Invalid paths don't exist
            }

            const fullPath = validation.resolvedPath!;
            await stat(fullPath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Get workspace folder (using centralized validation service)
     */
    private getWorkspaceFolder(): vscode.WorkspaceFolder | undefined {
        return workspaceValidation.getWorkspaceFolder();
    }

    /**
     * Get directory structure
     */
    public async getDirectoryStructure(dirPath: string = '', maxDepth: number = 3): Promise<DirectoryStructure> {
        // Use centralized workspace validation
        const validation = workspaceValidation.validateFilePath(dirPath || '.');
        if (!validation.valid) {
            throw new Error(validation.error || 'Invalid directory path');
        }

        const fullPath = validation.resolvedPath!;
        return this.buildDirectoryStructure(fullPath, path.basename(fullPath) || 'workspace', 0, maxDepth);
    }

    private async buildDirectoryStructure(fullPath: string, name: string, currentDepth: number, maxDepth: number): Promise<DirectoryStructure> {
        const stats = await stat(fullPath);

        if (stats.isFile()) {
            return {
                name,
                path: fullPath,
                type: 'file',
                size: stats.size,
                lastModified: stats.mtime
            };
        }

        const structure: DirectoryStructure = {
            name,
            path: fullPath,
            type: 'directory',
            lastModified: stats.mtime,
            children: []
        };

        if (currentDepth < maxDepth) {
            try {
                const entries = await readdir(fullPath);
                const children = await Promise.all(
                    entries
                        .filter(entry => !entry.startsWith('.') && entry !== 'node_modules')
                        .map(async entry => {
                            const entryPath = path.join(fullPath, entry);
                            return this.buildDirectoryStructure(entryPath, entry, currentDepth + 1, maxDepth);
                        })
                );
                structure.children = children.sort((a, b) => {
                    if (a.type !== b.type) {
                        return a.type === 'directory' ? -1 : 1;
                    }
                    return a.name.localeCompare(b.name);
                });
            } catch (error) {
                // Ignore permission errors
            }
        }

        return structure;
    }

    /**
     * Create directory
     */
    public async createDirectory(dirPath: string): Promise<FileOperationResult> {
        try {
            // Use centralized workspace validation
            const validation = workspaceValidation.validateFilePath(dirPath);
            if (!validation.valid) {
                throw new Error(validation.error || 'Invalid directory path');
            }

            const fullPath = validation.resolvedPath!;
            await mkdir(fullPath, { recursive: true });

            this.outputChannel.appendLine(`✓ Created directory: ${dirPath}`);

            return {
                success: true,
                operation: { type: 'create', path: dirPath },
                uri: vscode.Uri.file(fullPath)
            };
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Unknown error';
            this.outputChannel.appendLine(`✗ Failed to create directory ${dirPath}: ${errorMsg}`);

            return {
                success: false,
                operation: { type: 'create', path: dirPath },
                error: errorMsg
            };
        }
    }

    /**
     * Queue file operation for reliable execution
     */
    public queueFileOperation(operation: FileOperation, priority: number = 5): string {
        const task: FileOperationTask = async () => {
            switch (operation.type) {
                case 'create':
                    return this.createFileReliable(operation.path, operation.content || '', operation.encoding);
                case 'modify':
                    return this.modifyFileReliable(operation.path, operation.content || '', operation.encoding);
                case 'delete':
                    return this.deleteFileReliable(operation.path);
                case 'rename':
                    return this.renameFileReliable(operation.path, operation.newPath!);
                default:
                    return {
                        success: false,
                        operation,
                        error: `Unsupported operation type: ${operation.type}`,
                        timestamp: new Date()
                    };
            }
        };

        return this.queueOperation(operation, task, priority);
    }

    /**
     * Queue multiple file operations for reliable batch execution
     */
    public queueBatchOperations(operations: FileOperation[], priority: number = 5): string[] {
        return operations.map(operation => this.queueFileOperation(operation, priority));
    }

    /**
     * Get queue status
     */
    public getQueueStatus(): { pending: number; processing: boolean; operations: QueuedOperation[] } {
        return {
            pending: this.operationQueue.length,
            processing: this.isProcessingQueue,
            operations: [...this.operationQueue] // Return copy for safety
        };
    }

    /**
     * Clear operation queue (emergency stop)
     */
    public clearQueue(): void {
        const clearedCount = this.operationQueue.length;
        this.operationQueue = [];
        this.outputChannel.appendLine(`🛑 Cleared ${clearedCount} pending operations from queue`);
    }

    /**
     * Enhanced batch file operations with queue support and reliability
     */
    public async batchOperations(operations: FileOperation[], useQueue: boolean = true): Promise<FileOperationResult[]> {
        if (useQueue) {
            // Queue all operations and wait for completion
            const operationIds = this.queueBatchOperations(operations, 10); // High priority for batch

            this.outputChannel.appendLine(`🔄 Queued ${operations.length} operations for reliable execution`);

            // Wait for all operations to complete
            return new Promise((resolve) => {
                const results: FileOperationResult[] = [];
                const startTime = Date.now();
                const timeout = 60000; // 1 minute timeout

                const checkCompletion = () => {
                    const queueStatus = this.getQueueStatus();
                    const remainingOps = queueStatus.operations.filter(op =>
                        operationIds.includes(op.id)
                    );

                    if (remainingOps.length === 0 && !queueStatus.processing) {
                        this.outputChannel.appendLine(`✅ All batch operations completed`);
                        resolve(results);
                    } else if (Date.now() - startTime > timeout) {
                        this.outputChannel.appendLine(`⏰ Batch operation timeout - ${remainingOps.length} operations still pending`);
                        resolve(results);
                    } else {
                        setTimeout(checkCompletion, 100);
                    }
                };

                checkCompletion();
            });
        }

        // Fallback to direct execution
        this.outputChannel.appendLine(`🔄 Executing ${operations.length} operations directly`);
        const results: FileOperationResult[] = [];

        for (const operation of operations) {
            let result: FileOperationResult;

            switch (operation.type) {
                case 'create':
                    result = await this.createFile(operation.path, operation.content || '', operation.encoding);
                    break;
                case 'modify':
                    result = await this.modifyFile(operation.path, operation.content || '', operation.encoding);
                    break;
                case 'delete':
                    result = await this.deleteFile(operation.path);
                    break;
                case 'rename':
                    result = await this.renameFile(operation.path, operation.newPath!);
                    break;
                default:
                    result = {
                        success: false,
                        operation,
                        error: `Unsupported operation type: ${operation.type}`,
                        timestamp: new Date()
                    };
            }

            results.push(result);

            // Stop on first failure if desired
            if (!result.success) {
                this.outputChannel.appendLine(`❌ Batch operation failed at: ${operation.path}`);
                break;
            }
        }

        return results;
    }

    /**
     * Search files by pattern
     */
    public async searchFiles(pattern: string, dirPath: string = ''): Promise<string[]> {
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            throw new Error('No workspace folder found');
        }

        const searchPath = path.resolve(workspaceFolder.uri.fsPath, dirPath);
        const results: string[] = [];

        await this.searchFilesRecursive(searchPath, pattern, results, workspaceFolder.uri.fsPath);
        return results;
    }

    private async searchFilesRecursive(currentPath: string, pattern: string, results: string[], basePath: string): Promise<void> {
        try {
            const entries = await readdir(currentPath);

            for (const entry of entries) {
                if (entry.startsWith('.') || entry === 'node_modules') {
                    continue;
                }

                const entryPath = path.join(currentPath, entry);
                const stats = await stat(entryPath);

                if (stats.isFile()) {
                    if (entry.includes(pattern) || entry.match(new RegExp(pattern, 'i'))) {
                        const relativePath = path.relative(basePath, entryPath);
                        results.push(relativePath);
                    }
                } else if (stats.isDirectory()) {
                    await this.searchFilesRecursive(entryPath, pattern, results, basePath);
                }
            }
        } catch (error) {
            // Ignore permission errors
        }
    }

    /**
     * Get file diff between two versions
     */
    public async getFileDiff(filePath: string, newContent: string): Promise<string> {
        try {
            const currentContent = await this.readFile(filePath);

            // Simple diff implementation - in production, use a proper diff library
            const currentLines = currentContent.split('\n');
            const newLines = newContent.split('\n');

            let diff = '';
            const maxLines = Math.max(currentLines.length, newLines.length);

            for (let i = 0; i < maxLines; i++) {
                const currentLine = currentLines[i] || '';
                const newLine = newLines[i] || '';

                if (currentLine !== newLine) {
                    if (currentLine) {
                        diff += `- ${currentLine}\n`;
                    }
                    if (newLine) {
                        diff += `+ ${newLine}\n`;
                    }
                } else {
                    diff += `  ${currentLine}\n`;
                }
            }

            return diff;
        } catch (error) {
            return `Error generating diff: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
    }

    /**
     * Dispose resources and clean up queue
     */
    public dispose(): void {
        // Clear any pending operations
        this.clearQueue();

        // Stop queue processing
        this.isProcessingQueue = false;

        // Dispose output channel
        this.outputChannel.dispose();

        this.outputChannel.appendLine('🧹 FileOperationsService disposed - all resources cleaned up');
    }
}
