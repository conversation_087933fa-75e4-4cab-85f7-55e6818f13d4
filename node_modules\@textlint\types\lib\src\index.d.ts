/**
 * @fileoverview
 * Public function and interface should be included `Textlint` prefix or postfix.
 * It aim to avoid conflict naming on user land code.
 */
export type { TextlintSourceCode, TextlintSourceCodeArgs, TextlintSourceCodeLocation, TextlintSourceCodePosition, TextlintSourceCodeRange, } from "./Source/TextlintSourceCode";
export type { TextlintRulePaddingLocator } from "./Rule/TextlintRulePaddingLocator";
export type { TextlintRuleContextFixCommand } from "./Rule/TextlintRuleContextFixCommand";
export type { TextlintRuleContextFixCommandGenerator } from "./Rule/TextlintRuleContextFixCommandGenerator";
export type { TextlintRuleError, TextlintRuleErrorConstructor, TextlintRuleErrorDetails, TextlintRuleErrorPaddingLocation, TextlintRuleErrorPaddingLocationRange, TextlintRuleErrorPaddingLocationLoc, TextlintRuleReportedObject, } from "./Rule/TextlintRuleError";
export type { TextlintRuleSeverityLevel } from "./Rule/TextlintRuleSeverityLevel";
export type { TextlintRuleSeverityLevelKey } from "./Rule/TextlintRuleSeverityLevelKey";
export type { TextlintRuleContext, TextlintRuleContextReportFunction, TextlintRuleContextReportFunctionArgs, } from "./Rule/TextlintRuleContext";
export type { TextlintRuleOptions } from "./Rule/TextlintRuleOptions";
export type { TextlintRuleReporter, TextlintFixableRuleModule, TextlintRuleModule, TextlintRuleReportHandler, } from "./Rule/TextlintRuleModule";
export type { TextlintFilterRuleContext, TextlintFilterRuleShouldIgnoreFunction, TextlintFilterRuleShouldIgnoreFunctionArgs, } from "./Rule/TextlintFilterRuleContext";
export type { TextlintFilterRuleModule, TextlintFilterRuleOptions, TextlintFilterRuleReporter, TextlintFilterRuleReportHandler, } from "./Rule/TextlintFilterRuleModule";
export type { TextlintPluginCreator, TextlintPluginOptions, TextlintPluginProcessor, TextlintPluginProcessorConstructor, TextlintPluginPreProcessResult, TextlintPluginPostProcessResult, } from "./Plugin/TextlintPluginModule";
export type { TextlintResult, TextlintFixResult, TextlintMessage, TextlintMessageFixCommand, } from "./Message/TextlintResult";
//# sourceMappingURL=index.d.ts.map