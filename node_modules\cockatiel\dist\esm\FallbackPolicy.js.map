{"version": 3, "file": "FallbackPolicy.js", "sourceRoot": "", "sources": ["../../src/FallbackPolicy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAIpD,MAAM,OAAO,cAAc;IAazB,YACmB,QAAwB,EACxB,KAAsB;QADtB,aAAQ,GAAR,QAAQ,CAAgB;QACxB,UAAK,GAAL,KAAK,CAAiB;QAZzC;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAKjD,CAAC;IAEJ;;;;OAIG;IACI,KAAK,CAAC,OAAO,CAClB,EAA0D,EAC1D,MAAM,GAAG,kBAAkB;QAE3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1D,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF", "sourcesContent": ["import { neverAbortedSignal } from './common/abort';\nimport { ExecuteWrapper } from './common/Executor';\nimport { IDefaultPolicyContext, IPolicy } from './Policy';\n\nexport class FallbackPolicy<AltReturn> implements IPolicy<IDefaultPolicyContext, AltReturn> {\n  declare readonly _altReturn: AltReturn;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onSuccess = this.executor.onSuccess;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onFailure = this.executor.onFailure;\n\n  constructor(\n    private readonly executor: ExecuteWrapper,\n    private readonly value: () => AltReturn,\n  ) {}\n\n  /**\n   * Executes the given function.\n   * @param fn Function to execute.\n   * @returns The function result or fallback value.\n   */\n  public async execute<T>(\n    fn: (context: IDefaultPolicyContext) => PromiseLike<T> | T,\n    signal = neverAbortedSignal,\n  ): Promise<T | AltReturn> {\n    const result = await this.executor.invoke(fn, { signal });\n    if ('success' in result) {\n      return result.success;\n    }\n\n    return this.value();\n  }\n}\n"]}