{"version": 3, "file": "clientAssertionCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientAssertionCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EACL,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAE,0BAA0B,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,MAAM,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAE7D;;GAEG;AACH,MAAM,OAAO,yBAAyB;IAOpC;;;;;;;;;OASG;IACH,YACE,QAAgB,EAChB,QAAgB,EAChB,YAAmC,EACnC,UAA4C,EAAE;QAE9C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAA0B,CAClC,8DAA8D,CAC/D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAA0B,CAClC,8DAA8D,CAC/D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAA0B,CAClC,qEAAqE,CACtE,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,kCAChD,OAAO,KACV,MAAM,EACN,sBAAsB,EAAE,IAAI,CAAC,OAAO,IACpC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAC9C,WAAW,EACX,IAAI,CAAC,YAAY,EACjB,UAAU,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport type { MsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport { createMsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils.js\";\n\nimport type { ClientAssertionCredentialOptions } from \"./clientAssertionCredentialOptions.js\";\nimport { CredentialUnavailableError } from \"../errors.js\";\nimport { credentialLogger } from \"../util/logging.js\";\nimport { tracingClient } from \"../util/tracing.js\";\n\nconst logger = credentialLogger(\"ClientAssertionCredential\");\n\n/**\n * Authenticates a service principal with a JWT assertion.\n */\nexport class ClientAssertionCredential implements TokenCredential {\n  private msalClient: MsalClient;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private getAssertion: () => Promise<string>;\n  private options: ClientAssertionCredentialOptions;\n\n  /**\n   * Creates an instance of the ClientAssertionCredential with the details\n   * needed to authenticate against Microsoft Entra ID with a client\n   * assertion provided by the developer through the `getAssertion` function parameter.\n   *\n   * @param tenantId - The Microsoft Entra tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param getAssertion - A function that retrieves the assertion for the credential to use.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    getAssertion: () => Promise<string>,\n    options: ClientAssertionCredentialOptions = {},\n  ) {\n    if (!tenantId) {\n      throw new CredentialUnavailableError(\n        \"ClientAssertionCredential: tenantId is a required parameter.\",\n      );\n    }\n\n    if (!clientId) {\n      throw new CredentialUnavailableError(\n        \"ClientAssertionCredential: clientId is a required parameter.\",\n      );\n    }\n\n    if (!getAssertion) {\n      throw new CredentialUnavailableError(\n        \"ClientAssertionCredential: clientAssertion is a required parameter.\",\n      );\n    }\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n\n    this.options = options;\n    this.getAssertion = getAssertion;\n    this.msalClient = createMsalClient(clientId, tenantId, {\n      ...options,\n      logger,\n      tokenCredentialOptions: this.options,\n    });\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger,\n        );\n\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        return this.msalClient.getTokenByClientAssertion(\n          arrayScopes,\n          this.getAssertion,\n          newOptions,\n        );\n      },\n    );\n  }\n}\n"]}