/**
 * Context Engine Service Interface
 * Provides sophisticated code context understanding and retrieval
 */

import * as vscode from 'vscode';
import { ExecutionPlan, ExecutionStep } from './ILLMService';

export interface AstSymbol {
    name: string;
    kind: string;
    filePath: string;
    start: number;
    end: number;
    line: number;
    column: number;
    type?: string;
    scope?: string;
    documentation?: string;
    signature?: string;
}

export interface CodeRelationship {
    sourceFile: string;
    targetFile: string;
    relationType: 'import' | 'export' | 'extends' | 'implements' | 'calls' | 'references';
    symbolName?: string;
    line?: number;
}

export interface ContextRankingFactors {
    semanticSimilarity: number;
    recency: number;
    activeFileBoost: number;
    userInteraction: number;
    graphProximity: number;
    frequency: number;
}

export interface RankedContext {
    filePath: string;
    content: string;
    symbols: AstSymbol[];
    score: number;
    rankingFactors: ContextRankingFactors;
    relevantLines?: number[];
    snippet?: string;
}

export interface ContextQuery {
    query: string;
    activeFile?: string;
    selectedText?: string;
    maxResults?: number;
    includeSymbols?: boolean;
    includeRelationships?: boolean;
    languageFilter?: string[];
    fileTypeFilter?: string[];
}

export interface ContextSearchResult {
    results: RankedContext[];
    totalMatches: number;
    searchTime: number;
    queryAnalysis: {
        intent: 'definition' | 'usage' | 'similar' | 'related' | 'general';
        extractedSymbols: string[];
        confidence: number;
    };
}

export interface IndexingProgress {
    totalFiles: number;
    processedFiles: number;
    currentFile: string;
    stage: 'scanning' | 'parsing' | 'indexing' | 'relationships' | 'complete';
    errors: string[];
}

export interface ContextEngineStats {
    totalFiles: number;
    totalSymbols: number;
    totalRelationships: number;
    indexSize: number;
    lastIndexed: number;
    supportedLanguages: string[];
    memoryUsage: number;
}

export interface LearningData {
    acceptedSuggestions: Map<string, number>;
    rejectedSuggestions: Map<string, number>;
    frequentlyAccessedFiles: Map<string, number>;
    userPatterns: Map<string, any>;
    contextPreferences: Map<string, number>;
}

export interface ExecutionPlanContext {
    plan: ExecutionPlan;
    createdAt: Date;
    createdBy: string; // user prompt or trigger
    status: 'pending' | 'executing' | 'completed' | 'failed' | 'cancelled';
    currentStep?: number;
    executedSteps: string[];
    failedSteps: string[];
    relatedFiles: string[];
    dependencies: string[];
    estimatedImpact: {
        filesAffected: number;
        linesChanged: number;
        complexity: 'low' | 'medium' | 'high';
    };
    // Optional timing fields
    startedAt?: Date;
    pausedAt?: Date;
    completedAt?: Date;
    error?: string;
}

export interface ExecutionPlanQuery {
    query: string;
    includeCompleted?: boolean;
    includeActive?: boolean;
    includeFailed?: boolean;
    timeRange?: {
        start: Date;
        end: Date;
    };
    relatedToFiles?: string[];
    maxResults?: number;
}

export interface IContextEngineService {
    /**
     * Initialize the context engine and start workspace indexing
     */
    initialize(): Promise<void>;

    /**
     * Get ranked context for a query
     */
    getRankedContext(query: ContextQuery): Promise<ContextSearchResult>;

    /**
     * Get enhanced context for AI prompts - automatically provides relevant code context
     */
    getEnhancedContext(userPrompt: string): Promise<string>;

    /**
     * Find symbol definition
     */
    findSymbolDefinition(symbolName: string, filePath?: string): Promise<AstSymbol | undefined>;

    /**
     * Find all references to a symbol
     */
    findSymbolReferences(symbolName: string, filePath?: string): Promise<AstSymbol[]>;

    /**
     * Find similar code snippets based on AST structure
     */
    findSimilarCode(code: string, language?: string): Promise<RankedContext[]>;

    /**
     * Get code relationships for a file
     */
    getCodeRelationships(filePath: string): Promise<CodeRelationship[]>;

    /**
     * Get files related to the current context
     */
    getRelatedFiles(filePath: string, maxDepth?: number): Promise<string[]>;

    /**
     * Update index for a specific file
     */
    updateFileIndex(filePath: string): Promise<void>;

    /**
     * Remove file from index
     */
    removeFileFromIndex(filePath: string): Promise<void>;

    /**
     * Reindex entire workspace
     */
    reindexWorkspace(): Promise<void>;

    /**
     * Get indexing progress
     */
    getIndexingProgress(): IndexingProgress;

    /**
     * Get context engine statistics
     */
    getStats(): Promise<ContextEngineStats>;

    /**
     * Record user feedback for learning
     */
    recordFeedback(query: string, result: RankedContext, accepted: boolean): Promise<void>;

    /**
     * Get learning insights
     */
    getLearningData(): Promise<LearningData>;

    /**
     * Clear all cached data
     */
    clearCache(): Promise<void>;

    /**
     * Add execution plan to context engine for tracking and analysis
     */
    addExecutionPlan(plan: ExecutionPlan, createdBy: string): Promise<void>;

    /**
     * Update execution plan status and progress
     */
    updateExecutionPlan(planId: string, updates: Partial<ExecutionPlanContext>): Promise<void>;

    /**
     * Get execution plans based on query criteria
     */
    getExecutionPlans(query: ExecutionPlanQuery): Promise<ExecutionPlanContext[]>;

    /**
     * Get execution plan by ID
     */
    getExecutionPlan(planId: string): Promise<ExecutionPlanContext | undefined>;

    /**
     * Get execution plans related to specific files
     */
    getExecutionPlansForFiles(filePaths: string[]): Promise<ExecutionPlanContext[]>;

    /**
     * Analyze execution plan impact on codebase
     */
    analyzeExecutionPlanImpact(plan: ExecutionPlan): Promise<{
        affectedFiles: string[];
        potentialConflicts: string[];
        dependencies: string[];
        complexity: 'low' | 'medium' | 'high';
        estimatedChanges: number;
    }>;

    /**
     * Get context for execution plan generation
     */
    getExecutionPlanContext(userPrompt: string, targetFiles?: string[]): Promise<string>;

    /**
     * Process user request through context engine
     */
    processRequest(content: string): Promise<{
        filesIndexed?: number;
        relatedFiles?: number;
        complexity?: string;
        confidence?: number;
    }>;

    /**
     * Dispose resources
     */
    dispose(): void;

    /**
     * Event emitters
     */
    onIndexingProgress: vscode.Event<IndexingProgress>;
    onIndexingComplete: vscode.Event<ContextEngineStats>;
    onError: vscode.Event<Error>;
}
