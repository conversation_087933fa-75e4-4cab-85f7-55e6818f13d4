{"version": 3, "file": "scopeUtils.js", "sourceRoot": "", "sources": ["../../../src/util/scopeUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE3C;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,MAAyB;IACpD,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,+BAA+B,CAAC,KAAa,EAAE,MAAwB;IACrF,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QACrF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QAChD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAa;IAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CredentialLogger } from \"./logging.js\";\nimport { formatError } from \"./logging.js\";\n\n/**\n * Ensures the scopes value is an array.\n * @internal\n */\nexport function ensureScopes(scopes: string | string[]): string[] {\n  return Array.isArray(scopes) ? scopes : [scopes];\n}\n\n/**\n * Throws if the received scope is not valid.\n * @internal\n */\nexport function ensureValidScopeForDevTimeCreds(scope: string, logger: CredentialLogger): void {\n  if (!scope.match(/^[0-9a-zA-Z-_.:/]+$/)) {\n    const error = new Error(\"Invalid scope was specified by the user or calling client\");\n    logger.getToken.info(formatError(scope, error));\n    throw error;\n  }\n}\n\n/**\n * Returns the resource out of a scope.\n * @internal\n */\nexport function getScopeResource(scope: string): string {\n  return scope.replace(/\\/.default$/, \"\");\n}\n"]}