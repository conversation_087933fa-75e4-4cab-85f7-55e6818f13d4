{"version": "1.0.0", "lastUpdated": "2025-01-20T00:00:00.000Z", "providers": [{"id": "deepseek", "name": "DeepSeek", "displayName": "DeepSeek", "apiBaseUrl": "https://api.deepseek.com/v1", "defaultModel": "deepseek-chat", "requiresApiKey": true, "supportedFeatures": ["chat", "code", "streaming", "reasoning"], "modelsDiscoveryUrl": "/models", "authType": "bearerToken", "healthCheckUrl": "/models", "maxTokens": 64000, "supportsStreaming": true, "rateLimit": {"requestsPerMinute": 60, "tokensPerMinute": 1000000}}, {"id": "groq", "name": "Groq", "displayName": "Groq", "apiBaseUrl": "https://api.groq.com/openai/v1", "defaultModel": "llama-3.3-70b-versatile", "requiresApiKey": true, "supportedFeatures": ["chat", "streaming", "fast-inference", "reasoning"], "modelsDiscoveryUrl": "/models", "authType": "bearerToken", "healthCheckUrl": "/models", "maxTokens": 131072, "supportsStreaming": true, "rateLimit": {"requestsPerMinute": 30, "tokensPerMinute": 6000}}, {"id": "openrouter", "name": "OpenRouter", "displayName": "OpenRouter", "apiBaseUrl": "https://openrouter.ai/api/v1", "defaultModel": "anthropic/claude-3.5-sonnet", "requiresApiKey": true, "supportedFeatures": ["chat", "streaming", "multi-model"], "modelsDiscoveryUrl": "/models", "authType": "bearerToken", "healthCheckUrl": "/models", "maxTokens": 200000, "supportsStreaming": true, "rateLimit": {"requestsPerMinute": 200, "tokensPerMinute": 1000000}}, {"id": "local", "name": "Local", "displayName": "Local (Ollama/LM Studio)", "apiBaseUrl": "http://localhost:11434", "defaultModel": "llama3.2", "requiresApiKey": false, "supportedFeatures": ["chat", "streaming", "offline"], "modelsDiscoveryUrl": "/api/tags", "authType": "none", "healthCheckUrl": "/api/version", "supportsStreaming": true}]}