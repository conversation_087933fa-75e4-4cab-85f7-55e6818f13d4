{"version": 3, "file": "authorizationCodeCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/authorizationCodeCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAGnE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,kIAAkI,CACnI,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D,MAAM,OAAO,2BAA2B;IAmBtC;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\nimport type { AuthorizationCodeCredentialOptions } from \"./authorizationCodeCredentialOptions.js\";\n\nconst BrowserNotSupportedError = new Error(\n  \"AuthorizationCodeCredential is not supported in the browser. InteractiveBrowserCredential is more appropriate for this use case.\",\n);\nconst logger = credentialLogger(\"AuthorizationCodeCredential\");\n\nexport class AuthorizationCodeCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecret: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions,\n  );\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions,\n  );\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}