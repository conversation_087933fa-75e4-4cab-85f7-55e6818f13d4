{"name": "rc-config-loader", "version": "4.1.3", "description": "load config file from .{product}rc.{json,yml,js}", "keywords": ["config", "configuration", "json", "loader", "rc", "yaml", "yml"], "homepage": "https://github.com/azu/rc-config-loader", "bugs": {"url": "https://github.com/azu/rc-config-loader/issues"}, "repository": {"type": "git", "url": "https://github.com/azu/rc-config-loader.git"}, "license": "MIT", "author": "azu", "files": ["bin/", "lib/", "src/"], "main": "lib/rc-config-loader.js", "types": "lib/rc-config-loader.d.ts", "directories": {"test": "test"}, "scripts": {"build": "tsc -p .", "prepublish": "npm run --if-present build", "test": "mocha \"test/**/*.{js,ts}\"", "watch": "tsc -p . --watch", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepare": "git config --local core.hooksPath .githooks"}, "husky": {"hooks": {"post-commit": "git reset", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx,css}": ["prettier --write"]}, "prettier": {"singleQuote": false, "printWidth": 120, "tabWidth": 4, "trailingComma": "none"}, "dependencies": {"debug": "^4.3.4", "js-yaml": "^4.1.0", "json5": "^2.2.2", "require-from-string": "^2.0.2"}, "devDependencies": {"@types/mocha": "^9.1.0", "@types/node": "^17.0.23", "@types/require-from-string": "^1.2.1", "lint-staged": "^12.3.7", "mocha": "^9.2.2", "prettier": "^2.6.2", "ts-node": "^10.7.0", "ts-node-test-register": "^10.0.0", "typescript": "^4.6.3"}}