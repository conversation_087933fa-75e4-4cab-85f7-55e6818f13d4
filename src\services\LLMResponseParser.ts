import * as vscode from 'vscode';
import { AutoApproval<PERSON>anager, ApprovalResult } from './AutoApprovalManager';
import { ProcessedFileOperation } from './AutoExecutionEngine';
import { ExecutionPlan, ExecutionStep } from '../interfaces/ILLMService';
import { ProjectScaffoldRequest, TechnologyStack } from '../interfaces/IProjectScaffold';

export interface FileOperation {
    type: 'create' | 'modify' | 'create_directory';
    path: string;
    content: string;
    language?: string;
}

export interface ParsedResponse {
    textContent: string;
    fileOperations: FileOperation[];
    hasFileOperations: boolean;
    executionPlan?: ExecutionPlan;
    hasExecutionPlan: boolean;
    projectScaffoldRequest?: ProjectScaffoldRequest;
    hasProjectScaffold: boolean;
}

export interface ProcessedResponse {
    textContent: string;
    fileOperations: ProcessedFileOperation[];
    autoExecutedOperations: ProcessedFileOperation[];
    pendingOperations: ProcessedFileOperation[];
    hasFileOperations: boolean;
}

export interface FileOperationCard {
    id: string;
    operation: FileOperation;
    status: 'pending' | 'approved' | 'rejected' | 'completed';
}

/**
 * Service to parse structured LLM responses and extract file operations
 */
export class LLMResponseParser {
    private static readonly FILE_OPERATION_REGEX = /<file_operation\s+type="([^"]+)"\s+path="([^"]+)"(?:\s+language="([^"]+)")?>([\s\S]*?)<\/file_operation>/g;
    private static readonly EXECUTION_PLAN_REGEX = /<execution_plan\s+id="([^"]+)"\s+description="([^"]+)"(?:\s+duration="([^"]+)")?>([\s\S]*?)<\/execution_plan>/g;
    private static readonly EXECUTION_STEP_REGEX = /<step\s+id="([^"]+)"\s+type="([^"]+)"\s+order="([^"]+)"(?:\s+path="([^"]*)")?(?:\s+command="([^"]*)")?>([\s\S]*?)<\/step>/g;

    /**
     * Parse LLM response and extract file operations, execution plans, and project scaffolds
     * Enhanced for Augment-style automatic file operations and industry-standard project generation
     */
    public static parseResponse(content: string): ParsedResponse {
        const fileOperations: FileOperation[] = [];
        let textContent = content;
        let executionPlan: ExecutionPlan | undefined;
        let projectScaffoldRequest: ProjectScaffoldRequest | undefined;

        try {
            // Reset regex lastIndex to ensure proper matching
            this.FILE_OPERATION_REGEX.lastIndex = 0;
            this.EXECUTION_PLAN_REGEX.lastIndex = 0;

            // AUGMENT-STYLE: Aggressively detect code that should be in files
            const codeBlocksWithoutTags = this.detectUntaggedCodeBlocks(content);
            if (codeBlocksWithoutTags.length > 0) {
                // Auto-convert untagged code blocks to file operations
                for (const codeBlock of codeBlocksWithoutTags) {
                    fileOperations.push(codeBlock);
                }
            }

            // ENHANCED: Also detect inline code patterns that should be files
            const inlineCodeOperations = this.detectInlineCodePatterns(content);
            if (inlineCodeOperations.length > 0) {
                for (const operation of inlineCodeOperations) {
                    fileOperations.push(operation);
                }
            }

            // Extract file operations
            let match;
            while ((match = this.FILE_OPERATION_REGEX.exec(content)) !== null) {
                const [fullMatch, type, path, language, operationContent] = match;

                // Validate operation type
                if (!['create', 'modify', 'create_directory'].includes(type)) {
                    console.warn(`Invalid file operation type: ${type}`);
                    continue;
                }

                // Validate path
                if (!path || path.trim() === '') {
                    console.warn('Empty path in file operation');
                    continue;
                }

                // Create file operation
                const operation: FileOperation = {
                    type: type as 'create' | 'modify' | 'create_directory',
                    path: this.sanitizePath(path.trim()),
                    content: operationContent ? operationContent.trim() : '',
                    language: language || this.inferLanguageFromPath(path)
                };

                // Additional validation
                const validation = this.validateFileOperation(operation);
                if (!validation.valid) {
                    console.warn(`Invalid file operation: ${validation.error}`);
                    continue;
                }

                fileOperations.push(operation);

                // Remove the file operation from text content
                textContent = textContent.replace(fullMatch, '');
            }

            // Parse execution plans
            executionPlan = this.parseExecutionPlan(content);
            if (executionPlan) {
                // Remove execution plan from text content
                textContent = textContent.replace(this.EXECUTION_PLAN_REGEX, '');
            }

            // Parse project scaffold requests
            projectScaffoldRequest = this.parseProjectScaffoldRequest(content);
            if (projectScaffoldRequest) {
                // Remove project scaffold tags from text content
                textContent = textContent.replace(/<project_scaffold[\s\S]*?<\/project_scaffold>/gi, '');
            }

            // Clean up the text content
            textContent = this.cleanTextContent(textContent);

            // If no file operations found, try alternative parsing methods
            if (fileOperations.length === 0) {
                const alternativeOperations = this.parseAlternativeFormats(content);
                fileOperations.push(...alternativeOperations);

                // Update text content if alternative operations were found
                if (alternativeOperations.length > 0) {
                    textContent = this.removeAlternativeOperations(content, alternativeOperations);
                }
            }

            return {
                textContent,
                fileOperations,
                hasFileOperations: fileOperations.length > 0,
                executionPlan,
                hasExecutionPlan: !!executionPlan,
                projectScaffoldRequest,
                hasProjectScaffold: !!projectScaffoldRequest
            };
        } catch (error) {
            console.error('Error parsing LLM response:', error);
            return {
                textContent: content,
                fileOperations: [],
                hasFileOperations: false,
                executionPlan: undefined,
                hasExecutionPlan: false,
                projectScaffoldRequest: undefined,
                hasProjectScaffold: false
            };
        }
    }

    /**
     * Parse execution plan from LLM response
     */
    private static parseExecutionPlan(content: string): ExecutionPlan | undefined {
        this.EXECUTION_PLAN_REGEX.lastIndex = 0;
        const planMatch = this.EXECUTION_PLAN_REGEX.exec(content);

        if (!planMatch) {
            return undefined;
        }

        const [, id, description, duration, stepsContent] = planMatch;
        const steps: ExecutionStep[] = [];

        // Parse steps within the execution plan
        this.EXECUTION_STEP_REGEX.lastIndex = 0;
        let stepMatch;
        while ((stepMatch = this.EXECUTION_STEP_REGEX.exec(stepsContent)) !== null) {
            const [, stepId, type, order, path, command, stepDescription] = stepMatch;

            const step: ExecutionStep = {
                id: stepId,
                type: type as ExecutionStep['type'],
                description: stepDescription.trim(),
                path: path || undefined,
                command: command || undefined,
                order: parseInt(order, 10)
            };

            // For file operations, we need to generate content when executing
            // The content will be generated by the LLM when the step is executed
            if (['file_create', 'file_modify'].includes(type) && path) {
                // Add a placeholder content that will be replaced during execution
                step.content = `// Content will be generated for ${path}`;
            }

            steps.push(step);
        }

        return {
            id,
            description,
            steps: steps.sort((a, b) => a.order - b.order),
            estimatedDuration: duration ? parseInt(duration, 10) : undefined
        };
    }

    /**
     * Convert parsed response to webview message format
     */
    public static createWebviewMessage(parsedResponse: ParsedResponse): any {
        if (parsedResponse.hasExecutionPlan) {
            // Response with execution plan - new interactive execution mode
            return {
                type: 'response_with_plan',
                content: parsedResponse.textContent,
                executionPlan: parsedResponse.executionPlan
            };
        } else if (parsedResponse.hasFileOperations) {
            // Legacy response with file operations (for backward compatibility)
            return {
                type: 'response_with_operations',
                content: parsedResponse.textContent,
                fileOperations: parsedResponse.fileOperations.map((op, index) => ({
                    id: `op_${Date.now()}_${index}`,
                    operation: op,
                    status: 'pending'
                }))
            };
        } else {
            // Standard text response
            return {
                type: 'response',
                content: parsedResponse.textContent
            };
        }
    }

    /**
     * Create file operation cards for the UI
     */
    public static createFileOperationCards(fileOperations: FileOperation[]): FileOperationCard[] {
        return fileOperations.map((operation, index) => ({
            id: `op_${Date.now()}_${index}`,
            operation,
            status: 'pending'
        }));
    }

    /**
     * Infer programming language from file path
     */
    private static inferLanguageFromPath(path: string): string {
        const extension = path.split('.').pop()?.toLowerCase();
        
        const languageMap: { [key: string]: string } = {
            'js': 'javascript',
            'jsx': 'jsx',
            'ts': 'typescript',
            'tsx': 'tsx',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'h': 'c',
            'cs': 'csharp',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go',
            'rs': 'rust',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'sass': 'sass',
            'json': 'json',
            'yaml': 'yaml',
            'yml': 'yaml',
            'xml': 'xml',
            'md': 'markdown',
            'sh': 'bash',
            'bat': 'batch',
            'ps1': 'powershell'
        };

        return languageMap[extension || ''] || 'text';
    }

    /**
     * Clean up text content by removing extra whitespace and empty lines
     */
    private static cleanTextContent(content: string): string {
        return content
            .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove multiple empty lines
            .replace(/^\s+|\s+$/g, '') // Trim start and end
            .replace(/\n\s*$/g, ''); // Remove trailing whitespace on lines
    }

    /**
     * Validate file operation before execution
     */
    public static validateFileOperation(operation: FileOperation): { valid: boolean; error?: string } {
        // Check path
        if (!operation.path || operation.path.trim() === '') {
            return { valid: false, error: 'File path cannot be empty' };
        }

        // Check for dangerous paths
        if (operation.path.includes('..') || operation.path.startsWith('/')) {
            return { valid: false, error: 'Invalid file path: cannot use absolute paths or parent directory references' };
        }

        // Check content for non-directory operations
        if (operation.type !== 'create_directory' && !operation.content) {
            return { valid: false, error: 'File content cannot be empty for create/modify operations' };
        }

        // Check for valid file extensions for code files
        const extension = operation.path.split('.').pop()?.toLowerCase();
        const codeExtensions = ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go', 'rs', 'html', 'css', 'scss', 'json', 'yaml', 'yml', 'xml', 'md'];
        
        if (operation.type === 'create' && extension && !codeExtensions.includes(extension)) {
            console.warn(`Unusual file extension: ${extension}`);
        }

        return { valid: true };
    }

    /**
     * Process LLM response with auto-approval evaluation
     */
    public static async processResponseWithAutoApproval(
        content: string,
        autoApprovalManager: AutoApprovalManager
    ): Promise<ProcessedResponse> {
        // First parse the response normally
        const parsedResponse = this.parseResponse(content);

        if (!parsedResponse.hasFileOperations) {
            return {
                textContent: parsedResponse.textContent,
                fileOperations: [],
                autoExecutedOperations: [],
                pendingOperations: [],
                hasFileOperations: false
            };
        }

        // Evaluate each operation for auto-approval
        const processedOperations: ProcessedFileOperation[] = [];
        const autoExecutedOperations: ProcessedFileOperation[] = [];
        const pendingOperations: ProcessedFileOperation[] = [];

        for (let i = 0; i < parsedResponse.fileOperations.length; i++) {
            const operation = parsedResponse.fileOperations[i];
            const approvalResult = await autoApprovalManager.evaluateFileOperation(operation);

            const processedOp: ProcessedFileOperation = {
                ...operation,
                id: `op_${Date.now()}_${i}`,
                approvalStatus: approvalResult.action === 'auto_approve' ? 'auto_approved' :
                              approvalResult.action === 'deny' ? 'denied' : 'requires_approval',
                approvalRule: approvalResult.rule,
                approvalReason: approvalResult.reason,
                executionStatus: 'pending',
                timestamp: Date.now()
            };

            processedOperations.push(processedOp);

            if (processedOp.approvalStatus === 'auto_approved') {
                autoExecutedOperations.push(processedOp);
            } else if (processedOp.approvalStatus === 'requires_approval') {
                pendingOperations.push(processedOp);
            }
            // Denied operations are not added to either list
        }

        return {
            textContent: parsedResponse.textContent,
            fileOperations: processedOperations,
            autoExecutedOperations,
            pendingOperations,
            hasFileOperations: processedOperations.length > 0
        };
    }

    /**
     * Generate preview text for file operation
     */
    public static generateOperationPreview(operation: FileOperation): string {
        switch (operation.type) {
            case 'create':
                return `Create new file: ${operation.path}`;
            case 'modify':
                return `Modify existing file: ${operation.path}`;
            case 'create_directory':
                return `Create directory: ${operation.path}`;
            default:
                return `Unknown operation on: ${operation.path}`;
        }
    }

    /**
     * Sanitize file path to prevent security issues
     */
    private static sanitizePath(path: string): string {
        // Remove any leading/trailing whitespace
        path = path.trim();

        // Remove any path traversal attempts
        path = path.replace(/\.\./g, '');

        // Normalize path separators
        path = path.replace(/\\/g, '/');

        // Remove leading slash to ensure relative paths
        path = path.replace(/^\/+/, '');

        return path;
    }

    /**
     * Parse alternative file operation formats
     */
    private static parseAlternativeFormats(content: string): FileOperation[] {
        const operations: FileOperation[] = [];

        // Try to parse markdown code blocks with file paths
        const codeBlockRegex = /```(\w+)?\s*(?:\/\/\s*(.+\.[\w]+))?\n([\s\S]*?)```/g;
        let match;

        while ((match = codeBlockRegex.exec(content)) !== null) {
            const [, language, filePath, code] = match;

            if (filePath && code.trim()) {
                operations.push({
                    type: 'create',
                    path: this.sanitizePath(filePath),
                    content: code.trim(),
                    language: language || this.inferLanguageFromPath(filePath)
                });
            }
        }

        // ENHANCED: Parse standalone code blocks and auto-generate file names
        const standaloneCodeBlockRegex = /```(\w+)\n([\s\S]*?)```/g;
        const existingPaths = new Set(operations.map(op => op.path));
        let codeBlockIndex = 1;

        while ((match = standaloneCodeBlockRegex.exec(content)) !== null) {
            const [fullMatch, language, code] = match;

            // Skip if this code block was already processed with a file path
            if (operations.some(op => fullMatch.includes(op.content))) {
                continue;
            }

            // Skip very short code blocks (likely examples)
            if (code.trim().length < 50) {
                continue;
            }

            // Generate appropriate file name based on language and content
            const fileName = this.generateFileName(language, code, codeBlockIndex, existingPaths);

            if (fileName) {
                operations.push({
                    type: 'create',
                    path: fileName,
                    content: code.trim(),
                    language: language || this.inferLanguageFromPath(fileName)
                });
                existingPaths.add(fileName);
                codeBlockIndex++;
            }
        }

        // Try to parse file creation patterns in text
        const filePatternRegex = /(?:create|make|write)\s+(?:a\s+)?(?:new\s+)?file\s+(?:called\s+|named\s+)?["`']?([^"`'\s]+\.[a-zA-Z0-9]+)["`']?/gi;

        while ((match = filePatternRegex.exec(content)) !== null) {
            const filePath = match[1];
            if (filePath) {
                operations.push({
                    type: 'create',
                    path: this.sanitizePath(filePath),
                    content: '// TODO: Add implementation',
                    language: this.inferLanguageFromPath(filePath)
                });
            }
        }

        return operations;
    }

    /**
     * Detect code blocks that should be converted to file operations (Augment-style)
     * This ensures ALL code ends up in files, never in chat
     */
    private static detectUntaggedCodeBlocks(content: string): FileOperation[] {
        const operations: FileOperation[] = [];

        // Regex to find code blocks that are NOT inside file_operation tags
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const fileOperationRegex = /<file_operation[^>]*>[\s\S]*?<\/file_operation>/g;

        // Get all file_operation tag ranges to exclude them
        const fileOpRanges: Array<{start: number, end: number}> = [];
        let fileOpMatch;
        while ((fileOpMatch = fileOperationRegex.exec(content)) !== null) {
            fileOpRanges.push({
                start: fileOpMatch.index,
                end: fileOpMatch.index + fileOpMatch[0].length
            });
        }

        // Find code blocks that are NOT within file_operation tags
        let codeMatch;
        let blockIndex = 0;
        while ((codeMatch = codeBlockRegex.exec(content)) !== null) {
            const blockStart = codeMatch.index;
            const blockEnd = codeMatch.index + codeMatch[0].length;

            // Check if this code block is inside a file_operation tag
            const isInsideFileOp = fileOpRanges.some(range =>
                blockStart >= range.start && blockEnd <= range.end
            );

            if (!isInsideFileOp) {
                const language = codeMatch[1] || 'text';
                const code = codeMatch[2].trim();

                // Skip very short code blocks (likely examples)
                if (code.length < 30) continue;

                // Skip non-code languages
                if (['text', 'markdown', 'md', 'plain'].includes(language.toLowerCase())) continue;

                // Generate appropriate file name and path
                const fileName = this.generateIntelligentFileName(language, code, blockIndex);

                if (fileName) {
                    operations.push({
                        type: 'create',
                        path: fileName,
                        content: code,
                        language: language
                    });
                    blockIndex++;
                }
            }
        }

        return operations;
    }

    /**
     * Generate intelligent file names based on code content and context (Language-Agnostic)
     */
    private static generateIntelligentFileName(language: string, code: string, index: number): string | null {
        const lowerCode = code.toLowerCase();

        // Analyze code content to determine appropriate file name
        let baseName = '';
        let extension = this.getExtensionForLanguage(language);
        let directory = '';

        // Language-specific pattern detection
        baseName = this.extractBaseNameFromCode(language, code, lowerCode) || `generated_${index + 1}`;
        directory = this.determineDirectoryFromCode(language, code, baseName);

        return `${directory}${baseName}${extension}`;
    }

    /**
     * Extract base name from code based on language patterns
     */
    private static extractBaseNameFromCode(language: string, code: string, lowerCode: string): string | null {
        // JavaScript/TypeScript patterns
        if (['javascript', 'js', 'jsx', 'typescript', 'ts', 'tsx'].includes(language)) {
            const componentMatch = code.match(/(?:const|function|class|export\s+(?:default\s+)?(?:class|function))\s+(\w+)/);
            if (componentMatch) return componentMatch[1];

            const hookMatch = code.match(/^use[A-Z]\w*/m);
            if (hookMatch) return hookMatch[0];
        }

        // Python patterns
        if (['python', 'py'].includes(language)) {
            const classMatch = code.match(/class\s+(\w+)/);
            if (classMatch) return classMatch[1].toLowerCase();

            const functionMatch = code.match(/def\s+(\w+)/);
            if (functionMatch) return functionMatch[1];
        }

        // Java patterns
        if (language === 'java') {
            const classMatch = code.match(/(?:public\s+)?class\s+(\w+)/);
            if (classMatch) return classMatch[1];

            const interfaceMatch = code.match(/(?:public\s+)?interface\s+(\w+)/);
            if (interfaceMatch) return interfaceMatch[1];
        }

        // C++ patterns
        if (['cpp', 'c++', 'cc', 'cxx'].includes(language)) {
            const classMatch = code.match(/class\s+(\w+)/);
            if (classMatch) return classMatch[1];

            const functionMatch = code.match(/(?:int|void|double|float|char|bool)\s+(\w+)\s*\(/);
            if (functionMatch) return functionMatch[1];
        }

        // C# patterns
        if (['csharp', 'cs'].includes(language)) {
            const classMatch = code.match(/(?:public\s+)?class\s+(\w+)/);
            if (classMatch) return classMatch[1];

            const interfaceMatch = code.match(/(?:public\s+)?interface\s+I(\w+)/);
            if (interfaceMatch) return `I${interfaceMatch[1]}`;
        }

        // Go patterns
        if (language === 'go') {
            const structMatch = code.match(/type\s+(\w+)\s+struct/);
            if (structMatch) return structMatch[1].toLowerCase();

            const funcMatch = code.match(/func\s+(\w+)\s*\(/);
            if (funcMatch) return funcMatch[1].toLowerCase();
        }

        // Rust patterns
        if (['rust', 'rs'].includes(language)) {
            const structMatch = code.match(/struct\s+(\w+)/);
            if (structMatch) return structMatch[1].toLowerCase();

            const fnMatch = code.match(/fn\s+(\w+)\s*\(/);
            if (fnMatch) return fnMatch[1];
        }

        // PHP patterns
        if (language === 'php') {
            const classMatch = code.match(/class\s+(\w+)/);
            if (classMatch) return classMatch[1];

            const functionMatch = code.match(/function\s+(\w+)\s*\(/);
            if (functionMatch) return functionMatch[1];
        }

        // Ruby patterns
        if (language === 'ruby') {
            const classMatch = code.match(/class\s+(\w+)/);
            if (classMatch) return classMatch[1].toLowerCase();

            const defMatch = code.match(/def\s+(\w+)/);
            if (defMatch) return defMatch[1];
        }

        // Swift patterns
        if (language === 'swift') {
            const classMatch = code.match(/(?:class|struct|enum)\s+(\w+)/);
            if (classMatch) return classMatch[1];
        }

        // Kotlin patterns
        if (language === 'kotlin') {
            const classMatch = code.match(/(?:class|object|interface)\s+(\w+)/);
            if (classMatch) return classMatch[1];
        }

        return null;
    }

    /**
     * Determine appropriate directory based on language and code content
     */
    private static determineDirectoryFromCode(language: string, code: string, baseName: string): string {
        const lowerCode = code.toLowerCase();

        // Test files
        if (baseName.includes('test') || baseName.includes('spec') || lowerCode.includes('test') || lowerCode.includes('spec')) {
            if (language === 'java') return 'src/test/java/';
            if (language === 'python') return 'tests/';
            if (['javascript', 'typescript'].includes(language)) return 'src/__tests__/';
            return 'tests/';
        }

        // Language-specific directory structures
        switch (language) {
            case 'java':
                if (lowerCode.includes('controller')) return 'src/main/java/com/example/controller/';
                if (lowerCode.includes('service')) return 'src/main/java/com/example/service/';
                if (lowerCode.includes('model') || lowerCode.includes('entity')) return 'src/main/java/com/example/model/';
                if (lowerCode.includes('repository')) return 'src/main/java/com/example/repository/';
                return 'src/main/java/';

            case 'python':
                if (lowerCode.includes('model')) return 'models/';
                if (lowerCode.includes('view')) return 'views/';
                if (lowerCode.includes('controller')) return 'controllers/';
                return 'src/';

            case 'csharp':
            case 'cs':
                if (lowerCode.includes('controller')) return 'Controllers/';
                if (lowerCode.includes('model')) return 'Models/';
                if (lowerCode.includes('service')) return 'Services/';
                return 'src/';

            case 'go':
                if (lowerCode.includes('main')) return 'cmd/';
                if (lowerCode.includes('handler')) return 'internal/handlers/';
                if (lowerCode.includes('service')) return 'internal/services/';
                return 'pkg/';

            case 'rust':
                if (baseName === 'main') return 'src/bin/';
                if (lowerCode.includes('lib')) return 'src/';
                return 'src/';

            case 'php':
                if (lowerCode.includes('controller')) return 'app/Controllers/';
                if (lowerCode.includes('model')) return 'app/Models/';
                if (lowerCode.includes('service')) return 'app/Services/';
                return 'src/';

            case 'ruby':
                if (lowerCode.includes('controller')) return 'app/controllers/';
                if (lowerCode.includes('model')) return 'app/models/';
                if (lowerCode.includes('service')) return 'app/services/';
                return 'lib/';

            case 'jsx':
            case 'tsx':
                if (baseName.startsWith('use')) return 'src/hooks/';
                return 'src/components/';

            case 'css':
            case 'scss':
            case 'sass':
                return 'src/styles/';

            default:
                return 'src/';
        }
    }

    /**
     * Get file extension for programming language
     */
    private static getExtensionForLanguage(language: string): string {
        const extensionMap: Record<string, string> = {
            'javascript': '.js',
            'js': '.js',
            'jsx': '.jsx',
            'typescript': '.ts',
            'ts': '.ts',
            'tsx': '.tsx',
            'python': '.py',
            'py': '.py',
            'java': '.java',
            'csharp': '.cs',
            'cs': '.cs',
            'css': '.css',
            'scss': '.scss',
            'sass': '.sass',
            'html': '.html',
            'xml': '.xml',
            'json': '.json',
            'yaml': '.yaml',
            'yml': '.yml',
            'markdown': '.md',
            'md': '.md',
            'sql': '.sql',
            'php': '.php',
            'ruby': '.rb',
            'go': '.go',
            'rust': '.rs',
            'cpp': '.cpp',
            'c': '.c',
            'shell': '.sh',
            'bash': '.sh',
            'powershell': '.ps1'
        };

        return extensionMap[language.toLowerCase()] || '.txt';
    }

    /**
     * Generate appropriate file name for standalone code blocks
     */
    private static generateFileName(language: string, code: string, index: number, existingPaths: Set<string>): string | null {
        // Skip if no language specified
        if (!language) {
            return null;
        }

        // Language to extension mapping
        const extensionMap: { [key: string]: string } = {
            'javascript': 'js',
            'typescript': 'ts',
            'jsx': 'jsx',
            'tsx': 'tsx',
            'python': 'py',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'csharp': 'cs',
            'php': 'php',
            'ruby': 'rb',
            'go': 'go',
            'rust': 'rs',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'json': 'json',
            'yaml': 'yaml',
            'yml': 'yaml',
            'xml': 'xml',
            'markdown': 'md',
            'bash': 'sh',
            'shell': 'sh',
            'powershell': 'ps1'
        };

        const extension = extensionMap[language.toLowerCase()] || language.toLowerCase();

        // Try to extract meaningful name from code content
        let baseName = this.extractMeaningfulName(code, language);

        // Fallback to generic name
        if (!baseName) {
            baseName = `generated_${language}_${index}`;
        }

        // Ensure unique filename
        let fileName = `${baseName}.${extension}`;
        let counter = 1;
        while (existingPaths.has(fileName)) {
            fileName = `${baseName}_${counter}.${extension}`;
            counter++;
        }

        return fileName;
    }

    /**
     * Extract meaningful name from code content
     */
    private static extractMeaningfulName(code: string, language: string): string | null {
        const lines = code.split('\n');

        // Try to find class names, function names, or component names
        for (const line of lines) {
            const trimmed = line.trim();

            // JavaScript/TypeScript class or function
            if (['javascript', 'typescript', 'jsx', 'tsx'].includes(language.toLowerCase())) {
                const classMatch = trimmed.match(/^(?:export\s+)?(?:default\s+)?class\s+(\w+)/);
                if (classMatch) return classMatch[1];

                const functionMatch = trimmed.match(/^(?:export\s+)?(?:default\s+)?function\s+(\w+)/);
                if (functionMatch) return functionMatch[1];

                const componentMatch = trimmed.match(/^(?:export\s+)?(?:default\s+)?const\s+(\w+)\s*=.*=>/);
                if (componentMatch) return componentMatch[1];
            }

            // Python class or function
            if (language.toLowerCase() === 'python') {
                const classMatch = trimmed.match(/^class\s+(\w+)/);
                if (classMatch) return classMatch[1];

                const functionMatch = trimmed.match(/^def\s+(\w+)/);
                if (functionMatch) return functionMatch[1];
            }

            // Java class
            if (language.toLowerCase() === 'java') {
                const classMatch = trimmed.match(/^(?:public\s+)?class\s+(\w+)/);
                if (classMatch) return classMatch[1];
            }

            // HTML title or main element
            if (language.toLowerCase() === 'html') {
                const titleMatch = trimmed.match(/<title>(.*?)<\/title>/i);
                if (titleMatch) return titleMatch[1].replace(/\s+/g, '_').toLowerCase();
            }
        }

        return null;
    }

    /**
     * Remove alternative operations from content
     */
    private static removeAlternativeOperations(content: string, operations: FileOperation[]): string {
        let cleanContent = content;

        // Remove code blocks that were converted to operations
        const codeBlockRegex = /```(\w+)?\s*(?:\/\/\s*(.+\.[\w]+))?\n([\s\S]*?)```/g;
        cleanContent = cleanContent.replace(codeBlockRegex, (match, language, filePath, code) => {
            // Check if this code block was converted to a file operation
            const matchingOp = operations.find(op =>
                (filePath && op.path === this.sanitizePath(filePath)) ||
                (op.content === code?.trim())
            );

            if (matchingOp) {
                return `✅ **File created: ${matchingOp.path}**\n\n*Code has been written to the file above.*`;
            }
            return match;
        });

        // Also handle standalone code blocks
        const standaloneCodeBlockRegex = /```(\w+)\n([\s\S]*?)```/g;
        cleanContent = cleanContent.replace(standaloneCodeBlockRegex, (match, language, code) => {
            const matchingOp = operations.find(op => op.content === code.trim());
            if (matchingOp) {
                return `✅ **File created: ${matchingOp.path}**\n\n*Code has been written to the file above.*`;
            }
            return match;
        });

        return this.cleanTextContent(cleanContent);
    }

    /**
     * Parse project scaffold request from LLM response
     */
    private static parseProjectScaffoldRequest(content: string): ProjectScaffoldRequest | undefined {
        // Look for project scaffold tags
        const projectScaffoldRegex = /<project_scaffold\s+name="([^"]+)"\s+type="([^"]+)"(?:\s+features="([^"]*)")?[\s\S]*?<\/project_scaffold>/gi;
        const match = projectScaffoldRegex.exec(content);

        if (!match) {
            // Try to detect project creation intent from natural language
            return this.detectProjectCreationIntent(content);
        }

        const [, projectName, projectType, features] = match;

        // Map string to TechnologyStack enum
        const stack = this.mapStringToTechnologyStack(projectType);
        if (!stack) {
            console.warn(`Unknown project type: ${projectType}`);
            return undefined;
        }

        return {
            projectName: projectName.trim(),
            projectType: stack,
            features: features ? features.split(',').map(f => f.trim()) : [],
            customizations: {}
        };
    }

    /**
     * Detect project creation intent from natural language
     */
    private static detectProjectCreationIntent(content: string): ProjectScaffoldRequest | undefined {
        const lowerContent = content.toLowerCase();

        // Look for project creation keywords
        const projectKeywords = [
            'create a project', 'new project', 'build a project', 'start a project',
            'generate a project', 'scaffold a project', 'initialize project',
            'create an app', 'new app', 'build an app', 'start an app'
        ];

        const hasProjectIntent = projectKeywords.some(keyword => lowerContent.includes(keyword));
        if (!hasProjectIntent) {
            return undefined;
        }

        // Extract project name
        const projectNameMatch = content.match(/(?:project|app)\s+(?:called|named)\s+"([^"]+)"/i) ||
                                content.match(/(?:project|app)\s+(?:called|named)\s+([a-zA-Z][a-zA-Z0-9-_]*)/i) ||
                                content.match(/"([^"]+)"\s+(?:project|app)/i);

        const projectName = projectNameMatch ? projectNameMatch[1] : 'my-project';

        // Detect technology stack from content
        const detectedStack = this.detectTechnologyStackFromContent(lowerContent);

        return {
            projectName: projectName.trim(),
            projectType: detectedStack,
            features: this.extractFeaturesFromContent(lowerContent),
            customizations: {}
        };
    }

    /**
     * Map string to TechnologyStack enum
     */
    private static mapStringToTechnologyStack(typeString: string): TechnologyStack | null {
        const mapping: Record<string, TechnologyStack> = {
            'vanilla-web': TechnologyStack.VANILLA_WEB,
            'react-vite-ts': TechnologyStack.REACT_VITE_TS,
            'vue-vite-ts': TechnologyStack.VUE_VITE_TS,
            'svelte-vite-ts': TechnologyStack.SVELTE_VITE_TS,
            'angular-ts': TechnologyStack.ANGULAR_TS,
            'node-express-ts': TechnologyStack.NODE_EXPRESS_TS,
            'node-fastify-ts': TechnologyStack.NODE_FASTIFY_TS,
            'python-fastapi': TechnologyStack.PYTHON_FASTAPI,
            'python-flask': TechnologyStack.PYTHON_FLASK,
            'python-django': TechnologyStack.PYTHON_DJANGO,
            'react-native-expo': TechnologyStack.REACT_NATIVE_EXPO,
            'flutter': TechnologyStack.FLUTTER,
            'electron-ts': TechnologyStack.ELECTRON_TS,
            'tauri-rust': TechnologyStack.TAURI_RUST,
            'python-jupyter': TechnologyStack.PYTHON_JUPYTER,
            'python-data-science': TechnologyStack.PYTHON_DATA_SCIENCE,
            'phaser-ts': TechnologyStack.PHASER_TS,
            'unity-csharp': TechnologyStack.UNITY_CSHARP,
            'godot-gdscript': TechnologyStack.GODOT_GDSCRIPT,
            'rust-cli': TechnologyStack.RUST_CLI,
            'go-cli': TechnologyStack.GO_CLI,
            'python-cli': TechnologyStack.PYTHON_CLI
        };

        return mapping[typeString.toLowerCase()] || null;
    }

    /**
     * Detect technology stack from content
     */
    private static detectTechnologyStackFromContent(content: string): TechnologyStack {
        // React detection
        if (content.includes('react') || content.includes('jsx') || content.includes('tsx')) {
            return TechnologyStack.REACT_VITE_TS;
        }

        // Vue detection
        if (content.includes('vue') || content.includes('vuejs')) {
            return TechnologyStack.VUE_VITE_TS;
        }

        // Svelte detection
        if (content.includes('svelte')) {
            return TechnologyStack.SVELTE_VITE_TS;
        }

        // Angular detection
        if (content.includes('angular')) {
            return TechnologyStack.ANGULAR_TS;
        }

        // Backend detection
        if (content.includes('server') || content.includes('api') || content.includes('backend')) {
            if (content.includes('python') || content.includes('fastapi') || content.includes('flask')) {
                return TechnologyStack.PYTHON_FASTAPI;
            }
            return TechnologyStack.NODE_EXPRESS_TS;
        }

        // Mobile detection
        if (content.includes('mobile') || content.includes('react native') || content.includes('expo')) {
            return TechnologyStack.REACT_NATIVE_EXPO;
        }

        // Desktop detection
        if (content.includes('desktop') || content.includes('electron')) {
            return TechnologyStack.ELECTRON_TS;
        }

        // Data science detection
        if (content.includes('data') || content.includes('analysis') || content.includes('jupyter') || content.includes('notebook')) {
            return TechnologyStack.PYTHON_DATA_SCIENCE;
        }

        // Game detection
        if (content.includes('game') || content.includes('phaser')) {
            return TechnologyStack.PHASER_TS;
        }

        // Default to vanilla web
        return TechnologyStack.VANILLA_WEB;
    }

    /**
     * Extract features from content
     */
    private static extractFeaturesFromContent(content: string): string[] {
        const features: string[] = [];

        if (content.includes('typescript') || content.includes('ts')) {
            features.push('typescript');
        }

        if (content.includes('testing') || content.includes('test')) {
            features.push('testing');
        }

        if (content.includes('eslint') || content.includes('linting')) {
            features.push('linting');
        }

        if (content.includes('prettier') || content.includes('formatting')) {
            features.push('formatting');
        }

        if (content.includes('docker') || content.includes('containerization')) {
            features.push('docker');
        }

        if (content.includes('ci/cd') || content.includes('github actions')) {
            features.push('ci-cd');
        }

        return features;
    }

    /**
     * Detect inline code patterns that should be converted to file operations
     */
    private static detectInlineCodePatterns(content: string): FileOperation[] {
        const operations: FileOperation[] = [];

        // Pattern 1: "Here's the [filename]:" followed by code
        const fileIntroPattern = /(?:here'?s?\s+(?:the\s+)?|create\s+(?:a\s+)?|add\s+(?:this\s+)?|save\s+(?:this\s+)?(?:as\s+)?|write\s+(?:this\s+)?(?:to\s+)?)`?([^`\s]+\.[a-zA-Z0-9]+)`?:?\s*\n([\s\S]*?)(?=\n\n|\n(?:[A-Z]|$)|$)/gi;

        let match;
        while ((match = fileIntroPattern.exec(content)) !== null) {
            const fileName = match[1];
            const code = match[2].trim();

            // Skip if it's too short or looks like explanation text
            if (code.length < 20 || !this.looksLikeCode(code)) continue;

            operations.push({
                type: 'create',
                path: this.sanitizePath(fileName),
                content: code,
                language: this.inferLanguageFromPath(fileName)
            });
        }

        // Pattern 2: File paths mentioned with code context
        const filePathPattern = /(?:in|to|create|update|modify)\s+`([^`]+\.[a-zA-Z0-9]+)`[:\s]*\n([\s\S]*?)(?=\n\n|\n(?:[A-Z]|$)|$)/gi;

        while ((match = filePathPattern.exec(content)) !== null) {
            const fileName = match[1];
            const code = match[2].trim();

            if (code.length < 20 || !this.looksLikeCode(code)) continue;

            // Avoid duplicates
            if (!operations.some(op => op.path === fileName)) {
                operations.push({
                    type: 'create',
                    path: this.sanitizePath(fileName),
                    content: code,
                    language: this.inferLanguageFromPath(fileName)
                });
            }
        }

        return operations;
    }

    /**
     * Determine if text looks like code rather than explanation
     */
    private static looksLikeCode(text: string): boolean {
        const codeIndicators = [
            /[{}();]/,  // Common code punctuation
            /^\s*(?:function|class|const|let|var|def|import|export|from|if|for|while)/m,  // Keywords
            /^\s*[<>]/m,  // HTML/XML tags
            /^\s*[#.][a-zA-Z]/m,  // CSS selectors
            /^\s*\/\//m,  // Comments
            /^\s*\/\*/m,  // Block comments
            /^\s*#/m,  // Python/shell comments
            /[=:]\s*[{[\]]/,  // Object/array assignments
        ];

        return codeIndicators.some(pattern => pattern.test(text));
    }
}
