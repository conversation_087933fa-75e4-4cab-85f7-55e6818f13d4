"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.userAgentPolicyName = exports.userAgentPolicy = exports.tlsPolicyName = exports.tlsPolicy = exports.redirectPolicyName = exports.redirectPolicy = exports.getDefaultProxySettings = exports.proxyPolicyName = exports.proxyPolicy = exports.multipartPolicyName = exports.multipartPolicy = exports.logPolicyName = exports.logPolicy = exports.formDataPolicyName = exports.formDataPolicy = exports.throttlingRetryPolicyName = exports.throttlingRetryPolicy = exports.systemErrorRetryPolicyName = exports.systemErrorRetryPolicy = exports.retryPolicy = exports.exponentialRetryPolicyName = exports.exponentialRetryPolicy = exports.defaultRetryPolicyName = exports.defaultRetryPolicy = exports.decompressResponsePolicyName = exports.decompressResponsePolicy = exports.agentPolicyName = exports.agentPolicy = void 0;
var agentPolicy_js_1 = require("./agentPolicy.js");
Object.defineProperty(exports, "agentPolicy", { enumerable: true, get: function () { return agentPolicy_js_1.agentPolicy; } });
Object.defineProperty(exports, "agentPolicyName", { enumerable: true, get: function () { return agentPolicy_js_1.agentPolicyName; } });
var decompressResponsePolicy_js_1 = require("./decompressResponsePolicy.js");
Object.defineProperty(exports, "decompressResponsePolicy", { enumerable: true, get: function () { return decompressResponsePolicy_js_1.decompressResponsePolicy; } });
Object.defineProperty(exports, "decompressResponsePolicyName", { enumerable: true, get: function () { return decompressResponsePolicy_js_1.decompressResponsePolicyName; } });
var defaultRetryPolicy_js_1 = require("./defaultRetryPolicy.js");
Object.defineProperty(exports, "defaultRetryPolicy", { enumerable: true, get: function () { return defaultRetryPolicy_js_1.defaultRetryPolicy; } });
Object.defineProperty(exports, "defaultRetryPolicyName", { enumerable: true, get: function () { return defaultRetryPolicy_js_1.defaultRetryPolicyName; } });
var exponentialRetryPolicy_js_1 = require("./exponentialRetryPolicy.js");
Object.defineProperty(exports, "exponentialRetryPolicy", { enumerable: true, get: function () { return exponentialRetryPolicy_js_1.exponentialRetryPolicy; } });
Object.defineProperty(exports, "exponentialRetryPolicyName", { enumerable: true, get: function () { return exponentialRetryPolicy_js_1.exponentialRetryPolicyName; } });
var retryPolicy_js_1 = require("./retryPolicy.js");
Object.defineProperty(exports, "retryPolicy", { enumerable: true, get: function () { return retryPolicy_js_1.retryPolicy; } });
var systemErrorRetryPolicy_js_1 = require("./systemErrorRetryPolicy.js");
Object.defineProperty(exports, "systemErrorRetryPolicy", { enumerable: true, get: function () { return systemErrorRetryPolicy_js_1.systemErrorRetryPolicy; } });
Object.defineProperty(exports, "systemErrorRetryPolicyName", { enumerable: true, get: function () { return systemErrorRetryPolicy_js_1.systemErrorRetryPolicyName; } });
var throttlingRetryPolicy_js_1 = require("./throttlingRetryPolicy.js");
Object.defineProperty(exports, "throttlingRetryPolicy", { enumerable: true, get: function () { return throttlingRetryPolicy_js_1.throttlingRetryPolicy; } });
Object.defineProperty(exports, "throttlingRetryPolicyName", { enumerable: true, get: function () { return throttlingRetryPolicy_js_1.throttlingRetryPolicyName; } });
var formDataPolicy_js_1 = require("./formDataPolicy.js");
Object.defineProperty(exports, "formDataPolicy", { enumerable: true, get: function () { return formDataPolicy_js_1.formDataPolicy; } });
Object.defineProperty(exports, "formDataPolicyName", { enumerable: true, get: function () { return formDataPolicy_js_1.formDataPolicyName; } });
var logPolicy_js_1 = require("./logPolicy.js");
Object.defineProperty(exports, "logPolicy", { enumerable: true, get: function () { return logPolicy_js_1.logPolicy; } });
Object.defineProperty(exports, "logPolicyName", { enumerable: true, get: function () { return logPolicy_js_1.logPolicyName; } });
var multipartPolicy_js_1 = require("./multipartPolicy.js");
Object.defineProperty(exports, "multipartPolicy", { enumerable: true, get: function () { return multipartPolicy_js_1.multipartPolicy; } });
Object.defineProperty(exports, "multipartPolicyName", { enumerable: true, get: function () { return multipartPolicy_js_1.multipartPolicyName; } });
var proxyPolicy_js_1 = require("./proxyPolicy.js");
Object.defineProperty(exports, "proxyPolicy", { enumerable: true, get: function () { return proxyPolicy_js_1.proxyPolicy; } });
Object.defineProperty(exports, "proxyPolicyName", { enumerable: true, get: function () { return proxyPolicy_js_1.proxyPolicyName; } });
Object.defineProperty(exports, "getDefaultProxySettings", { enumerable: true, get: function () { return proxyPolicy_js_1.getDefaultProxySettings; } });
var redirectPolicy_js_1 = require("./redirectPolicy.js");
Object.defineProperty(exports, "redirectPolicy", { enumerable: true, get: function () { return redirectPolicy_js_1.redirectPolicy; } });
Object.defineProperty(exports, "redirectPolicyName", { enumerable: true, get: function () { return redirectPolicy_js_1.redirectPolicyName; } });
var tlsPolicy_js_1 = require("./tlsPolicy.js");
Object.defineProperty(exports, "tlsPolicy", { enumerable: true, get: function () { return tlsPolicy_js_1.tlsPolicy; } });
Object.defineProperty(exports, "tlsPolicyName", { enumerable: true, get: function () { return tlsPolicy_js_1.tlsPolicyName; } });
var userAgentPolicy_js_1 = require("./userAgentPolicy.js");
Object.defineProperty(exports, "userAgentPolicy", { enumerable: true, get: function () { return userAgentPolicy_js_1.userAgentPolicy; } });
Object.defineProperty(exports, "userAgentPolicyName", { enumerable: true, get: function () { return userAgentPolicy_js_1.userAgentPolicyName; } });
//# sourceMappingURL=internal.js.map