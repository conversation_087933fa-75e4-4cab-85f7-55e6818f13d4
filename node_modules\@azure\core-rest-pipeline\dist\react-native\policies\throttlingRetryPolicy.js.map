{"version": 3, "file": "throttlingRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/throttlingRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,yBAAyB,IAAI,4BAA4B,EACzD,qBAAqB,IAAI,wBAAwB,GAClD,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,4BAA4B,CAAC;AAYtE;;;;;;;;;GASG;AACH,MAAM,UAAU,qBAAqB,CAAC,UAAwC,EAAE;IAC9E,OAAO,wBAAwB,CAAC,OAAO,CAAC,CAAC;AAC3C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  throttlingRetryPolicyName as tspThrottlingRetryPolicyName,\n  throttlingRetryPolicy as tspThrottlingRetryPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the {@link throttlingRetryPolicy}\n */\nexport const throttlingRetryPolicyName = tspThrottlingRetryPolicyName;\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ThrottlingRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy that retries when the server sends a 429 response with a Retry-After header.\n *\n * To learn more, please refer to\n * https://learn.microsoft.com/azure/azure-resource-manager/resource-manager-request-limits,\n * https://learn.microsoft.com/azure/azure-subscription-service-limits and\n * https://learn.microsoft.com/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n *\n * @param options - Options that configure retry logic.\n */\nexport function throttlingRetryPolicy(options: ThrottlingRetryPolicyOptions = {}): PipelinePolicy {\n  return tspThrottlingRetryPolicy(options);\n}\n"]}