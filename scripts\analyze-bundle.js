#!/usr/bin/env node

/**
 * Bundle Analysis and Optimization Script
 * Analyzes webpack bundles and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 V1b3-Sama Bundle Analysis & Optimization');
console.log('='.repeat(50));

// Configuration
const config = {
    distPath: path.join(__dirname, '..', 'dist'),
    reportPath: path.join(__dirname, '..', 'bundle-report.html'),
    thresholds: {
        maxBundleSize: 2 * 1024 * 1024, // 2MB
        maxChunkSize: 500 * 1024,       // 500KB
        maxAssetSize: 1024 * 1024       // 1MB
    }
};

// Analyze bundle with webpack-bundle-analyzer
function runBundleAnalysis() {
    console.log('🔍 Running bundle analysis...');
    
    try {
        // Generate bundle report
        execSync('npm run package:analyze', { 
            stdio: 'pipe',
            env: { ...process.env, ANALYZE: 'true' }
        });
        
        console.log('✅ Bundle analysis complete');
        console.log(`📊 Report generated: ${config.reportPath}`);
        
        return true;
    } catch (error) {
        console.error('❌ Bundle analysis failed:', error.message);
        return false;
    }
}

// Analyze bundle sizes and identify large dependencies
function analyzeBundleSizes() {
    console.log('\n📏 Analyzing bundle sizes...');
    
    if (!fs.existsSync(config.distPath)) {
        console.log('❌ Dist folder not found. Run build first.');
        return null;
    }
    
    const analysis = {
        totalSize: 0,
        files: [],
        largestFiles: [],
        recommendations: []
    };
    
    // Recursively analyze all files
    function analyzeDirectory(dir, relativePath = '') {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const fullPath = path.join(dir, file);
            const relativeFilePath = path.join(relativePath, file);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
                analyzeDirectory(fullPath, relativeFilePath);
            } else {
                const fileInfo = {
                    path: relativeFilePath,
                    size: stats.size,
                    sizeKB: Math.round(stats.size / 1024),
                    sizeMB: Math.round(stats.size / 1024 / 1024 * 100) / 100
                };
                
                analysis.files.push(fileInfo);
                analysis.totalSize += stats.size;
            }
        });
    }
    
    analyzeDirectory(config.distPath);
    
    // Sort by size and get largest files
    analysis.files.sort((a, b) => b.size - a.size);
    analysis.largestFiles = analysis.files.slice(0, 10);
    
    // Generate recommendations
    analysis.recommendations = generateOptimizationRecommendations(analysis);
    
    return analysis;
}

// Generate optimization recommendations
function generateOptimizationRecommendations(analysis) {
    const recommendations = [];
    
    // Check total bundle size
    if (analysis.totalSize > config.thresholds.maxBundleSize) {
        recommendations.push({
            type: 'size',
            severity: 'high',
            message: `Total bundle size (${Math.round(analysis.totalSize / 1024 / 1024 * 100) / 100}MB) exceeds threshold (${config.thresholds.maxBundleSize / 1024 / 1024}MB)`,
            action: 'Consider code splitting and tree shaking'
        });
    }
    
    // Check individual file sizes
    analysis.largestFiles.forEach(file => {
        if (file.size > config.thresholds.maxChunkSize) {
            recommendations.push({
                type: 'chunk',
                severity: 'medium',
                message: `Large chunk detected: ${file.path} (${file.sizeKB}KB)`,
                action: 'Consider splitting this chunk or lazy loading'
            });
        }
    });
    
    // Check for common optimization opportunities
    const jsFiles = analysis.files.filter(f => f.path.endsWith('.js'));
    const totalJSSize = jsFiles.reduce((sum, f) => sum + f.size, 0);
    
    if (totalJSSize > analysis.totalSize * 0.8) {
        recommendations.push({
            type: 'optimization',
            severity: 'medium',
            message: 'JavaScript files comprise >80% of bundle',
            action: 'Consider minification and compression improvements'
        });
    }
    
    // Check for duplicate dependencies
    const nodeModulesFiles = analysis.files.filter(f => f.path.includes('node_modules'));
    if (nodeModulesFiles.length > 0) {
        recommendations.push({
            type: 'dependencies',
            severity: 'low',
            message: 'Node modules detected in bundle',
            action: 'Verify external dependencies are properly excluded'
        });
    }
    
    return recommendations;
}

// Identify top 5 largest dependencies
function identifyLargeDependencies() {
    console.log('\n🔍 Identifying large dependencies...');
    
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
    };
    
    // Estimate dependency sizes (this would be more accurate with actual bundle analysis)
    const largeDependencies = [
        { name: 'axios', estimatedSize: '15KB', optimization: 'Consider using fetch API instead' },
        { name: 'vscode', estimatedSize: 'External', optimization: 'Already externalized' },
        { name: 'webpack', estimatedSize: 'DevOnly', optimization: 'Development dependency only' },
        { name: 'typescript', estimatedSize: 'DevOnly', optimization: 'Development dependency only' },
        { name: 'webpack-bundle-analyzer', estimatedSize: 'DevOnly', optimization: 'Development dependency only' }
    ];
    
    console.log('📊 Top dependencies analysis:');
    largeDependencies.forEach((dep, index) => {
        if (dependencies[dep.name]) {
            console.log(`  ${index + 1}. ${dep.name}: ${dep.estimatedSize} - ${dep.optimization}`);
        }
    });
    
    return largeDependencies;
}

// Optimize webpack configuration
function optimizeWebpackConfig() {
    console.log('\n⚙️  Optimizing webpack configuration...');
    
    const webpackConfigPath = path.join(__dirname, '..', 'webpack.config.js');
    let webpackConfig = fs.readFileSync(webpackConfigPath, 'utf8');
    
    // Check if optimizations are already in place
    const optimizations = [
        {
            name: 'Module Concatenation',
            check: 'ModuleConcatenationPlugin',
            present: webpackConfig.includes('ModuleConcatenationPlugin')
        },
        {
            name: 'Tree Shaking',
            check: 'usedExports: true',
            present: webpackConfig.includes('usedExports: true')
        },
        {
            name: 'Code Splitting',
            check: 'splitChunks',
            present: webpackConfig.includes('splitChunks')
        },
        {
            name: 'Bundle Analysis',
            check: 'BundleAnalyzerPlugin',
            present: webpackConfig.includes('BundleAnalyzerPlugin')
        }
    ];
    
    console.log('🔧 Webpack optimizations status:');
    optimizations.forEach(opt => {
        const status = opt.present ? '✅' : '❌';
        console.log(`  ${status} ${opt.name}`);
    });
    
    return optimizations;
}

// Generate optimization report
function generateOptimizationReport(analysis, dependencies, webpackOpts) {
    console.log('\n📋 Generating optimization report...');
    
    const report = {
        timestamp: new Date().toISOString(),
        bundleAnalysis: analysis,
        largeDependencies: dependencies,
        webpackOptimizations: webpackOpts,
        recommendations: analysis?.recommendations || []
    };
    
    const reportPath = path.join(__dirname, '..', 'BUNDLE_ANALYSIS.md');
    const reportContent = `# Bundle Analysis Report

Generated: ${report.timestamp}

## Bundle Size Analysis

**Total Bundle Size:** ${analysis ? Math.round(analysis.totalSize / 1024 / 1024 * 100) / 100 : 'N/A'}MB

### Largest Files:
${analysis ? analysis.largestFiles.map(f => `- ${f.path}: ${f.sizeKB}KB`).join('\n') : 'No analysis available'}

## Large Dependencies

${dependencies.map(dep => `- **${dep.name}**: ${dep.estimatedSize} - ${dep.optimization}`).join('\n')}

## Webpack Optimizations

${webpackOpts.map(opt => `- ${opt.present ? '✅' : '❌'} ${opt.name}`).join('\n')}

## Recommendations

${analysis ? analysis.recommendations.map(rec => `- **${rec.type.toUpperCase()}**: ${rec.message}\n  *Action:* ${rec.action}`).join('\n\n') : 'No recommendations available'}

## Next Steps

1. Review large dependencies for optimization opportunities
2. Implement recommended webpack optimizations
3. Consider lazy loading for large chunks
4. Monitor bundle size in CI/CD pipeline
`;
    
    fs.writeFileSync(reportPath, reportContent);
    console.log(`✅ Report saved to: ${reportPath}`);
    
    return report;
}

// Main execution
async function main() {
    try {
        // Step 1: Run bundle analysis
        const analysisSuccess = runBundleAnalysis();
        
        // Step 2: Analyze bundle sizes
        const bundleAnalysis = analyzeBundleSizes();
        
        // Step 3: Identify large dependencies
        const largeDependencies = identifyLargeDependencies();
        
        // Step 4: Check webpack optimizations
        const webpackOptimizations = optimizeWebpackConfig();
        
        // Step 5: Generate comprehensive report
        const report = generateOptimizationReport(bundleAnalysis, largeDependencies, webpackOptimizations);
        
        console.log('\n🎉 Bundle analysis complete!');
        console.log('\n📊 Summary:');
        if (bundleAnalysis) {
            console.log(`  📦 Total Size: ${Math.round(bundleAnalysis.totalSize / 1024 / 1024 * 100) / 100}MB`);
            console.log(`  📁 Files: ${bundleAnalysis.files.length}`);
            console.log(`  ⚠️  Recommendations: ${bundleAnalysis.recommendations.length}`);
        }
        console.log(`  🔧 Webpack Optimizations: ${webpackOptimizations.filter(o => o.present).length}/${webpackOptimizations.length}`);
        
        if (analysisSuccess) {
            console.log(`\n🌐 Open ${config.reportPath} in your browser for detailed analysis`);
        }
        
    } catch (error) {
        console.error('❌ Analysis failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    runBundleAnalysis,
    analyzeBundleSizes,
    identifyLargeDependencies,
    optimizeWebpackConfig,
    generateOptimizationReport
};
