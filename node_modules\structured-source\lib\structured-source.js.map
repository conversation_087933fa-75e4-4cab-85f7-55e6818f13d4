{"version": 3, "file": "structured-source.js", "sourceRoot": "", "sources": ["../src/structured-source.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;;EAsBE;;;AAEF,uCAAsC;AAatC;;GAEG;AACH,MAAa,gBAAgB;IAGzB;;;OAGG;IACH,YAAY,MAAc;QACtB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,qBAAqB,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;QACrB,OAAO,IAAI,EAAE;YACT,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,CAAC,MAAM,EAAE;gBACT,MAAM;aACT;YACD,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACzB,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,CAAE,UAAU;gBAC7C,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE,UAAU,EAAE;gBACnD,KAAK,IAAI,CAAC,CAAC;aACd;YACD,IAAI,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;YAC1B,+DAA+D;YAC/D,0BAA0B;YAC1B,IAAI,MAAM,GAAG,SAAS,EAAE;gBACpB,MAAM;aACT;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5B,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;SAChC;IACL,CAAC;IAED,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,GAAmB;QAC/B,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,KAAkB;QAC9B,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACtC,CAAC;IACN,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,GAAmB;QAC/B,6BAA6B;QAC7B,+BAA+B;QAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACtC,OAAO,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,KAAa;QACzB,MAAM,SAAS,GAAG,IAAA,qBAAU,EAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO;YACH,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;SAC7C,CAAC;IACN,CAAC;CACJ;AA9ED,4CA8EC;AAED,mCAAmC"}