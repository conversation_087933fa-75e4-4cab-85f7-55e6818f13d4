{"version": 3, "file": "checkstyle.js", "sourceRoot": "", "sources": ["../../../src/formatters/checkstyle.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,YAAY,CAAC;;AAGb,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF;;;;;GAKG;AACH,SAAS,cAAc,CAAC,OAAY;IAChC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;QAC1C,OAAO,OAAO,CAAC;IACnB,CAAC;SAAM,CAAC;QACJ,OAAO,SAAS,CAAC;IACrB,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,SAAS,SAAS,CAAC,CAAM;IACrB,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;QAC3C,QAAQ,CAAC,EAAE,CAAC;YACR,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,OAAO,CAAC;YACnB,KAAK,GAAG;gBACJ,OAAO,QAAQ,CAAC;YACpB,KAAK,GAAG;gBACJ,OAAO,QAAQ,CAAC;YACpB;gBACI,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,gFAAgF;AAChF,mBAAmB;AACnB,gFAAgF;AAEhF,SAAS,SAAS,CAAC,OAAyB;IACxC,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,MAAM,IAAI,wCAAwC,CAAC;IACnD,MAAM,IAAI,4BAA4B,CAAC;IAEvC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;QAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEjC,MAAM,IAAI,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QAE7D,QAAQ,CAAC,OAAO,CAAC,UAAU,OAAO;YAC9B,MAAM;gBACF,eAAe;oBACf,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;oBACvB,IAAI;oBACJ,UAAU;oBACV,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;oBACzB,IAAI;oBACJ,YAAY;oBACZ,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAI;oBACJ,WAAW;oBACX,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC1B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC9D,IAAI;oBACJ,UAAU;oBACV,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACnE,MAAM,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,SAAS,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,MAAM,IAAI,eAAe,CAAC;IAE1B,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,kBAAe,SAAS,CAAC"}