{"version": 3, "file": "xhrHttpClient.js", "sourceRoot": "", "sources": ["../../src/xhrHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAQ9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AAExD;;;GAGG;AACH,MAAM,aAAa;IACjB;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,OAAwB;;QAC/C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAE7C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,CAAC,GAAG,0CAA0C,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QAEjC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,UAAU,CAAC,+DAA+D,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,QAAQ,GAAG,GAAS,EAAE;gBAC1B,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,CAAC;YACF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAChD,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC5C,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE,CAAC;oBAC3C,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1D,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAErD,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC9C,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5C,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,GAAG,CAAC,YAAY,GAAG,CAAA,MAAA,OAAO,CAAC,yBAAyB,0CAAE,IAAI,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7E,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAChF,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,kBAAkB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBAClD,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM;gBAC1C,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,CAChC,OAAO,CAAC;oBACN,OAAO;oBACP,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC1B,UAAU,EAAE,GAAG,CAAC,YAAY;iBAC7B,CAAC,CACH,CAAC;gBACF,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAED,SAAS,kBAAkB,CACzB,GAAmB,EACnB,OAAwB,EACxB,GAAsE,EACtE,GAA2B;IAE3B,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;;QAC5C,wCAAwC;QACxC,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,gBAAgB,EAAE,CAAC;YACvD;YACE,2FAA2F;YAC3F,CAAA,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC;iBAChE,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA,EAClD,CAAC;gBACD,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrD,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;wBAChC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC;oBACH,qBAAqB,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC;oBACF,OAAO;oBACP,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC1B,QAAQ;iBACT,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;oBAChC,uFAAuF;oBACvF,sEAAsE;oBACtE,oDAAoD;oBACpD,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;wBACjB,GAAG,CAAC,QAAQ;6BACT,IAAI,EAAE;6BACN,IAAI,CAAC,CAAC,IAAY,EAAE,EAAE;4BACrB,GAAG,CAAC;gCACF,OAAO,EAAE,OAAO;gCAChB,MAAM,EAAE,GAAG,CAAC,MAAM;gCAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;gCAC1B,UAAU,EAAE,IAAI;6BACjB,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC,CAAC;6BACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;4BAChB,GAAG,CAAC,CAAC,CAAC,CAAC;wBACT,CAAC,CAAC,CAAC;oBACP,CAAC;yBAAM,CAAC;wBACN,GAAG,CAAC;4BACF,OAAO;4BACP,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC;yBAC3B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,GAA8B,EAC9B,QAAoD;IAEpD,IAAI,QAAQ,EAAE,CAAC;QACb,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE,CAC5C,QAAQ,CAAC;YACP,WAAW,EAAE,QAAQ,CAAC,MAAM;SAC7B,CAAC,CACH,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,GAAmB;IACvC,MAAM,eAAe,GAAG,iBAAiB,EAAE,CAAC;IAC5C,MAAM,WAAW,GAAG,GAAG;SACpB,qBAAqB,EAAE;SACvB,IAAI,EAAE;SACN,KAAK,CAAC,SAAS,CAAC,CAAC;IACpB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC1C,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,qBAAqB,CAC5B,OAAwB,EACxB,GAAmB,EACnB,MAA0B;IAE1B,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CACjC,MAAM,CACJ,IAAI,SAAS,CAAC,6BAA6B,OAAO,CAAC,GAAG,EAAE,EAAE;QACxD,IAAI,EAAE,SAAS,CAAC,kBAAkB;QAClC,OAAO;KACR,CAAC,CACH,CACF,CAAC;IACF,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;IAChE,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IACxD,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,mBAAmB;IACjC,OAAO,IAAI,aAAa,EAAE,CAAC;AAC7B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"./abort-controller/AbortError.js\";\nimport type {\n  HttpClient,\n  HttpHeaders,\n  PipelineRequest,\n  PipelineResponse,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { RestError } from \"./restError.js\";\nimport { isReadableStream } from \"./util/typeGuards.js\";\n\n/**\n * A HttpClient implementation that uses XMLHttpRequest to send HTTP requests.\n * @internal\n */\nclass XhrHttpClient implements HttpClient {\n  /**\n   * Makes a request over an underlying transport layer and returns the response.\n   * @param request - The request to be made.\n   */\n  public async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    const url = new URL(request.url);\n    const isInsecure = url.protocol !== \"https:\";\n\n    if (isInsecure && !request.allowInsecureConnection) {\n      throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n    }\n\n    const xhr = new XMLHttpRequest();\n\n    if (request.proxySettings) {\n      throw new Error(\"HTTP proxy is not supported in browser environment\");\n    }\n\n    const abortSignal = request.abortSignal;\n    if (abortSignal) {\n      if (abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted. Request has already been canceled.\");\n      }\n\n      const listener = (): void => {\n        xhr.abort();\n      };\n      abortSignal.addEventListener(\"abort\", listener);\n      xhr.addEventListener(\"readystatechange\", () => {\n        if (xhr.readyState === XMLHttpRequest.DONE) {\n          abortSignal.removeEventListener(\"abort\", listener);\n        }\n      });\n    }\n\n    addProgressListener(xhr.upload, request.onUploadProgress);\n    addProgressListener(xhr, request.onDownloadProgress);\n\n    xhr.open(request.method, request.url);\n    xhr.timeout = request.timeout;\n    xhr.withCredentials = request.withCredentials;\n    for (const [name, value] of request.headers) {\n      xhr.setRequestHeader(name, value);\n    }\n\n    xhr.responseType = request.streamResponseStatusCodes?.size ? \"blob\" : \"text\";\n\n    const body = typeof request.body === \"function\" ? request.body() : request.body;\n    if (isReadableStream(body)) {\n      throw new Error(\"streams are not supported in XhrHttpClient.\");\n    }\n\n    xhr.send(body === undefined ? null : body);\n\n    if (xhr.responseType === \"blob\") {\n      return new Promise((resolve, reject) => {\n        handleBlobResponse(xhr, request, resolve, reject);\n        rejectOnTerminalEvent(request, xhr, reject);\n      });\n    } else {\n      return new Promise(function (resolve, reject) {\n        xhr.addEventListener(\"load\", () =>\n          resolve({\n            request,\n            status: xhr.status,\n            headers: parseHeaders(xhr),\n            bodyAsText: xhr.responseText,\n          }),\n        );\n        rejectOnTerminalEvent(request, xhr, reject);\n      });\n    }\n  }\n}\n\nfunction handleBlobResponse(\n  xhr: XMLHttpRequest,\n  request: PipelineRequest,\n  res: (value: PipelineResponse | PromiseLike<PipelineResponse>) => void,\n  rej: (reason?: any) => void,\n): void {\n  xhr.addEventListener(\"readystatechange\", () => {\n    // Resolve as soon as headers are loaded\n    if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\n      if (\n        // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n        request.streamResponseStatusCodes?.has(Number.POSITIVE_INFINITY) ||\n        request.streamResponseStatusCodes?.has(xhr.status)\n      ) {\n        const blobBody = new Promise<Blob>((resolve, reject) => {\n          xhr.addEventListener(\"load\", () => {\n            resolve(xhr.response);\n          });\n          rejectOnTerminalEvent(request, xhr, reject);\n        });\n        res({\n          request,\n          status: xhr.status,\n          headers: parseHeaders(xhr),\n          blobBody,\n        });\n      } else {\n        xhr.addEventListener(\"load\", () => {\n          // xhr.response is of Blob type if the request is sent with xhr.responseType === \"blob\"\n          // but the status code is not one of the stream response status codes,\n          // so treat it as text and convert from Blob to text\n          if (xhr.response) {\n            xhr.response\n              .text()\n              .then((text: string) => {\n                res({\n                  request: request,\n                  status: xhr.status,\n                  headers: parseHeaders(xhr),\n                  bodyAsText: text,\n                });\n                return;\n              })\n              .catch((e: any) => {\n                rej(e);\n              });\n          } else {\n            res({\n              request,\n              status: xhr.status,\n              headers: parseHeaders(xhr),\n            });\n          }\n        });\n      }\n    }\n  });\n}\n\nfunction addProgressListener(\n  xhr: XMLHttpRequestEventTarget,\n  listener?: (progress: TransferProgressEvent) => void,\n): void {\n  if (listener) {\n    xhr.addEventListener(\"progress\", (rawEvent) =>\n      listener({\n        loadedBytes: rawEvent.loaded,\n      }),\n    );\n  }\n}\n\nfunction parseHeaders(xhr: XMLHttpRequest): HttpHeaders {\n  const responseHeaders = createHttpHeaders();\n  const headerLines = xhr\n    .getAllResponseHeaders()\n    .trim()\n    .split(/[\\r\\n]+/);\n  for (const line of headerLines) {\n    const index = line.indexOf(\":\");\n    const headerName = line.slice(0, index);\n    const headerValue = line.slice(index + 2);\n    responseHeaders.set(headerName, headerValue);\n  }\n  return responseHeaders;\n}\n\nfunction rejectOnTerminalEvent(\n  request: PipelineRequest,\n  xhr: XMLHttpRequest,\n  reject: (err: any) => void,\n): void {\n  xhr.addEventListener(\"error\", () =>\n    reject(\n      new RestError(`Failed to send request to ${request.url}`, {\n        code: RestError.REQUEST_SEND_ERROR,\n        request,\n      }),\n    ),\n  );\n  const abortError = new AbortError(\"The operation was aborted.\");\n  xhr.addEventListener(\"abort\", () => reject(abortError));\n  xhr.addEventListener(\"timeout\", () => reject(abortError));\n}\n\n/**\n * Create a new HttpClient instance for the browser environment.\n * @internal\n */\nexport function createXhrHttpClient(): HttpClient {\n  return new XhrHttpClient();\n}\n"]}