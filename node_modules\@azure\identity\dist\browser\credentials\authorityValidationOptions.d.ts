/**
 * Provides options to configure how the Identity library
 * does authority validation during authentication requests
 * to Microsoft Entra ID.
 */
export interface AuthorityValidationOptions {
    /**
     * The field determines whether instance discovery is performed when attempting to authenticate.
     * Setting this to `true` will completely disable both instance discovery and authority validation.
     * As a result, it's crucial to ensure that the configured authority host is valid and trustworthy.
     * This functionality is intended for use in scenarios where the metadata endpoint cannot be reached, such as in private clouds or Azure Stack.
     * The process of instance discovery entails retrieving authority metadata from https://login.microsoft.com/ to validate the authority.
     */
    disableInstanceDiscovery?: boolean;
}
//# sourceMappingURL=authorityValidationOptions.d.ts.map