{"version": 3, "file": "defaultAzureCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEnE,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AAGrE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,mGAAmG,CACpG,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAE1D;;;;;GAKG;AACH,MAAM,OAAO,sBAAuB,SAAQ,sBAAsB;IAChE;;;;OAIG;IACH,YAAY,uBAAgD;QAC1D,KAAK,EAAE,CAAC;QACR,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { credentialLogger, formatError } from \"../util/logging.js\";\nimport type { AccessToken } from \"@azure/core-auth\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential.js\";\nimport type { TokenCredentialOptions } from \"../tokenCredentialOptions.js\";\n\nconst BrowserNotSupportedError = new Error(\n  \"DefaultAzureCredential is not supported in the browser. Use InteractiveBrowserCredential instead.\",\n);\nconst logger = credentialLogger(\"DefaultAzureCredential\");\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration for\n * applications that will be deployed to Azure.\n *\n * Only available in Node.js.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class.\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(_tokenCredentialOptions?: TokenCredentialOptions) {\n    super();\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  public getToken(): Promise<AccessToken> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}