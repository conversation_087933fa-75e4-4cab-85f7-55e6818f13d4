{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../../src/node.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAqB,MAAM,gBAAgB,CAAC;AAEhE,UAAU,kBAAkB;IACxB,mDAAmD;IACnD,SAAS,EAAE,MAAM,CAAC;IAClB,qDAAqD;IACrD,QAAQ,EAAE,MAAM,CAAC;IACjB,wCAAwC;IACxC,WAAW,EAAE,MAAM,CAAC;IACpB,kDAAkD;IAClD,OAAO,EAAE,MAAM,CAAC;IAChB,gGAAgG;IAChG,MAAM,EAAE,MAAM,CAAC;IACf,mFAAmF;IACnF,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,UAAU,qBAAsB,SAAQ,kBAAkB;IACtD,QAAQ,CAAC,EAAE,kBAAkB,CAAC;IAC9B,MAAM,CAAC,EAAE,kBAAkB,CAAC;CAC/B;AAED,oBAAY,UAAU,GAAG,QAAQ,GAAG,OAAO,GAAG,KAAK,CAAC;AACpD,oBAAY,SAAS,GACf,IAAI,GACJ,OAAO,GACP,qBAAqB,GACrB,OAAO,GACP,KAAK,GAEL,QAAQ,CAAC;AACf,oBAAY,OAAO,GAAG,UAAU,GAAG,SAAS,CAAC;AAE7C;;;GAGG;AACH,8BAAsB,IAAI;IACtB,4BAA4B;IAC5B,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;IAEpC,yBAAyB;IACzB,MAAM,EAAE,UAAU,GAAG,IAAI,CAAQ;IAEjC,uBAAuB;IACvB,IAAI,EAAE,SAAS,GAAG,IAAI,CAAQ;IAE9B,mBAAmB;IACnB,IAAI,EAAE,SAAS,GAAG,IAAI,CAAQ;IAE9B,2FAA2F;IAC3F,UAAU,EAAE,MAAM,GAAG,IAAI,CAAQ;IAEjC,uFAAuF;IACvF,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAQ;IAE/B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAI/C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAInC;;;OAGG;IACH,IAAI,UAAU,IAAI,UAAU,GAAG,IAAI,CAElC;IAED,IAAI,UAAU,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,EAEvC;IAED;;;OAGG;IACH,IAAI,eAAe,IAAI,SAAS,GAAG,IAAI,CAEtC;IAED,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,EAEzC;IAED;;;OAGG;IACH,IAAI,WAAW,IAAI,SAAS,GAAG,IAAI,CAElC;IAED,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,EAErC;IAED;;;;;OAKG;IACH,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,UAAQ,GAAG,CAAC;CAG3D;AAED;;GAEG;AACH,8BAAsB,QAAS,SAAQ,IAAI;IAIpB,IAAI,EAAE,MAAM;IAH/B;;OAEG;gBACgB,IAAI,EAAE,MAAM;IAI/B;;;OAGG;IACH,IAAI,SAAS,IAAI,MAAM,CAEtB;IAED,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAEzB;CACJ;AAED;;GAEG;AACH,qBAAa,IAAK,SAAQ,QAAQ;IAC9B,IAAI,EAAE,WAAW,CAAC,IAAI,CAAoB;IAE1C,IAAI,QAAQ,IAAI,CAAC,CAEhB;CACJ;AAED;;GAEG;AACH,qBAAa,OAAQ,SAAQ,QAAQ;IACjC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAuB;IAEhD,IAAI,QAAQ,IAAI,CAAC,CAEhB;CACJ;AAED;;GAEG;AACH,qBAAa,qBAAsB,SAAQ,QAAQ;IAG5B,IAAI,EAAE,MAAM;IAF/B,IAAI,EAAE,WAAW,CAAC,SAAS,CAAyB;gBAEjC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;IAI7C,IAAa,QAAQ,IAAI,CAAC,CAEzB;IAED,kEAAkE;IAClE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,+EAA+E;IAC/E,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,+EAA+E;IAC/E,YAAY,CAAC,EAAE,MAAM,CAAC;CACzB;AAED;;GAEG;AACH,8BAAsB,gBAAiB,SAAQ,IAAI;IAI5B,QAAQ,EAAE,SAAS,EAAE;IAHxC;;OAEG;gBACgB,QAAQ,EAAE,SAAS,EAAE;IAKxC,+BAA+B;IAC/B,IAAI,UAAU,IAAI,SAAS,GAAG,IAAI,CAEjC;IAED,8BAA8B;IAC9B,IAAI,SAAS,IAAI,SAAS,GAAG,IAAI,CAIhC;IAED;;;OAGG;IACH,IAAI,UAAU,IAAI,SAAS,EAAE,CAE5B;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,EAEnC;CACJ;AAED,qBAAa,KAAM,SAAQ,gBAAgB;IACvC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAqB;IAE5C,IAAI,QAAQ,IAAI,CAAC,CAEhB;CACJ;AAED;;GAEG;AACH,qBAAa,QAAS,SAAQ,gBAAgB;IAC1C,IAAI,EAAE,WAAW,CAAC,IAAI,CAAoB;IAE1C,IAAI,QAAQ,IAAI,CAAC,CAEhB;IAED,mGAAmG;IACnG,QAAQ,CAAC,EAAE,WAAW,GAAG,QAAQ,GAAG,gBAAgB,CAAC;CACxD;AAED;;GAEG;AACH,UAAU,SAAS;IACf,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,qBAAa,OAAQ,SAAQ,gBAAgB;IAO9B,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;IAEnC,IAAI,EACL,WAAW,CAAC,GAAG,GACf,WAAW,CAAC,MAAM,GAClB,WAAW,CAAC,KAAK;IAZ3B;;;;OAIG;gBAEQ,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,EAC1C,QAAQ,GAAE,SAAS,EAAO,EACnB,IAAI,GACL,WAAW,CAAC,GAAG,GACf,WAAW,CAAC,MAAM,GAClB,WAAW,CAAC,KAIG;IAKzB,IAAI,QAAQ,IAAI,CAAC,CAEhB;IAED;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,qBAAqB,GAAG,IAAI,CAAC;IAIlD;;;OAGG;IACH,IAAI,OAAO,IAAI,MAAM,CAEpB;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAEvB;IAED,IAAI,UAAU,IAAI,SAAS,EAAE,CAO5B;IAED,uCAAuC;IACvC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,kDAAkD;IAClD,oBAAoB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9C,kEAAkE;IAClE,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAC9C;AAED;;;GAGG;AACH,wBAAgB,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,OAAO,CAEjD;AAED;;;GAGG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,CAEjD;AAED;;;GAGG;AACH,wBAAgB,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,CAE/C;AAED;;;GAGG;AACH,wBAAgB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,OAAO,CAErD;AAED;;;GAGG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,qBAAqB,CAErE;AAED;;;GAGG;AACH,wBAAgB,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,QAAQ,CAEvD;AAED;;;GAGG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,UAAU,CAE1D;AAED;;;;;GAKG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,UAAQ,GAAG,CAAC,CA4DvE"}