import type { TextlintMessage, TextlintResult } from "@textlint/types";
import { FormatterOptions } from "../FormatterOptions";
/**
 *
 * @param {string} code
 * @param {string} filePath
 * @param {TextLintMessage} message
 * @returns {*}
 */
declare function prettyError(code: string, filePath: string, message: TextlintMessage): any;
/**
 *
 * @param {TextLintResult[]} results
 * @param {TextLintFormatterOption} options
 * @returns {string}
 */
declare function formatter(results: TextlintResult[], options: FormatterOptions): string;
export default formatter;
export { prettyError };
//# sourceMappingURL=pretty-error.d.ts.map