{"version": 3, "file": "onBehalfOfCredential.d.ts", "sourceRoot": "", "sources": ["../../../src/credentials/onBehalfOfCredential.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAGtF,OAAO,KAAK,EACV,oCAAoC,EACpC,sCAAsC,EAEtC,iCAAiC,EAClC,MAAM,kCAAkC,CAAC;AAS1C,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,mCAAmC,CAAC;AAEtF,OAAO,KAAK,EAAE,iCAAiC,EAAE,MAAM,wCAAwC,CAAC;AAShG;;GAEG;AACH,qBAAa,oBAAqB,YAAW,eAAe;IAC1D,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,4BAA4B,CAAW;IAC/C,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,oBAAoB,CAAC,CAAU;IACvC,OAAO,CAAC,eAAe,CAAC,CAAS;IACjC,OAAO,CAAC,YAAY,CAAC,CAAS;IAC9B,OAAO,CAAC,kBAAkB,CAAS;IACnC,OAAO,CAAC,eAAe,CAAC,CAAwB;IAEhD;;;;;;;;;;;;;;;;;;;;;;;OAuBG;gBAED,OAAO,EAAE,sCAAsC,GAC7C,iCAAiC,GACjC,4BAA4B;IAEhC;;;;;;;;;;;;;;;;;;;;;;;OAuBG;gBAED,OAAO,EAAE,iCAAiC,GACxC,iCAAiC,GACjC,4BAA4B;IAGhC;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;gBAED,OAAO,EAAE,oCAAoC,GAC3C,iCAAiC,GACjC,4BAA4B;IAuDhC;;;;;;OAMG;IACG,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,OAAO,GAAE,eAAoB,GAAG,OAAO,CAAC,WAAW,CAAC;YA0ChF,sBAAsB;YAetB,gBAAgB;CAyC/B"}