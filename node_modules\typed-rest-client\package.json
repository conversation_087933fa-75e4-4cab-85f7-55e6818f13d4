{"name": "typed-rest-client", "version": "1.8.11", "description": "Node Rest and Http Clients for use with TypeScript", "main": "./RestClient.js", "scripts": {"build": "node make.js build", "test": "node make.js test", "bt": "node make.js buildtest", "samples": "node make.js samples", "units": "node make.js units", "validate": "node make.js validate"}, "repository": {"type": "git", "url": "git+https://github.com/Microsoft/typed-rest-client.git"}, "keywords": ["rest", "http", "client", "typescript", "node"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Microsoft/typed-rest-client/issues"}, "homepage": "https://github.com/Microsoft/typed-rest-client#readme", "devDependencies": {"@types/mocha": "^2.2.44", "@types/node": "^6.0.92", "@types/shelljs": "0.7.4", "mocha": "^6.2.3", "nock": "9.6.1", "semver": "4.3.3", "shelljs": "^0.8.5", "typescript": "3.1.5"}, "dependencies": {"qs": "^6.9.1", "tunnel": "0.0.6", "underscore": "^1.12.1"}}