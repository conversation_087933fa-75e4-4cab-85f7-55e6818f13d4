{"name": "@textlint/linter-formatter", "version": "14.8.0", "description": "textlint output formatter", "homepage": "https://github.com/textlint/textlint/tree/master/packages/@textlint/linter-formatter", "bugs": {"url": "https://github.com/textlint/textlint/issues"}, "repository": {"type": "git", "url": "https://github.com/textlint/textlint.git"}, "license": "MIT", "author": "azu", "type": "commonjs", "main": "lib/src/index.js", "module": "module/src/index.js", "types": "lib/src/index.d.ts", "directories": {"test": "test/"}, "files": ["bin/", "lib/", "module/", "src/", "!*.tsbuil<PERSON><PERSON>"], "scripts": {"build": "tsc -b && tsc -b tsconfig.module.json", "clean": "rimraf lib/ module/", "prepack": "npm run build", "test": "mocha"}, "dependencies": {"@azu/format-text": "^1.0.2", "@azu/style-format": "^1.0.1", "@textlint/module-interop": "^14.8.0", "@textlint/resolver": "^14.8.0", "@textlint/types": "^14.8.0", "chalk": "^4.1.2", "debug": "^4.4.1", "js-yaml": "^3.14.1", "lodash": "^4.17.21", "pluralize": "^2.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "table": "^6.9.0", "text-table": "^0.2.0"}, "devDependencies": {"@types/js-yaml": "^3.12.10", "@types/lodash": "^4.17.0", "@types/mocha": "^9.1.1", "@types/node": "^18.19.9", "chai": "^4.5.0", "mocha": "^10.8.1", "proxyquire": "^2.1.3", "rimraf": "^6.0.1", "sinon": "^1.17.7", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "publishConfig": {"access": "public"}, "gitHead": "aeb76ceab6e7a38007b5a16db7302991f970a2a7"}