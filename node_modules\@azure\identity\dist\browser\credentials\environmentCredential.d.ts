import type { AccessToken, TokenCredential } from "@azure/core-auth";
/**
 * Enables authentication to Microsoft Entra ID using client secret
 * details configured in environment variables
 */
export declare class EnvironmentCredential implements TokenCredential {
    /**
     * Only available in Node.js
     */
    constructor();
    getToken(): Promise<AccessToken | null>;
}
//# sourceMappingURL=environmentCredential-browser.d.mts.map