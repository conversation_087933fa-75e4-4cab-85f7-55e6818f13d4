{"version": 3, "file": "instrumenter.js", "sourceRoot": "", "sources": ["../../src/instrumenter.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AASlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAEnC,MAAM,UAAU,wBAAwB;IACtC,OAAO;QACL,GAAG,EAAE,GAAG,EAAE;YACR,OAAO;QACT,CAAC;QACD,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK;QACxB,eAAe,EAAE,GAAG,EAAE;YACpB,OAAO;QACT,CAAC;QACD,YAAY,EAAE,GAAG,EAAE;YACjB,OAAO;QACT,CAAC;QACD,SAAS,EAAE,GAAG,EAAE;YACd,OAAO;QACT,CAAC;QACD,QAAQ,EAAE,GAAG,EAAE;YACb,OAAO;QACT,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,yBAAyB;IACvC,OAAO;QACL,oBAAoB,EAAE,GAA2B,EAAE;YACjD,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,sBAAsB,EAAE,GAA+B,EAAE;YACvD,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,SAAS,EAAE,CACT,KAAa,EACb,WAAoC,EACmB,EAAE;YACzD,OAAO;gBACL,IAAI,EAAE,wBAAwB,EAAE;gBAChC,cAAc,EAAE,oBAAoB,CAAC,EAAE,aAAa,EAAE,WAAW,CAAC,cAAc,EAAE,CAAC;aACpF,CAAC;QACJ,CAAC;QACD,WAAW,CAIT,QAAwB,EACxB,QAAkB,EAClB,GAAG,YAA0B;YAE7B,OAAO,QAAQ,CAAC,GAAG,YAAY,CAAC,CAAC;QACnC,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,YAA0B;IACxD,KAAK,CAAC,0BAA0B,GAAG,YAAY,CAAC;AAClD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe;IAC7B,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC;QACtC,KAAK,CAAC,0BAA0B,GAAG,yBAAyB,EAAE,CAAC;IACjE,CAAC;IACD,OAAO,KAAK,CAAC,0BAA0B,CAAC;AAC1C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport {\n  Instrumenter,\n  InstrumenterSpanOptions,\n  TracingContext,\n  TracingSpan,\n} from \"./interfaces.js\";\n\nimport { createTracingContext } from \"./tracingContext.js\";\nimport { state } from \"./state.js\";\n\nexport function createDefaultTracingSpan(): TracingSpan {\n  return {\n    end: () => {\n      // noop\n    },\n    isRecording: () => false,\n    recordException: () => {\n      // noop\n    },\n    setAttribute: () => {\n      // noop\n    },\n    setStatus: () => {\n      // noop\n    },\n    addEvent: () => {\n      // noop\n    },\n  };\n}\n\nexport function createDefaultInstrumenter(): Instrumenter {\n  return {\n    createRequestHeaders: (): Record<string, string> => {\n      return {};\n    },\n    parseTraceparentHeader: (): TracingContext | undefined => {\n      return undefined;\n    },\n    startSpan: (\n      _name: string,\n      spanOptions: InstrumenterSpanOptions,\n    ): { span: TracingSpan; tracingContext: TracingContext } => {\n      return {\n        span: createDefaultTracingSpan(),\n        tracingContext: createTracingContext({ parentContext: spanOptions.tracingContext }),\n      };\n    },\n    withContext<\n      CallbackArgs extends unknown[],\n      Callback extends (...args: CallbackArgs) => ReturnType<Callback>,\n    >(\n      _context: TracingContext,\n      callback: Callback,\n      ...callbackArgs: CallbackArgs\n    ): ReturnType<Callback> {\n      return callback(...callbackArgs);\n    },\n  };\n}\n\n/**\n * Extends the Azure SDK with support for a given instrumenter implementation.\n *\n * @param instrumenter - The instrumenter implementation to use.\n */\nexport function useInstrumenter(instrumenter: Instrumenter): void {\n  state.instrumenterImplementation = instrumenter;\n}\n\n/**\n * Gets the currently set instrumenter, a No-Op instrumenter by default.\n *\n * @returns The currently set instrumenter\n */\nexport function getInstrumenter(): Instrumenter {\n  if (!state.instrumenterImplementation) {\n    state.instrumenterImplementation = createDefaultInstrumenter();\n  }\n  return state.instrumenterImplementation;\n}\n"]}