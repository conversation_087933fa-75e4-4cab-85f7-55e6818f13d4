const path = require('path');
const webpack = require('webpack');

/**@type {import('webpack').Configuration}*/
const config = {
  target: 'webworker', // Web Worker environment
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',

  entry: {
    FileSystemWorker: './src/workers/FileSystemWorker.ts'
  },

  output: {
    path: path.resolve(__dirname, 'dist', 'workers'),
    filename: '[name].js',
    libraryTarget: 'self', // For Web Workers
    clean: true
  },

  devtool: process.env.NODE_ENV === 'production' ? 'source-map' : 'eval-source-map',

  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@workers': path.resolve(__dirname, 'src/workers')
    }
  },

  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
              experimentalWatchApi: true,
              configFile: path.resolve(__dirname, 'tsconfig.workers.json')
            }
          }
        ]
      }
    ]
  },

  optimization: {
    minimize: process.env.NODE_ENV === 'production',
    usedExports: true,
    sideEffects: false
  },

  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV === 'production' ? 'production' : 'development')
    })
  ],

  stats: {
    errorDetails: true,
    chunks: false,
    modules: false,
    assets: true
  }
};

module.exports = config;
