{"version": 3, "file": "TimeoutPolicy.js", "sourceRoot": "", "sources": ["../../src/TimeoutPolicy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AACvD,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAGjE,MAAM,CAAN,IAAY,eAWX;AAXD,WAAY,eAAe;IACzB;;;OAGG;IACH,6CAA0B,CAAA;IAE1B;;OAEG;IACH,4CAAyB,CAAA;AAC3B,CAAC,EAXW,eAAe,KAAf,eAAe,QAW1B;AAgBD,MAAM,OAAO,aAAa;IAoBxB,YACmB,QAAgB,EAChB,OAAwB,EACxB,WAAW,IAAI,cAAc,EAAE,EAC/B,QAAQ,KAAK;QAHb,aAAQ,GAAR,QAAQ,CAAQ;QAChB,YAAO,GAAP,OAAO,CAAiB;QACxB,aAAQ,GAAR,QAAQ,CAAuB;QAC/B,UAAK,GAAL,KAAK,CAAQ;QArBf,mBAAc,GAAG,IAAI,YAAY,EAAQ,CAAC;QAE3D;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAE5D;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpD;;WAEG;QACa,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAOjD,CAAC;IAEJ;;;;;;OAMG;IACI,gBAAgB;QACrB,MAAM,CAAC,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC9E,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAO,CAClB,EAA8E,EAC9E,MAAoB;QAEpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC/E,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;QAE3C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;gBAC1D,OAAO,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAChF,CAAC;YAED,OAAO,MAAM,IAAI,CAAC,QAAQ;iBACvB,MAAM,CAAC,KAAK,IAAI,EAAE,CACjB,OAAO,CAAC,IAAI,CAAI;gBACd,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC5C,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC3C,MAAM,IAAI,kBAAkB,CAAC,6BAA6B,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;gBAC/E,CAAC,CAAC;aACH,CAAC,CACH;iBACA,IAAI,CAAC,aAAa,CAAC,CAAC;QACzB,CAAC;gBAAS,CAAC;YACT,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC9B,WAAW,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;gBACzC,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,CAAC;YACD,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,YAAY,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;CACF", "sourcesContent": ["import { deriveAbortController } from './common/abort';\nimport { Event, EventEmitter, onAbort } from './common/Event';\nimport { ExecuteWrapper, returnOrThrow } from './common/Executor';\nimport { TaskCancelledError } from './errors/TaskCancelledError';\nimport { IPolicy } from './Policy';\n\nexport enum TimeoutStrategy {\n  /**\n   * Cooperative timeouts will simply revoke the inner cancellation token,\n   * assuming the caller handles cancellation and throws or returns appropriately.\n   */\n  Cooperative = 'optimistic',\n\n  /**\n   * Aggressive cancellation immediately throws\n   */\n  Aggressive = 'aggressive',\n}\n\nexport interface ICancellationContext {\n  signal: AbortSignal;\n}\n\nexport interface ITimeoutOptions {\n  /** Strategy for timeouts, \"Cooperative\", or \"Accessive\" */\n  strategy: TimeoutStrategy;\n  /**\n   * Whether the AbortSignal should be aborted when the\n   * function returns. Defaults to true.\n   */\n  abortOnReturn?: boolean;\n}\n\nexport class TimeoutPolicy implements IPolicy<ICancellationContext> {\n  declare readonly _altReturn: never;\n\n  private readonly timeoutEmitter = new EventEmitter<void>();\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onTimeout = this.timeoutEmitter.addListener;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onFailure = this.executor.onFailure;\n\n  /**\n   * @inheritdoc\n   */\n  public readonly onSuccess = this.executor.onSuccess;\n\n  constructor(\n    private readonly duration: number,\n    private readonly options: ITimeoutOptions,\n    private readonly executor = new ExecuteWrapper(),\n    private readonly unref = false,\n  ) {}\n\n  /**\n   * When timing out, a referenced timer is created. This means the Node.js\n   * event loop is kept active while we're waiting for the timeout, as long as\n   * the function hasn't returned. Calling this method on the timeout builder\n   * will unreference the timer, allowing the process to exit even if a\n   * timeout might still be happening.\n   */\n  public dangerouslyUnref() {\n    const t = new TimeoutPolicy(this.duration, this.options, this.executor, true);\n    return t;\n  }\n\n  /**\n   * Executes the given function.\n   * @param fn Function to execute. Takes in a nested cancellation token.\n   * @throws a {@link TaskCancelledError} if a timeout occurs\n   */\n  public async execute<T>(\n    fn: (context: ICancellationContext, signal: AbortSignal) => PromiseLike<T> | T,\n    signal?: AbortSignal,\n  ): Promise<T> {\n    const { ctrl: aborter, dispose: disposeAbort } = deriveAbortController(signal);\n    const timer = setTimeout(() => aborter.abort(), this.duration);\n    if (this.unref) {\n      timer.unref();\n    }\n\n    const context = { signal: aborter.signal };\n\n    const onceAborted = onAbort(aborter.signal);\n    const onCancelledListener = onceAborted.event(() => this.timeoutEmitter.emit());\n\n    try {\n      if (this.options.strategy === TimeoutStrategy.Cooperative) {\n        return returnOrThrow(await this.executor.invoke(fn, context, aborter.signal));\n      }\n\n      return await this.executor\n        .invoke(async () =>\n          Promise.race<T>([\n            Promise.resolve(fn(context, aborter.signal)),\n            Event.toPromise(onceAborted.event).then(() => {\n              throw new TaskCancelledError(`Operation timed out after ${this.duration}ms`);\n            }),\n          ]),\n        )\n        .then(returnOrThrow);\n    } finally {\n      onCancelledListener.dispose();\n      onceAborted.dispose();\n      if (this.options.abortOnReturn !== false) {\n        aborter.abort();\n      }\n      clearTimeout(timer);\n      disposeAbort();\n    }\n  }\n}\n"]}