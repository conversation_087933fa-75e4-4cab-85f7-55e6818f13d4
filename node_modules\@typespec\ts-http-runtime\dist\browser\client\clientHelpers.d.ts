import type { HttpClient } from "../interfaces.js";
import type { Pipeline } from "../pipeline.js";
import type { ClientOptions } from "./common.js";
/**
 * Creates a default rest pipeline to re-use accross Rest Level Clients
 */
export declare function createDefaultPipeline(options?: ClientOptions): Pipeline;
export declare function getCachedDefaultHttpsClient(): HttpClient;
//# sourceMappingURL=clientHelpers.d.ts.map