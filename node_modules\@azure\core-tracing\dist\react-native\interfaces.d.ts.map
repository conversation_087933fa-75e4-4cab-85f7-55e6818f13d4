{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAGA;;;GAGG;AACH,MAAM,MAAM,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS;IAAE,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,GAAG,CAAA;CAAE,GACnE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,GAC/B,QAAQ,CAAC,CAAC,CAAC,GACX,KAAK,GACP,CAAC,CAAC;AAEN;;;;GAIG;AACH,MAAM,WAAW,aAAa;IAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,QAAQ,CACN,OAAO,SAAS;QAAE,cAAc,CAAC,EAAE,uBAAuB,CAAA;KAAE,EAC5D,QAAQ,SAAS,CACf,cAAc,EAAE,OAAO,EACvB,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,KAC3B,UAAU,CAAC,QAAQ,CAAC,EAEzB,IAAI,EAAE,MAAM,EACZ,gBAAgB,EAAE,OAAO,EACzB,QAAQ,EAAE,QAAQ,EAClB,WAAW,CAAC,EAAE,kBAAkB,GAC/B,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3C;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,OAAO,SAAS;QAAE,cAAc,CAAC,EAAE,uBAAuB,CAAA;KAAE,EACpE,IAAI,EAAE,MAAM,EACZ,gBAAgB,CAAC,EAAE,OAAO,EAC1B,WAAW,CAAC,EAAE,kBAAkB,GAC/B;QACD,IAAI,EAAE,WAAW,CAAC;QAClB,cAAc,EAAE,yBAAyB,CAAC,OAAO,CAAC,CAAC;KACpD,CAAC;IACF;;;;;;;;;OASG;IACH,WAAW,CACT,YAAY,SAAS,OAAO,EAAE,EAC9B,QAAQ,SAAS,CAAC,GAAG,IAAI,EAAE,YAAY,KAAK,UAAU,CAAC,QAAQ,CAAC,EAEhE,OAAO,EAAE,cAAc,EACvB,QAAQ,EAAE,QAAQ,EAClB,GAAG,YAAY,EAAE,YAAY,GAC5B,UAAU,CAAC,QAAQ,CAAC,CAAC;IAExB;;;;;OAKG;IACH,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS,CAAC;IAE9E;;;;;OAKG;IACH,oBAAoB,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAC/E;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC,8EAA8E;IAC9E,SAAS,EAAE,MAAM,CAAC;IAClB,mDAAmD;IACnD,WAAW,EAAE,MAAM,CAAC;IACpB,8DAA8D;IAC9D,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAED,wBAAwB;AACxB,MAAM,MAAM,eAAe,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAEzF,wDAAwD;AACxD,MAAM,WAAW,kBAAkB;IACjC,yEAAyE;IACzE,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B,oEAAoE;IACpE,SAAS,CAAC,EAAE,eAAe,EAAE,CAAC;IAC9B,kDAAkD;IAClD,cAAc,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,CAAC;CAC7C;AAED,uGAAuG;AACvG,MAAM,WAAW,eAAe;IAC9B,yEAAyE;IACzE,cAAc,EAAE,cAAc,CAAC;IAC/B,uCAAuC;IACvC,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,CAAC;CACzC;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;;;;;OAMG;IACH,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,uBAAuB,GACnC;QAAE,IAAI,EAAE,WAAW,CAAC;QAAC,cAAc,EAAE,cAAc,CAAA;KAAE,CAAC;IACzD;;;;;;;OAOG;IACH,WAAW,CACT,YAAY,SAAS,OAAO,EAAE,EAC9B,QAAQ,SAAS,CAAC,GAAG,IAAI,EAAE,YAAY,KAAK,UAAU,CAAC,QAAQ,CAAC,EAEhE,OAAO,EAAE,cAAc,EACvB,QAAQ,EAAE,QAAQ,EAClB,GAAG,YAAY,EAAE,YAAY,GAC5B,UAAU,CAAC,QAAQ,CAAC,CAAC;IAExB;;;OAGG;IACH,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS,CAAC;IAC9E;;;OAGG;IACH,oBAAoB,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAC/E;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,kBAAkB;IACjE,mDAAmD;IACnD,WAAW,EAAE,MAAM,CAAC;IACpB,sDAAsD;IACtD,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,4FAA4F;IAC5F,cAAc,CAAC,EAAE,cAAc,CAAC;CACjC;AAED;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG;IAAE,MAAM,EAAE,SAAS,CAAA;CAAE,CAAC;AAEtD;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAAE,MAAM,EAAE,OAAO,CAAC;IAAC,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,CAAA;CAAE,CAAC;AAE1E;;;;GAIG;AACH,MAAM,MAAM,UAAU,GAAG,iBAAiB,GAAG,eAAe,CAAC;AAE7D;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACrC;;OAEG;IACH,SAAS,CAAC,EAAE,IAAI,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,SAAS,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,CAAC;IAEpC;;;;;OAKG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC;IAEjD;;OAEG;IACH,GAAG,IAAI,IAAI,CAAC;IAEZ;;;;;;;OAOG;IACH,eAAe,CAAC,SAAS,EAAE,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC;IAEjD;;;;OAIG;IACH,WAAW,IAAI,OAAO,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC;CAC1D;AAED,4EAA4E;AAC5E,MAAM,WAAW,cAAc;IAC7B;;;;;;OAMG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,cAAc,CAAC;IACtD;;;;;OAKG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;IAC/B;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,cAAc,CAAC;CAC1C;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC,oDAAoD;IACpD,cAAc,CAAC,EAAE,cAAc,CAAC;CACjC;AAED;;;GAGG;AACH,MAAM,MAAM,yBAAyB,CACnC,OAAO,SAAS;IAAE,cAAc,CAAC,EAAE,uBAAuB,CAAA;CAAE,IAC1D,OAAO,GAAG;IACZ,cAAc,EAAE;QACd,cAAc,EAAE,cAAc,CAAC;KAChC,CAAC;CACH,CAAC"}