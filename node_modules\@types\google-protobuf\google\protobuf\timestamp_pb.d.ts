// package: google.protobuf
// file: timestamp.proto

import * as jspb from "../../index";

export class Timestamp extends jspb.Message {
    getSeconds(): number;
    setSeconds(value: number): Timestamp;

    getNanos(): number;
    setNanos(value: number): Timestamp;

    toDate(): Date;
    fromDate(date: Date): void;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Timestamp.AsObject;
    static toObject(includeInstance: boolean, msg: Timestamp): Timestamp.AsObject;
    static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
    static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
    static serializeBinaryToWriter(message: Timestam<PERSON>, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Timestamp;
    static deserializeBinaryFromReader(message: <PERSON>tamp, reader: jspb.BinaryReader): Timestamp;
    static fromDate(date: Date): Timestamp;
}

export namespace Timestamp {
    export type AsObject = {
        seconds: number;
        nanos: number;
    };
}
