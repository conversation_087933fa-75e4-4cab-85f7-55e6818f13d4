{"version": 3, "file": "ConsecutiveBreaker.js", "sourceRoot": "", "sources": ["../../src/breaker/ConsecutiveBreaker.ts"], "names": [], "mappings": ";;;AAEA,MAAa,kBAAkB;IAM7B;;;OAGG;IACH,YAA6B,SAAiB;QAAjB,cAAS,GAAT,SAAS,CAAQ;QAT9C;;WAEG;QACI,UAAK,GAAG,CAAC,CAAC;IAMgC,CAAC;IAElD;;OAEG;IACI,OAAO;QACZ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC;IACxC,CAAC;CACF;AAzBD,gDAyBC", "sourcesContent": ["import { IBreaker } from './Breaker';\n\nexport class ConsecutiveBreaker implements IBreaker {\n  /**\n   * @inheritdoc\n   */\n  public state = 0;\n\n  /**\n   * ConsecutiveBreaker breaks if more than `threshold` exceptions are received\n   * over a time period.\n   */\n  constructor(private readonly threshold: number) {}\n\n  /**\n   * @inheritdoc\n   */\n  public success() {\n    this.state = 0;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public failure() {\n    return ++this.state >= this.threshold;\n  }\n}\n"]}