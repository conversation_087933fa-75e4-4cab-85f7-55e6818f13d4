import type { ASTNodeTypes } from "./ASTNodeTypes";
import type { AnyTxtNode, TxtBlockQuoteNode, TxtBreakNode, TxtCodeBlockNode, TxtCommentNode, TxtDeleteNode, TxtDocumentNode, TxtEmphasisNode, TxtHeaderNode, TxtHorizontalRuleNode, TxtHtmlNode, TxtImageNode, TxtImageReferenceNode, TxtDefinitionNode, TxtCodeNode, TxtLinkNode, TxtLinkReferenceNode, TxtListItemNode, TxtListNode, TxtParagraphNode, TxtStrNode, TxtStrongNode, TxtTableCellNode, TxtTableNode, TxtTableRowNode } from "./NodeType";
/**
 * Type utility for TxtNodeType
 * Return TxtNode interface for the TxtNodeTYpe
 *
 * @example
 * ```ts
 * type NodeType = TxtNodeTypeOfNode<ASTNodeTypes.Document>;
 * ```
 */
export type TypeofTxtNode<T extends ASTNodeTypes | string> = T extends ASTNodeTypes.Document ? TxtDocumentNode : T extends ASTNodeTypes.DocumentExit ? TxtDocumentNode : T extends ASTNodeTypes.Paragraph ? TxtParagraphNode : T extends ASTNodeTypes.ParagraphExit ? TxtParagraphNode : T extends ASTNodeTypes.BlockQuote ? TxtBlockQuoteNode : T extends ASTNodeTypes.BlockQuoteExit ? TxtBlockQuoteNode : T extends ASTNodeTypes.List ? TxtListNode : T extends ASTNodeTypes.ListExit ? TxtListNode : T extends ASTNodeTypes.ListItem ? TxtListItemNode : T extends ASTNodeTypes.ListItemExit ? TxtListItemNode : T extends ASTNodeTypes.Header ? TxtHeaderNode : T extends ASTNodeTypes.HeaderExit ? TxtHeaderNode : T extends ASTNodeTypes.CodeBlock ? TxtCodeBlockNode : T extends ASTNodeTypes.CodeBlockExit ? TxtCodeBlockNode : T extends ASTNodeTypes.HtmlBlock ? TxtHtmlNode : T extends ASTNodeTypes.HtmlBlockExit ? TxtHtmlNode : T extends ASTNodeTypes.Link ? TxtLinkNode : T extends ASTNodeTypes.LinkExit ? TxtLinkNode : T extends ASTNodeTypes.LinkReference ? TxtLinkReferenceNode : T extends ASTNodeTypes.LinkReferenceExit ? TxtLinkReferenceNode : T extends ASTNodeTypes.Delete ? TxtDeleteNode : T extends ASTNodeTypes.DeleteExit ? TxtDeleteNode : T extends ASTNodeTypes.Emphasis ? TxtEmphasisNode : T extends ASTNodeTypes.EmphasisExit ? TxtEmphasisNode : T extends ASTNodeTypes.Strong ? TxtStrongNode : T extends ASTNodeTypes.StrongExit ? TxtStrongNode : T extends ASTNodeTypes.Break ? TxtBreakNode : T extends ASTNodeTypes.BreakExit ? TxtBreakNode : T extends ASTNodeTypes.Image ? TxtImageNode : T extends ASTNodeTypes.ImageExit ? TxtImageNode : T extends ASTNodeTypes.ImageReference ? TxtImageReferenceNode : T extends ASTNodeTypes.ImageReferenceExit ? TxtImageReferenceNode : T extends ASTNodeTypes.Definition ? TxtDefinitionNode : T extends ASTNodeTypes.DefinitionExit ? TxtDefinitionNode : T extends ASTNodeTypes.HorizontalRule ? TxtHorizontalRuleNode : T extends ASTNodeTypes.HorizontalRuleExit ? TxtHorizontalRuleNode : T extends ASTNodeTypes.Comment ? TxtCommentNode : T extends ASTNodeTypes.CommentExit ? TxtCommentNode : T extends ASTNodeTypes.Str ? TxtStrNode : T extends ASTNodeTypes.StrExit ? TxtStrNode : T extends ASTNodeTypes.Code ? TxtCodeNode : T extends ASTNodeTypes.CodeExit ? TxtCodeNode : T extends ASTNodeTypes.Html ? TxtHtmlNode : T extends ASTNodeTypes.HtmlExit ? TxtHtmlNode : T extends ASTNodeTypes.Table ? TxtTableNode : T extends ASTNodeTypes.TableExit ? TxtTableNode : T extends ASTNodeTypes.TableRow ? TxtTableRowNode : T extends ASTNodeTypes.TableRowExit ? TxtTableRowNode : T extends ASTNodeTypes.TableCell ? TxtTableCellNode : T extends ASTNodeTypes.TableCellExit ? TxtTableCellNode : AnyTxtNode;
//# sourceMappingURL=TypeofTxtNode.d.ts.map