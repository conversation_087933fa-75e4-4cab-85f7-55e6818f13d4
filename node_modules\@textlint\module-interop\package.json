{"name": "@textlint/module-interop", "version": "14.8.0", "description": "ECMAScript module interop library", "keywords": ["commonjs", "default", "es", "import", "module"], "homepage": "https://github.com/textlint/textlint/tree/master/packages/@textlint/module-interop/", "bugs": {"url": "https://github.com/textlint/textlint/issues"}, "repository": {"type": "git", "url": "https://github.com/textlint/textlint.git"}, "license": "MIT", "author": "azu", "type": "commonjs", "main": "lib/src/index.js", "module": "module/src/index.js", "types": "lib/src/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "lib/", "module/", "src/", "!*.tsbuil<PERSON><PERSON>"], "scripts": {"build": "tsc -b && tsc -b tsconfig.module.json", "clean": "rimraf lib/ module/", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepack": "npm run --if-present build", "test": "mocha", "watch": "tsc -b --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "^18.19.9", "mocha": "^10.8.1", "prettier": "^2.7.1", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "publishConfig": {"access": "public"}, "gitHead": "aeb76ceab6e7a38007b5a16db7302991f970a2a7"}