// MCP Context Provider Service
// Automatically sends VS Code editor context to connected MCP servers

import * as vscode from 'vscode';
import { IMcpClientService, McpContextData } from '../interfaces/IMcpClientService';
import { IFileSystemService } from '../interfaces/IFileSystemService';

export class McpContextProvider {
    private mcpClientService: IMcpClientService;
    private fileSystemService: IFileSystemService;
    private disposables: vscode.Disposable[] = [];
    private contextUpdateTimer?: NodeJS.Timeout;
    private lastContextSent: McpContextData | null = null;

    constructor(
        mcpClientService: IMcpClientService,
        fileSystemService: IFileSystemService
    ) {
        this.mcpClientService = mcpClientService;
        this.fileSystemService = fileSystemService;
        
        this.setupContextWatchers();
    }

    private setupContextWatchers(): void {
        // Watch for active editor changes
        this.disposables.push(
            vscode.window.onDidChangeActiveTextEditor(() => {
                this.scheduleContextUpdate();
            })
        );

        // Watch for text selection changes
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(() => {
                this.scheduleContextUpdate();
            })
        );

        // Watch for file changes
        this.disposables.push(
            vscode.workspace.onDidOpenTextDocument(() => {
                this.scheduleContextUpdate();
            })
        );

        this.disposables.push(
            vscode.workspace.onDidCloseTextDocument(() => {
                this.scheduleContextUpdate();
            })
        );

        // Watch for workspace folder changes
        this.disposables.push(
            vscode.workspace.onDidChangeWorkspaceFolders(() => {
                this.scheduleContextUpdate();
            })
        );

        // Send initial context
        this.scheduleContextUpdate();
    }

    private scheduleContextUpdate(): void {
        // Debounce context updates to avoid spam
        if (this.contextUpdateTimer) {
            clearTimeout(this.contextUpdateTimer);
        }

        this.contextUpdateTimer = setTimeout(() => {
            this.sendCurrentContext().catch(console.error);
        }, 500); // 500ms debounce
    }

    private async sendCurrentContext(): Promise<void> {
        try {
            const context = await this.gatherCurrentContext();
            
            // Only send if context has changed significantly
            if (this.hasContextChanged(context)) {
                await this.mcpClientService.sendContext(context);
                this.lastContextSent = context;
                console.log('Sent updated context to MCP servers');
            }
        } catch (error) {
            console.error('Failed to send context to MCP servers:', error);
        }
    }

    private async gatherCurrentContext(): Promise<McpContextData> {
        const workspaceRoot = this.fileSystemService.getWorkspaceRoot();
        const activeEditor = vscode.window.activeTextEditor;
        
        // Get active file information
        const activeFile = activeEditor?.document.uri.fsPath;
        const selectedText = activeEditor?.selection && !activeEditor.selection.isEmpty
            ? activeEditor.document.getText(activeEditor.selection)
            : undefined;

        // Get open files
        const openFiles = vscode.workspace.textDocuments
            .filter(doc => doc.uri.scheme === 'file')
            .map(doc => doc.uri.fsPath);

        // Get recent files from VS Code's recent files list
        const recentFiles = await this.getRecentFiles();

        // Get project structure (basic version)
        const projectStructure = await this.getProjectStructure();

        // Get git status if available
        const gitStatus = await this.getGitStatus();

        return {
            workspaceRoot,
            activeFile,
            selectedText,
            openFiles,
            recentFiles,
            projectStructure,
            gitStatus
        };
    }

    private async getRecentFiles(): Promise<string[]> {
        // This is a simplified version - in a real implementation,
        // you might want to track recently opened files more comprehensively
        const recentFiles: string[] = [];
        
        // Get recently modified files from workspace
        if (this.fileSystemService.getWorkspaceRoot()) {
            try {
                const files = await this.fileSystemService.searchFiles('.', {
                    pattern: '**/*.{ts,js,json,md,txt,py,java,cpp,c,h}',
                    recursive: true,
                    maxResults: 20
                });
                
                // Sort by modification time (most recent first)
                const filesWithStats = await Promise.all(
                    files.map(async file => {
                        try {
                            const stats = await this.fileSystemService.getFileStats(file);
                            return { file, modified: stats.modified };
                        } catch {
                            return null;
                        }
                    })
                );

                recentFiles.push(
                    ...filesWithStats
                        .filter(item => item !== null)
                        .sort((a, b) => b!.modified.getTime() - a!.modified.getTime())
                        .slice(0, 10)
                        .map(item => item!.file)
                );
            } catch (error) {
                console.error('Failed to get recent files:', error);
            }
        }

        return recentFiles;
    }

    private async getProjectStructure(): Promise<any> {
        const workspaceRoot = this.fileSystemService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return null;
        }

        try {
            // Get basic project structure
            const structure: any = {
                root: workspaceRoot,
                directories: [],
                files: []
            };

            // Get top-level directories and files
            const items = await this.fileSystemService.listFiles(workspaceRoot, false);
            
            for (const item of items.slice(0, 50)) { // Limit to avoid too much data
                try {
                    const stats = await this.fileSystemService.getFileStats(item);
                    if (stats.isDirectory) {
                        structure.directories.push(item);
                    } else {
                        structure.files.push(item);
                    }
                } catch {
                    // Skip items we can't stat
                }
            }

            return structure;
        } catch (error) {
            console.error('Failed to get project structure:', error);
            return null;
        }
    }

    private async getGitStatus(): Promise<any> {
        // This is a placeholder - in a real implementation,
        // you might want to use the VS Code Git extension API
        // or execute git commands to get status information
        try {
            const workspaceRoot = this.fileSystemService.getWorkspaceRoot();
            if (!workspaceRoot) {
                return null;
            }

            // Check if this is a git repository
            const gitDir = await this.fileSystemService.fileExists(`${workspaceRoot}/.git`);
            if (!gitDir) {
                return null;
            }

            return {
                isGitRepo: true,
                // Add more git status information as needed
                branch: 'main', // Placeholder
                hasChanges: false // Placeholder
            };
        } catch (error) {
            console.error('Failed to get git status:', error);
            return null;
        }
    }

    private hasContextChanged(newContext: McpContextData): boolean {
        if (!this.lastContextSent) {
            return true;
        }

        // Compare key fields to determine if context has changed significantly
        const lastContext = this.lastContextSent;
        
        return (
            newContext.activeFile !== lastContext.activeFile ||
            newContext.selectedText !== lastContext.selectedText ||
            newContext.openFiles.length !== lastContext.openFiles.length ||
            JSON.stringify(newContext.openFiles.sort()) !== JSON.stringify(lastContext.openFiles.sort())
        );
    }

    /**
     * Manually trigger a context update
     */
    public async updateContext(): Promise<void> {
        await this.sendCurrentContext();
    }

    /**
     * Get the current context without sending it
     */
    public async getCurrentContext(): Promise<McpContextData> {
        return this.gatherCurrentContext();
    }

    public dispose(): void {
        if (this.contextUpdateTimer) {
            clearTimeout(this.contextUpdateTimer);
            this.contextUpdateTimer = undefined;
        }

        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}
