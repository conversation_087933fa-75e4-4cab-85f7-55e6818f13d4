{"version": 3, "file": "serializer.js", "sourceRoot": "", "sources": ["../../src/serializer.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAC;AActC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAErD,MAAM,cAAc;IAClB,YACkB,eAAuC,EAAE,EACzC,QAAiB,KAAK;QADtB,iBAAY,GAAZ,YAAY,CAA6B;QACzC,UAAK,GAAL,KAAK,CAAiB;IACrC,CAAC;IAEJ;;OAEG;IACH,mBAAmB,CAAC,MAAc,EAAE,KAAU,EAAE,UAAkB;QAChE,MAAM,cAAc,GAAG,CACrB,cAAuC,EACvC,eAAoB,EACb,EAAE;YACT,MAAM,IAAI,KAAK,CACb,IAAI,UAAU,iBAAiB,KAAK,oCAAoC,cAAc,MAAM,eAAe,GAAG,CAC/G,CAAC;QACJ,CAAC,CAAC;QACF,IAAI,MAAM,CAAC,WAAW,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChE,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,GACZ,GAAG,MAAM,CAAC,WAAW,CAAC;YACvB,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,IAAI,gBAAgB,EAAE,CAAC;gBAChE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,IAAI,gBAAgB,EAAE,CAAC;gBAChE,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,GAAG,gBAAgB,EAAE,CAAC;gBAC/D,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,gBAAgB,KAAK,SAAS,IAAI,KAAK,GAAG,gBAAgB,EAAE,CAAC;gBAC/D,cAAc,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,QAAQ,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;gBACtD,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;gBACxD,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,QAAQ,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;gBACtD,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;gBACxD,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,UAAU,KAAK,SAAS,IAAI,KAAK,GAAG,UAAU,KAAK,CAAC,EAAE,CAAC;gBACzD,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,OAAO,GAAW,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACpF,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;oBAC/D,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YACD,IACE,WAAW;gBACX,KAAK,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAc,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC5E,CAAC;gBACD,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,SAAS,CACP,MAAc,EACd,MAAW,EACX,UAAmB,EACnB,UAA6B,EAAE,GAAG,EAAE,EAAE,EAAE;;QAExC,MAAM,cAAc,GAA8B;YAChD,GAAG,EAAE;gBACH,QAAQ,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,mCAAI,EAAE;gBACpC,WAAW,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,WAAW,mCAAI,KAAK;gBAC7C,UAAU,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,WAAW;aAClD;SACF,CAAC;QACF,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAc,CAAC;QAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;QACtC,CAAC;QACD,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7C,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;QAC/B,CAAC;QAED,mDAAmD;QACnD,sDAAsD;QACtD,mDAAmD;QACnD,wBAAwB;QACxB,iCAAiC;QACjC,0CAA0C;QAC1C,0CAA0C;QAC1C,qCAAqC;QACrC,0CAA0C;QAE1C,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,uBAAuB,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,+BAA+B,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,kBAAkB,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAC5C,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;gBACxC,OAAO,GAAG,MAAM,CAAC;YACnB,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,KAAK,IAAI,EAAE,CAAC;gBACtF,OAAO,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAChE,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChD,MAAM,UAAU,GAAG,MAAoB,CAAC;gBACxC,OAAO,GAAG,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACjF,CAAC;iBAAM,IACL,UAAU,CAAC,KAAK,CAAC,sDAAsD,CAAC,KAAK,IAAI,EACjF,CAAC;gBACD,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YAC/D,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrD,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;gBACpD,OAAO,GAAG,qBAAqB,CAC7B,IAAI,EACJ,MAAwB,EACxB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;YACJ,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;gBACtD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAA0B,EAC1B,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;YACJ,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrD,OAAO,GAAG,sBAAsB,CAC9B,IAAI,EACJ,MAAyB,EACzB,MAAM,EACN,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACnB,cAAc,CACf,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,WAAW,CACT,MAAc,EACd,YAAiB,EACjB,UAAkB,EAClB,UAA6B,EAAE,GAAG,EAAE,EAAE,EAAE;;QAExC,MAAM,cAAc,GAA8B;YAChD,GAAG,EAAE;gBACH,QAAQ,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,mCAAI,EAAE;gBACpC,WAAW,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,WAAW,mCAAI,KAAK;gBAC7C,UAAU,EAAE,MAAA,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,WAAW;aAClD;YACD,uBAAuB,EAAE,MAAA,OAAO,CAAC,uBAAuB,mCAAI,KAAK;SAClE,CAAC;QACF,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YACxD,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC1E,sEAAsE;gBACtE,qDAAqD;gBACrD,qEAAqE;gBACrE,YAAY,GAAG,EAAE,CAAC;YACpB,CAAC;YACD,+FAA+F;YAC/F,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACtC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;YACrC,CAAC;YACD,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,OAAY,CAAC;QACjB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,UAAU,GAAG,MAAM,CAAC,cAAe,CAAC;QACtC,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9C,OAAO,GAAG,wBAAwB,CAChC,IAAI,EACJ,MAAyB,EACzB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;gBACjD;;;;mBAIG;gBACH,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;oBACtF,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC;YAED,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3C,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBACnB,OAAO,GAAG,YAAY,CAAC;gBACzB,CAAC;YACH,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,CAAC;gBACnD,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;oBAC5B,OAAO,GAAG,IAAI,CAAC;gBACjB,CAAC;qBAAM,IAAI,YAAY,KAAK,OAAO,EAAE,CAAC;oBACpC,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,YAAY,CAAC;gBACzB,CAAC;YACH,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,kDAAkD,CAAC,KAAK,IAAI,EAAE,CAAC;gBACzF,OAAO,GAAG,YAAY,CAAC;YACzB,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,oCAAoC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3E,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;gBACpD,OAAO,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrD,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrD,OAAO,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC/C,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;gBACpD,OAAO,GAAG,uBAAuB,CAC/B,IAAI,EACJ,MAAwB,EACxB,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;YACJ,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;gBACtD,OAAO,GAAG,yBAAyB,CACjC,IAAI,EACJ,MAA0B,EAC1B,YAAY,EACZ,UAAU,EACV,cAAc,CACf,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC;QAChC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAC9B,eAAuC,EAAE,EACzC,QAAiB,KAAK;IAEtB,OAAO,IAAI,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,EAAU;IACtC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACrB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;QAC3C,EAAE,GAAG,CAAC;IACR,CAAC;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAkB;IAC3C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,CAAC,CAAC,MAAM,YAAY,UAAU,CAAC,EAAE,CAAC;QACpC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;IAC7F,CAAC;IACD,wBAAwB;IACxB,MAAM,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC3C,uBAAuB;IACvB,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAW;IACvC,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IACzF,CAAC;IACD,uBAAuB;IACvB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAChD,wBAAwB;IACxB,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAwB;IAClD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC1C,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,YAAY,IAAI,IAAI,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,cAAc,CAAC,CAAgB;IACtC,IAAI,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;QACpC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAW,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAE,CAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,CAAS;IAC/B,IAAI,CAAC,CAAC,EAAE,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB,EAAE,UAAkB,EAAE,KAAU;IAC3E,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QAC1C,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;YACzC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,0BAA0B,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;YAChD,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,gBAAgB,KAAK,2BAA2B,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9C,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,gBAAgB,KAAK,4CAA4C,CAC/E,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,CAAC;YACjD,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,eAAe,KAAK,2BAA2B,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC;YAChC,IACE,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,wBAAwB;gBAC5D,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,IAAI,yBAAyB;gBAC5D,CAAC,CAAC,KAAK,YAAY,WAAW,CAAC;gBAC/B,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC1B,gFAAgF;gBAChF,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC;gBACpF,UAAU,KAAK,UAAU,EACzB,CAAC;gBACD,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,iGAAiG,CAC/G,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAkB,EAAE,aAAyB,EAAE,KAAU;IAClF,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CACb,qDAAqD,UAAU,mBAAmB,CACnF,CAAC;IACJ,CAAC;IACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5C,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,KAAK,KAAK,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,GAAG,KAAK,6BAA6B,UAAU,2BAA2B,IAAI,CAAC,SAAS,CACtF,aAAa,CACd,GAAG,CACL,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAU;IAC5D,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,CAAC,KAAK,YAAY,UAAU,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;QAC/D,CAAC;QACD,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkB,EAAE,KAAU;IAC5D,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,CAAC,KAAK,YAAY,UAAU,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,8BAA8B,CAAC,CAAC;QAC/D,CAAC;QACD,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB,EAAE,KAAU,EAAE,UAAkB;IAC1E,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;YACvC,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;YAC7F,CAAC;YACD,KAAK;gBACH,KAAK,YAAY,IAAI;oBACnB,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;oBACtC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAClD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,4DAA4D,CAAC,CAAC;YAC7F,CAAC;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACtF,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,KAAK,IAAI,EAAE,CAAC;YACzD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,6DAA6D,CAAC,CAAC;YAC9F,CAAC;YACD,KAAK,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACtF,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAClD,IACE,CAAC,CACC,KAAK,YAAY,IAAI;gBACrB,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACnE,EACD,CAAC;gBACD,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,qEAAqE;oBAChF,mDAAmD,CACtD,CAAC;YACJ,CAAC;YACD,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CACb,GAAG,UAAU,sDAAsD,KAAK,IAAI,CAC7E,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,MAAsB,EACtB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC;;IAElC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,yBAAyB,CAAC,CAAC;IAC1D,CAAC;IACD,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IACtC,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QACpD,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;IACJ,CAAC;IACD,yDAAyD;IACzD,wDAAwD;IACxD,uDAAuD;IACvD,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACxE,WAAW,GAAG,MAAA,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,mCAAI,WAAW,CAAC;IACnF,CAAC;IACD,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1F,IAAI,KAAK,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,WAAW,CAAC,kBAAkB;gBAC7C,CAAC,CAAC,SAAS,WAAW,CAAC,kBAAkB,EAAE;gBAC3C,CAAC,CAAC,OAAO,CAAC;YACZ,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC1C,SAAS,CAAC,CAAC,CAAC,qBAAQ,eAAe,CAAE,CAAC;gBACtC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAClB,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;gBACvD,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,YAAY,EAAE,CAAC;YACvE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;QACjC,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAwB,EACxB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC;IAElC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,GAAG,UAAU,0BAA0B,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IACpC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,GAAG,CAC1D,CAAC;IACJ,CAAC;IACD,MAAM,cAAc,GAA2B,EAAE,CAAC;IAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QACtC,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1F,gFAAgF;QAChF,cAAc,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED,kDAAkD;IAClD,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAC5F,MAAM,MAAM,GAAG,cAAc,CAAC;QAC9B,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC;QAC1D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;GAKG;AACH,SAAS,2BAA2B,CAClC,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAE9D,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,OAAO,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,oBAAoB,CAAC;IAChD,CAAC;IAED,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,yBAAyB,UAAU,oCAAoC,IAAI,CAAC,SAAS,CACnF,MAAM,EACN,SAAS,EACT,CAAC,CACF,IAAI,CACN,CAAC;IACJ,CAAC;IAED,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5C,CAAC;AAED;;;;GAIG;AACH,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,UAAkB;IAElB,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;IAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,WAAW,GAAG,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,mDAAmD,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;QAChG,CAAC;QACD,UAAU,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CACb,qDAAqD;gBACnD,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,cACpC,MAAM,CAAC,IAAI,CAAC,SACd,iBAAiB,UAAU,IAAI,CAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,UAAkB,EAClB,KAAc,EACd,OAAkC;IAElC,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC;QAC/D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC5B,SAAS;YACX,CAAC;YAED,IAAI,QAA4B,CAAC;YACjC,IAAI,YAAY,GAAQ,OAAO,CAAC;YAChC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrB,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;oBAChC,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;gBACrE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC,cAAe,CAAC,CAAC;gBACjE,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBAEvB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;oBAC7B,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IACE,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,CAAC;wBACnD,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;4BAClD,cAAc,CAAC,YAAY,KAAK,SAAS,CAAC,EAC5C,CAAC;wBACD,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;oBAC9B,CAAC;oBACD,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;YAED,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;gBACxD,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB;wBACxC,CAAC,CAAC,SAAS,MAAM,CAAC,kBAAkB,EAAE;wBACtC,CAAC,CAAC,OAAO,CAAC;oBACZ,YAAY,CAAC,WAAW,CAAC,mCACpB,YAAY,CAAC,WAAW,CAAC,KAC5B,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,YAAY,GAChC,CAAC;gBACJ,CAAC;gBACD,MAAM,kBAAkB,GACtB,cAAc,CAAC,cAAc,KAAK,EAAE;oBAClC,CAAC,CAAC,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC,cAAc;oBAClD,CAAC,CAAC,UAAU,CAAC;gBAEjB,IAAI,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC5F,IACE,wBAAwB;oBACxB,wBAAwB,CAAC,UAAU,KAAK,GAAG;oBAC3C,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,CAAC,EACnD,CAAC;oBACD,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;gBACtC,CAAC;gBAED,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAC1C,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,IAAI,eAAe,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACjF,MAAM,KAAK,GAAG,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACjF,IAAI,KAAK,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;wBAC3C,uEAAuE;wBACvE,2DAA2D;wBAC3D,gCAAgC;wBAChC,YAAY,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;wBAC5D,YAAY,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;oBACxD,CAAC;yBAAM,IAAI,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;wBAChD,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAe,CAAC,EAAE,KAAK,EAAE,CAAC;oBACvE,CAAC;yBAAM,CAAC;wBACN,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,0BAA0B,GAAG,2BAA2B,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAC/F,IAAI,0BAA0B,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1C,KAAK,MAAM,cAAc,IAAI,MAAM,EAAE,CAAC;gBACpC,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;gBAC5E,IAAI,oBAAoB,EAAE,CAAC;oBACzB,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,SAAS,CAC5C,0BAA0B,EAC1B,MAAM,CAAC,cAAc,CAAC,EACtB,UAAU,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,EACzC,OAAO,CACR,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAsB,EACtB,eAAoB,EACpB,KAAc,EACd,OAAkC;IAElC,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QAC3C,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB;QAChD,CAAC,CAAC,SAAS,cAAc,CAAC,kBAAkB,EAAE;QAC9C,CAAC,CAAC,OAAO,CAAC;IACZ,MAAM,YAAY,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,YAAY,EAAE,CAAC;IAEjE,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACrD,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;YACjC,OAAO,eAAe,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,qBAAa,eAAe,CAAE,CAAC;YAC3C,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;YACnC,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;IACjD,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;IACnC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAoB,EAAE,OAAkC;IACpF,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAsB,EACtB,MAAuB,EACvB,YAAiB,EACjB,UAAkB,EAClB,OAAkC;;IAElC,MAAM,UAAU,GAAG,MAAA,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,WAAW,CAAC;IACzD,IAAI,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC;QAC/D,MAAM,GAAG,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;IACpF,CAAC;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAC1E,IAAI,QAAQ,GAA2B,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAE1C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1C,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,CAAC;QAClE,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;QACnE,IAAI,kBAAkB,GAAG,UAAU,CAAC;QACpC,IAAI,cAAc,KAAK,EAAE,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YAC1D,kBAAkB,GAAG,UAAU,GAAG,GAAG,GAAG,cAAc,CAAC;QACzD,CAAC;QAED,MAAM,sBAAsB,GAAI,cAAmC,CAAC,sBAAsB,CAAC;QAC3F,IAAI,sBAAsB,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAQ,EAAE,CAAC;YAC3B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClD,IAAI,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE,CAAC;oBACjD,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACpF,cAAmC,CAAC,IAAI,CAAC,KAAK,EAC/C,YAAY,CAAC,SAAS,CAAC,EACvB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACJ,CAAC;gBAED,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;YACD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;QAC7B,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5B,IAAI,cAAc,CAAC,cAAc,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/D,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,YAAY,CAAC,WAAW,CAAC,CAAC,OAAQ,CAAC,EACnC,kBAAkB,EAClB,OAAO,CACR,CAAC;YACJ,CAAC;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE,CAAC;gBACtC,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC3C,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC3C,CAAC;qBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;oBAC5C,+EAA+E;oBAC/E,kEAAkE;oBAClE,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;gBAC/B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,cAAc,IAAI,OAAO,IAAI,cAAc,CAAC;gBACjE,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;oBAChC;;;;;;;;;;;;;sBAaE;oBACF,MAAM,OAAO,GAAG,YAAY,CAAC,OAAQ,CAAC,CAAC;oBACvC,MAAM,WAAW,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,cAAe,CAAC,mCAAI,EAAE,CAAC;oBACrD,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,OAAO,CACR,CAAC;oBACF,oBAAoB,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAa,CAAC,CAAC;oBAC7C,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CACpC,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,OAAO,CACR,CAAC;oBACF,oBAAoB,CAAC,IAAI,CAAC,YAAa,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,kFAAkF;YAClF,IAAI,gBAAgB,CAAC;YACrB,IAAI,GAAG,GAAG,YAAY,CAAC;YACvB,sCAAsC;YACtC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,GAAG;oBAAE,MAAM;gBAChB,KAAK,EAAE,CAAC;gBACR,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;YACD,6FAA6F;YAC7F,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBACzC,GAAG,GAAG,SAAS,CAAC;YAClB,CAAC;YACD,gBAAgB,GAAG,GAAG,CAAC;YACvB,MAAM,wBAAwB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;YACtE,sEAAsE;YACtE,yEAAyE;YACzE,kFAAkF;YAClF,kFAAkF;YAClF,gGAAgG;YAChG,8FAA8F;YAC9F,qFAAqF;YACrF,mFAAmF;YACnF,sFAAsF;YACtF,IACE,wBAAwB;gBACxB,GAAG,KAAK,wBAAwB,CAAC,UAAU;gBAC3C,CAAC,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,IAAI,CAAC,EAC7D,CAAC;gBACD,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC;YAC3C,CAAC;YAED,IAAI,eAAe,CAAC;YACpB,SAAS;YACT,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,KAAK,EAAE,EAAE,CAAC;gBAC9E,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM,aAAa,GAAG,UAAU,CAAC,WAAW,CAC1C,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,yFAAyF;gBACzF,6CAA6C;gBAC7C,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC;wBAC5D,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;gBACD,QAAQ,GAAG,aAAa,CAAC;YAC3B,CAAC;iBAAM,IAAI,gBAAgB,KAAK,SAAS,IAAI,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACvF,eAAe,GAAG,UAAU,CAAC,WAAW,CACtC,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;gBACF,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACpE,IAAI,0BAA0B,EAAE,CAAC;QAC/B,MAAM,oBAAoB,GAAG,CAAC,gBAAwB,EAAW,EAAE;YACjE,KAAK,MAAM,cAAc,IAAI,UAAU,EAAE,CAAC;gBACxC,MAAM,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5E,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC;oBAClC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,KAAK,MAAM,gBAAgB,IAAI,YAAY,EAAE,CAAC;YAC5C,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC3C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,WAAW,CACjD,0BAA0B,EAC1B,YAAY,CAAC,gBAAgB,CAAC,EAC9B,UAAU,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI,EAC3C,OAAO,CACR,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;SAAM,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;QAC5D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,IACE,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS;gBAC3B,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACnC,CAAC,oBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC,CAAC;gBACD,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAsB,EACtB,MAAwB,EACxB,YAAiB,EACjB,UAAkB,EAClB,OAAkC;IAElC,4BAA4B;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,KAAK,CACb,2DAA2D;YACzD,0CAA0C,UAAU,EAAE,CACzD,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,cAAc,GAA2B,EAAE,CAAC;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC9F,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,uBAAuB,CAC9B,UAAsB,EACtB,MAAsB,EACtB,YAAiB,EACjB,UAAkB,EAClB,OAAkC;;IAElC,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;IAClC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,MAAM,IAAI,KAAK,CACb,wDAAwD;YACtD,0CAA0C,UAAU,EAAE,CACzD,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,+FAA+F;YAC/F,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,yDAAyD;QACzD,wDAAwD;QACxD,uDAAuD;QACvD,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAChE,OAAO,GAAG,MAAA,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,mCAAI,OAAO,CAAC;QACvE,CAAC;QAED,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,WAAW,CACnC,OAAO,EACP,YAAY,CAAC,CAAC,CAAC,EACf,GAAG,UAAU,IAAI,CAAC,GAAG,EACrB,OAAO,CACR,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,qBAAqB,CAC5B,cAA+C,EAC/C,kBAA0B,EAC1B,QAAgB;IAEhB,MAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,OAAO,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC/B,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC7C,MAAM,kBAAkB,GACtB,kBAAkB,KAAK,WAAW;YAChC,CAAC,CAAC,kBAAkB;YACpB,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,kBAAkB,CAAC;QAC7C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,EAAE,CAAC;YAC7E,OAAO,cAAc,CAAC,kBAAkB,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5D,IACE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;oBAClC,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,WAAW;oBACtC,MAAM,CAAC,IAAI,CAAC,SAAS,EACrB,CAAC;oBACD,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,MAAuB,EACvB,MAAW,EACX,uBAAwD;;IAExD,MAAM,wBAAwB,GAAG,sCAAsC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAE5F,IAAI,wBAAwB,EAAE,CAAC;QAC7B,IAAI,iBAAiB,GAAG,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;QAC1E,IAAI,iBAAiB,EAAE,CAAC;YACtB,iEAAiE;YACjE,IAAI,uBAAuB,KAAK,gBAAgB,EAAE,CAAC;gBACjD,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC5D,CAAC;YACD,MAAM,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,MAAA,MAAM,CAAC,IAAI,CAAC,UAAU,mCAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAEjE,IAAI,OAAO,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBACvD,MAAM,iBAAiB,GAAG,qBAAqB,CAC7C,UAAU,CAAC,YAAY,CAAC,cAAc,EACtC,kBAAkB,EAClB,QAAQ,CACT,CAAC;gBACF,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,GAAG,iBAAiB,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,UAAsB,EACtB,MAAuB;IAEvB,OAAO,CACL,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACpC,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACrE,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACrE,CAAC;AACJ,CAAC;AAED,SAAS,iCAAiC,CACxC,UAAsB,EACtB,QAAiB;IAEjB,OAAO,CACL,QAAQ;QACR,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAChE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,SAAS;IAClB,SAAS,EAAE,WAAW;IACtB,SAAS,EAAE,WAAW;IACtB,IAAI,EAAE,MAAM;IACZ,QAAQ,EAAE,UAAU;IACpB,eAAe,EAAE,iBAAiB;IAClC,UAAU,EAAE,YAAY;IACxB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;CACZ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as base64 from \"./base64.js\";\nimport type {\n  BaseMapper,\n  CompositeMapper,\n  DictionaryMapper,\n  EnumMapper,\n  Mapper,\n  MapperConstraints,\n  PolymorphicDiscriminator,\n  RequiredSerializerOptions,\n  SequenceMapper,\n  Serializer,\n  SerializerOptions,\n} from \"./interfaces.js\";\nimport { XML_ATTRKEY, XML_CHARKEY } from \"./interfaces.js\";\nimport { isDuration, isValidUuid } from \"./utils.js\";\n\nclass SerializerImpl implements Serializer {\n  constructor(\n    public readonly modelMappers: { [key: string]: any } = {},\n    public readonly isXML: boolean = false,\n  ) {}\n\n  /**\n   * @deprecated Removing the constraints validation on client side.\n   */\n  validateConstraints(mapper: Mapper, value: any, objectName: string): void {\n    const failValidation = (\n      constraintName: keyof MapperConstraints,\n      constraintValue: any,\n    ): never => {\n      throw new Error(\n        `\"${objectName}\" with value \"${value}\" should satisfy the constraint \"${constraintName}\": ${constraintValue}.`,\n      );\n    };\n    if (mapper.constraints && value !== undefined && value !== null) {\n      const {\n        ExclusiveMaximum,\n        ExclusiveMinimum,\n        InclusiveMaximum,\n        InclusiveMinimum,\n        MaxItems,\n        MaxLength,\n        MinItems,\n        MinLength,\n        MultipleOf,\n        Pattern,\n        UniqueItems,\n      } = mapper.constraints;\n      if (ExclusiveMaximum !== undefined && value >= ExclusiveMaximum) {\n        failValidation(\"ExclusiveMaximum\", ExclusiveMaximum);\n      }\n      if (ExclusiveMinimum !== undefined && value <= ExclusiveMinimum) {\n        failValidation(\"ExclusiveMinimum\", ExclusiveMinimum);\n      }\n      if (InclusiveMaximum !== undefined && value > InclusiveMaximum) {\n        failValidation(\"InclusiveMaximum\", InclusiveMaximum);\n      }\n      if (InclusiveMinimum !== undefined && value < InclusiveMinimum) {\n        failValidation(\"InclusiveMinimum\", InclusiveMinimum);\n      }\n      if (MaxItems !== undefined && value.length > MaxItems) {\n        failValidation(\"MaxItems\", MaxItems);\n      }\n      if (MaxLength !== undefined && value.length > MaxLength) {\n        failValidation(\"MaxLength\", MaxLength);\n      }\n      if (MinItems !== undefined && value.length < MinItems) {\n        failValidation(\"MinItems\", MinItems);\n      }\n      if (MinLength !== undefined && value.length < MinLength) {\n        failValidation(\"MinLength\", MinLength);\n      }\n      if (MultipleOf !== undefined && value % MultipleOf !== 0) {\n        failValidation(\"MultipleOf\", MultipleOf);\n      }\n      if (Pattern) {\n        const pattern: RegExp = typeof Pattern === \"string\" ? new RegExp(Pattern) : Pattern;\n        if (typeof value !== \"string\" || value.match(pattern) === null) {\n          failValidation(\"Pattern\", Pattern);\n        }\n      }\n      if (\n        UniqueItems &&\n        value.some((item: any, i: number, ar: Array<any>) => ar.indexOf(item) !== i)\n      ) {\n        failValidation(\"UniqueItems\", UniqueItems);\n      }\n    }\n  }\n\n  /**\n   * Serialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   *\n   * @param object - A valid Javascript object to be serialized\n   *\n   * @param objectName - Name of the serialized object\n   *\n   * @param options - additional options to serialization\n   *\n   * @returns A valid serialized Javascript object\n   */\n  serialize(\n    mapper: Mapper,\n    object: any,\n    objectName?: string,\n    options: SerializerOptions = { xml: {} },\n  ): any {\n    const updatedOptions: RequiredSerializerOptions = {\n      xml: {\n        rootName: options.xml.rootName ?? \"\",\n        includeRoot: options.xml.includeRoot ?? false,\n        xmlCharKey: options.xml.xmlCharKey ?? XML_CHARKEY,\n      },\n    };\n    let payload: any = {};\n    const mapperType = mapper.type.name as string;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n    if (mapperType.match(/^Sequence$/i) !== null) {\n      payload = [];\n    }\n\n    if (mapper.isConstant) {\n      object = mapper.defaultValue;\n    }\n\n    // This table of allowed values should help explain\n    // the mapper.required and mapper.nullable properties.\n    // X means \"neither undefined or null are allowed\".\n    //           || required\n    //           || true      | false\n    //  nullable || ==========================\n    //      true || null      | undefined/null\n    //     false || X         | undefined\n    // undefined || X         | undefined/null\n\n    const { required, nullable } = mapper;\n\n    if (required && nullable && object === undefined) {\n      throw new Error(`${objectName} cannot be undefined.`);\n    }\n    if (required && !nullable && (object === undefined || object === null)) {\n      throw new Error(`${objectName} cannot be null or undefined.`);\n    }\n    if (!required && nullable === false && object === null) {\n      throw new Error(`${objectName} cannot be null.`);\n    }\n\n    if (object === undefined || object === null) {\n      payload = object;\n    } else {\n      if (mapperType.match(/^any$/i) !== null) {\n        payload = object;\n      } else if (mapperType.match(/^(Number|String|Boolean|Object|Stream|Uuid)$/i) !== null) {\n        payload = serializeBasicTypes(mapperType, objectName, object);\n      } else if (mapperType.match(/^Enum$/i) !== null) {\n        const enumMapper = mapper as EnumMapper;\n        payload = serializeEnumType(objectName, enumMapper.type.allowedValues, object);\n      } else if (\n        mapperType.match(/^(Date|DateTime|TimeSpan|DateTimeRfc1123|UnixTime)$/i) !== null\n      ) {\n        payload = serializeDateTypes(mapperType, object, objectName);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = serializeByteArrayType(objectName, object);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = serializeBase64UrlType(objectName, object);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = serializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions,\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = serializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions,\n        );\n      } else if (mapperType.match(/^Composite$/i) !== null) {\n        payload = serializeCompositeType(\n          this,\n          mapper as CompositeMapper,\n          object,\n          objectName,\n          Boolean(this.isXML),\n          updatedOptions,\n        );\n      }\n    }\n    return payload;\n  }\n\n  /**\n   * Deserialize the given object based on its metadata defined in the mapper\n   *\n   * @param mapper - The mapper which defines the metadata of the serializable object\n   *\n   * @param responseBody - A valid Javascript entity to be deserialized\n   *\n   * @param objectName - Name of the deserialized object\n   *\n   * @param options - Controls behavior of XML parser and builder.\n   *\n   * @returns A valid deserialized Javascript object\n   */\n  deserialize(\n    mapper: Mapper,\n    responseBody: any,\n    objectName: string,\n    options: SerializerOptions = { xml: {} },\n  ): any {\n    const updatedOptions: RequiredSerializerOptions = {\n      xml: {\n        rootName: options.xml.rootName ?? \"\",\n        includeRoot: options.xml.includeRoot ?? false,\n        xmlCharKey: options.xml.xmlCharKey ?? XML_CHARKEY,\n      },\n      ignoreUnknownProperties: options.ignoreUnknownProperties ?? false,\n    };\n    if (responseBody === undefined || responseBody === null) {\n      if (this.isXML && mapper.type.name === \"Sequence\" && !mapper.xmlIsWrapped) {\n        // Edge case for empty XML non-wrapped lists. xml2js can't distinguish\n        // between the list being empty versus being missing,\n        // so let's do the more user-friendly thing and return an empty list.\n        responseBody = [];\n      }\n      // specifically check for undefined as default value can be a falsey value `0, \"\", false, null`\n      if (mapper.defaultValue !== undefined) {\n        responseBody = mapper.defaultValue;\n      }\n      return responseBody;\n    }\n\n    let payload: any;\n    const mapperType = mapper.type.name;\n    if (!objectName) {\n      objectName = mapper.serializedName!;\n    }\n\n    if (mapperType.match(/^Composite$/i) !== null) {\n      payload = deserializeCompositeType(\n        this,\n        mapper as CompositeMapper,\n        responseBody,\n        objectName,\n        updatedOptions,\n      );\n    } else {\n      if (this.isXML) {\n        const xmlCharKey = updatedOptions.xml.xmlCharKey;\n        /**\n         * If the mapper specifies this as a non-composite type value but the responseBody contains\n         * both header (\"$\" i.e., XML_ATTRKEY) and body (\"#\" i.e., XML_CHARKEY) properties,\n         * then just reduce the responseBody value to the body (\"#\" i.e., XML_CHARKEY) property.\n         */\n        if (responseBody[XML_ATTRKEY] !== undefined && responseBody[xmlCharKey] !== undefined) {\n          responseBody = responseBody[xmlCharKey];\n        }\n      }\n\n      if (mapperType.match(/^Number$/i) !== null) {\n        payload = parseFloat(responseBody);\n        if (isNaN(payload)) {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^Boolean$/i) !== null) {\n        if (responseBody === \"true\") {\n          payload = true;\n        } else if (responseBody === \"false\") {\n          payload = false;\n        } else {\n          payload = responseBody;\n        }\n      } else if (mapperType.match(/^(String|Enum|Object|Stream|Uuid|TimeSpan|any)$/i) !== null) {\n        payload = responseBody;\n      } else if (mapperType.match(/^(Date|DateTime|DateTimeRfc1123)$/i) !== null) {\n        payload = new Date(responseBody);\n      } else if (mapperType.match(/^UnixTime$/i) !== null) {\n        payload = unixTimeToDate(responseBody);\n      } else if (mapperType.match(/^ByteArray$/i) !== null) {\n        payload = base64.decodeString(responseBody);\n      } else if (mapperType.match(/^Base64Url$/i) !== null) {\n        payload = base64UrlToByteArray(responseBody);\n      } else if (mapperType.match(/^Sequence$/i) !== null) {\n        payload = deserializeSequenceType(\n          this,\n          mapper as SequenceMapper,\n          responseBody,\n          objectName,\n          updatedOptions,\n        );\n      } else if (mapperType.match(/^Dictionary$/i) !== null) {\n        payload = deserializeDictionaryType(\n          this,\n          mapper as DictionaryMapper,\n          responseBody,\n          objectName,\n          updatedOptions,\n        );\n      }\n    }\n\n    if (mapper.isConstant) {\n      payload = mapper.defaultValue;\n    }\n\n    return payload;\n  }\n}\n\n/**\n * Method that creates and returns a Serializer.\n * @param modelMappers - Known models to map\n * @param isXML - If XML should be supported\n */\nexport function createSerializer(\n  modelMappers: { [key: string]: any } = {},\n  isXML: boolean = false,\n): Serializer {\n  return new SerializerImpl(modelMappers, isXML);\n}\n\nfunction trimEnd(str: string, ch: string): string {\n  let len = str.length;\n  while (len - 1 >= 0 && str[len - 1] === ch) {\n    --len;\n  }\n  return str.substr(0, len);\n}\n\nfunction bufferToBase64Url(buffer: Uint8Array): string | undefined {\n  if (!buffer) {\n    return undefined;\n  }\n  if (!(buffer instanceof Uint8Array)) {\n    throw new Error(`Please provide an input of type Uint8Array for converting to Base64Url.`);\n  }\n  // Uint8Array to Base64.\n  const str = base64.encodeByteArray(buffer);\n  // Base64 to Base64Url.\n  return trimEnd(str, \"=\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n}\n\nfunction base64UrlToByteArray(str: string): Uint8Array | undefined {\n  if (!str) {\n    return undefined;\n  }\n  if (str && typeof str.valueOf() !== \"string\") {\n    throw new Error(\"Please provide an input of type string for converting to Uint8Array\");\n  }\n  // Base64Url to Base64.\n  str = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Base64 to Uint8Array.\n  return base64.decodeString(str);\n}\n\nfunction splitSerializeName(prop: string | undefined): string[] {\n  const classes: string[] = [];\n  let partialclass = \"\";\n  if (prop) {\n    const subwords = prop.split(\".\");\n\n    for (const item of subwords) {\n      if (item.charAt(item.length - 1) === \"\\\\\") {\n        partialclass += item.substr(0, item.length - 1) + \".\";\n      } else {\n        partialclass += item;\n        classes.push(partialclass);\n        partialclass = \"\";\n      }\n    }\n  }\n\n  return classes;\n}\n\nfunction dateToUnixTime(d: string | Date): number | undefined {\n  if (!d) {\n    return undefined;\n  }\n\n  if (typeof d.valueOf() === \"string\") {\n    d = new Date(d as string);\n  }\n  return Math.floor((d as Date).getTime() / 1000);\n}\n\nfunction unixTimeToDate(n: number): Date | undefined {\n  if (!n) {\n    return undefined;\n  }\n  return new Date(n * 1000);\n}\n\nfunction serializeBasicTypes(typeName: string, objectName: string, value: any): any {\n  if (value !== null && value !== undefined) {\n    if (typeName.match(/^Number$/i) !== null) {\n      if (typeof value !== \"number\") {\n        throw new Error(`${objectName} with value ${value} must be of type number.`);\n      }\n    } else if (typeName.match(/^String$/i) !== null) {\n      if (typeof value.valueOf() !== \"string\") {\n        throw new Error(`${objectName} with value \"${value}\" must be of type string.`);\n      }\n    } else if (typeName.match(/^Uuid$/i) !== null) {\n      if (!(typeof value.valueOf() === \"string\" && isValidUuid(value))) {\n        throw new Error(\n          `${objectName} with value \"${value}\" must be of type string and a valid uuid.`,\n        );\n      }\n    } else if (typeName.match(/^Boolean$/i) !== null) {\n      if (typeof value !== \"boolean\") {\n        throw new Error(`${objectName} with value ${value} must be of type boolean.`);\n      }\n    } else if (typeName.match(/^Stream$/i) !== null) {\n      const objectType = typeof value;\n      if (\n        objectType !== \"string\" &&\n        typeof value.pipe !== \"function\" && // NodeJS.ReadableStream\n        typeof value.tee !== \"function\" && // browser ReadableStream\n        !(value instanceof ArrayBuffer) &&\n        !ArrayBuffer.isView(value) &&\n        // File objects count as a type of Blob, so we want to use instanceof explicitly\n        !((typeof Blob === \"function\" || typeof Blob === \"object\") && value instanceof Blob) &&\n        objectType !== \"function\"\n      ) {\n        throw new Error(\n          `${objectName} must be a string, Blob, ArrayBuffer, ArrayBufferView, ReadableStream, or () => ReadableStream.`,\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeEnumType(objectName: string, allowedValues: Array<any>, value: any): any {\n  if (!allowedValues) {\n    throw new Error(\n      `Please provide a set of allowedValues to validate ${objectName} as an Enum Type.`,\n    );\n  }\n  const isPresent = allowedValues.some((item) => {\n    if (typeof item.valueOf() === \"string\") {\n      return item.toLowerCase() === value.toLowerCase();\n    }\n    return item === value;\n  });\n  if (!isPresent) {\n    throw new Error(\n      `${value} is not a valid value for ${objectName}. The valid values are: ${JSON.stringify(\n        allowedValues,\n      )}.`,\n    );\n  }\n  return value;\n}\n\nfunction serializeByteArrayType(objectName: string, value: any): any {\n  if (value !== undefined && value !== null) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    value = base64.encodeByteArray(value);\n  }\n  return value;\n}\n\nfunction serializeBase64UrlType(objectName: string, value: any): any {\n  if (value !== undefined && value !== null) {\n    if (!(value instanceof Uint8Array)) {\n      throw new Error(`${objectName} must be of type Uint8Array.`);\n    }\n    value = bufferToBase64Url(value);\n  }\n  return value;\n}\n\nfunction serializeDateTypes(typeName: string, value: any, objectName: string): any {\n  if (value !== undefined && value !== null) {\n    if (typeName.match(/^Date$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value =\n        value instanceof Date\n          ? value.toISOString().substring(0, 10)\n          : new Date(value).toISOString().substring(0, 10);\n    } else if (typeName.match(/^DateTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in ISO8601 format.`);\n      }\n      value = value instanceof Date ? value.toISOString() : new Date(value).toISOString();\n    } else if (typeName.match(/^DateTimeRfc1123$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(`${objectName} must be an instanceof Date or a string in RFC-1123 format.`);\n      }\n      value = value instanceof Date ? value.toUTCString() : new Date(value).toUTCString();\n    } else if (typeName.match(/^UnixTime$/i) !== null) {\n      if (\n        !(\n          value instanceof Date ||\n          (typeof value.valueOf() === \"string\" && !isNaN(Date.parse(value)))\n        )\n      ) {\n        throw new Error(\n          `${objectName} must be an instanceof Date or a string in RFC-1123/ISO8601 format ` +\n            `for it to be serialized in UnixTime/Epoch format.`,\n        );\n      }\n      value = dateToUnixTime(value);\n    } else if (typeName.match(/^TimeSpan$/i) !== null) {\n      if (!isDuration(value)) {\n        throw new Error(\n          `${objectName} must be a string in ISO 8601 format. Instead was \"${value}\".`,\n        );\n      }\n    }\n  }\n  return value;\n}\n\nfunction serializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions,\n): any {\n  if (!Array.isArray(object)) {\n    throw new Error(`${objectName} must be of type Array.`);\n  }\n  let elementType = mapper.type.element;\n  if (!elementType || typeof elementType !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`,\n    );\n  }\n  // Quirk: Composite mappers referenced by `element` might\n  // not have *all* properties declared (like uberParent),\n  // so let's try to look up the full definition by name.\n  if (elementType.type.name === \"Composite\" && elementType.type.className) {\n    elementType = serializer.modelMappers[elementType.type.className] ?? elementType;\n  }\n  const tempArray = [];\n  for (let i = 0; i < object.length; i++) {\n    const serializedValue = serializer.serialize(elementType, object[i], objectName, options);\n    if (isXml && elementType.xmlNamespace) {\n      const xmlnsKey = elementType.xmlNamespacePrefix\n        ? `xmlns:${elementType.xmlNamespacePrefix}`\n        : \"xmlns\";\n      if (elementType.type.name === \"Composite\") {\n        tempArray[i] = { ...serializedValue };\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      } else {\n        tempArray[i] = {};\n        tempArray[i][options.xml.xmlCharKey] = serializedValue;\n        tempArray[i][XML_ATTRKEY] = { [xmlnsKey]: elementType.xmlNamespace };\n      }\n    } else {\n      tempArray[i] = serializedValue;\n    }\n  }\n  return tempArray;\n}\n\nfunction serializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions,\n): any {\n  if (typeof object !== \"object\") {\n    throw new Error(`${objectName} must be of type object.`);\n  }\n  const valueType = mapper.type.value;\n  if (!valueType || typeof valueType !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}.`,\n    );\n  }\n  const tempDictionary: { [key: string]: any } = {};\n  for (const key of Object.keys(object)) {\n    const serializedValue = serializer.serialize(valueType, object[key], objectName, options);\n    // If the element needs an XML namespace we need to add it within the $ property\n    tempDictionary[key] = getXmlObjectValue(valueType, serializedValue, isXml, options);\n  }\n\n  // Add the namespace to the root element if needed\n  if (isXml && mapper.xmlNamespace) {\n    const xmlnsKey = mapper.xmlNamespacePrefix ? `xmlns:${mapper.xmlNamespacePrefix}` : \"xmlns\";\n    const result = tempDictionary;\n    result[XML_ATTRKEY] = { [xmlnsKey]: mapper.xmlNamespace };\n    return result;\n  }\n\n  return tempDictionary;\n}\n\n/**\n * Resolves the additionalProperties property from a referenced mapper\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n * @param objectName - name of the object being serialized\n */\nfunction resolveAdditionalProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string,\n): SequenceMapper | BaseMapper | CompositeMapper | DictionaryMapper | EnumMapper | undefined {\n  const additionalProperties = mapper.type.additionalProperties;\n\n  if (!additionalProperties && mapper.type.className) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    return modelMapper?.type.additionalProperties;\n  }\n\n  return additionalProperties;\n}\n\n/**\n * Finds the mapper referenced by className\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n * @param objectName - name of the object being serialized\n */\nfunction resolveReferencedMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string,\n): CompositeMapper | undefined {\n  const className = mapper.type.className;\n  if (!className) {\n    throw new Error(\n      `Class name for model \"${objectName}\" is not provided in the mapper \"${JSON.stringify(\n        mapper,\n        undefined,\n        2,\n      )}\".`,\n    );\n  }\n\n  return serializer.modelMappers[className];\n}\n\n/**\n * Resolves a composite mapper's modelProperties.\n * @param serializer - the serializer containing the entire set of mappers\n * @param mapper - the composite mapper to resolve\n */\nfunction resolveModelProperties(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  objectName: string,\n): { [propertyName: string]: Mapper } {\n  let modelProps = mapper.type.modelProperties;\n  if (!modelProps) {\n    const modelMapper = resolveReferencedMapper(serializer, mapper, objectName);\n    if (!modelMapper) {\n      throw new Error(`mapper() cannot be null or undefined for model \"${mapper.type.className}\".`);\n    }\n    modelProps = modelMapper?.type.modelProperties;\n    if (!modelProps) {\n      throw new Error(\n        `modelProperties cannot be null or undefined in the ` +\n          `mapper \"${JSON.stringify(modelMapper)}\" of type \"${\n            mapper.type.className\n          }\" for object \"${objectName}\".`,\n      );\n    }\n  }\n\n  return modelProps;\n}\n\nfunction serializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  objectName: string,\n  isXml: boolean,\n  options: RequiredSerializerOptions,\n): any {\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, object, \"clientName\");\n  }\n\n  if (object !== undefined && object !== null) {\n    const payload: any = {};\n    const modelProps = resolveModelProperties(serializer, mapper, objectName);\n    for (const key of Object.keys(modelProps)) {\n      const propertyMapper = modelProps[key];\n      if (propertyMapper.readOnly) {\n        continue;\n      }\n\n      let propName: string | undefined;\n      let parentObject: any = payload;\n      if (serializer.isXML) {\n        if (propertyMapper.xmlIsWrapped) {\n          propName = propertyMapper.xmlName;\n        } else {\n          propName = propertyMapper.xmlElementName || propertyMapper.xmlName;\n        }\n      } else {\n        const paths = splitSerializeName(propertyMapper.serializedName!);\n        propName = paths.pop();\n\n        for (const pathName of paths) {\n          const childObject = parentObject[pathName];\n          if (\n            (childObject === undefined || childObject === null) &&\n            ((object[key] !== undefined && object[key] !== null) ||\n              propertyMapper.defaultValue !== undefined)\n          ) {\n            parentObject[pathName] = {};\n          }\n          parentObject = parentObject[pathName];\n        }\n      }\n\n      if (parentObject !== undefined && parentObject !== null) {\n        if (isXml && mapper.xmlNamespace) {\n          const xmlnsKey = mapper.xmlNamespacePrefix\n            ? `xmlns:${mapper.xmlNamespacePrefix}`\n            : \"xmlns\";\n          parentObject[XML_ATTRKEY] = {\n            ...parentObject[XML_ATTRKEY],\n            [xmlnsKey]: mapper.xmlNamespace,\n          };\n        }\n        const propertyObjectName =\n          propertyMapper.serializedName !== \"\"\n            ? objectName + \".\" + propertyMapper.serializedName\n            : objectName;\n\n        let toSerialize = object[key];\n        const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n        if (\n          polymorphicDiscriminator &&\n          polymorphicDiscriminator.clientName === key &&\n          (toSerialize === undefined || toSerialize === null)\n        ) {\n          toSerialize = mapper.serializedName;\n        }\n\n        const serializedValue = serializer.serialize(\n          propertyMapper,\n          toSerialize,\n          propertyObjectName,\n          options,\n        );\n        if (serializedValue !== undefined && propName !== undefined && propName !== null) {\n          const value = getXmlObjectValue(propertyMapper, serializedValue, isXml, options);\n          if (isXml && propertyMapper.xmlIsAttribute) {\n            // XML_ATTRKEY, i.e., $ is the key attributes are kept under in xml2js.\n            // This keeps things simple while preventing name collision\n            // with names in user documents.\n            parentObject[XML_ATTRKEY] = parentObject[XML_ATTRKEY] || {};\n            parentObject[XML_ATTRKEY][propName] = serializedValue;\n          } else if (isXml && propertyMapper.xmlIsWrapped) {\n            parentObject[propName] = { [propertyMapper.xmlElementName!]: value };\n          } else {\n            parentObject[propName] = value;\n          }\n        }\n      }\n    }\n\n    const additionalPropertiesMapper = resolveAdditionalProperties(serializer, mapper, objectName);\n    if (additionalPropertiesMapper) {\n      const propNames = Object.keys(modelProps);\n      for (const clientPropName in object) {\n        const isAdditionalProperty = propNames.every((pn) => pn !== clientPropName);\n        if (isAdditionalProperty) {\n          payload[clientPropName] = serializer.serialize(\n            additionalPropertiesMapper,\n            object[clientPropName],\n            objectName + '[\"' + clientPropName + '\"]',\n            options,\n          );\n        }\n      }\n    }\n\n    return payload;\n  }\n  return object;\n}\n\nfunction getXmlObjectValue(\n  propertyMapper: Mapper,\n  serializedValue: any,\n  isXml: boolean,\n  options: RequiredSerializerOptions,\n): any {\n  if (!isXml || !propertyMapper.xmlNamespace) {\n    return serializedValue;\n  }\n\n  const xmlnsKey = propertyMapper.xmlNamespacePrefix\n    ? `xmlns:${propertyMapper.xmlNamespacePrefix}`\n    : \"xmlns\";\n  const xmlNamespace = { [xmlnsKey]: propertyMapper.xmlNamespace };\n\n  if ([\"Composite\"].includes(propertyMapper.type.name)) {\n    if (serializedValue[XML_ATTRKEY]) {\n      return serializedValue;\n    } else {\n      const result: any = { ...serializedValue };\n      result[XML_ATTRKEY] = xmlNamespace;\n      return result;\n    }\n  }\n  const result: any = {};\n  result[options.xml.xmlCharKey] = serializedValue;\n  result[XML_ATTRKEY] = xmlNamespace;\n  return result;\n}\n\nfunction isSpecialXmlProperty(propertyName: string, options: RequiredSerializerOptions): boolean {\n  return [XML_ATTRKEY, options.xml.xmlCharKey].includes(propertyName);\n}\n\nfunction deserializeCompositeType(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions,\n): any {\n  const xmlCharKey = options.xml.xmlCharKey ?? XML_CHARKEY;\n  if (getPolymorphicDiscriminatorRecursively(serializer, mapper)) {\n    mapper = getPolymorphicMapper(serializer, mapper, responseBody, \"serializedName\");\n  }\n\n  const modelProps = resolveModelProperties(serializer, mapper, objectName);\n  let instance: { [key: string]: any } = {};\n  const handledPropertyNames: string[] = [];\n\n  for (const key of Object.keys(modelProps)) {\n    const propertyMapper = modelProps[key];\n    const paths = splitSerializeName(modelProps[key].serializedName!);\n    handledPropertyNames.push(paths[0]);\n    const { serializedName, xmlName, xmlElementName } = propertyMapper;\n    let propertyObjectName = objectName;\n    if (serializedName !== \"\" && serializedName !== undefined) {\n      propertyObjectName = objectName + \".\" + serializedName;\n    }\n\n    const headerCollectionPrefix = (propertyMapper as DictionaryMapper).headerCollectionPrefix;\n    if (headerCollectionPrefix) {\n      const dictionary: any = {};\n      for (const headerKey of Object.keys(responseBody)) {\n        if (headerKey.startsWith(headerCollectionPrefix)) {\n          dictionary[headerKey.substring(headerCollectionPrefix.length)] = serializer.deserialize(\n            (propertyMapper as DictionaryMapper).type.value,\n            responseBody[headerKey],\n            propertyObjectName,\n            options,\n          );\n        }\n\n        handledPropertyNames.push(headerKey);\n      }\n      instance[key] = dictionary;\n    } else if (serializer.isXML) {\n      if (propertyMapper.xmlIsAttribute && responseBody[XML_ATTRKEY]) {\n        instance[key] = serializer.deserialize(\n          propertyMapper,\n          responseBody[XML_ATTRKEY][xmlName!],\n          propertyObjectName,\n          options,\n        );\n      } else if (propertyMapper.xmlIsMsText) {\n        if (responseBody[xmlCharKey] !== undefined) {\n          instance[key] = responseBody[xmlCharKey];\n        } else if (typeof responseBody === \"string\") {\n          // The special case where xml parser parses \"<Name>content</Name>\" into JSON of\n          //   `{ name: \"content\"}` instead of `{ name: { \"_\": \"content\" }}`\n          instance[key] = responseBody;\n        }\n      } else {\n        const propertyName = xmlElementName || xmlName || serializedName;\n        if (propertyMapper.xmlIsWrapped) {\n          /* a list of <xmlElementName> wrapped by <xmlName>\n            For the xml example below\n              <Cors>\n                <CorsRule>...</CorsRule>\n                <CorsRule>...</CorsRule>\n              </Cors>\n            the responseBody has\n              {\n                Cors: {\n                  CorsRule: [{...}, {...}]\n                }\n              }\n            xmlName is \"Cors\" and xmlElementName is\"CorsRule\".\n          */\n          const wrapped = responseBody[xmlName!];\n          const elementList = wrapped?.[xmlElementName!] ?? [];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            elementList,\n            propertyObjectName,\n            options,\n          );\n          handledPropertyNames.push(xmlName!);\n        } else {\n          const property = responseBody[propertyName!];\n          instance[key] = serializer.deserialize(\n            propertyMapper,\n            property,\n            propertyObjectName,\n            options,\n          );\n          handledPropertyNames.push(propertyName!);\n        }\n      }\n    } else {\n      // deserialize the property if it is present in the provided responseBody instance\n      let propertyInstance;\n      let res = responseBody;\n      // traversing the object step by step.\n      let steps = 0;\n      for (const item of paths) {\n        if (!res) break;\n        steps++;\n        res = res[item];\n      }\n      // only accept null when reaching the last position of object otherwise it would be undefined\n      if (res === null && steps < paths.length) {\n        res = undefined;\n      }\n      propertyInstance = res;\n      const polymorphicDiscriminator = mapper.type.polymorphicDiscriminator;\n      // checking that the model property name (key)(ex: \"fishtype\") and the\n      // clientName of the polymorphicDiscriminator {metadata} (ex: \"fishtype\")\n      // instead of the serializedName of the polymorphicDiscriminator (ex: \"fish.type\")\n      // is a better approach. The generator is not consistent with escaping '\\.' in the\n      // serializedName of the property (ex: \"fish\\.type\") that is marked as polymorphic discriminator\n      // and the serializedName of the metadata polymorphicDiscriminator (ex: \"fish.type\"). However,\n      // the clientName transformation of the polymorphicDiscriminator (ex: \"fishtype\") and\n      // the transformation of model property name (ex: \"fishtype\") is done consistently.\n      // Hence, it is a safer bet to rely on the clientName of the polymorphicDiscriminator.\n      if (\n        polymorphicDiscriminator &&\n        key === polymorphicDiscriminator.clientName &&\n        (propertyInstance === undefined || propertyInstance === null)\n      ) {\n        propertyInstance = mapper.serializedName;\n      }\n\n      let serializedValue;\n      // paging\n      if (Array.isArray(responseBody[key]) && modelProps[key].serializedName === \"\") {\n        propertyInstance = responseBody[key];\n        const arrayInstance = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options,\n        );\n        // Copy over any properties that have already been added into the instance, where they do\n        // not exist on the newly de-serialized array\n        for (const [k, v] of Object.entries(instance)) {\n          if (!Object.prototype.hasOwnProperty.call(arrayInstance, k)) {\n            arrayInstance[k] = v;\n          }\n        }\n        instance = arrayInstance;\n      } else if (propertyInstance !== undefined || propertyMapper.defaultValue !== undefined) {\n        serializedValue = serializer.deserialize(\n          propertyMapper,\n          propertyInstance,\n          propertyObjectName,\n          options,\n        );\n        instance[key] = serializedValue;\n      }\n    }\n  }\n\n  const additionalPropertiesMapper = mapper.type.additionalProperties;\n  if (additionalPropertiesMapper) {\n    const isAdditionalProperty = (responsePropName: string): boolean => {\n      for (const clientPropName in modelProps) {\n        const paths = splitSerializeName(modelProps[clientPropName].serializedName);\n        if (paths[0] === responsePropName) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    for (const responsePropName in responseBody) {\n      if (isAdditionalProperty(responsePropName)) {\n        instance[responsePropName] = serializer.deserialize(\n          additionalPropertiesMapper,\n          responseBody[responsePropName],\n          objectName + '[\"' + responsePropName + '\"]',\n          options,\n        );\n      }\n    }\n  } else if (responseBody && !options.ignoreUnknownProperties) {\n    for (const key of Object.keys(responseBody)) {\n      if (\n        instance[key] === undefined &&\n        !handledPropertyNames.includes(key) &&\n        !isSpecialXmlProperty(key, options)\n      ) {\n        instance[key] = responseBody[key];\n      }\n    }\n  }\n\n  return instance;\n}\n\nfunction deserializeDictionaryType(\n  serializer: Serializer,\n  mapper: DictionaryMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions,\n): any {\n  /* jshint validthis: true */\n  const value = mapper.type.value;\n  if (!value || typeof value !== \"object\") {\n    throw new Error(\n      `\"value\" metadata for a Dictionary must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`,\n    );\n  }\n  if (responseBody) {\n    const tempDictionary: { [key: string]: any } = {};\n    for (const key of Object.keys(responseBody)) {\n      tempDictionary[key] = serializer.deserialize(value, responseBody[key], objectName, options);\n    }\n    return tempDictionary;\n  }\n  return responseBody;\n}\n\nfunction deserializeSequenceType(\n  serializer: Serializer,\n  mapper: SequenceMapper,\n  responseBody: any,\n  objectName: string,\n  options: RequiredSerializerOptions,\n): any {\n  let element = mapper.type.element;\n  if (!element || typeof element !== \"object\") {\n    throw new Error(\n      `element\" metadata for an Array must be defined in the ` +\n        `mapper and it must of type \"object\" in ${objectName}`,\n    );\n  }\n  if (responseBody) {\n    if (!Array.isArray(responseBody)) {\n      // xml2js will interpret a single element array as just the element, so force it to be an array\n      responseBody = [responseBody];\n    }\n\n    // Quirk: Composite mappers referenced by `element` might\n    // not have *all* properties declared (like uberParent),\n    // so let's try to look up the full definition by name.\n    if (element.type.name === \"Composite\" && element.type.className) {\n      element = serializer.modelMappers[element.type.className] ?? element;\n    }\n\n    const tempArray = [];\n    for (let i = 0; i < responseBody.length; i++) {\n      tempArray[i] = serializer.deserialize(\n        element,\n        responseBody[i],\n        `${objectName}[${i}]`,\n        options,\n      );\n    }\n    return tempArray;\n  }\n  return responseBody;\n}\n\nfunction getIndexDiscriminator(\n  discriminators: Record<string, CompositeMapper>,\n  discriminatorValue: string,\n  typeName: string,\n): CompositeMapper | undefined {\n  const typeNamesToCheck = [typeName];\n  while (typeNamesToCheck.length) {\n    const currentName = typeNamesToCheck.shift();\n    const indexDiscriminator =\n      discriminatorValue === currentName\n        ? discriminatorValue\n        : currentName + \".\" + discriminatorValue;\n    if (Object.prototype.hasOwnProperty.call(discriminators, indexDiscriminator)) {\n      return discriminators[indexDiscriminator];\n    } else {\n      for (const [name, mapper] of Object.entries(discriminators)) {\n        if (\n          name.startsWith(currentName + \".\") &&\n          mapper.type.uberParent === currentName &&\n          mapper.type.className\n        ) {\n          typeNamesToCheck.push(mapper.type.className);\n        }\n      }\n    }\n  }\n\n  return undefined;\n}\n\nfunction getPolymorphicMapper(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n  object: any,\n  polymorphicPropertyName: \"clientName\" | \"serializedName\",\n): CompositeMapper {\n  const polymorphicDiscriminator = getPolymorphicDiscriminatorRecursively(serializer, mapper);\n\n  if (polymorphicDiscriminator) {\n    let discriminatorName = polymorphicDiscriminator[polymorphicPropertyName];\n    if (discriminatorName) {\n      // The serializedName might have \\\\, which we just want to ignore\n      if (polymorphicPropertyName === \"serializedName\") {\n        discriminatorName = discriminatorName.replace(/\\\\/gi, \"\");\n      }\n      const discriminatorValue = object[discriminatorName];\n      const typeName = mapper.type.uberParent ?? mapper.type.className;\n\n      if (typeof discriminatorValue === \"string\" && typeName) {\n        const polymorphicMapper = getIndexDiscriminator(\n          serializer.modelMappers.discriminators,\n          discriminatorValue,\n          typeName,\n        );\n        if (polymorphicMapper) {\n          mapper = polymorphicMapper;\n        }\n      }\n    }\n  }\n  return mapper;\n}\n\nfunction getPolymorphicDiscriminatorRecursively(\n  serializer: Serializer,\n  mapper: CompositeMapper,\n): PolymorphicDiscriminator | undefined {\n  return (\n    mapper.type.polymorphicDiscriminator ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.uberParent) ||\n    getPolymorphicDiscriminatorSafely(serializer, mapper.type.className)\n  );\n}\n\nfunction getPolymorphicDiscriminatorSafely(\n  serializer: Serializer,\n  typeName?: string,\n): PolymorphicDiscriminator | undefined {\n  return (\n    typeName &&\n    serializer.modelMappers[typeName] &&\n    serializer.modelMappers[typeName].type.polymorphicDiscriminator\n  );\n}\n\n/**\n * Known types of Mappers\n */\nexport const MapperTypeNames = {\n  Base64Url: \"Base64Url\",\n  Boolean: \"Boolean\",\n  ByteArray: \"ByteArray\",\n  Composite: \"Composite\",\n  Date: \"Date\",\n  DateTime: \"DateTime\",\n  DateTimeRfc1123: \"DateTimeRfc1123\",\n  Dictionary: \"Dictionary\",\n  Enum: \"Enum\",\n  Number: \"Number\",\n  Object: \"Object\",\n  Sequence: \"Sequence\",\n  String: \"String\",\n  Stream: \"Stream\",\n  TimeSpan: \"TimeSpan\",\n  UnixTime: \"UnixTime\",\n} as const;\n"]}