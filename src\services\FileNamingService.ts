/**
 * File Naming Service - Industry-standard file naming conventions
 */

import {
    IFileNamingService,
    NamingConventions,
    FileNamingConvention,
    TechnologyStack
} from '../interfaces/IProjectScaffold';

export class FileNamingService implements IFileNamingService {
    private conventions: Map<TechnologyStack, NamingConventions> = new Map();

    constructor() {
        this.initializeConventions();
    }

    public standardizeFileName(fileName: string, language: string, projectType: TechnologyStack): string {
        const conventions = this.getNamingConventions(projectType);
        
        // Apply language-specific conventions
        const languageConvention = conventions.files[language];
        if (languageConvention && languageConvention.pattern.test(fileName)) {
            return fileName.replace(languageConvention.pattern, languageConvention.replacement);
        }

        // Apply general naming rules based on project type and language
        return this.applyGeneralNamingRules(fileName, language, projectType);
    }

    public validateFileName(fileName: string, language: string): boolean {
        // Check for invalid characters
        const invalidChars = /[<>:"|?*\x00-\x1f]/;
        if (invalidChars.test(fileName)) {
            return false;
        }

        // Check for reserved names (Windows)
        const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
        if (reservedNames.test(fileName)) {
            return false;
        }

        // Language-specific validation
        return this.validateLanguageSpecific(fileName, language);
    }

    public getNamingConventions(projectType: TechnologyStack): NamingConventions {
        return this.conventions.get(projectType) || this.getDefaultConventions();
    }

    public generateFileNameFromContent(content: string, language: string, projectType: TechnologyStack): string {
        // Extract meaningful name from content
        const extractedName = this.extractNameFromContent(content, language);
        
        if (extractedName) {
            return this.standardizeFileName(extractedName, language, projectType);
        }

        // Fallback to generic name
        return this.generateGenericFileName(language, projectType);
    }

    private initializeConventions(): void {
        // Web Frontend Conventions
        this.conventions.set(TechnologyStack.VANILLA_WEB, this.createWebConventions());
        this.conventions.set(TechnologyStack.REACT_VITE_TS, this.createReactConventions());
        this.conventions.set(TechnologyStack.VUE_VITE_TS, this.createVueConventions());
        this.conventions.set(TechnologyStack.SVELTE_VITE_TS, this.createSvelteConventions());

        // Backend Conventions
        this.conventions.set(TechnologyStack.NODE_EXPRESS_TS, this.createNodeConventions());
        this.conventions.set(TechnologyStack.PYTHON_FASTAPI, this.createPythonConventions());
        this.conventions.set(TechnologyStack.PYTHON_FLASK, this.createPythonConventions());

        // Mobile Conventions
        this.conventions.set(TechnologyStack.REACT_NATIVE_EXPO, this.createReactNativeConventions());

        // Desktop Conventions
        this.conventions.set(TechnologyStack.ELECTRON_TS, this.createElectronConventions());

        // Data Science Conventions
        this.conventions.set(TechnologyStack.PYTHON_DATA_SCIENCE, this.createDataScienceConventions());

        // Game Development Conventions
        this.conventions.set(TechnologyStack.PHASER_TS, this.createGameConventions());
    }

    private createWebConventions(): NamingConventions {
        return {
            files: {
                'html': {
                    pattern: /^generated_.*\.html$/,
                    replacement: 'index.html',
                    description: 'HTML files should use semantic names'
                },
                'css': {
                    pattern: /^generated_.*\.css$/,
                    replacement: 'style.css',
                    description: 'CSS files should use kebab-case'
                },
                'javascript': {
                    pattern: /^generated_.*\.js$/,
                    replacement: 'main.js',
                    description: 'JavaScript files should use camelCase'
                },
                'typescript': {
                    pattern: /^generated_.*\.ts$/,
                    replacement: 'main.ts',
                    description: 'TypeScript files should use camelCase'
                }
            },
            directories: {
                'source': {
                    pattern: /^src$/,
                    replacement: 'src',
                    description: 'Source directory'
                },
                'assets': {
                    pattern: /^assets$/,
                    replacement: 'assets',
                    description: 'Static assets directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private createReactConventions(): NamingConventions {
        return {
            files: {
                'typescript': {
                    pattern: /^generated_.*\.tsx?$/,
                    replacement: 'App.tsx',
                    description: 'React components should use PascalCase'
                },
                'css': {
                    pattern: /^generated_.*\.css$/,
                    replacement: 'App.css',
                    description: 'CSS modules should match component names'
                },
                'javascript': {
                    pattern: /^generated_.*\.jsx?$/,
                    replacement: 'App.jsx',
                    description: 'React components should use PascalCase'
                }
            },
            directories: {
                'components': {
                    pattern: /^components$/,
                    replacement: 'components',
                    description: 'React components directory'
                },
                'hooks': {
                    pattern: /^hooks$/,
                    replacement: 'hooks',
                    description: 'Custom hooks directory'
                },
                'utils': {
                    pattern: /^utils$/,
                    replacement: 'utils',
                    description: 'Utility functions directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private createPythonConventions(): NamingConventions {
        return {
            files: {
                'python': {
                    pattern: /^generated_.*\.py$/,
                    replacement: 'main.py',
                    description: 'Python files should use snake_case'
                }
            },
            directories: {
                'source': {
                    pattern: /^src$/,
                    replacement: 'src',
                    description: 'Source directory'
                },
                'tests': {
                    pattern: /^tests$/,
                    replacement: 'tests',
                    description: 'Tests directory'
                }
            },
            variables: {
                'snake_case': {
                    pattern: /^[a-z][a-z0-9_]*$/,
                    replacement: '$&',
                    description: 'Variables should use snake_case'
                }
            }
        };
    }

    private createNodeConventions(): NamingConventions {
        return {
            files: {
                'typescript': {
                    pattern: /^generated_.*\.ts$/,
                    replacement: 'server.ts',
                    description: 'Node.js files should use camelCase'
                },
                'javascript': {
                    pattern: /^generated_.*\.js$/,
                    replacement: 'server.js',
                    description: 'Node.js files should use camelCase'
                }
            },
            directories: {
                'routes': {
                    pattern: /^routes$/,
                    replacement: 'routes',
                    description: 'API routes directory'
                },
                'middleware': {
                    pattern: /^middleware$/,
                    replacement: 'middleware',
                    description: 'Middleware directory'
                },
                'controllers': {
                    pattern: /^controllers$/,
                    replacement: 'controllers',
                    description: 'Controllers directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private createVueConventions(): NamingConventions {
        return {
            files: {
                'vue': {
                    pattern: /^generated_.*\.vue$/,
                    replacement: 'App.vue',
                    description: 'Vue components should use PascalCase'
                },
                'typescript': {
                    pattern: /^generated_.*\.ts$/,
                    replacement: 'main.ts',
                    description: 'TypeScript files should use camelCase'
                }
            },
            directories: {
                'components': {
                    pattern: /^components$/,
                    replacement: 'components',
                    description: 'Vue components directory'
                },
                'composables': {
                    pattern: /^composables$/,
                    replacement: 'composables',
                    description: 'Vue composables directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private createSvelteConventions(): NamingConventions {
        return {
            files: {
                'svelte': {
                    pattern: /^generated_.*\.svelte$/,
                    replacement: 'App.svelte',
                    description: 'Svelte components should use PascalCase'
                },
                'typescript': {
                    pattern: /^generated_.*\.ts$/,
                    replacement: 'main.ts',
                    description: 'TypeScript files should use camelCase'
                }
            },
            directories: {
                'lib': {
                    pattern: /^lib$/,
                    replacement: 'lib',
                    description: 'Svelte library directory'
                },
                'routes': {
                    pattern: /^routes$/,
                    replacement: 'routes',
                    description: 'SvelteKit routes directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private createReactNativeConventions(): NamingConventions {
        return this.createReactConventions(); // Similar to React
    }

    private createElectronConventions(): NamingConventions {
        return this.createNodeConventions(); // Similar to Node.js
    }

    private createDataScienceConventions(): NamingConventions {
        return {
            files: {
                'python': {
                    pattern: /^generated_.*\.py$/,
                    replacement: 'analysis.py',
                    description: 'Python files should use snake_case'
                },
                'jupyter': {
                    pattern: /^generated_.*\.ipynb$/,
                    replacement: 'notebook.ipynb',
                    description: 'Jupyter notebooks should use snake_case'
                }
            },
            directories: {
                'data': {
                    pattern: /^data$/,
                    replacement: 'data',
                    description: 'Data directory'
                },
                'notebooks': {
                    pattern: /^notebooks$/,
                    replacement: 'notebooks',
                    description: 'Jupyter notebooks directory'
                },
                'models': {
                    pattern: /^models$/,
                    replacement: 'models',
                    description: 'ML models directory'
                }
            },
            variables: {
                'snake_case': {
                    pattern: /^[a-z][a-z0-9_]*$/,
                    replacement: '$&',
                    description: 'Variables should use snake_case'
                }
            }
        };
    }

    private createGameConventions(): NamingConventions {
        return {
            files: {
                'typescript': {
                    pattern: /^generated_.*\.ts$/,
                    replacement: 'Game.ts',
                    description: 'Game files should use PascalCase'
                },
                'javascript': {
                    pattern: /^generated_.*\.js$/,
                    replacement: 'Game.js',
                    description: 'Game files should use PascalCase'
                }
            },
            directories: {
                'scenes': {
                    pattern: /^scenes$/,
                    replacement: 'scenes',
                    description: 'Game scenes directory'
                },
                'assets': {
                    pattern: /^assets$/,
                    replacement: 'assets',
                    description: 'Game assets directory'
                }
            },
            variables: {
                'camelCase': {
                    pattern: /^[a-z][a-zA-Z0-9]*$/,
                    replacement: '$&',
                    description: 'Variables should use camelCase'
                }
            }
        };
    }

    private getDefaultConventions(): NamingConventions {
        return this.createWebConventions();
    }

    private applyGeneralNamingRules(fileName: string, language: string, projectType: TechnologyStack): string {
        // Remove generic prefixes
        let standardized = fileName.replace(/^generated_[^_]*_?/, '');
        
        // Apply language-specific rules
        switch (language.toLowerCase()) {
            case 'html':
                return standardized || 'index.html';
            case 'css':
                return this.toKebabCase(standardized) || 'style.css';
            case 'javascript':
            case 'typescript':
                if (this.isReactProject(projectType)) {
                    return this.toPascalCase(standardized) || 'App.tsx';
                }
                return this.toCamelCase(standardized) || 'main.js';
            case 'python':
                return this.toSnakeCase(standardized) || 'main.py';
            case 'vue':
                return this.toPascalCase(standardized) || 'App.vue';
            case 'svelte':
                return this.toPascalCase(standardized) || 'App.svelte';
            default:
                return standardized || fileName;
        }
    }

    private validateLanguageSpecific(fileName: string, language: string): boolean {
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'typescript':
                // Should not start with number, should use camelCase or PascalCase
                return /^[a-zA-Z_$][a-zA-Z0-9_$]*\.(js|ts|jsx|tsx)$/.test(fileName);
            case 'python':
                // Should use snake_case
                return /^[a-z_][a-z0-9_]*\.py$/.test(fileName);
            case 'css':
                // Should use kebab-case
                return /^[a-z][a-z0-9-]*\.css$/.test(fileName);
            case 'html':
                // Should use kebab-case
                return /^[a-z][a-z0-9-]*\.html$/.test(fileName);
            default:
                return true;
        }
    }

    private extractNameFromContent(content: string, language: string): string | null {
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'typescript':
                return this.extractJsName(content);
            case 'python':
                return this.extractPythonName(content);
            case 'html':
                return this.extractHtmlName(content);
            case 'css':
                return this.extractCssName(content);
            default:
                return null;
        }
    }

    private extractJsName(content: string): string | null {
        // Look for class names, function names, or component names
        const classMatch = content.match(/class\s+([A-Z][a-zA-Z0-9]*)/);
        if (classMatch) return classMatch[1];

        const functionMatch = content.match(/function\s+([a-zA-Z][a-zA-Z0-9]*)/);
        if (functionMatch) return functionMatch[1];

        const componentMatch = content.match(/const\s+([A-Z][a-zA-Z0-9]*)\s*=/);
        if (componentMatch) return componentMatch[1];

        return null;
    }

    private extractPythonName(content: string): string | null {
        // Look for class names or main function
        const classMatch = content.match(/class\s+([A-Z][a-zA-Z0-9]*)/);
        if (classMatch) return this.toSnakeCase(classMatch[1]);

        const functionMatch = content.match(/def\s+([a-zA-Z][a-zA-Z0-9_]*)/);
        if (functionMatch) return functionMatch[1];

        return null;
    }

    private extractHtmlName(content: string): string | null {
        // Look for title tag
        const titleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) return this.toKebabCase(titleMatch[1]);

        return null;
    }

    private extractCssName(content: string): string | null {
        // Look for main class or id
        const classMatch = content.match(/\.([a-zA-Z][a-zA-Z0-9-]*)/);
        if (classMatch) return classMatch[1];

        return null;
    }

    private generateGenericFileName(language: string, projectType: TechnologyStack): string {
        const timestamp = Date.now().toString(36);
        
        switch (language.toLowerCase()) {
            case 'html':
                return 'index.html';
            case 'css':
                return 'style.css';
            case 'javascript':
                return this.isReactProject(projectType) ? 'Component.jsx' : 'main.js';
            case 'typescript':
                return this.isReactProject(projectType) ? 'Component.tsx' : 'main.ts';
            case 'python':
                return 'main.py';
            case 'vue':
                return 'Component.vue';
            case 'svelte':
                return 'Component.svelte';
            default:
                return `file_${timestamp}.${language}`;
        }
    }

    private isReactProject(projectType: TechnologyStack): boolean {
        return [
            TechnologyStack.REACT_VITE_TS,
            TechnologyStack.REACT_NATIVE_EXPO
        ].includes(projectType);
    }

    private toKebabCase(str: string): string {
        return str
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .replace(/[\s_]+/g, '-')
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    private toCamelCase(str: string): string {
        return str
            .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
            .replace(/^[A-Z]/, char => char.toLowerCase())
            .replace(/[^a-zA-Z0-9]/g, '');
    }

    private toPascalCase(str: string): string {
        return str
            .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
            .replace(/^[a-z]/, char => char.toUpperCase())
            .replace(/[^a-zA-Z0-9]/g, '');
    }

    private toSnakeCase(str: string): string {
        return str
            .replace(/([a-z])([A-Z])/g, '$1_$2')
            .replace(/[-\s]+/g, '_')
            .toLowerCase()
            .replace(/[^a-z0-9_]/g, '')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    }
}
