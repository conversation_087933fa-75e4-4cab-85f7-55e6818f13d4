{"version": 3, "file": "BaseClient.mjs", "sources": ["../../src/client/BaseClient.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.networkError", "RequestParameterBuilder.addBrokerParameters", "RequestParameterBuilder.addExtraQueryParameters", "RequestParameterBuilder.addCorrelationId", "RequestParameterBuilder.instrumentBrokerParams", "UrlUtils.mapToQueryString"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAsCH;;;AAGG;MACmB,UAAU,CAAA;IAyB5B,WACI,CAAA,aAAkC,EAClC,iBAAsC,EAAA;;AAGtC,QAAA,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;;AAGtD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;QAGnE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;;QAG/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;QAGjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;QAGlD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;;QAGjE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;;AAGnD,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;AAEG;AACO,IAAA,yBAAyB,CAC/B,OAAuB,EAAA;QAEvB,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,qBAAqB,CAAC;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,EAAE;YAC5D,QAAQ,OAAO,CAAC,IAAI;gBAChB,KAAK,iBAAiB,CAAC,eAAe;oBAClC,IAAI;wBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,UAAU,CACrB,CAAC;AACF,wBAAA,OAAO,CACH,WAAW,CAAC,UAAU,CACzB,GAAG,CAAA,IAAA,EAAO,UAAU,CAAC,GAAG,CAAI,CAAA,EAAA,UAAU,CAAC,IAAI,EAAE,CAAC;AAClD,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACR,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kDAAkD;AAC9C,4BAAA,CAAC,CACR,CAAC;AACL,qBAAA;oBACD,MAAM;gBACV,KAAK,iBAAiB,CAAC,GAAG;oBACtB,OAAO,CACH,WAAW,CAAC,UAAU,CACzB,GAAG,CAAA,KAAA,EAAQ,OAAO,CAAC,UAAU,CAAA,CAAE,CAAC;oBACjC,MAAM;AACb,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;KAClB;AAED;;;;;;AAMG;AACO,IAAA,MAAM,0BAA0B,CACtC,aAAqB,EACrB,WAAmB,EACnB,OAA+B,EAC/B,UAA6B,EAC7B,aAAqB,EACrB,WAAoB,EAAA;AAEpB,QAAA,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,WAAW,EACX,aAAa,CAChB,CAAC;AACL,SAAA;QAED,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,eAAe,CACtB,UAAU,EACV,aAAa,EACb,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,EACvC,aAAa,CAChB,CAAC;AAEN,QAAA,IACI,IAAI,CAAC,MAAM,CAAC,sBAAsB;YAClC,QAAQ,CAAC,MAAM,GAAG,GAAG;AACrB,YAAA,QAAQ,CAAC,MAAM,KAAK,GAAG,EACzB;;AAEE,YAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,CAAC;AAC5D,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;AAED;;;;;;AAMG;IACH,MAAM,eAAe,CACjB,UAA6B,EAC7B,aAAqB,EACrB,OAA8B,EAC9B,aAAqB,EAAA;QAErB,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAE1D,QAAA,IAAI,QAAQ,CAAC;QACb,IAAI;AACA,YAAA,QAAQ,GAAG,MAAM,WAAW,EACxB,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CACxC,IAAI,CAAC,aAAa,CAClB,GACJ,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC1B,YAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;AAC/C,YAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;gBACI,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC;gBAC1D,YAAY,EACR,eAAe,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE;gBACxD,SAAS,EACL,eAAe,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE;aACzD,EACD,aAAa,CAChB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,YAAY,EAAE;AAC3B,gBAAA,MAAM,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;AAC1C,gBAAA,IAAI,eAAe,EAAE;AACjB,oBAAA,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAC7B;wBACI,YAAY,EACR,eAAe,CACX,WAAW,CAAC,iBAAiB,CAChC,IAAI,EAAE;AACX,wBAAA,SAAS,EACL,eAAe,CAAC,WAAW,CAAC,eAAe,CAAC;4BAC5C,EAAE;AACN,wBAAA,iBAAiB,EACb,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC;4BACzC,SAAS;AACb,wBAAA,mBAAmB,EACf,eAAe,CAAC,WAAW,CAAC,cAAc,CAAC;4BAC3C,SAAS;wBACb,UAAU,EAAE,CAAC,CAAC,UAAU;qBAC3B,EACD,aAAa,CAChB,CAAC;AACL,iBAAA;gBACD,MAAM,CAAC,CAAC,KAAK,CAAC;AACjB,aAAA;YACD,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,MAAM,CAAC,CAAC;AACX,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,qBAAqB,CAACA,YAAiC,CAAC,CAAC;AAClE,aAAA;AACJ,SAAA;QAED,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AAErE,QAAA,OAAO,QAAQ,CAAC;KACnB;AAED;;;AAGG;AACH,IAAA,MAAM,eAAe,CACjB,qBAA6B,EAC7B,aAAqB,EAAA;QAErB,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,4BAA4B,EAC9C,aAAa,CAChB,CAAC;QACF,MAAM,yBAAyB,GAAG,CAAA,QAAA,EAAW,qBAAqB,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAA,CAAA,CAAG,CAAC;AAC/F,QAAA,MAAM,sBAAsB,GAAG,MAAM,wBAAwB,CACzD,yBAAyB,EACzB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,GAAG,sBAAsB,CAAC;KAC3C;AAED;;;AAGG;AACH,IAAA,0BAA0B,CAAC,OAAwB,EAAA;AAC/C,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1BC,mBAA2C,CACvC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CACtC,CAAC;AACL,SAAA;QAED,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC9BC,uBAA+C,CAC3C,UAAU,EACV,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACL,SAAA;QAEDC,gBAAwC,CACpC,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAAC,sBAA8C,CAC1C,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;AACF,QAAA,OAAOC,gBAAyB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}